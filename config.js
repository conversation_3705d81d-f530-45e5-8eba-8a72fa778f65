var config = {
    //通用
    serviceIpPort: "http://************:5000/EMS_Finein/Spring/MVC",
    // serviceIpPort: '127.0.0.1:8080',
    // serviceIpPort: '************:8080',
    // ！！！现场环境
    // serviceIpPort: '*************:12121',
    // serviceIpPort: '************:8080',

    // serviceIpPort: '***************:8888',
    schema: "EMS_Finein",
    //租户
    pageSizeDefault: 200,
    //尚格云
    logins: {
        Mindin: 'DistributionMonitor', //配电产品线
        Cpeco: 'ColdSiteMonitor', //冷站产品线
        Wellzoom: 'environmentmonitor', //环境产品线
        Finein: 'tenantmonitor', //租户产品线，可能的值为tenantmonitor、tenantmanage
        //能源产品线，可能的值为quotastandard、energystaanalysis、reportprint、energysaveaccount
        iSagy: 'QuotaStandard',
        Opteam: '' //智慧物业
    },
    //登录页面显示配置
    loginView: {},
    isLinux: false, //是否在Linux下运行（能耗报告模块使用）
    port: 9099,
    
};
module.exports = config;