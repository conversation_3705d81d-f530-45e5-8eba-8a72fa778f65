function tool() {
    this.async = require('async');
    this.fs = require('fs');
    this.toolManager = require('../manager/toolManager');
    this.attachment = require('common/attachmentOper');
    //不需要修改url的F的编码
    this.cloudCodes = {
        mst: {
            name: 'msetting',
            file: '',
            chindName: '组件设置'
        },
        mstr: {
            name: 'modulepreviewrender',
            file: '',
            chindName: '组件预览'
        },
        wc: {
            name: 'WorkCalendar',
            file: 'workcalendar'
        },
        ep: {
            name: 'EnergyReport',
            file: 'energyreport'
        },
        ga: {
            name: 'GlobalAlarm',
            file: 'globalalarm'
        },
        qm: {
            name: 'Equipment',
            file: 'equipment'
        },
        st: {
            name: 'setting',
            file: 'setting',
            chindName: '系统管理'
        },
        mindinset: {
            name: 'mindinset',
            file: 'mindinSet',
            chindName: '支路设置'
        },
        // wp20180831
        // 租赁管理
        Tenant: {
            name: 'Tenant',
            file: 'Tenant',
            chindName: '租赁管理'
        },
        // 物业监控
        PropertyMonitor: {
            name: 'PropertyMonitor',
            file: 'PropertyMonitor',
            chindName: '物业监控'
        },
        // 后台配置
        TenantConfig: {
            name: 'TenantConfig',
            file: 'TenantConfig',
            chindName: '后台配置'
        },
        // 报表打印
        TenantReport: {
            name: 'TenantReport',
            file: 'TenantReport',
            chindName: '报表打印'
        }
    };
    //尚格云产品线编码
    // this.cloudCode = 'PersagyCloud';
    this.cloudCode = 'PersagyCloud';
    this.fileRoot = '/pcloud/';
    this.headerPath = this.fileRoot + 'user'; //存放用户头像的路径
    this.buildImgPath = this.fileRoot + 'build'; //存放建筑图片的路径
    this.buildDocPath = this.fileRoot + 'build'; //存放建筑文档的路径
    this.fIconPath = this.fileRoot + 'f'; //存放F图标的路径
    this.logImgPath = this.fileRoot + 'log'; //存放日志图片的路径
    this.eqFileRoot = this.fileRoot + 'equement/'; //设备台帐内文件的根目录
    this.eqInfoExcel = this.fileRoot + 'eqinfo'; //上传设备时的excel目录
    this.eqRecordImgPath = this.eqFileRoot + 'record'; //存放设备操作记录照片的路径
    this.eqPicPath = this.eqFileRoot + 'pic'; //存放设备照片的路径
    this.eqDrawPath = this.eqFileRoot + 'drawing'; //存放设备图纸的路径
    this.eqQcPath = this.eqFileRoot + 'qcreport'; //存放设备质检报告的路径
    this.eqTagPath = this.eqFileRoot + 'tag'; //存放设备标签logo的路径
    this.modulePath = this.fileRoot + 'module'; //存放用户组件html的路径
    this.moduleHtmlPath = '../views/basic/'; //存放用户组件和预览组件的路径
    this.gisImgPath = this.fileRoot + 'gis'; //存放GIS地图照片的路径
    this.allFList = []; //所有的F列表
    this.allDeparts = []; //所有的部门，平铺的
    this.logins = {}; //用户配置的登录信息
    this.defaultRole = 'PC-Normal'; //所有用户均具有该角色，不可修改。
    this.superUserName = 'persagyAdmin';
    this.adminRole = 'PC-Admin';
    this.allBuildFnTypes = []; //所有的建筑功能类型
    this.allProvincesCr = []; //所有的省份及其市及其区
    this.allAirList = []; //所有的空调类型列表
    this.allEtiList = []; //所有的外保温列表
    this.allHtlList = []; //所有的采暖类型列表
    this.allStlList = []; //所有的建筑结构类型列表
    this.allEilList = []; //所有的经济指标列表
    this.allCrlList = []; //所有的气候分区
    this.allOtlList = []; //所有的建筑朝向
    this.country = '中国'; //国家
    this.mainBuild = {}; //总建筑信息
    this.downLoadUrl = '/pfiledown?id='; //下载路径
    this.report = '/report/';
    this.allF = [];
};

//包装获取总建筑信息
tool.prototype.get_mainBuild = function () {
    this.getMainBuild();
    return this.mainBuild;
}

/*判断F编码是否在不需要url的F编码内*/
tool.prototype.fcodeJudge = function (cpcode, fode) {
    if (cpcode != this.cloudCode) return false;
    for (var cc in this.cloudCodes) {
        if (this.cloudCodes.hasOwnProperty(cc) == false) continue;
        if (this.cloudCodes[cc].name == fode) return this.cloudCodes[cc];
    }
    return false;
};

/*获取所有的F*/
tool.prototype.getAllF = function (call) {
    var _this = this;
    this.toolManager.getAllF(function (err, data) {
        if (err) pLogger.error('getAllF err：' + (err.stack || err));
        data = data || [];
        var cps = [];
        for (var i = 0; i < data.length; i++) {
            var currCp = data[i] || {};
            var objCp = {
                name: currCp.name,
                isDisabled: !currCp.enable,
                code: currCp.id,
                sort: currCp.order,
                isSwitch: currCp.id == _this.cloudCode ? false : true,
                list: []
            };
            var description = '';
            var currFlist = currCp.function || [];
            var fList = [];
            for (var j = 0; j < currFlist.length; j++) {
                var currF = currFlist[j];
                fList.push({
                    name: currF.name,
                    icon: _this.downLoadUrl + currF.icon,
                    rawCode: currF.id,
                    code: currF.id,
                    url: currF.url,
                    isEditUrl: !_this.fcodeJudge(currCp.id, currF.id)
                });
            }

            var currRoleList = currCp.role || [];
            var roles = [];
            for (var k = 0; k < currRoleList.length; k++) {
                var currRole = currRoleList[k];
                roles.push({
                    code: currRole.id,
                    name: currRole.name
                });
                if (currRole.description)
                    description += currRole.name + '：' + currRole.description + '\n';
            }

            // TODO 
            var currF = currFlist[currFlist.length - 1];
            fList.push({
                name: currF.name,
                icon: _this.downLoadUrl + currF.icon,
                rawCode: currF.id,
                code: currF.id,
                url: currF.url,
                isEditUrl: !_this.fcodeJudge(currCp.id, currF.id)
            });

            objCp.list = fList;
            objCp.roles = roles;
            objCp.description = description;
            cps.push(objCp);
        }
        cps.sort(function (a, b) {
            return a.sort - b.sort;
        });
        _this.allFList = cps;
        if (typeof call == 'function') call();
    });
};

/*根据产品线和F编码取得F*/
tool.prototype.getFByCode = function (cpCode, fCode, req) {
    var maxList = req.session.user.allF;
    for (var i = 0; i < maxList.length; i++) {
        var currCp = maxList[i];
        if (currCp.code != cpCode) continue;
        for (var j = 0; j < currCp.list.length; j++) {
            var currF = currCp.list[j];
            if (currF.rawCode.toLowerCase() != fCode.toLowerCase()) continue;
            return currF;
        }
    }
    return null;
};

/*获取所有的部门*/
tool.prototype.getAllDeparts = function (call) {
    var _this = this;
    this.toolManager.getAllDeparts(function (err, data) {
        if (err) pLogger.error('getAllDeparts err：' + (err.stack || err));
        _this.allDeparts = data || [];
        if (typeof call == 'function') call();
    });
};

/*根据部门ID获取对应的部门*/
tool.prototype.getDepartById = function (id) {
    for (var i = 0; i < this.allDeparts.length; i++) {
        if (this.allDeparts[i].id == id) return this.allDeparts[i];
    }
    return null;
};

/*获取建筑功能类型及其下二级类型*/
tool.prototype.getBuildFnType = function () {
    var _this = this;
    this.toolManager.getBuildFnType('0', function (err, result) {
        if (err) pLogger.error('getBuildFnType err：' + (err.stack || err));
        result = result || [];
        var index = 0;
        _this.async.whilst(
            function () {
                return index < result.length;
            },
            function (callback) {
                var currCode = result[index].code;
                _this.toolManager.getBuildFnType(currCode, function (err2, result2) {
                    if (err2)
                        pLogger.error('code：' + currCode + ' getBuildFnType err：' + (err2.stack || err2));
                    result[index].child = result2 || [];
                    index++;
                    callback(err2);
                });
            },
            function (err3) {
                _this.allBuildFnTypes = result;
            }
        );
    });
};

/*获取省及其市及其区*/
tool.prototype.getBuildAreaList = function () {
    var _this = this;
    this.toolManager.getBuildAreaList('0', function (err, result) {
        if (err) pLogger.error('getBuildAreaList err：' + (err.stack || err));
        result = result || [];
        var index = 0;
        _this.async.whilst(
            function () {
                return index < result.length;
            },
            function (callback) {
                var currCode = result[index].code;
                result[index].child = [];
                _this.toolManager.getBuildAreaList(currCode, function (err2, result2) {
                    if (err2)
                        pLogger.error('获取code：' + currCode + '省的市 err：' + (err2.stack || err2));
                    var index2 = 0;
                    _this.async.whilst(function () {
                        return index2 < result2.length;
                    }, function (callback2) {
                        var cityCode = result2[index2].code;
                        _this.toolManager.getBuildAreaList(cityCode, function (errRegion, resultRegion) {
                            if (errRegion)
                                pLogger.error('获取code：' + cityCode + '市的区 err：' +
                                    (errRegion.stack || errRegion));
                            result2[index2].child = resultRegion || [];
                            index2++;
                            callback2(errRegion);
                        });
                    }, function (callback2Err) {
                        result[index].child = result2;
                        index++;
                        callback(err2 || callback2Err);
                    });
                });
            },
            function (err3) {
                _this.allProvincesCr = result;
                _this.getMainBuild();
            }
        );
    });
};

/*根据编码从缓存中获取不同的对象*/
tool.prototype.getObjByCode = function (type, code1, code2, code3) {
    var listObj = {
        ftp: this.allBuildFnTypes, //所有的建筑功能类型
        pri: this.allProvincesCr, //所有的省份及其市及其区
        air: this.allAirList, //所有的空调类型列表
        eti: this.allEtiList, //所有的外保温列表
        htl: this.allHtlList, //所有的采暖类型列表
        stl: this.allStlList, //所有的建筑结构类型列表
        eil: this.allEilList, //所有的经济指标列表
        crl: this.allCrlList, //所有的气候分区
        otl: this.allOtlList //所有的建筑朝向
    };
    var list = listObj[type];
    switch (type) {
        case 'ftp':
            for (var i = 0; i < list.length; i++) {
                var curr = list[i];
                var childList = curr.child || [];
                for (var j = 0; j < childList.length; j++) {
                    var currChild = childList[j];
                    if (currChild.code == code1) {
                        return [curr, currChild];
                    }
                }
            }
            return [];
        case 'pri':
            for (var i = 0; i < list.length; i++) {
                var currProvince = list[i];
                if (currProvince.code != code1) continue;

                var cityList = currProvince.child || [];
                for (var j = 0; j < cityList.length; j++) {
                    var currCity = cityList[j];
                    if (currCity.code != code2) continue;

                    var regionList = currCity.child || [];
                    for (var k = 0; k < regionList.length; k++) {
                        var currRegion = regionList[k];
                        if (currRegion.code != code3) continue;
                        return [currProvince, currCity, currRegion];
                    }
                }
            }
            return [];
        default:
            for (var i = 0; i < list.length; i++) {
                var curr = list[i];
                if (curr.code == code1) return curr;
            }
            return {};
    }
};


/*获取空调类型列表*/

/*获取外保温列表*/

/*获取采暖类型列表*/

/*获取建筑结构类型列表*/

/*获取经济指标列表*/

/*获取所有的气候分区列表*/

/*获取朝向列表*/

/*根据编码从缓存中获取不同的对象*/

/*上传建筑图片和文档*/
tool.prototype.uploadBuildFile = function (build, call) {
    var _this = this;
    var buildInfo = build.building || {};
    var functionArr = [];
    //上传建筑图片和文档
    var files = [];
    buildInfo.picture1.newFile ? functionArr.push(function (seriesCall) {
        _this.attachment.upload({
            files: {
                file: {
                    path: buildInfo.picture1.newFile
                },
                subdirectory: _this.buildImgPath
            },
            call: function (uploadSmallErr, uploadSmallResult) {
                uploadSmallResult = (uploadSmallResult ? uploadSmallResult[0] : null) || {};
                if (uploadSmallErr)
                    pLogger.error('uploadSmallErr err：' + (uploadSmallErr.stack || uploadSmallErr));
                if (!uploadSmallErr && !uploadSmallResult.id) {
                    uploadSmallErr = '上传建筑小图片失败';
                    pLogger.error(uploadSmallErr);
                }
                buildInfo.picture1 = uploadSmallResult.id || '';
                seriesCall(uploadSmallErr);
            }
        });
    }) : buildInfo.picture1 = buildInfo.picture1.oldFile || '';
    buildInfo.picture2.newFile ? functionArr.push(function (seriesCall) {
        _this.attachment.upload({
            files: {
                file: {
                    path: buildInfo.picture2.newFile
                },
                subdirectory: _this.buildImgPath
            },
            call: function (uploadSmallErr, uploadSmallResult) {
                uploadSmallResult = (uploadSmallResult ? uploadSmallResult[0] : null) || {};
                if (uploadSmallErr)
                    pLogger.error('uploadBigErr err：' + (uploadSmallErr.stack || uploadSmallErr));
                if (!uploadSmallErr && !uploadSmallResult.id) {
                    uploadSmallErr = '上传建筑大图片失败';
                    pLogger.error(uploadSmallErr);
                }
                buildInfo.picture2 = uploadSmallResult.id || '';
                seriesCall(uploadSmallErr);
            }
        });
    }) : buildInfo.picture2 = buildInfo.picture2.oldFile || '';
    var proPrefix = 'blueprint';

    for (var xy = 1; xy < 9; xy++) {
        var currPp = buildInfo[proPrefix + xy] || {};
        var newDocs = currPp.newFiles || [];
        var oldDocs = currPp.oldFiles || [];
        var uploadFiles = [];
        for (var jk = 0; jk < newDocs.length; jk++) {
            uploadFiles.push({
                file: newDocs[jk],
                subdirectory: _this.buildDocPath
            });
        }
        if (uploadFiles.length > 0) {
            functionArr.push((function (uf, od, index) {
                return function (seriesCall) {
                    _this.attachment.upload({
                        files: uf,
                        call: function (uploadSmallErr, uploadSmallResult) {
                            uploadSmallResult = uploadSmallResult || [];
                            if (uploadSmallErr)
                                pLogger.error('上传建筑文档 err：' + (uploadSmallErr.stack || uploadSmallErr));
                            if (!uploadSmallErr && uploadSmallResult.length == 0) {
                                uploadSmallErr = '上传建筑文档失败';
                                pLogger.error(uploadSmallErr);
                            }
                            od = od || [];
                            for (var yk = 0; yk < uploadSmallResult.length; yk++) {
                                var currYk = uploadSmallResult[yk];
                                od.push({
                                    id: currYk.id,
                                    name: currYk.name,
                                    suffix: currYk.suffix
                                });
                            }
                            buildInfo[proPrefix + index] = JSON.stringify(od);
                            seriesCall(uploadSmallErr);
                        }
                    });
                }
            })(uploadFiles, oldDocs, xy));
        } else {
            buildInfo[proPrefix + xy] = JSON.stringify(oldDocs);
        }
    }


    _this.async.series(functionArr,
        function (err, results) {
            call(err, build);
        });
};

/*登录*/
tool.prototype.login = function (userId, pass, req, call) {
    var _this = this;
    _this.toolManager.login(userId, pass, function (err, result) {
        result = (result ? result[0] : {}) || {};
        if (!result.id) err = '无效的用户：' + userId;
        if (err)
            return call(err);
        //默认头像
        var headerIcon = '/img/frame/user/user-big.png';
        if (result.headPortrait) {
            _this.attachment.createDownUrl({
                req: req,
                realPath: result.headPortrait,
                call: function (err2, result2) {
                    if (err2)
                        err2 = '获取头像地址 err：' + (err2.stack || err2);
                    headerIcon = result2 ? result2 : headerIcon;
                    send();
                }
            });
        } else send();

        function send(sendErr) {
            var struct = (result.departmentId ? _this.getDepartById(result.departmentId) : null) || {};
            var isAdmin = false;
            var isSuper = _this.superUserName == result.name ? true : false;
            isAdmin = isSuper == true ? true : isAdmin;

            var currFlist = result.functionList || [];
            var fList = [];
            for (var j = 0; j < currFlist.length; j++) {
                var currF = currFlist[j];
                fList.push({
                    name: currF.name || "租赁空间管理",
                    icon: _this.downLoadUrl + currF.icon,
                    rawCode: currF.id,
                    // rawCode: 'TenantReport',
                    code: currF.id,
                    // url: "./TenantReport",
                    url: currF.url
                    //isEditUrl: !_this.fcodeJudge(currCp.id, currF.id)
                });
            }
            var allF = [{
                "name": "租赁空间管理与付费系统",
                "isDisabled": false,
                "code": "Finein",
                "sort": 5,
                "isSwitch": true,
                list: fList
            }]
            req.session.user = {
                id: result.id,
                userId: result.name,
                name: result.showName,
                icon: headerIcon,
                email: result.email,
                phone: result.mobile,
                structName: struct.name,
                structId: struct.id,
                eps: pass,
                sid: req.session.id,
                isSuper: isSuper,
                isAdmin: isAdmin,
                allF: allF
            };
            _this.allF = req.session.user.allF;
            req.session.puser = {
                id: result.id,
                name: result.name,
                showName: result.showName,
                mobile: result.mobile,
                email: result.email,
            };
            call(sendErr);
        }
    });
};

//免密登录
tool.prototype.noPassLogin = function (userId, token, systemCode, productId, req, call) {
    var _this = this;
    _this.toolManager.noPassLogin(userId, token, systemCode, productId, function (err, result) {
        result = (result ? result[0] : {}) || {};
        if (!result.id) err = '无效的用户：' + userId;
        if (err)
            return call(err);
        //默认头像
        var headerIcon = '/img/frame/user/user-big.png';
        if (result.headPortrait) {
            _this.attachment.createDownUrl({
                req: req,
                realPath: result.headPortrait,
                call: function (err2, result2) {
                    if (err2)
                        err2 = '获取头像地址 err：' + (err2.stack || err2);
                    headerIcon = result2 ? result2 : headerIcon;
                    send();
                }
            });
        } else send();

        function send(sendErr) {
            var struct = (result.departmentId ? _this.getDepartById(result.departmentId) : null) || {};
            var isSuper = _this.superUserName == result.name ? true : false;
            var isAdmin = false;
            isAdmin = isSuper == true ? true : isAdmin;

            var currFlist = result.functionList || [];
            var fList = [];
            for (var j = 0; j < currFlist.length; j++) {
                var currF = currFlist[j];
                fList.push({
                    name: currF.name || "租赁空间管理",
                    icon: _this.downLoadUrl + currF.icon,
                    rawCode: currF.id,
                    code: currF.id,
                    url: currF.url
                });
            }
            var allF = [{
                "name": "租赁空间管理与付费系统",
                "isDisabled": false,
                "code": "Finein",
                "sort": 5,
                "isSwitch": true,
                list: fList
            }]
            req.session.user = {
                id: result.id,
                userId: result.name,
                name: result.showName,
                icon: headerIcon,
                email: result.email,
                phone: result.mobile,
                structName: struct.name,
                structId: struct.id,
                sid: req.session.id,
                isSuper: isSuper,
                isAdmin: isAdmin,
                allF: allF
            };
            _this.allF = req.session.user.allF;
            req.session.puser = {
                id: result.id,
                name: result.name,
                showName: result.showName,
                mobile: result.mobile,
                email: result.email,
            }
            call(sendErr);
        }
    });
};

/*根据用户Id（用户标识，非登录名称）获取用户信息*/
tool.prototype.getUserById = function (userId, call) {
    this.toolManager.getUserById(userId, call);
};

/*获取总建筑信息*/
tool.prototype.getMainBuild = function (call) {
    var _this = this;
    _this.toolManager.getMainBuild(function (err, result) {
        if (err)
            return pLogger.error('getMainBuild err：' + (err.stack || err)), callFn();
        result = (result ? result[0][0] : null) || {};
        result = result.building || {};
        var provices = _this.getObjByCode('pri', result.province, result.city, result.partition);
        var icon = result.picture1 ? _this.downLoadUrl + result.picture1 : '/img/frame/build/build.png';
        var sendObj = {
            id: result.id,
            name: result.name || '项目名称',
            icon: icon,
            country: _this.country,
            province: (provices[0] || {}).name,
            city: (provices[1] || {}).name,
            region: (provices[2] || {}).name
        };
        _this.mainBuild = sendObj;
        callFn();
    });

    function callFn() {
        if (typeof call == 'function') call();
    }
};

/*获取用户组件配置*/
tool.prototype.getUserModule = function (id, call) {
    this.toolManager.getUserModule(id, call);
};

/*访问量统计*/
tool.prototype.requestStatis = function (req, call) {
    var userInfo = req.session.user;
    if (!userInfo || !userInfo.id) return typeof call == 'function' ? call('保存访问量时，丢失用户信息') : '';
    var requestRecordId = userInfo.requestRecordId || '';
    var userId = userInfo.userId;
    var currTime = new Date().format('y-M-d h:m:s');
    var clientIp = req.headers['x-forwarded-for'] || (req.connection || {}).remoteAddress ||
        (req.socket || {}).remoteAddress ||
        ((req.connection || {}).socket || {}).remoteAddress || '';
    this.toolManager.requestStatis(requestRecordId, userId, currTime, clientIp, function (err, result) {
        if (err) console.error('保存访问量错误' + (err.stack || JSON.stringify(err)));
        result = (result ? result[0] : {}) || {};
        if (typeof call == 'function')
            call(err, result);
    });
};


module.exports = new tool();