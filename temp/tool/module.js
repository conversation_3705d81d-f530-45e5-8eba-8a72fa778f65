/*关于factors中的type 可能的值包括radio 单选按钮   combox 下拉框   multi 多选   multi2 带切换的多选，但前台界面只有一个下拉列表
*multigl 配置界面是树形菜单，前台是联动下拉列表
*/

/*关于条件中的项、默认项、默认索引
*需后台在返回默认项和所有项时，每项均具有id、name属性
*   defaultItems: [{ id: '', name: '电' }, { id: '', name: '水' }, { id: '', name: '冷' }
*   , { id: '', name: '热' }, { id: '', name: '蒸汽' }, { id: '', name: '燃气' }],
*   defaultIndex:0,
*   items:[id:'',name:'',parentId:'']
*/

var allModules = [{
    name: '尚格云', code: 'PersagyCloud', modules: [{
        code: 'pc-buildDescription',
        title: '建筑简介', description: '展示建筑的基本信息，由建筑图片和建筑信息文字简介组成。',
        sizes: ['1*1', '2*1'], factors: [],
        url: '', interval: 0
    }, {
        code: 'pc-notices',
        title: '公告信息', description: '展示工作历中公告栏内的信息。',
        sizes: ['1*1'], factors: [],
        url: '/PersagyCloud/WorkCalendar', interval: 0
    }, {
        code: 'pc-reporttemplate',
        title: '最常使用报告模板', description: '展示用户生成报告数最多的五个模板，供快捷使用。',
        sizes: ['1*1'], factors: [],
        url: '/PersagyCloud/EnergyReport', interval: 0
    }]
}, {
    name: '能源管理系统', code: 'iSagy', modules: [{
        code: 'isagy-zrnhcdefx',                   //组件编码
        title: '昨日能耗超预算分项',    //组件标题
        //组件描述
        description: '按降序显示昨日能耗超预算值的所有分项，掌握昨日能耗异常项，为今日运行提供参考。',
        sizes: ['2*1'],  //组件尺寸
        factors: [],
        url: '/iSagy/QuotaStandard', interval: 0
    }, {
        code: 'isagy-nhjhjc',
        title: '能耗计划监测',
        description: '显示当前能耗计划的进展情况，以指标的形式展示预期能耗目标值、当前能耗值以及能耗预测值，显示能耗计划中每月的能耗预算和实际能耗。',
        sizes: ['1*1', '3*1', '4*1'],
        factors: [],
        url: '/iSagy/QuotaStandard', interval: 0
    }, {
        code: 'isagy-nhydejc',
        title: '能耗与预算监测',
        description: '显示日能耗以及逐时能耗与预算的关系，显示月能耗以及逐日能耗与预算的关系。',
        sizes: ['1*1', '3*1', '4*1'],
        factors: [],
        url: '/iSagy/QuotaStandard', interval: 0
    }, {
        code: 'isagy-tqhqnhzbdb',
        title: '同气候区能耗指标对比',
        description: '同类型、同气候区建筑的单平米能耗指标对比。',
        sizes: ['2*1', '3*1', '4*1'],
        factors: [],
        url: '', interval: 0
    }, {
        code: 'isagy-lsnhzb',
        title: '历史能耗指标',
        description: '显示历史年单平米能耗指标，查看历史能耗趋势。',
        sizes: ['2*1', '3*1', '4*1'],
        factors: [],
        url: '', interval: 0
    }, {
        code: 'isagy-fxnhzb',
        title: '分项能耗占比',
        description: '显示分项今日/本月/本年的能耗以及占比情况，分析能耗同比的变化趋势。',
        sizes: ['3*1', '4*1'],
        factors: [{
            code: 'fxNhZbNhMoXing',
            name: '选择能耗模型',
            type: 'multi'
        }],
        url: '/iSagy/EnergyAnalysis', interval: 0
    }, {
        code: 'isagy-nhlsdb',
        title: '能耗历史对比',
        description: '显示建筑总能耗今日/本月/本年的能耗及时间分布情况，分析能耗历史同比与环比趋势。',
        sizes: ['3*1', '4*1'],
        factors: [{
            code: 'nhLsDbNhLx',
            name: '选择能耗类型',
            type: 'multi2'
        }, {
            code: 'nhLsDbDbLx',
            name: '选择对比类型',
            type: 'multi'
        }],
        url: '/iSagy/EnergyAnalysis', interval: 0
    }, {
        code: 'isagy-fxnhjc',
        title: '分项能耗监测',
        description: '设置重点分项，对其能耗进行检测，并从同比、环比以及下级能耗分布等维度对其进行分析。',
        sizes: ['2*1'],
        factors: [{
            code: 'fxNhJcHymxFxjd',
            name: '选择能耗模型',
            type: 'multi2'
        }],
        url: '/iSagy/QuotaStandard', interval: 0
    }, {
        code: 'isagy-nhrl',
        title: '能耗日历',
        description: '以历史的形式展示能耗以及同预算的关系。',
        sizes: ['2*1'],
        factors: [],
        url: '/iSagy/QuotaStandard', interval: 0
    }]
}, {
    name: '冷站群控', code: 'Cpeco', modules: [{
        code: 'cpeco-duolznhtj',
        title: '多冷站能耗统计',
        description: '冷站系统分项能耗对比分析，包含多个冷站冷机组总能耗、冷冻泵组总能耗、冷却泵组总能耗、冷却塔总能耗',
        sizes: ['1*1'],
        factors: [{
            code: 'cpecoDuoLzNhTjNhzLx',
            name: '选择能耗值类型',
            type: 'multi'
        }],
        url: '', interval: 0
    }, {
        code: 'cpeco-danlznhtj',
        title: '单冷站能耗统计',
        description: '冷站系统分项能耗占比分析，包含单个冷站冷机组总能耗、冷冻泵组总能耗、冷却泵组总能耗、冷却塔总能耗。',
        sizes: ['1*1'],
        factors: [{
            code: 'cpecoDanLzNhTjNhzLx',
            name: '选择能耗值类型',
            type: 'multi'
        }],
        url: '', interval: 0
    }, {
        code: 'cpeco-lzxnpg',
        title: '冷站性能评估',
        description: '评价单冷站运行效果，横向对比多冷站运行效果，包含多个冷站EER、冷机COP、冷冻泵输送系数、冷却塔输送系数、冷却塔风机输送系数，同一标尺横向对比多冷站性能指标，亦可展示多冷站性能指标的实时对比曲线。',
        sizes: ['2*1', '3*1', '4*1'],
        factors: [{
            code: 'cpecoLzxnpgNhblx',
            name: '选择能耗比类型',
            type: 'multi'
        }],
        url: '', interval: 0
    }, {
        code: 'cpeco-lzxtyxzt',
        title: '冷站系统运行状态',
        description: '实时监测冷站运行状态，及计划与实际运行情况分析，包含冷机开关状态、各供冷区域时间表启停计划、各供冷区域实际启停进度条。',
        sizes: ['2*1'],
        factors: [{
            code: 'cpecoLzxtyxztLz',
            name: '选择冷站',
            type: 'multi'
        }],
        url: '/Cpeco/ColdSiteMonitor', interval: 0
    }, {
        code: 'cpeco-duolznhyhzqx',
        title: '多冷站能耗与焓值曲线',
        description: '分析室外气象因素对冷站能耗的影响，包含多个冷站逐时能耗对比柱状图，及各冷站所在地区室外空气焓值实时曲线',
        sizes: ['2*1', '3*1', '4*1'],
        factors: [],
        url: '', interval: 0
    }, {
        code: 'cpeco-danlznhyhzqx',
        title: '单冷站能耗与焓值曲线',
        description: '分析室外气象因素对冷站能耗的影响，包含多个冷站逐时能耗对比柱状图，及各冷站所在地区室外空气焓值实时曲线。',
        sizes: ['2*1'],
        factors: [{
            code: 'cpecoDanlznhyhzqxLz',
            name: '选择冷站',
            type: 'multi'
        }],
        url: '', interval: 0
    }, {
        code: 'cpeco-swqxcs',
        title: '室外气象参数',
        description: '包含室外空气干球温度、湿球温度、相对湿度、焓值',
        sizes: ['2*1'],
        factors: [],
        url: '', interval: 0
    }, {
        code: 'cpeco-lzfwqyhjpz',
        title: '冷站服务区域环境品质',
        description: '评估冷站服务区域室内环境品质，包含室内所有温湿度测点的干球温度最大值、最小值、中位数、上四分位数、下四分位数，最终呈现为箱图。',
        sizes: ['2*1', '3*1', '4*1'],
        factors: [{
            code: 'cpecoLzfwqyhjpzLz',
            name: '选择冷站',
            type: 'multi'
        }],
        url: '', interval: 0
    }, {
        code: 'cpeco-sssbycjyjfx',
        title: '实时设备扬程及压降分析',
        description: '展示阻力部件的局部阻力值及水泵扬程',
        sizes: ['2*1', '3*1', '4*1'],
        factors: [{
            code: 'cpecoSssbycjyjfxLzsb',
            name: '选择冷站设备',
            type: 'multigl'
        }],
        url: '/Cpeco/ColdSiteAnalysis', interval: 0
    }, {
        code: 'cpeco-duolzkzcl',
        title: '多冷站控制策略',
        description: '了解冷站优化控制概况，包含多冷站的冷机、冷冻泵、冷却泵、冷却塔风机优化控制策略。',
        sizes: ['2*1', '3*1'],
        factors: [],
        url: '/Cpeco/ColdSiteMonitor', interval: 0
    }, {
        code: 'cpeco-danlzkzcl',
        title: '单冷站控制策略',
        description: '了解冷站优化控制概况，包含多冷站的冷机、冷冻泵、冷却泵、冷却塔风机优化控制策略',
        sizes: ['1*1'],
        factors: [{
            code: 'cpecoDanlzkzclLz',
            name: '选择冷站',
            type: 'multi'
        }],
        url: '/Cpeco/ColdSiteMonitor', interval: 0
    }, {
        code: 'cpeco-lzfhyc',
        title: '冷站负荷预测',
        description: '根据今日气象参数及历史运行数据，预测今日负荷曲线，对比分析今日实际负荷曲线',
        sizes: ['2*1', '3*1'],
        factors: [{
            code: 'cpecoLzfhycLz',
            name: '选择冷站',
            type: 'multi'
        }],
        url: '', interval: 0
    }]
}, {
    name: '配电监控', code: 'Mindin', modules: [{
        code: 'mindin-byqwdpm',
        title: '变压器温度排名',
        description: '显示每台变压器的ABC三相温度，按照温度从高到低排列（以变压器为单位，某一相温度最高此变压器即排在首位）。',
        sizes: ['1*1', '2*1', '3*1'],
        factors: [],
        url: '/Mindin/DistributionMonitor', interval: 120
    }, {
        code: 'mindin-byqfzlpm1',
        title: '变压器负载率排名-1',
        description: '按照负载率值从大到小排列，可显示当前功率（或者视在功率，如果采集了视在功率，需要显示视在功率）、额定容量和负载率。只列出前三名。',
        sizes: ['1*1'],
        factors: [],
        url: '/Mindin/DistributionMonitor', interval: 120
    }, {
        code: 'mindin-byqfzlpm2',
        title: '变压器负载率排名-2',
        description: '按照负载率值从大到小排列，可显示当前功率（或者视在功率，如果采集了视在功率，需要显示视在功率）、额定容量和负载率。显示所有的变压器负载率。',
        sizes: ['1*1', '2*1'],
        factors: [],
        url: '/Mindin/DistributionMonitor', interval: 120
    }, {
        code: 'mindin-sssjjc',
        title: '实时数据监测',
        description: '此组件主要用于显示电参量曲线',
        sizes: ['2*1'],
        factors: [{
            code: 'mindinSssjjcZl',
            name: '选择支路',
            type: 'multi'
        }, {
            code: 'mindinSssjjcCs',
            name: '选择参数',
            type: 'multi'
        }],
        url: '', interval: 120
    }, {
        code: 'mindin-byqfhfb',
        title: '变压器负荷分布',
        description: '显示全部变压器的当前运行负荷柱状图，阴影部分显示历史最高负荷。组件上部显示总负荷。',
        sizes: ['2*1', '3*1', '4*1'],
        factors: [],
        url: '', interval: 0
    }, {
        code: 'mindin-byqdnfb',
        title: '变压器电能分布',
        description: '显示全部变压器当天截止到前一小时用电量，组件上部显示当天截止到前一小时总用电量。',
        sizes: ['2*1', '3*1', '4*1'],
        factors: [{
            code: 'mindinByqdnfbSjlx',
            name: '选择查看类型',
            type: 'multi'
        }],
        url: '', interval: 0
    }]
}


//, {
//    name: '租赁空间管理与付费系统', code: 'Finein', modules: [{
//        code: 'finein-yffzhsytspm',
//        title: '预付费租户剩余天数排名',
//        description: '显示当前预付费剩余天数最少的租户排名，物业人员可以及时提醒租户充值缴费',
//        sizes: ['2*1', '3*1', '4*1'],
//        factors: [{
//            code: 'fineinYffzhsytspmJz',
//            name: '选择建筑',
//            type: 'multi'
//        }, {
//            code: 'fineinYffzhsytspmNhlx',
//            name: '选择能耗类型',
//            type: 'multi'
//        }]
//    }, {
//        code: 'finein-hffzhqfjepm',
//        title: '后付费租户欠费金额排名',
//        description: '显示当前预付费剩余天数最少的租户排名，物业人员可以及时提醒租户充值缴费',
//        sizes: ['3*1', '4*1'],
//        factors: [{
//            code: 'fineinHffzhqfjepmJz',
//            name: '选择建筑',
//            type: 'multi'
//        }, {
//            code: 'fineinHffzhqfjepmNhlx',
//            name: '选择能耗类型',
//            type: 'multi'
//        }]
//    }, {
//        code: 'finein-zhdfhlpm',
//        title: '租户电负荷率排名',
//        description: '实时显示电功率负载率最大的租户排名，物业人员需对高负载租户调整配电容量。',
//        sizes: ['3*1', '4*1'],
//        factors: [{
//            code: 'fineinZhdfhlpmJz',
//            name: '选择建筑',
//            type: 'multi'
//        }]
//    }, {
//        code: 'finein-zrnhzzlqsm',
//        title: '昨日能耗增长率前十名',
//        description: '分析统计昨日租户运行状况，对能耗突增租户需进行检查是否正常。',
//        sizes: ['1*1', '2*1'],
//        factors: [{
//            code: 'fineinZrnhzzlqsmJz',
//            name: '选择建筑',
//            type: 'multi'
//        }, {
//            code: 'fineinZrnhzzlqsmJklx',
//            name: '选择监控类型',
//            type: 'multi'
//        }]
//    }]
//}
//, {
    //name: '环境与健康监测系统', code: 'Wellzoom', modules: [
    // ,{
    //    code: 'wellzoom-j24xshjfxqx',
    //    title: '近24小时环境分析曲线',
    //    description: '该组件可以指定几个重点空间在首页快速查看24小时内每5分钟的环境曲线走势。',
    //    sizes: ['2*1', '3*1'],
    //    factors: [{
    //        code: 'wellzoomJ24xshjfxqxKj',
    //        name: '选择空间',
    //        type: 'combox'
    //    }, {
    //        code: 'wellzoomJ24xshjfxqxJklx',
    //        name: '选择监控类型',
    //        type: 'multi'
    //    }]
    //}
    //, {
    //    code: 'wellzoom-j30rhjfxqx',
    //    title: '近30日环境分析曲线',
    //    description: '该组件可以指定几个重点空间在首页快速查看30天内每1小时的环境曲线走势。',
    //    sizes: ['2*1', '3*1'],
    //    factors: [{
    //        code: 'wellzoomJ30rhjfxqxKj',
    //        name: '选择空间',
    //        type: 'combox'
    //    }, {
    //        code: 'wellzoomJ30rhjfxqxJklx',
    //        name: '选择监控类型',
    //        type: 'multi'
    //    }]
    //}, {
    //    code: 'wellzoom-bjcxsjpm',
    //    title: '报警持续时间排名',
    //    description: '该组件可以展现在一段时间内，空间持续报警时间长短的排名，针对排名靠前的空间可以重点检查问题。',
    //    sizes: ['3*1', '4*1'],
    //    factors: [{
    //        code: 'wellzoomBjcxsjpmKj',
    //        name: '选择空间',
    //        type: 'combox'
    //    }, {
    //        code: 'wellzoomBjcxsjpmJklx',
    //        name: '选择监控类型',
    //        type: 'multi'
    //    }, {
    //        code: 'wellzoomBjcxsjpmPmlx',
    //        name: '选择排名类型',
    //        type: 'multi'
    //    }]
    //}, {
    //    code: 'wellzoom-wclts',
    //    title: '未处理投诉',
    //    description: '该组件可以实时显示目前存在投诉空间数量统计。',
    //    sizes: ['2*1'],
    //    factors: [{
    //        code: 'wellzoomWcltsKj',
    //        name: '选择空间',
    //        type: 'combox'
    //    }, {
    //        code: 'wellzoomWcltsJklx',
    //        name: '选择监控类型',
    //        type: 'multi'
    //    }]
    //}, {
    //    code: 'wellzoom-jcdxxtj',
    //    title: '监测点信息统计',
    //    description: '该组件可以实时显示故障点位信息，协助物业人员及时恢复故障点位。',
    //    sizes: ['1*1'],
    //    factors: [{
    //        code: 'wellzoomJcdxxtjJz',
    //        name: '选择建筑',
    //        type: 'multi'
    //    }]
    //}, {
    //    code: 'wellzoom-kjxxtj',
    //    title: '空间信息统计',
    //    description: '该组件可以实时显示目前报警空间数量统计。',
    //    sizes: ['1*1'],
    //    factors: [{
    //        code: 'wellzoomKjxxtjJz',
    //        name: '选择建筑',
    //        type: 'multi'
    //    }]
    //}
    //]
//}
];

module.exports = allModules;