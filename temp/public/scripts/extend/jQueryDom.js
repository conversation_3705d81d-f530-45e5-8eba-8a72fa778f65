/*扩展jQuery、htmlelement方法*/
void function () {
    var extendFn = {
        /*启用或禁用按钮 disabled true禁用 false启用*/
        pdisable: function (disabled) {
            var target = $(this)[0];
            if (!target) return;
            target.setAttribute('pdisabled', disabled);
        },
        /*某元素注册mouseenter、mouseleave 以实现色彩变化
        *changeType  0 背景色变化(默认)   1 字体颜色变化    2 两者同时变化
        *changeTarget 背景色、字体变化的元素，默认当前注册事件的元素
        */
        registerEventForColorChange: function (changeTarget, changeType) {
            var jqTarget = $(this);
            if (jqTarget.length > 1) {
                jqTarget.each(function () {
                    this.registerEventForColorChange(changeTarget, changeType);
                });
                return;
            }

            changeType = parseInt(changeType) || 0;
            var jqChangeTarget = changeTarget ? $(changeTarget) : jqTarget;
            //jqChangeTarget = jqChangeTarget.length > 1 ? jqTarget : jqChangeTarget;
            var domTarget = ptool.getDomElement(jqChangeTarget);
            var oldCssArr = [];

            jqTarget.off('mouseenter'); // 先解绑 否则有可能会执行两遍
            jqTarget.on({
                mouseenter: function (event) {
                    getColor();
                    setElementColor();
                },
                mouseleave: function (event) {
                    var _oldCssArr = domTarget[pconst.targetHoverCssSourcePro] || [];
                    for (var i = 0; i < _oldCssArr.length; i = i + 2) {
                        var cssName = _oldCssArr[i];
                        var cssColor = _oldCssArr[i + 1];
                        jqChangeTarget.css(cssName, cssColor);
                    }
                    oldCssArr = [];
                    domTarget[pconst.targetHoverCssSourcePro] = [];
                }
            });

            function getColor() {
                oldCssArr = [];
                switch (changeType) {
                    case 0:
                        getOldBack();
                        break;
                    case 1:
                        getOldFontColor();
                        break;
                    case 2:
                        getOldBack();
                        getOldFontColor();
                        break;
                }
                domTarget[pconst.targetHoverCssSourcePro] = oldCssArr;
            };

            function getOldBack() {
                var backColorName = 'backgroundColor';
                oldCssArr.push(backColorName);
                oldCssArr.push(jqChangeTarget.css(backColorName));
            };
            function getOldFontColor() {
                var colorName = 'color';
                oldCssArr.push(colorName);
                oldCssArr.push(jqChangeTarget.css(colorName));
            };

            function setElementColor(ele) {
                for (var i = 0; i < oldCssArr.length; i = i + 2) {
                    var cssName = oldCssArr[i];
                    var cssColor = oldCssArr[i + 1];
                    cssColor = ptool.colorChangeToHover(cssColor);
                    jqChangeTarget.css(cssName, cssColor);
                }
            };
        },
        /*某元素注册mouseenter、mouseleave 以实现title的显示隐藏
        *titleSourceTarget title值的来源元素；title值的来源元素，默认当前注册事件的元素
        *titleSourceAttr title值的来源属性，默认text
        *title 值，优先于titleSourceAttr
        */
        registerEventForTitle: function (titleSourceTarget, titleSourceAttr, title) {
            titleSourceTarget = titleSourceTarget || this;
            titleSourceAttr = titleSourceAttr || '';
            var jqSourceTarget = $(titleSourceTarget);
            var domSourceTarget = jqSourceTarget[0];
            if (!domSourceTarget) return;
            var jqTarget = $(this);

            jqTarget.on({
                mouseenter: function (event) {
                    jqTarget.elementShowTitle(titleSourceTarget, titleSourceAttr, title);
                },
                mouseleave: function (event) {
                    jqTarget.attr('title', '');
                }
            });
        },
        /*某元素是否显示title*/
        elementShowTitle: function (titleSourceTarget, titleSourceAttr, title) {
            titleSourceTarget = titleSourceTarget || this;
            titleSourceAttr = titleSourceAttr || '';
            var jqSourceTarget = $(titleSourceTarget);
            var domSourceTarget = jqSourceTarget[0];
            if (!domSourceTarget) return;
            title = title || jqSourceTarget.attr(titleSourceAttr) || jqSourceTarget.text();

            var jqTarget = $(this);
            var clientWidth = domSourceTarget.clientWidth;
            var scrollWidth = domSourceTarget.scrollWidth;
            if (scrollWidth > clientWidth)
                jqTarget.attr('title', title);
            else
                jqTarget.attr('title', '');
        }
    };

    for (var fnName in extendFn) {
        if (extendFn.hasOwnProperty(fnName) === false) continue;
        var fnV = extendFn[fnName];
        if (typeof fnV == 'function') {
            HTMLElement.prototype[fnName] = fnV;
            if (jQuery) jQuery.fn[fnName] = fnV;
        }
    };
}();