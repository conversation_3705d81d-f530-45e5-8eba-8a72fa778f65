﻿(function () {
    var _pcolor = {
        /*主色系*/
        cm: {
            'cm1': '#02a9d1',
            'cm2': '#0094c2',
            'cm3': '#68c5b3',
            'cm4': '#49ab99',
            'cm5': '#f87c7c',
            'cm6': '#ef6767',
            'cm7': '#fd9054',
            'cm8': '#fd7b3d',
            'cm9': '#7a94ad',
            'cm10': '#637e99'
        },
        /*主要用于背景色、分割线颜色、 或文字颜色*/
        cb: {
            'cb0': '#ffffff',
            'cb1': '#000000',
            'cb2': '#333333',
            'cb3': '#6d6d6d',
            'cb4': '#d9d9d9',
            'cb5': '#cacaca',
            'cb6': '#eeeeee',
            'cb7': '#f5f5f5',
            'cb8': '#f8f8f8',
            'cb9': '#e0e0e0',
            'cb10': '#262F34',
            'cb11': '#374045',
            'cb12': '#F0F3F6'
        },
        /*辅助色*/
        ca: {
            'ca1': '#e26db4',
            'ca2': '#f79862',
            'ca3': '#d2e500',
            'ca4': '#ffcd02',
            'ca5': '#8b70e2',
            'ca6': '#61d6bf',
            'ca7': '#4a74e0',
            'ca8': '#af23d2',
            'ca9': '#99f090',
            'ca10': '#04e143',
            'ca11': '#008ba4',
            'ca12': '#ed7cf7',
            'ca13': '#008c1b',
            'ca14': '#973c08',
            'ca15': '#da603a',
            'ca16': '#d19505',
            'ca17': '#1313d8',
            'ca18': '#db0f48',
            'ca19': '#93ea8a',
            'ca20': '#4566b8',
            'ca21': '#45b4b8',
            'ca22': '#45b848',
            'ca23': '#b1b845',
            'ca24': '#b87b45',
            'ca25': '#b84586',
            'ca26': '#8e45b8',
            'ca27': '#6150ff',
            'ca28': '#129dd1',
            'ca29': '#18eedc',
            'ca30': '#ecf249',
            'ca31': '#e29c22'
        },
        /*主要应用于图表。来源于cm.cm1和ca的全部*/
        cd: []
    };
    _pcolor.cd.push(_pcolor.cm.cm1);
    for (var caPro in _pcolor.ca) {
        if (_pcolor.ca.hasOwnProperty(caPro) == false) continue;
        _pcolor.cd.push(_pcolor.ca[caPro]);
    }
    typeof window == 'object' && typeof window.document == 'object' && (window.pajax = _pajax);
    return _pcolor;
})();