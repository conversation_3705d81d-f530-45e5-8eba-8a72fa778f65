﻿Date.prototype.format = function (formatter) {
    if (!formatter || formatter == "") {
        formatter = "yyyy/MM/dd";
    }
    var year = '', month = '', day = '', hour = '', minute = '', second = '';

    var yearMarker = formatter.replace(/[^y]/g, '');
    if (yearMarker) year = this.getFullYear().toString();

    var monthMarker = formatter.replace(/[^M]/g, '');
    if (monthMarker) month = this.appendZero(this.getMonth() + 1);

    var dayMarker = formatter.replace(/[^d]/g, '');
    if (dayMarker) day = this.appendZero(this.getDate());

    var hourMarker = formatter.replace(/[^h]/g, '');
    if (hourMarker) hour = this.appendZero(this.getHours());

    var minuteMarker = formatter.replace(/[^m]/g, '');
    if (minuteMarker) minute = this.appendZero(this.getMinutes());

    var secondMarker = formatter.replace(/[^s]/g, '');
    if (secondMarker) second = this.appendZero(this.getSeconds());

    return formatter.replace(yearMarker, year).replace(monthMarker, month).replace(dayMarker, day)
        .replace(hourMarker, hour).replace(minuteMarker, minute).replace(secondMarker, second);
}
Date.prototype.appendZero = function (val) {
    return val < 10 ? '0' + val : val.toString();
};
Date.prototype.getChineseWeek = function () {
    var currDay = this.getDay();
    switch (currDay) {
        case 0:
            return '周日';
        case 1:
            return '周一';
        case 2:
            return '周二';
        case 3:
            return '周三';
        case 4:
            return '周四';
        case 5:
            return '周五';
        case 6:
            return '周六';
    }
};

Date.parseString = function (dateString, formatter) {
    var today = new Date();
    if (!dateString || dateString == "") {
        return today;
    }
    if (!formatter || formatter == "") {
        formatter = "yyyy-MM-dd";
    }
    var yearMarker = formatter.replace(/[^y|Y]/g, '');
    var monthMarker = formatter.replace(/[^m|M]/g, '');
    var dayMarker = formatter.replace(/[^d]/g, '');
    var yearPosition = formatter.indexOf(yearMarker);
    var yearLength = yearMarker.length;
    var year = dateString.substring(yearPosition, yearPosition + yearLength) * 1;
    if (yearLength == 2) {
        if (year < 50) {
            year += 2000;
        }
        else {
            year += 1900;
        }
    }
    var monthPosition = formatter.indexOf(monthMarker);
    var month = dateString.substring(monthPosition, monthPosition + monthMarker.length) * 1 - 1;
    var dayPosition = formatter.indexOf(dayMarker);
    var day = dateString.substring(dayPosition, dayPosition + dayMarker.length) * 1;
    return new Date(year, month, day);
};

/*
*取得当前周的第一天和最后一天，从周日到周六
*返回{startDate:开始时间字符串,endDate:结束时间字符串}
*/
Date.prototype.getWeekStartAndEnd = function () {
    var currDate = this.getDate();
    var currDay = this.getDay();

    this.setDate(currDate - currDay);
    var startDate = this.format();

    this.setDate(this.getDate() + 6);
    var endDate = this.format();
    return { startDate: startDate, endDate: endDate };
};

//取得当前月的天数
Date.prototype.getMonthLength = function () {
    var month = this.getMonth() + 1;
    var year = this.getFullYear();
    switch (month) {
        case 1:
        case 3:
        case 5:
        case 7:
        case 8:
        case 10:
        case 12:
            return 31;
        case 4:
        case 6:
        case 9:
        case 11:
            return 30;
        case 2:
            if (year % 100 == 0 && year % 400 == 0) return 29;
            if (year % 100 != 0 && year % 4 == 0) return 29;
            return 28;
    }
};

//取得当前年的天数
Date.prototype.getYearLength = function () {
    var year = this.getFullYear();
    if (year % 100 == 0 && year % 400 == 0) return 366;
    if (year % 100 != 0 && year % 4 == 0) return 366;
    return 365;
};