/*
 Highcharts JS v3.0.5 (2013-08-23)

 (c) 2009-2013 Torstein Hønsi

 License: www.highcharts.com/license
*/
!function (a, b) { function y(a, b, c) { this.init.call(this, a, b, c) } function H(a, b, c) { a.call(this, b, c); if (this.chart.polar) { this.closeSegment = function (a) { var b = this.xAxis.center; a.push("L", b[0], b[1]) }; this.closedStacks = !0 } } function I(a, b) { var c = this.chart, d = this.options.animation, e = this.group, f = this.markerGroup, g = this.xAxis.center, h = c.plotLeft, i = c.plotTop, j; if (c.polar) { if (c.renderer.isSVG) { d === !0 && (d = {}); if (b) { j = { translateX: g[0] + h, translateY: g[1] + i, scaleX: .001, scaleY: .001 }; e.attr(j); if (f) { f.attrSetters = e.attrSetters; f.attr(j) } } else { j = { translateX: h, translateY: i, scaleX: 1, scaleY: 1 }; e.animate(j, d); f && f.animate(j, d); this.animate = null } } } else a.call(this, b) } var z, A, B, C, D, E, F, G, c = a.arrayMin, d = a.arrayMax, e = a.each, f = a.extend, g = a.merge, h = a.map, i = a.pick, j = a.pInt, k = a.getOptions().plotOptions, l = a.seriesTypes, m = a.extendClass, n = a.splat, o = a.wrap, p = a.Axis, q = a.Tick, r = a.Series, s = l.column.prototype, t = Math, u = t.round, v = t.floor, w = t.max, x = function () { }; f(y.prototype, { init: function (a, b, c) { var d = this, f, h = d.defaultOptions; d.chart = b; b.angular && (h.background = {}); d.options = a = g(h, a); f = a.background; f && e([].concat(n(f)).reverse(), function (a) { var b = a.backgroundColor; a = g(d.defaultBackgroundOptions, a); b && (a.backgroundColor = b); a.color = a.backgroundColor; c.options.plotBands.unshift(a) }) }, defaultOptions: { center: ["50%", "50%"], size: "85%", startAngle: 0 }, defaultBackgroundOptions: { shape: "circle", borderWidth: 1, borderColor: "silver", backgroundColor: { linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 }, stops: [[0, "#FFF"], [1, "#DDD"]] }, from: Number.MIN_VALUE, innerRadius: 0, to: Number.MAX_VALUE, outerRadius: "105%" } }); z = p.prototype, A = q.prototype; B = { getOffset: x, redraw: function () { this.isDirty = !1 }, render: function () { this.isDirty = !1 }, setScale: x, setCategories: x, setTitle: x }; C = { isRadial: !0, defaultRadialGaugeOptions: { labels: { align: "center", x: 0, y: null }, minorGridLineWidth: 0, minorTickInterval: "auto", minorTickLength: 10, minorTickPosition: "inside", minorTickWidth: 1, plotBands: [], tickLength: 10, tickPosition: "inside", tickWidth: 2, title: { rotation: 0 }, zIndex: 2 }, defaultRadialXOptions: { gridLineWidth: 1, labels: { align: null, distance: 15, x: 0, y: null }, maxPadding: 0, minPadding: 0, plotBands: [], showLastLabel: !1, tickLength: 0 }, defaultRadialYOptions: { gridLineInterpolation: "circle", labels: { align: "right", x: -3, y: -2 }, plotBands: [], showLastLabel: !1, title: { x: 4, text: null, rotation: 90 } }, setOptions: function (a) { this.options = g(this.defaultOptions, this.defaultRadialOptions, a) }, getOffset: function () { z.getOffset.call(this); this.chart.axisOffset[this.side] = 0; this.center = this.pane.center = l.pie.prototype.getCenter.call(this.pane) }, getLinePath: function (a, b) { var c = this.center; b = i(b, c[2] / 2 - this.offset); return this.chart.renderer.symbols.arc(this.left + c[0], this.top + c[1], b, b, { start: this.startAngleRad, end: this.endAngleRad, open: !0, innerR: 0 }) }, setAxisTranslation: function () { z.setAxisTranslation.call(this); if (this.center) { this.transA = this.isCircular ? (this.endAngleRad - this.startAngleRad) / (this.max - this.min || 1) : this.center[2] / 2 / (this.max - this.min || 1); this.isXAxis && (this.minPixelPadding = this.transA * this.minPointOffset + (this.reversed ? (this.endAngleRad - this.startAngleRad) / 4 : 0)) } }, beforeSetTickPositions: function () { this.autoConnect && (this.max += this.categories && 1 || this.pointRange || this.closestPointRange) }, setAxisSize: function () { z.setAxisSize.call(this); this.center && (this.len = this.width = this.height = this.isCircular ? this.center[2] * (this.endAngleRad - this.startAngleRad) / 2 : this.center[2] / 2) }, getPosition: function (a, b) { if (!this.isCircular) { b = this.translate(a); a = this.min } return this.postTranslate(this.translate(a), i(b, this.center[2] / 2) - this.offset) }, postTranslate: function (a, b) { var c = this.chart, d = this.center; a = this.startAngleRad + a; return { x: c.plotLeft + d[0] + Math.cos(a) * b, y: c.plotTop + d[1] + Math.sin(a) * b } }, getPlotBandPath: function (a, b, c) { var d = this.center, e = this.startAngleRad, f = d[2] / 2, g = [i(c.outerRadius, "100%"), c.innerRadius, i(c.thickness, 10)], k = /%$/, l, m, n, o = this.isCircular, p; if ("polygon" === this.options.gridLineInterpolation) p = this.getPlotLinePath(a).concat(this.getPlotLinePath(b, !0)); else { if (!o) { g[0] = this.translate(a); g[1] = this.translate(b) } g = h(g, function (a) { k.test(a) && (a = j(a, 10) * f / 100); return a }); if ("circle" !== c.shape && o) { l = e + this.translate(a); m = e + this.translate(b) } else { l = -Math.PI / 2; m = 1.5 * Math.PI; n = !0 } p = this.chart.renderer.symbols.arc(this.left + d[0], this.top + d[1], g[0], g[0], { start: l, end: m, innerR: i(g[1], g[0] - g[2]), open: n }) } return p }, getPlotLinePath: function (a, b) { var c = this, d = c.center, f = c.chart, g = c.getPosition(a), h, i, j, k; if (c.isCircular) k = ["M", d[0] + f.plotLeft, d[1] + f.plotTop, "L", g.x, g.y]; else if ("circle" === c.options.gridLineInterpolation) { a = c.translate(a); a && (k = c.getLinePath(0, a)) } else { h = f.xAxis[0]; k = []; a = c.translate(a); j = h.tickPositions; h.autoConnect && (j = j.concat([j[0]])); b && (j = [].concat(j).reverse()); e(j, function (b, c) { i = h.getPosition(b, a); k.push(c ? "L" : "M", i.x, i.y) }) } return k }, getTitlePosition: function () { var a = this.center, b = this.chart, c = this.options.title; return { x: b.plotLeft + a[0] + (c.x || 0), y: b.plotTop + a[1] - { high: .5, middle: .25, low: 0 }[c.align] * a[2] + (c.y || 0) } } }; o(z, "init", function (a, c, d) { var e = this, h = c.angular, j = c.polar, k = d.isX, l = h && k, m, o, p, q, r = c.options, s = d.pane || 0, t, u; if (h) { f(this, l ? B : C); m = !k; m && (this.defaultRadialOptions = this.defaultRadialGaugeOptions) } else if (j) { f(this, C); m = k; this.defaultRadialOptions = k ? this.defaultRadialXOptions : g(this.defaultYAxisOptions, this.defaultRadialYOptions) } a.call(this, c, d); if (!l && (h || j)) { q = this.options; c.panes || (c.panes = []); this.pane = t = c.panes[s] = c.panes[s] || new y(n(r.pane)[s], c, e); u = t.options; c.inverted = !1; r.chart.zoomType = null; this.startAngleRad = o = (u.startAngle - 90) * Math.PI / 180; this.endAngleRad = p = (i(u.endAngle, u.startAngle + 360) - 90) * Math.PI / 180; this.offset = q.offset || 0; this.isCircular = m; m && d.max === b && p - o === 2 * Math.PI && (this.autoConnect = !0) } }); o(A, "getPosition", function (a, b, c, d, e) { var f = this.axis; return f.getPosition ? f.getPosition(c) : a.call(this, b, c, d, e) }); o(A, "getLabelPosition", function (a, b, c, d, e, f, g, h, k) { var l = this.axis, m = f.y, n, o = f.align, p = 180 * ((l.translate(this.pos) + l.startAngleRad + Math.PI / 2) / Math.PI) % 360; if (l.isRadial) { n = l.getPosition(this.pos, l.center[2] / 2 + i(f.distance, -25)); "auto" === f.rotation ? d.attr({ rotation: p }) : null === m && (m = .9 * j(d.styles.lineHeight) - d.getBBox().height / 2); if (null === o) { o = l.isCircular ? p > 20 && 160 > p ? "left" : p > 200 && 340 > p ? "right" : "center" : "center"; d.attr({ align: o }) } n.x += f.x; n.y += m } else n = a.call(this, b, c, d, e, f, g, h, k); return n }); o(A, "getMarkPath", function (a, b, c, d, e, f, g) { var h = this.axis, i, j; if (h.isRadial) { i = h.getPosition(this.pos, h.center[2] / 2 + d); j = ["M", b, c, "L", i.x, i.y] } else j = a.call(this, b, c, d, e, f, g); return j }); k.arearange = g(k.area, { lineWidth: 1, marker: null, threshold: null, tooltip: { pointFormat: '<span style="color:{series.color}">{series.name}</span>: <b>{point.low}</b> - <b>{point.high}</b><br/>' }, trackByArea: !0, dataLabels: { verticalAlign: null, xLow: 0, xHigh: 0, yLow: 0, yHigh: 0 } }); l.arearange = a.extendClass(l.area, { type: "arearange", pointArrayMap: ["low", "high"], toYData: function (a) { return [a.low, a.high] }, pointValKey: "low", getSegments: function () { var a = this; e(a.points, function (b) { a.options.connectNulls || null !== b.low && null !== b.high ? null === b.low && null !== b.high && (b.y = b.high) : b.y = null }); r.prototype.getSegments.call(this) }, translate: function () { var a = this, b = a.yAxis; l.area.prototype.translate.apply(a); e(a.points, function (a) { var c = a.low, d = a.high, e = a.plotY; if (null === d && null === c) a.y = null; else if (null === c) { a.plotLow = a.plotY = null; a.plotHigh = b.translate(d, 0, 1, 0, 1) } else if (null === d) { a.plotLow = e; a.plotHigh = null } else { a.plotLow = e; a.plotHigh = b.translate(d, 0, 1, 0, 1) } }) }, getSegmentPath: function (a) { var b, c = [], d = a.length, e = r.prototype.getSegmentPath, f, g, h, i = this.options, j = i.step, k; b = HighchartsAdapter.grep(a, function (a) { return null !== a.plotLow }); for (; d--;) { f = a[d]; null !== f.plotHigh && c.push({ plotX: f.plotX, plotY: f.plotHigh }) } h = e.call(this, b); if (j) { j === !0 && (j = "left"); i.step = { left: "right", center: "center", right: "left" }[j] } k = e.call(this, c); i.step = j; g = [].concat(h, k); k[0] = "L"; this.areaPath = this.areaPath.concat(h, k); return g }, drawDataLabels: function () { var a = this.data, b = a.length, c, d = [], e = r.prototype, f = this.options.dataLabels, g, h = this.chart.inverted; if (f.enabled || this._hasPointLabels) { c = b; for (; c--;) { g = a[c]; g.y = g.high; g.plotY = g.plotHigh; d[c] = g.dataLabel; g.dataLabel = g.dataLabelUpper; g.below = !1; if (h) { f.align = "left"; f.x = f.xHigh } else f.y = f.yHigh } e.drawDataLabels.apply(this, arguments); c = b; for (; c--;) { g = a[c]; g.dataLabelUpper = g.dataLabel; g.dataLabel = d[c]; g.y = g.low; g.plotY = g.plotLow; g.below = !0; if (h) { f.align = "right"; f.x = f.xLow } else f.y = f.yLow } e.drawDataLabels.apply(this, arguments) } }, alignDataLabel: l.column.prototype.alignDataLabel, getSymbol: l.column.prototype.getSymbol, drawPoints: x }); k.areasplinerange = g(k.arearange); l.areasplinerange = m(l.arearange, { type: "areasplinerange", getPointSpline: l.spline.prototype.getPointSpline }); k.columnrange = g(k.column, k.arearange, { lineWidth: 1, pointRange: null }); l.columnrange = m(l.arearange, { type: "columnrange", translate: function () { var a = this, b = a.yAxis, c; s.translate.apply(a); e(a.points, function (d) { var e = d.shapeArgs, f = a.options.minPointLength, g, h, i; d.plotHigh = c = b.translate(d.high, 0, 1, 0, 1); d.plotLow = d.plotY; i = c; h = d.plotY - c; if (f > h) { g = f - h; h += g; i -= g / 2 } e.height = h; e.y = i }) }, trackerGroups: ["group", "dataLabels"], drawGraph: x, pointAttrToOptions: s.pointAttrToOptions, drawPoints: s.drawPoints, drawTracker: s.drawTracker, animate: s.animate, getColumnMetrics: s.getColumnMetrics }); k.gauge = g(k.line, { dataLabels: { enabled: !0, y: 15, borderWidth: 1, borderColor: "silver", borderRadius: 3, style: { fontWeight: "bold" }, verticalAlign: "top", zIndex: 2 }, dial: {}, pivot: {}, tooltip: { headerFormat: "" }, showInLegend: !1 }); D = a.extendClass(a.Point, { setState: function (a) { this.state = a } }); E = { type: "gauge", pointClass: D, angular: !0, drawGraph: x, trackerGroups: ["group", "dataLabels"], translate: function () { var a = this, b = a.yAxis, c = a.options, d = b.center; a.generatePoints(); e(a.points, function (a) { var e = g(c.dial, a.dial), f = j(i(e.radius, 80)) * d[2] / 200, h = j(i(e.baseLength, 70)) * f / 100, k = j(i(e.rearLength, 10)) * f / 100, l = e.baseWidth || 3, m = e.topWidth || 1, n = b.startAngleRad + b.translate(a.y, null, null, null, !0); c.wrap === !1 && (n = Math.max(b.startAngleRad, Math.min(b.endAngleRad, n))); n = 180 * n / Math.PI; a.shapeType = "path"; a.shapeArgs = { d: e.path || ["M", -k, -l / 2, "L", h, -l / 2, f, -m / 2, f, m / 2, h, l / 2, -k, l / 2, "z"], translateX: d[0], translateY: d[1], rotation: n }; a.plotX = d[0]; a.plotY = d[1] }) }, drawPoints: function () { var a = this, b = a.yAxis.center, c = a.pivot, d = a.options, f = d.pivot, h = a.chart.renderer; e(a.points, function (b) { var c = b.graphic, e = b.shapeArgs, f = e.d, i = g(d.dial, b.dial); if (c) { c.animate(e); e.d = f } else b.graphic = h[b.shapeType](e).attr({ stroke: i.borderColor || "none", "stroke-width": i.borderWidth || 0, fill: i.backgroundColor || "black", rotation: e.rotation }).add(a.group) }); c ? c.animate({ translateX: b[0], translateY: b[1] }) : a.pivot = h.circle(0, 0, i(f.radius, 5)).attr({ "stroke-width": f.borderWidth || 0, stroke: f.borderColor || "silver", fill: f.backgroundColor || "black" }).translate(b[0], b[1]).add(a.group) }, animate: function (a) { var b = this; if (!a) { e(b.points, function (a) { var c = a.graphic; if (c) { c.attr({ rotation: 180 * b.yAxis.startAngleRad / Math.PI }); c.animate({ rotation: a.shapeArgs.rotation }, b.options.animation) } }); b.animate = null } }, render: function () { this.group = this.plotGroup("group", "series", this.visible ? "visible" : "hidden", this.options.zIndex, this.chart.seriesGroup); l.pie.prototype.render.call(this); this.group.clip(this.chart.clipRect) }, setData: l.pie.prototype.setData, drawTracker: l.column.prototype.drawTracker }; l.gauge = a.extendClass(l.line, E); k.boxplot = g(k.column, { fillColor: "#FFFFFF", lineWidth: 1, medianWidth: 2, states: { hover: { brightness: -.3 } }, threshold: null, tooltip: { pointFormat: '<span style="color:{series.color};font-weight:bold">{series.name}</span><br/>Maximum: {point.high}<br/>Upper quartile: {point.q3}<br/>Median: {point.median}<br/>Lower quartile: {point.q1}<br/>Minimum: {point.low}<br/>' }, whiskerLength: "50%", whiskerWidth: 2 }); l.boxplot = m(l.column, { type: "boxplot", pointArrayMap: ["low", "q1", "median", "q3", "high"], toYData: function (a) { return [a.low, a.q1, a.median, a.q3, a.high] }, pointValKey: "high", pointAttrToOptions: { fill: "fillColor", stroke: "color", "stroke-width": "lineWidth" }, drawDataLabels: x, translate: function () { var a = this, b = a.yAxis, c = a.pointArrayMap; l.column.prototype.translate.apply(a); e(a.points, function (a) { e(c, function (c) { null !== a[c] && (a[c + "Plot"] = b.translate(a[c], 0, 1, 0, 1)) }) }) }, drawPoints: function () { var a = this, c = a.points, d = a.options, f = a.chart, g = f.renderer, h, j, k, l, m, n, o, p, q, r, s, t, w, x, y, z, A, B, C, D, E, F, G = a.doQuartiles !== !1, H = parseInt(a.options.whiskerLength, 10) / 100; e(c, function (c) { q = c.graphic; E = c.shapeArgs; s = {}; x = {}; z = {}; F = c.color || a.color; if (c.plotY !== b) { h = c.pointAttr[c.selected ? "selected" : ""]; A = E.width; B = v(E.x); C = B + A; D = u(A / 2); j = v(G ? c.q1Plot : c.lowPlot); k = v(G ? c.q3Plot : c.lowPlot); l = v(c.highPlot); m = v(c.lowPlot); s.stroke = c.stemColor || d.stemColor || F; s["stroke-width"] = i(c.stemWidth, d.stemWidth, d.lineWidth); s.dashstyle = c.stemDashStyle || d.stemDashStyle; x.stroke = c.whiskerColor || d.whiskerColor || F; x["stroke-width"] = i(c.whiskerWidth, d.whiskerWidth, d.lineWidth); z.stroke = c.medianColor || d.medianColor || F; z["stroke-width"] = i(c.medianWidth, d.medianWidth, d.lineWidth); o = s["stroke-width"] % 2 / 2; p = B + D + o; r = ["M", p, k, "L", p, l, "M", p, j, "L", p, m, "z"]; if (G) { o = h["stroke-width"] % 2 / 2; p = v(p) + o; j = v(j) + o; k = v(k) + o; B += o; C += o; t = ["M", B, k, "L", B, j, "L", C, j, "L", C, k, "L", B, k, "z"] } if (H) { o = x["stroke-width"] % 2 / 2; l += o; m += o; w = ["M", p - D * H, l, "L", p + D * H, l, "M", p - D * H, m, "L", p + D * H, m] } o = z["stroke-width"] % 2 / 2; n = u(c.medianPlot) + o; y = ["M", B, n, "L", C, n, "z"]; if (q) { c.stem.animate({ d: r }); H && c.whiskers.animate({ d: w }); G && c.box.animate({ d: t }); c.medianShape.animate({ d: y }) } else { c.graphic = q = g.g().add(a.group); c.stem = g.path(r).attr(s).add(q); H && (c.whiskers = g.path(w).attr(x).add(q)); G && (c.box = g.path(t).attr(h).add(q)); c.medianShape = g.path(y).attr(z).add(q) } } }) } }); k.errorbar = g(k.boxplot, { color: "#000000", grouping: !1, linkedTo: ":previous", tooltip: { pointFormat: k.arearange.tooltip.pointFormat }, whiskerWidth: null }); l.errorbar = m(l.boxplot, { type: "errorbar", pointArrayMap: ["low", "high"], toYData: function (a) { return [a.low, a.high] }, pointValKey: "high", doQuartiles: !1, getColumnMetrics: function () { return this.linkedParent && this.linkedParent.columnMetrics || l.column.prototype.getColumnMetrics.call(this) } }); k.waterfall = g(k.column, { lineWidth: 1, lineColor: "#333", dashStyle: "dot", borderColor: "#333" }); l.waterfall = m(l.column, { type: "waterfall", upColorProp: "fill", pointArrayMap: ["low", "y"], pointValKey: "y", init: function (a, b) { b.stacking = !0; l.column.prototype.init.call(this, a, b) }, translate: function () { var a = this, b = a.options, c = a.yAxis, d, e, f, g, h, i, j, k, m, n = b.threshold, o = b.borderWidth % 2 / 2; l.column.prototype.translate.apply(this); k = n; f = a.points; for (e = 0, d = f.length; d > e; e++) { g = f[e]; h = g.shapeArgs; i = a.getStack(e); m = i.points[a.index]; isNaN(g.y) && (g.y = a.yData[e]); j = w(k, k + g.y) + m[0]; h.y = c.translate(j, 0, 1); if (g.isSum || g.isIntermediateSum) { h.y = c.translate(m[1], 0, 1); h.height = c.translate(m[0], 0, 1) - h.y } else k += i.total; if (h.height < 0) { h.y += h.height; h.height *= -1 } g.plotY = h.y = u(h.y) - o; h.height = u(h.height); g.yBottom = h.y + h.height } }, processData: function (a) { var b = this, c = b.options, d = b.yData, e = b.points, f, g = d.length, h = c.threshold || 0, i, j, k, l, m, n; j = i = k = l = h; for (n = 0; g > n; n++) { m = d[n]; f = e ? e[n] : {}; if ("sum" === m || f.isSum) d[n] = j; else if ("intermediateSum" === m || f.isIntermediateSum) { d[n] = i; i = h } else { j += m; i += m } k = Math.min(j, k); l = Math.max(j, l) } r.prototype.processData.call(this, a); b.dataMin = k; b.dataMax = l }, toYData: function (a) { return a.isSum ? "sum" : a.isIntermediateSum ? "intermediateSum" : a.y }, getAttribs: function () { l.column.prototype.getAttribs.apply(this, arguments); var b = this, c = b.options, d = c.states, f = c.upColor || b.color, h = a.Color(f).brighten(.1).get(), i = g(b.pointAttr), j = b.upColorProp; i[""][j] = f; i.hover[j] = d.hover.upColor || h; i.select[j] = d.select.upColor || f; e(b.points, function (a) { if (a.y > 0 && !a.color) { a.pointAttr = i; a.color = f } }) }, getGraphPath: function () { var a = this.data, b = a.length, c = this.options.lineWidth + this.options.borderWidth, d = u(c) % 2 / 2, e = [], f = "M", g = "L", h, i, j, k; for (j = 1; b > j; j++) { i = a[j].shapeArgs; h = a[j - 1].shapeArgs; k = [f, h.x + h.width, h.y + d, g, i.x, h.y + d]; if (a[j - 1].y < 0) { k[2] += h.height; k[5] += h.height } e = e.concat(k) } return e }, getExtremes: x, getStack: function (a) { var b = this.yAxis, c = b.stacks, d = this.stackKey; this.processedYData[a] < this.options.threshold && (d = "-" + d); return c[d][a] }, drawGraph: r.prototype.drawGraph }); k.bubble = g(k.scatter, { dataLabels: { inside: !0, style: { color: "white", textShadow: "0px 0px 3px black" }, verticalAlign: "middle" }, marker: { lineColor: null, lineWidth: 1 }, minSize: 8, maxSize: "20%", tooltip: { pointFormat: "({point.x}, {point.y}), Size: {point.z}" }, turboThreshold: 0, zThreshold: 0 }); l.bubble = m(l.scatter, { type: "bubble", pointArrayMap: ["y", "z"], trackerGroups: ["group", "dataLabelsGroup"], pointAttrToOptions: { stroke: "lineColor", "stroke-width": "lineWidth", fill: "fillColor" }, applyOpacity: function (b) { var c = this.options.marker, d = i(c.fillOpacity, .5); b = b || c.fillColor || this.color; 1 !== d && (b = a.Color(b).setOpacity(d).get("rgba")); return b }, convertAttribs: function () { var a = r.prototype.convertAttribs.apply(this, arguments); a.fill = this.applyOpacity(a.fill); return a }, getRadii: function (a, b, c, d) { var e, f, g, h = this.zData, i = [], j; for (f = 0, e = h.length; e > f; f++) { j = b - a; g = j > 0 ? (h[f] - a) / (b - a) : .5; i.push(t.ceil(c + g * (d - c)) / 2) } this.radii = i }, animate: function (a) { var b = this.options.animation; if (!a) { e(this.points, function (a) { var c = a.graphic, d = a.shapeArgs; if (c && d) { c.attr("r", 1); c.animate({ r: d.r }, b) } }); this.animate = null } }, translate: function () { var a, c = this.data, d, e, f = this.radii; l.scatter.prototype.translate.call(this); a = c.length; for (; a--;) { d = c[a]; e = this.chart.series[0].zData ? this.chart.series[0].zData[a] : 0; d.negative = d.z < (this.options.zThreshold || 0); if (e >= this.minPxSize / 2) { d.shapeType = "circle"; d.shapeArgs = { x: d.plotX, y: d.plotY, r: e }; d.dlBox = { x: d.plotX - e, y: d.plotY - e, width: 2 * e, height: 2 * e } } else d.shapeArgs = d.plotY = d.dlBox = b } }, drawLegendSymbol: function (a, b) { var c = j(a.itemStyle.fontSize) / 2; b.legendSymbol = this.chart.renderer.circle(c, a.baseline - c, c).attr({ zIndex: 3 }).add(b.legendGroup); b.legendSymbol.isMarker = !0 }, drawPoints: l.column.prototype.drawPoints, alignDataLabel: l.column.prototype.alignDataLabel }); p.prototype.beforePadding = function () { var a = this, f = this.len, g = this.chart, h = 0, k = f, l = this.isXAxis, m = l ? "xData" : "yData", n = this.min, o = {}, p = t.min(g.plotWidth, g.plotHeight), q = Number.MAX_VALUE, r = -Number.MAX_VALUE, s = this.max - n, u = f / s, v = []; if (this.tickPositions) { e(this.series, function (b) { var f = b.options, g; if ("bubble" === b.type && b.visible) { a.allowZoomOutside = !0; v.push(b); if (l) { e(["minSize", "maxSize"], function (a) { var b = f[a], c = /%$/.test(b); b = j(b); o[a] = c ? p * b / 100 : b }); b.minPxSize = o.minSize; g = b.zData; if (g.length) { q = t.min(q, t.max(c(g), f.displayNegative === !1 ? f.zThreshold : -Number.MAX_VALUE)); r = t.max(r, d(g)) } } } }); e(v, function (a) { var b = a[m], c = b.length, d; l && a.getRadii(q, r, o.minSize, o.maxSize); if (s > 0) for (; c--;) { d = a.radii[c]; h = Math.min((b[c] - n) * u - d, h); k = Math.max((b[c] - n) * u + d, k) } }); if (s > 0 && i(this.options.min, this.userMin) === b && i(this.options.max, this.userMax) === b) { k -= f; u *= (f + h - k) / f; this.min += h / u; this.max += k / u } } }; F = r.prototype, G = a.Pointer.prototype; F.toXY = function (a) { var b, c = this.chart, d = a.plotX, e = a.plotY; a.rectPlotX = d; a.rectPlotY = e; a.clientX = (180 * (d / Math.PI) + this.xAxis.pane.options.startAngle) % 360; b = this.xAxis.postTranslate(a.plotX, this.yAxis.len - e); a.plotX = a.polarPlotX = b.x - c.plotLeft; a.plotY = a.polarPlotY = b.y - c.plotTop }; F.orderTooltipPoints = function (a) { if (this.chart.polar) { a.sort(function (a, b) { return a.clientX - b.clientX }); if (a[0]) { a[0].wrappedClientX = a[0].clientX + 360; a.push(a[0]) } } }; o(l.area.prototype, "init", H); o(l.areaspline.prototype, "init", H); o(l.spline.prototype, "getPointSpline", function (a, b, c, d) { var e, f = 1.5, g = f + 1, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w, x; if (this.chart.polar) { h = c.plotX; i = c.plotY; j = b[d - 1]; k = b[d + 1]; if (this.connectEnds) { j || (j = b[b.length - 2]); k || (k = b[1]) } if (j && k) { l = j.plotX; m = j.plotY; n = k.plotX; o = k.plotY; p = (f * h + l) / g; q = (f * i + m) / g; r = (f * h + n) / g; s = (f * i + o) / g; t = Math.sqrt(Math.pow(p - h, 2) + Math.pow(q - i, 2)); u = Math.sqrt(Math.pow(r - h, 2) + Math.pow(s - i, 2)); v = Math.atan2(q - i, p - h); w = Math.atan2(s - i, r - h); x = Math.PI / 2 + (v + w) / 2; Math.abs(v - x) > Math.PI / 2 && (x -= Math.PI); p = h + Math.cos(x) * t; q = i + Math.sin(x) * t; r = h + Math.cos(Math.PI + x) * u; s = i + Math.sin(Math.PI + x) * u; c.rightContX = r; c.rightContY = s } if (d) { e = ["C", j.rightContX || j.plotX, j.rightContY || j.plotY, p || h, q || i, h, i]; j.rightContX = j.rightContY = null } else e = ["M", h, i] } else e = a.call(this, b, c, d); return e }); o(F, "translate", function (a) { a.call(this); if (this.chart.polar && !this.preventPostTranslate) { var b = this.points, c = b.length; for (; c--;) this.toXY(b[c]) } }); o(F, "getSegmentPath", function (a, b) { var c = this.points; if (this.chart.polar && this.options.connectEnds !== !1 && b[b.length - 1] === c[c.length - 1] && null !== c[0].y) { this.connectEnds = !0; b = [].concat(b, [c[0]]) } return a.call(this, b) }); o(F, "animate", I); o(s, "animate", I); o(F, "setTooltipPoints", function (a, b) { this.chart.polar && f(this.xAxis, { tooltipLen: 360 }); return a.call(this, b) }); o(s, "translate", function (a) { var b = this.xAxis, c = this.yAxis.len, d = b.center, e = b.startAngleRad, f = this.chart.renderer, g, h, j, k; this.preventPostTranslate = !0; a.call(this); if (b.isRadial) { h = this.points; k = h.length; for (; k--;) { j = h[k]; g = j.barX + e; j.shapeType = "path"; j.shapeArgs = { d: f.symbols.arc(d[0], d[1], c - j.plotY, null, { start: g, end: g + j.pointWidth, innerR: c - i(j.yBottom, c) }) }; this.toXY(j) } } }); o(s, "alignDataLabel", function (a, b, c, d, e, f) { if (this.chart.polar) { var g = 180 * (b.rectPlotX / Math.PI), h, i; if (null === d.align) { h = g > 20 && 160 > g ? "left" : g > 200 && 340 > g ? "right" : "center"; d.align = h } if (null === d.verticalAlign) { i = 45 > g || g > 315 ? "bottom" : g > 135 && 225 > g ? "top" : "middle"; d.verticalAlign = i } F.alignDataLabel.call(this, b, c, d, e, f) } else a.call(this, b, c, d, e, f) }); o(G, "getIndex", function (a, b) { var c, d = this.chart, e, f, g; if (d.polar) { e = d.xAxis[0].center; f = b.chartX - e[0] - d.plotLeft; g = b.chartY - e[1] - d.plotTop; c = 180 - Math.round(180 * (Math.atan2(f, g) / Math.PI)) } else c = a.call(this, b); return c }); o(G, "getCoordinates", function (a, b) { var c = this.chart, d = { xAxis: [], yAxis: [] }; c.polar ? e(c.axes, function (a) { var e = a.isXAxis, f = a.center, g = b.chartX - f[0] - c.plotLeft, h = b.chartY - f[1] - c.plotTop; d[e ? "xAxis" : "yAxis"].push({ axis: a, value: a.translate(e ? Math.PI - Math.atan2(g, h) : Math.sqrt(Math.pow(g, 2) + Math.pow(h, 2)), !0) }) }) : d = a.call(this, b); return d }) }(Highcharts);