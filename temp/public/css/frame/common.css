/*
*框架css
*@Author：wang<PERSON><PERSON>
*Update:2016-03-24  16:30
*/
html {
    height: 100%;
}

body {
    height: 100%;
    background: #f0f3f6;
    min-width: 1280px;
}

[disabled='true'] {
    pointer-events: none;
    cursor: default !important;
    opacity: .3;
}

/*loading*/
#v6s-loading {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 266;
    display: none;
}

/*nomodal*/
.addmodal-con {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -130px;
    margin-top: -100px;
    text-align: center;
}

.addmodal-text {
    margin: 0px auto;
}

.addmodal-button {
    cursor: pointer;
    box-sizing: border-box;
    height: 28px;
    line-height: 27px;
    display: inline-block;
    min-width: 82px;
    padding: 0 16px;
    font-size: 12px;
    border: 1px solid #0099be;
    color: #fff;
    border-radius: 3px;
    text-align: center;
    background: #02a9d1;
    margin-top: 13px;
    width: 160px;
}

.addmodal-button:hover {
    background: #0094c2;
}

/*黑色滚动条*/

/*#region 滚动条的样式开始 */

.blckScroll::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

.blckScroll::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0);
    -webkit-border-radius: 3px;
}

.blckScroll::-webkit-scrollbar-thumb {
    -webkit-border-radius: 3px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 0, 0, 0);
}



.blckScroll::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(0, 0, 0, 0);
}

.v6s-wrap {
    height: 100%;
    width: 100%;
    min-width: 1280px;
    overflow: hidden;
    position: relative;
}

/***************title**************/
.v6s-header {
    height: 50px;
    background: #374045;
    width: 100%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    position: absolute;
    top: 0;
    z-index: 86;
    min-width: 1280px;
}

.v6s-h-navButton {
    width: 80px;
    height: 100%;
    color: #fff;
    font-size: 30px;
    text-align: center;
    line-height: 1.5;
    cursor: pointer;
    font-weight: bold;
    position: absolute;
    left: 0;
    background: url(/img/frame/nav-l.png) no-repeat center center;
}

.v6s-h-navButton.active {
    background-color: #02a9d1;
}

.v6s-h-title {
    font-size: 18px;
    color: #fff;
    margin-left: 96px;
    line-height: 50px;
}

.v6s-h-title>span {
    margin: 0 5px;
}

.v6s-h-right {
    position: absolute;
    right: 0;
    top: 0;
}

.v6s-h-nav {
    position: absolute;
    right: 0px;
    top: 0;
    height: 100%;
    color: #fff;
}

.v6s-h-nav a {
    color: inherit;
    display: block;
    width: 100%;
    height: 100%;
}

.v6s-h-nav>div {
    float: right;
    position: relative;
    height: 100%;
}

/*用户*/
.v6s-h-nav>.v6s-h-nav-user {
    min-width: 80px;
    height: 100%;
}

.v6s-h-nav>.v6s-h-nav-user .user-title {
    padding-right: 20px;
    cursor: pointer;
    line-height: 50px;
    min-width: 74px;
    height: 100%;
    padding-left: 10px;
}

.v6s-h-nav>.v6s-h-nav-user .user-title:hover {
    background: #31393d;
}

.v6s-h-nav>.v6s-h-nav-user .user-title .pic {
    width: 20px;
    height: 20px;
    border-radius: 10px;
    background: url(/img/frame/user/user-big.png) no-repeat center center;
    background-size: cover;
    display: inline-block;
    margin: 15px 0 0 10px;
    float: left;
    position: absolute;
}

.v6s-h-nav>.v6s-h-nav-user .user-title em {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 5px;
    text-align: center;
    padding: 0 23px 0 36px;
    height: 100%;
}

.v6s-h-nav>.v6s-h-nav-user .user-title b {
    font-family: "perficon";
    float: right;
    position: absolute;
    right: 20px;
    transition: 0.3s;
}

.v6s-h-nav>.v6s-h-nav-user .user-con {
    width: 80px;
    background: #374045;
    background: -webkit-linear-gradient(top, #2c3134 0%, #374045 28%);
    position: absolute;
    box-sizing: border-box;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    top: 60px;
    right: 10px;
    display: none;
    font-size: 12px;
}

.v6s-h-nav>.v6s-h-nav-user .user-con:before {
    content: '';
    position: absolute;
    top: -6px;
    width: 0;
    height: 0;
    border-left: 9px solid rgba(0, 0, 0, 0);
    border-right: 9px solid rgba(0, 0, 0, 0);
    border-bottom: 6px solid #374045;
    right: 12px;
}

.v6s-h-nav>.v6s-h-nav-user .user-con ul {
    border-radius: 6px;
    overflow: hidden;
}

.v6s-h-nav>.v6s-h-nav-user .user-con ul li {
    height: 46px;
    line-height: 46px;
    text-align: center;
    cursor: pointer;
    padding: 0 15px;
}

.v6s-h-nav>.v6s-h-nav-user .user-con ul li>a {
    display: block;
    height: 100%;
    border-bottom: 1px solid #455056;
    -webkit-box-shadow: 0 -1px 0 0px #262c2f inset;
    box-shadow: 0 -1px 0 0px #262c2f inset;
    -moz-box-shadow: 0 -1px 0 0px #262c2f inset;
    -ms-box-shadow: 0 -1px 0 0px #262c2f inset;
    -o-box-shadow: 0 -1px 0 0px #262c2f inset;
}

.v6s-h-nav>.v6s-h-nav-user .user-con ul li:last-child>a {
    border-bottom: none;
    -webkit-box-shadow: 0 0px 0 0px #262c2f inset;
    box-shadow: 0 0px 0 0px #262c2f inset;
    -moz-box-shadow: 0 0px 0 0px #262c2f inset;
    -ms-box-shadow: 0 0px 0 0px #262c2f inset;
    -o-box-shadow: 0 0px 0 0px #262c2f inset;
}

.v6s-h-nav>.v6s-h-nav-user .user-con ul li:hover {
    background: #31393d;
}

/*用户资料*/
.user-data {
    width: 297px;
    background: #374045;
    background: -webkit-linear-gradient(top, #2c3134 0%, #374045 15%);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    position: absolute;
    border-radius: 6px;
    top: 59px;
    right: 15px;
    color: #fff;
    padding: 6px;
    box-sizing: border-box;
    display: none;
    font-size: 12px;
}

.user-data>h1 {
    font-size: 12px;
    padding: 15px 0 0 15px;
}

.user-data .user-data-x {
    position: absolute;
    right: 20px;
    top: 20px;
    width: 12px;
    height: 12px;
    font-family: 'perficon';
    color: #8c8c8c;
    cursor: pointer;
}

.user-data .user-pic {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 1px solid #fff;
    background: #d1d1d1;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    transition: 0.2s;
    -webkit-transition: 0.2s;
    -moz-transition: 0.2s;
    -ms-transition: 0.2s;
    -o-transition: 0.2s;
    margin-top: 12px;
}
.user-data .user-name {
    text-align: center;
    line-height: 30px;
    margin-top: 5px;
    border-bottom: 1px dashed #262c2f;
    box-sizing: border-box;
    padding-bottom: 7px;
}

.user-data .user-size {
    text-align: center;
    line-height: 30px;
    margin-top: 5px;
    color: #6c7478;
    font-size: 10px;
    display: none;
    border-bottom: 1px dashed #262c2f;
    box-sizing: border-box;
    padding-bottom: 7px;
}


.user-data .user-data-select {
    padding-top: 20px;
    border-top: 1px dashed #455056;
}

.user-data .user-data-select>ul {
    padding-left: 20px;
}

.user-data .user-data-edit {
    padding-top: 20px;
    border-top: 1px dashed #455056;
}

.user-data .user-data-edit>ul {
    padding-left: 12px;
}

.user-data[dataSign="edit"] .user-data-edit {
    display: block;
}

.v6s-header[p-type="text-textarea"] .error-tip,
.v6s-header [p-type="text-unit"] .error-tip,
.v6s-header [p-type="text-text"] .error-tip,
.v6s-header [p-type="text-password"] .error-tip {
    bottom: -22px;
}

.v6s-header [p-type="text-textarea"] .error-tip>span,
.v6s-header [p-type="text-unit"] .error-tip>span,
.v6s-header [p-type="text-text"] .error-tip>span,
.v6s-header [p-type="text-password"] .error-tip>span {
    font-size: 12px;
}

.user-data[dataSign="edit"] .user-data-select {
    display: none;
}

.user-data[dataSign="select"] .user-data-select {
    display: block;
}

.user-data[dataSign="select"] .user-data-edit {
    display: none;
}

.user-data .user-data-select ul li,
.user-data .user-data-edit ul li {
    height: 40px;
    line-height: 40px;
}

.user-data .user-data-edit ul li {
    height: 61px;
    line-height: 40px;
}

.user-data .user-data-select ul li>em,
.user-data .user-data-edit ul li>em {
    width: 56px;
    text-align: right;
    display: inline-block;
    margin-right: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: left;
}

.user-data .user-data-select ul li>em {
    width: 69px;
}

.user-data .user-data-select ul li>span {
    width: 180px;
    text-align: left;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-data .user-data-edit ul li>div {
    width: 180px;
    text-align: left;
    display: inline-block;
}

.user-data .user-data-select ul li>span {
    width: 177px;
}

.user-data .user-data-edit ul li>div {
    position: absolute;
}

.user-data .user-data-edit ul li>div input {
    font-size: 12px;
}

.user-data .select-button,
.user-data .edit-button {
    width: 180px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    color: #fff;
    background: #444e53;
    border: 1px solid #2b3236;
    border-radius: 3px;
    margin: 0 auto;
    cursor: pointer;
    margin-bottom: 15px;
    margin-top: 30px;
}

/*个人资料结构树*/
.user-data .tree-title {
    width: 100%;
    display: inline-block;
    height: 28px;
    line-height: 27px;
    padding-left: 12px;
    box-sizing: border-box;
    border: 1px solid #B8B9BA;
    color: #A7B4BB;
    position: relative;
    z-index: 3;
    cursor: pointer;
    margin-top: 7px;
}

.user-data .tree-title span {
    float: right;
    width: 28px;
    font-family: "perficon";
    text-align: center;
    font-size: 12px;
    color: #8D9BA3;
}

.user-data .tree-title em {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 100%;
    color: #8D9BA3;
}

.user-data .tree-con {
    width: 100%;
    background: #374045;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    position: absolute;
    max-height: 205px;
    top: 35px;
    z-index: 3;
    overflow: auto;
    display: none;
    font-size: 12px;
}

.user-data .tree-con .tree-temp .temp-con {
    display: none;
}

.user-data .tree-con .tree-temp .temp-tit,
.user-data .tree-con .tree-temp .temp-con>ul>li {
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #444f55;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-left: 7px;
    cursor: pointer;
}

.user-data .tree-con .tree-temp .temp-tit:hover {
    background: #31393d;
}

.user-data .tree-con .tree-temp .temp-tit span {
    font-family: 'perficon';
    color: #576369;
    margin-right: 7px;
    height: 40px;
    display: inline-block;
}

.user-data .tree-con .tree-temp .temp-tit:hover span {
    color: #fff;
}

.user-data .tree-con .tree-temp .temp-tit span:hover {
    color: #fff;
}

.user-data .tree-con .tree-temp .tree-temp .temp-tit {
    padding-left: 27px;
}

.user-data .tree-con .tree-temp .tree-temp .temp-con>ul>li {
    padding-left: 51px;
}

.user-data .tree-con .tree-temp .tree-temp .tree-temp .temp-tit {
    padding-left: 47px;
}

.user-data .tree-con .tree-temp .tree-temp .tree-temp .temp-con>ul>li {
    padding-left: 71px;
}

.user-data .tree-con .tree-temp .tree-temp .tree-temp .tree-temp .temp-tit {
    padding-left: 67px;
}

.user-data .tree-con .tree-temp .tree-temp .tree-temp .tree-temp .temp-con>ul>li {
    padding-left: 91px;
}

.user-data .tree-con .tree-temp .tree-temp .tree-temp .tree-temp .tree-temp .temp-tit {
    padding-left: 87px;
}

.user-data .tree-con .tree-temp .tree-temp .tree-temp .tree-temp .tree-temp .temp-con>ul>li {
    padding-left: 111px;
}

/*修改密码*/
.user-password-edit {
    width: 411px;
    background: #374045;
    background: -webkit-linear-gradient(top, #2c3134 0%, #374045 15%);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    position: absolute;
    border-radius: 6px;
    top: 59px;
    right: 15px;
    color: #fff;
    padding: 20px;
    box-sizing: border-box;
    display: none;
    font-size: 12px;
}

.user-password-edit>h1 {
    font-size: 12px;
}

.user-password-edit .password-edit-x {
    position: absolute;
    right: 20px;
    top: 20px;
    width: 12px;
    height: 12px;
    font-family: 'perficon';
    color: #8c8c8c;
    cursor: pointer;
}

.user-password-edit ul {
    padding-top: 20px;
}

.user-password-edit ul li {
    height: 61px;
    line-height: 40px;
}

.user-data .user-data-edit ul li input {
    border: 1px solid #B8B9BA;
    color: #A7B4BB;
}

.user-data .user-data-edit ul li .input-error[type="text"],
.user-data .user-data-edit ul li .input-error[type="password"] {
    background: transparent;
}

.user-data .user-data-edit ul li input.input-error[type="text"],
.user-data .user-data-edit ul li input.input-error[type="password"] {
    font-size: 14px;
    border: 1px solid #fd9054;
    background-color: #f7e7df;
}

.user-password-edit ul li>em {
    width: 134px;
    text-align: right;
    display: inline-block;
    margin-right: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 28px;
    line-height: 28px;
}

.user-password-edit ul li>div {
    width: 155px;
    text-align: left;
    display: inline-block;
    position: absolute;
    height: 28px;
    line-height: 28px;
}

.user-password-edit ul li>div input {
    border: 1px solid #B8B9BA;
    color: #A7B4BB;
}

.user-password-edit .edit-button {
    width: 180px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    color: #fff;
    background: #444e53;
    border: 1px solid #2b3236;
    border-radius: 3px;
    margin: 0 auto;
    cursor: pointer;
    margin-bottom: 15px;
    margin-top: 10px;
}

/*报警*/
.v6s-h-nav>.v6s-h-nav-alarm {
    width: 80px;
    background: #f87c7c;
    height: 100%;
    font-size: 16px;
    cursor: pointer;
    transition: 0.5s;
    -webkit-transition: 0.5s;
    -moz-transition: 0.5s;
    -webkit-transition: 0.5s;
}

.v6s-h-nav>.v6s-h-nav-alarm:hover {
    background: #ef6767;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-title {
    text-align: center;
    line-height: 50px;
    height: 100%;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-title>span {
    width: 25px;
    height: 26px;
    float: left;
    background: url(/img/frame/alarm-icon.png) no-repeat center center;
    margin: 11px 2px 0 11px;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-title>em {
    float: left;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con {
    background: #31393e;
    position: absolute;
    box-sizing: border-box;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    top: 60px;
    left: -134px;
    width: 310px;
    display: none;
    padding-top: 3px;
    box-sizing: border-box;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con:before {
    content: '';
    position: absolute;
    top: -6px;
    width: 0;
    height: 0;
    border-left: 9px solid rgba(0, 0, 0, 0);
    border-right: 9px solid rgba(0, 0, 0, 0);
    border-bottom: 6px solid #374045;
    right: 126px;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul {
    max-height: 321px;
    overflow: hidden;
    border-radius: 6px 6px 0 0;
    background: #374045;
    background: -webkit-linear-gradient(top, #2c3134 0%, #374045 28%);
    border-bottom: 1px solid #353a40;
    box-sizing: border-box;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul:hover {
    overflow: auto;
    overflow: overlay;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul li {
    height: 64px;
    font-size: 12px;
    padding: 0 15px;
    box-sizing: border-box;
    overflow: hidden;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul li .li-wrap {
    height: 100%;
    border-bottom: 1px dashed #262c2f;
    border-top: 1px dashed #455056;
    box-sizing: border-box;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul li:last-child .li-wrap {
    border-bottom: none;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul li:first-child .li-wrap {
    border-top-color: transparent;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul li:hover {
    background: #31393d;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul li span {
    display: block;
    height: 24px;
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #dddddd;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul li span:first-child {
    margin-top: 9px;
    color: #fff;
    font-weight: bold;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con ul li span em:last-child {
    float: right;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con .alarm-button {
    text-align: center;
    font-size: 12px;
    color: #02a9d1;
    border-top: 1px solid #3d474e;
    line-height: 34px;
    height: 34px;
    position: relative;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con .alarm-button:after {
    content: '';
    position: absolute;
    top: -1px;
    width: 100%;
}

.v6s-h-nav>.v6s-h-nav-alarm .alarm-con .nodata {
    height: 60px;
    line-height: 60px;
    text-align: center;
    background: #374045;
    background: -webkit-linear-gradient(top, #2c3134 0%, #374045 100%);
    border-radius: 6px 6px 0 0;
    font-size: 14px;
}

/*天气*/
.v6s-h-nav>.v6s-h-nav-weather .weather-title {
    font-size: 16px;
    cursor: pointer;
    line-height: 50px;
    height: 100%;
    padding: 0 10px;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-title:hover {
    background: #31393d;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-title .pic {
    width: 26px;
    height: 18px;
    display: inline-block;
    background-size: contain;
    float: left;
    margin-top: 15px;
    margin-right: 8px;
    line-height: 24px;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-title b img {
    height: 100%;
    width: 100%;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-title span>em {
    margin-right: 7px;
    float: left;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-title span>em.em2 {
    line-height: 51px;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-title span>em.em2>b {
    margin-right: 3px;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con {
    position: absolute;
    box-sizing: border-box;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    top: 60px;
    left: -100px;
    width: 260px;
    height: 265px;
    background: #31393e;
    display: none;
}


.v6s-h-nav>.v6s-h-nav-weather .weather-con:before {
    content: '';
    position: absolute;
    top: -6px;
    width: 0;
    height: 0;
    border-left: 9px solid rgba(0, 0, 0, 0);
    border-right: 9px solid rgba(0, 0, 0, 0);
    border-bottom: 6px solid #374045;
    right: 30%;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .today-weather {
    font-size: 12px;
    height: 161px;
    padding: 0 12px;
    background: #374045;
    background: -webkit-linear-gradient(top, #2c3134 0%, #374045 40%);
    border-radius: 6px 6px 0 0;
    box-sizing: border-box;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .today-weather>ul {
    width: 100%;
    height: 100%;
    border-bottom: 1px solid #455056;
    -webkit-box-shadow: 0 -1px 0 0px #262c2f inset;
    box-shadow: 0 -1px 0 0px #262c2f inset;
    -moz-box-shadow: 0 -1px 0 0px #262c2f inset;
    -ms-box-shadow: 0 -1px 0 0px #262c2f inset;
    -o-box-shadow: 0 -1px 0 0px #262c2f inset;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .today-weather>ul>li {
    line-height: 25px;
    height: 25px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .today-weather>ul>li:first-child {
    font-size: 34px;
    height: 56px;
    line-height: 65px;
    box-sizing: border-box;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .today-weather>ul>li:first-child em:last-child {
    font-size: 16px;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .today-weather>ul>li:last-child span:last-child {
    margin-left: 10px;
}


.v6s-h-nav>.v6s-h-nav-weather .weather-con .forecast-weather {
    height: 101px;
    width: 230px;
    margin: 0 auto;
    overflow: hidden;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .forecast-weather>ul>li {
    text-align: center;
    width: 33.3%;
    float: left;
    color: #dddddd;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .forecast-weather>ul>li>span {
    font-size: 12px;
    height: 18px;
    line-height: 18px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .forecast-weather>ul>li>span.pic {
    width: 28px;
    height: 20px;
    display: block;
    margin: 11px auto 9px;
    background-size: contain;
}

.v6s-h-nav>.v6s-h-nav-weather .weather-con .forecast-weather>ul>li>span.pic img {
    width: 100%;
    height: 100%;
}

/*工作日历*/
.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-title {
    position: relative;
    font-size: 15px;
    padding: 0 15px;
    cursor: pointer;
    line-height: 50px;
    height: 100%;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-title:hover {
    background: #31393d;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-title::after {
    content: "";
    position: absolute;
    height: 20px;
    right: 0;
    top: 15px;
    border-right: 1px solid #999999;
    box-sizing: border-box;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-title b {
    height: 30px;
    width: 30px;
    background: url(/img/frame/calendar-icon.png) no-repeat center center;
    float: left;
    margin-top: 8px;
    margin-right: 2px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-title em {
    float: left;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-title em.em2>i {
    float: left;
    line-height: 49px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-title em.em2>i.line-h {
    line-height: 46px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-title em.size {
    font-size: 16px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-title .reddot {
    position: absolute;
    left: 33px;
    background: #02a9d1;
    border-radius: 10px;
    width: 11px;
    height: 11px;
    display: block;
    top: 11px;
}


.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con {
    position: absolute;
    box-sizing: border-box;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    top: 60px;
    right: -54px;
    width: 290px;
    height: 195px;
    background: #31393e;
    display: none;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con:before {
    content: '';
    position: absolute;
    top: -6px;
    width: 0;
    height: 0;
    border-left: 9px solid rgba(0, 0, 0, 0);
    border-right: 9px solid rgba(0, 0, 0, 0);
    border-bottom: 6px solid #374045;
    right: 30%;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top {
    width: 100%;
    height: 133px;
    background: #374045;
    background: -webkit-linear-gradient(top, #2c3134 0%, #374045 40%);
    border-radius: 6px 6px 0 0;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.d {
    padding: 0 15px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.d li {
    height: 30px;
    line-height: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.d li:first-child {
    line-height: 65px;
    height: 50px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.d li:first-child em:first-child {
    font-size: 34px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.d li.time {
    line-height: 24px;
    margin-top: 2px;
    height: 52px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.d li.time span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: default;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.d li.time span em {
    margin-right: 10px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.special-d {
    position: absolute;
    right: 10px;
    top: 7px;
    height: 74px;
    overflow: hidden;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.special-d li {
    height: 25px;
    line-height: 25px;
    font-size: 12px;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-top ul.special-d li b {
    font-family: 'perficon';
    margin-right: 2px;
    color: #f39800;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-bottom {
    height: 60px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-bottom:hover {
    background: #31393d;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-bottom .message-tips {
    width: 261px;
    height: 30px;
    line-height: 37px;
    border-top: 1px solid #262c2f;
    -webkit-box-shadow: 0 1px 0 0px #455056 inset;
    box-shadow: 0 1px 0 0px #455056 inset;
    -moz-box-shadow: 0 1px 0 0px #455056 inset;
    -ms-box-shadow: 0 1px 0 0px #455056 inset;
    -o-box-shadow: 0 1px 0 0px #455056 inset;
    margin-left: 15px;
    color: #dddddd;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-bottom .jobcalendar-details {
    width: 100%;
    text-align: center;
    color: #02a9d1;
}

.v6s-h-nav>.v6s-h-nav-jobcalendar .jobcalendar-con .jobcalendar-c-bottom .jobcalendar-details a {
    width: 100%;
    color: inherit;
    position: absolute;
    top: 0;
    bottom: 0;
    line-height: 85px;
}

/************nav begin*********************/
.v6s-nav {
    position: fixed;
    width: 222px;
    background: #2e3539;
    top: 50px;
    overflow: hidden;
    bottom: 0;
    left: -222px;
    transition: 0.3s;
    -webkit-transition: 0.3s;
    -moz-transition: 0.3s;
    -ms-transition: 0.3s;
    -o-transition: 0.3s;
    z-index: 88;
}

.v6s-nav:hover {
    overflow: auto;
    overflow: overlay;
}

.v6s-nav .v6s-set-enter {
    position: absolute;
    top: 10px;
    color: #7d8688;
    right: 10px;
    cursor: pointer;
    font-size: 12px;
    line-height: 22px;
}

.v6s-nav .v6s-set-enter b {
    font-family: "perficon";
    font-size: 14px;
    float: left;
    margin-right: 2px;
}

.v6s-nav .v6s-build-wrap {
    margin-bottom: 20px;
    padding: 0 40px;
    box-sizing: border-box;
}

.v6s-nav .v6s-build-wrap dt {
    display: block;
    color: #fff;
    font-size: 16px;
    text-align: center;
    line-height: 28px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: default;
}

.v6s-nav .v6s-build-wrap dt:last-child {
    font-size: 12px;
    line-height: 18px;
    color: #ddd;
}

.v6s-nav .v6s-build-wrap .build-pic {
    height: 85px;
    width: 85px;
    border-radius: 100%;
    border: 1px solid #36393f;
    overflow: hidden;
    position: relative;
    margin: 42px auto 9px;
    box-sizing: border-box;
}

.v6s-nav .v6s-build-wrap .build-pic b {
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 2;
    border-radius: 100%;
}

.v6s-nav .v6s-build-wrap .build-pic img {
    width: 100%;
    height: 100%;
    position: absolute;
    display: block;
}

/*nav-list*/

.v6s-nav .v6s-nav-wrap .v6s-nav-temp {
    color: #a0b1c1;
    margin-top: 20px;
}

.v6s-nav .v6s-nav-wrap .v6s-nav-temp:first-child {
    margin-top: 0px;
}

.v6s-nav .v6s-nav-wrap .v6s-nav-temp .temp-title {
    color: #fff;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #3a4247;
    -webkit-box-shadow: 0 -1px 0 0px #262c2f inset;
    box-shadow: 0 -1px 0 0px #262c2f inset;
    -moz-box-shadow: 0 -1px 0 0px #262c2f inset;
    -ms-box-shadow: 0 -1px 0 0px #262c2f inset;
    -o-box-shadow: 0 -1px 0 0px #262c2f inset;
    padding-left: 20px;
    box-sizing: border-box;
}

.v6s-nav .v6s-nav-wrap .v6s-nav-temp ul li {
    height: 47px;
    line-height: 47px;
    cursor: pointer;
    border-bottom: 1px solid #3a4247;
    -webkit-box-shadow: 0 -1px 0 0px #262c2f inset;
    box-shadow: 0 -1px 0 0px #262c2f inset;
    -moz-box-shadow: 0 -1px 0 0px #262c2f inset;
    -ms-box-shadow: 0 -1px 0 0px #262c2f inset;
    -o-box-shadow: 0 -1px 0 0px #262c2f inset;
    position: relative;
    box-sizing: border-box;
}

.v6s-nav .v6s-nav-wrap .v6s-nav-temp ul li:before {
    content: '';
    position: absolute;
    width: 6px;
    left: 0;
    top: -1px;
    bottom: 0px;
    background: transparent;
    transition: 0.2s;
}




.v6s-nav .v6s-nav-wrap .v6s-nav-temp ul li:hover {
    color: #fff;
    background: #333c41;
}

.v6s-nav .v6s-nav-wrap .v6s-nav-temp ul li:hover:before {
    background: #02a9d1;
}


.v6s-nav .v6s-nav-wrap .v6s-nav-temp ul li a {
    height: 100%;
    display: block;
    color: inherit;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 12px 0 25px;
}

.v6s-nav .v6s-nav-wrap .v6s-nav-temp ul li b {
    font-family: "perficon";
    float: right;
    color: #4f5a64;
}



.v6s-nav .v6s-nav-wrap .v6s-nav-temp ul li:hover b {
    color: #fff;
}

.v6s-nav .v6s-nav-wrap .v6s-nav-temp ul li em {
    width: 20px;
    height: 20px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: contain;
    display: inline-block;
    margin: 13px 10px 0 0;
    float: left;
}

.v6s-nav .v6s-nav-wrap .v6s-nav-temp ul li span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/*内容页*/
.v6s-content {
    min-width: 1280px;
    max-width: 2560px;
    margin: 0 auto;
    width: 100%;
    height: 100%;
    padding-top: 50px;
    box-sizing: border-box;
}

/*prompt 成功失败提示信息*/
.prompt-success {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translate(-50%);
    -webkit-transform: translate(-50%);
    z-index: 31;
}

.prompt-failure {
    position: absolute;
    top: 50px;
    left: 50%;
    transform: translate(-50%);
    -webkit-transform: translate(-50%);
    z-index: 31;
}