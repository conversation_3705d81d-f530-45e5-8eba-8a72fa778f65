#dataAnalysispage {
    display: flex;
    width: 100%;
    height: calc(100vh - 82px);
    border: 1px solid #d9e2e8;
}

#dataAnalysispage .dataAnalysispage-left-wrapper {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    width: 240px;
    flex: 0 0 240px;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d9e2e8;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .drop-wrapper {
    width: 100%;
    height: 44px;
    line-height: 44px;
    padding: 0 10px;
    box-sizing: border-box;
    border-bottom: 1px solid #d9e2e8;
    background: #f9fbfc;
    font-size: 14px;
    color: #333333;
    font-weight: bold;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .chooseType-wrapper {
    height: 50px;
    width: 100%;
    background: #fff;
    border-bottom: 1px solid #d9e2e8;
    box-sizing: border-box;
    background: #f9fafc;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .chooseType-wrapper .chooseType {
    min-width: 220px;
    margin-left: 10px;
    margin-top: 11px;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper {
    max-height: 130px;
    border-bottom: 1px solid #d9e2e8;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .time-part-wrapper {
    max-height: 80px;
    overflow: auto;
    padding: 9px 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding-bottom: 0;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .time-part {
    height: 30px;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin-bottom: 10px;
    border: 1px solid #d9e2e8;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .time-part:last-child {
    margin-bottom: 0;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .time-part .time-conent {
    display: inline-block;
    vertical-align: top;
    height: 28px;
    line-height: 28px;
    text-align: center;
    border-right: none;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .time-part .time-conent:hover {
    background: #f5f5f5;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .time-part .timer-reduce {
    display: inline-block;
    width: 28px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    font-family: perficon;
    border-left: 1px solid #d9e2e8;
    cursor: pointer;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .time-part .timer-reduce:hover {
    background: #f5f5f5;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .addTimer-wrapper {
    position: relative;
    height: 50px;
    padding: 10px;
    box-sizing: border-box;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .addTimer-wrapper .time-add {
    position: absolute;
    left: 10px;
    right: 10px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid #d9e2e8;
    cursor: pointer;
    color: #637E99;
    z-index: 1;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .addTimer-wrapper .time-add:hover {
    background: #f5f5f5;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .addTimer-wrapper .time-add>em {
    font-family: perficon;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .timegroup-wrapper .addTimer-wrapper .time-group {
    display: none;
    background: #fff;
    border: 1px solid #d9e2e8;
    width: 200px;
    max-height: 150px;
    padding: 10px;
    position: absolute;
    top: 10px;
    left: 100%;
    margin-left: -8px;
    z-index: 10;
    box-shadow: 0 2px 9px rgba(0, 0, 0, 0.1);
}

#dataAnalysispage .dataAnalysispage-left-wrapper .toggleBuild {
    height: 50px;
    padding: 10px 10px 0;
    border-bottom: 1px solid #d9e2e8;
    box-sizing: border-box;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .searchTement {
    height: 50px;
    position: relative;
    padding: 10px;
    border-bottom: 1px solid #d9e2e8;
    box-sizing: border-box;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .searchTement>b {
    position: absolute;
    right: 26px;
    top: 18px;
    z-index: 9;
    width: 14px;
    height: 14px;
    line-height: 15px;
    text-align: center;
    font-family: perficon;
    font-size: 10px;
    color: #fff;
    border-radius: 50%;
    background: #d9d9d9;
    cursor: pointer;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .floor-wrapper {
    flex: 1;
    width: 100%;
    overflow: hidden;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .floor-wrapper .popTree-wrap {
    height: 100%;
    overflow: auto;
    position: relative;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .floor-wrapper .popTree-wrap .sosonodata {
    position: absolute;
    top: 50%;
    left: 50%;

    margin-top: -50px;
    margin-left: -50px;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .floor-wrapper .popTree-wrap .sosoData li {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #d9e2e8;
    padding: 0 20px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .floor-wrapper .popTree-wrap .sosoData li:hover {
    background: #f8f8f8;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .floor-wrapper .popTree-wrap .sosoData li i {
    font-family: perficon;
    float: right;
    color: #02a9d1;
    display: none;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .floor-wrapper .popTree-wrap .sosoData li.pitch i {
    display: block;
}

#dataAnalysispage .dataAnalysispage-left-wrapper .floor-wrapper .treeCont {
    border-right: none;
}

#dataAnalysispage .dataAnalysispage-right-wrapper {
    position: relative;
    flex: 1;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name {
    position: relative;
    width: 100%;
    height: 50px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-bottom: 1px solid #d9e2e8;
    background: #f9fafc;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .checkBox-wrapper {
    width: 160px;
    height: 38px;
    line-height: 16px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    top: 6px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .checkBox-wrapper>div {
    display: inline-block;
    margin-top: 11px;
    margin-left: 20px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .fun {
    position: absolute;
    right: 20px;
    top: 5px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #fff;
    color: #333;
    font-family: perficon;
    cursor: pointer;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox {
    display: inline-block;
    position: absolute;
    width: 30px;
    right: 20px;
    top: 8px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .combobox-menu {
    min-width: 30px;
    height: 30px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .combobox-title {
    width: 28px;
    height: 28px;
    padding-left: 0px;
    background: #fff;
    border: 1px solid #cacaca;
    color: #637e99;
    position: relative;
    font-size: 14px;
    line-height: 28px;
    border-radius: 3px;
    display: inline-block;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .combobox-title i {
    float: left;
    width: 30px;
    font-family: "perficon";
    text-align: center;
    font-size: 16px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .expand-transition {
    transition: all 0.5s ease;
    height: 68px;
    overflow: hidden;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .expand-enter,
#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .expand-leave {
    height: 0px;
    overflow: hidden;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .downcombobox-con {
    color: #637e99;
    width: 100px;
    position: absolute;
    box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e3e3;
    background: #fff;
    box-sizing: border-box;
    top: 29px;
    right: 0;
    max-height: 231px;
    display: none;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .downcombobox-con ul {
    position: relative;
    z-index: 2;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .downcombobox-con ul li {
    width: 100%;
    height: 33px;
    line-height: 33px;
    box-sizing: border-box;
    padding-left: 16px;
    border-bottom: 1px solid #e3e3e3;
    position: relative;
    color: #637e99;
    background: #fff;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .downcombobox-con ul li:hover {
    background: #f8f8f8;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .name .downCombox .downcombobox-con ul li b {
    display: block;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 15px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main {
    position: absolute;
    top: 50px;
    bottom: 0;
    left: 0;
    right: 300px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .dataComparaNodata {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 5;
    background: #fff;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .dataComparaNodata>div {
    position: absolute;
    top: 50%;
    left: 50%;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-chart {
    height: 60%;
    padding: 10px 10px 0;
    box-sizing: border-box;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-chart.showChart {
    height: 100%;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table {
    height: 40%;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table.showReport {
    height: 100%;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table .dataComparaGrid {
    position: relative;
    height: 100%;
    overflow: auto;
    overflow-y: hidden;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table .dataComparaGrid .formCont {
    height: 100%;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table .dataComparaGrid .dataComparaGrid-title-wrap {
    position: absolute;
    height: 50px;
    top: 0;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table .dataComparaGrid .dataComparaGrid-title-wrap .dataComparaGrid-title {
    display: flex;
    height: 50px;
    color: #fff;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table .dataComparaGrid .dataComparaGrid-title-wrap .dataComparaGrid-title li {
    flex: 1;
    text-align: center;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 30px;
    min-width: 130px;
    display: inline-block;
    border-right: 1px solid #cacaca;
    background: #a8a3b1;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table .dataComparaGrid .dataComparaGrid-title-wrap .dataComparaGrid-title li:last-child {
    border-right: none;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table .dataComparaGrid .dataComparaGrid-cont-wrap {
    position: absolute;
    top: 50px;
    bottom: 0;
    max-height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table .dataComparaGrid .dataComparaGrid-cont-wrap .dataComparaGrid-cont li {
    height: 30px;
    display: flex;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .main .comparative-table .dataComparaGrid .dataComparaGrid-cont-wrap .dataComparaGrid-cont div {
    flex: 1;
    text-align: center;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 30px;
    min-width: 130px;
    display: inline-block;
    border-right: 1px solid #cacaca;
    border-bottom: 1px solid #cacaca;
    background: #fff;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 300px;
    position: absolute;
    right: 0;
    top: 50px;
    bottom: 0;
    border-left: 1px solid #d9e2e8;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .fun-wrapper {
    box-sizing: border-box;
    height: 44px;
    line-height: 44px;
    padding: 0 10px 0 16px;
    font-size: 14px;
    border-bottom: 1px solid #d9e2e8;
    font-weight: bold;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .fun-wrapper span {
    float: right;
    font-size: 14px;
    color: #637e99;
    cursor: pointer;
    font-weight: normal;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .fun-wrapper span em {
    font-family: perficon;
    margin-right: 8px;
    position: relative;
    top: -1px;
    font-size: 16px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper {
    position: absolute;
    right: 0;
    top: 44px;
    bottom: 0;
    width: 299px;
    overflow: auto;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item {
    width: 100%;
    padding: 20px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-bottom: 1px solid #eee;
    position: relative;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item:hover {
    background: #f8f8f8;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item:hover .itemreduce {
    visibility: visible;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item ._time {
    font-size: 14px;
    color: #6d6d6d;
    padding-left: 20px;
    margin-top: 8px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item ._energy {
    padding-left: 20px;
    margin-top: 16px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item ._energy .itemtimer .energyNo {
    font-size: 20px;
    color: #333;
    margin-top: 0px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item .point {
    width: 12px;
    text-align: center;
    margin-right: 8px;
    float: left;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item .point i {
    display: inline-block;
    width: 8px;
    height: 8px;
    text-align: center;
    border-radius: 50%;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item .itemmain {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item .itemmain .itemname,
#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item .itemmain .itemtimer {
    display: block;
    flex: 1;
    color: #333;
    font-size: 14px;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item .itemmain .itemtimer {
    color: #6d6d6d;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item .itemmain .itemtimer .energyNo {
    font-size: 16px;
    font-weight: bold;
}

#dataAnalysispage .dataAnalysispage-right-wrapper .right-wrapper .comparative-wrapper .comparative-item .itemreduce {
    visibility: hidden;
    width: 18px;
    height: 18px;
    font-family: perficon;
    text-align: center;
    line-height: 18px;
    border-radius: 50%;
    background: #c1c1c1;
    color: #fff;
    cursor: pointer;
    position: absolute;
    top: 23px;
    right: 8px;
}

#dataAnalysispage .timegroup-wrapper .per-time-calendar,
#dataAnalysispage .timegroup-wrapper .per-calendar-title {
    width: 100%;
}

#dataAnalysispage .timegroup-wrapper .per-calendar-title {
    padding: 0;
}

#dataAnalysispage .timegroup-wrapper ._time-left,
#dataAnalysispage .timegroup-wrapper ._time-right {
    pointer-events: none;
    display: none;
}

#dataAnalysispage .timegroup-wrapper .per-calendar-text {
    color: transparent;
    background: transparent;
    border-color: transparent;
}

#dataAnalysispage .timegroup-wrapper .per-calendar-con {
    top: 0;
    left: 100%;
    margin-left: 2px;
    z-index: 20;
}

.table__box--analysis {
    height: 100%;
    border: 1px solid #dedede;
    overflow: hidden;
}

.common__column--body {
    height: 100%;
    overflow: auto;
}

.common__column {
    display: flex;
}

.common__column>ul {
    flex-basis: 0;
    flex-grow: 1;
    overflow: hidden;
}

.common__column>ul:first-child {
    flex: 0 0 130px;
}

.common__column>ul:not(:last-child) {
    border-right: 1px solid #dedede;
}

.common__column>ul>li {
    border-bottom: 1px solid #dedede;
    text-align: right;
}

.common__column>ul>li>div {
    padding-right: 20px;
    height: 36px;
    line-height: 36px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.common__column>ul:first-child li {
    text-align: left;
}

.common__column>ul:first-child li>div {
    padding-left: 20px;
    padding-right: 0;
}

.common__column>ul>li:nth-child(even) {
    background: #f8f8f8;
}

.common__row>div {
    float: left;
    flex: 1;
    min-width: 120px;
    overflow: hidden;
    border-bottom: 1px solid #dedede;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: right;
}

.common__row>div>b {
    padding-right: 20px;
}

.common__row>div:first-child {
    text-align: left;
    flex: 0 0 130px;
}

.common__row>div:first-child>b {
    padding-left: 20px;
    padding-right: 0;
}

.cr--header {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    background-color: #8b959a;
    color: #fff;
}

.common__row {
    height: 36px;
    line-height: 36px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
}

.combobox-menu {
    display: inline-block;
    position: relative;
    max-width: 225px;
    cursor: pointer;
    width: 100%;
}

#dataCompataBulid {
    z-index: 6;
}

#dataCompataBulid .per-combobox-title:before {
    content: 'g';
    display: inline-block;
    position: absolute;
    font-family: perficon;
    top: -1px;
    left: 10px;
    font-size: 16px;
    z-index: 10;
    color: #7A94AD;
}

#dataCompataBulid .per-combobox-title {
    padding-left: 38px;
}

#dataComparaTimer .per-calendar-con {
    min-width: 282px;
}

.disabledNoevent {
    cursor: default !important;
    opacity: .3;
}

#dataComparaTimer .per-calendar-lock {
    pointer-events: none;
}

#comparative-chart .highcharts-tooltip>span {
    max-height: 300px;
    overflow-y: auto;
}