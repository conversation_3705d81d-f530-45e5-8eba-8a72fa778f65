#detailPage {
    height: 100vh;
}

#detailPage [p-type="tab-button"]>.tab-tit {
    z-index: 0;
}

/*加响应式*/
.dpLeft {
    width: 292px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.dpRight {
    width: calc(100% - 293px);
    border-left: 1px solid #d9e2e8;
    height: 100%;
    position: relative;
}

#dpSearch {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-bottom: 1px solid #d9e2e8;
    padding: 10px 20px;
}

#dpSearch [p-type^="searchbox"] .dropdown-con {
    z-index: 1;
}

#dpSearch .deleteX {
    display: none;
    position: absolute;
    left: 272px;
    top: 30px;
    z-index: 5;
    font-family: perficon;
    font-size: 12px;
    color: #fdfdfd;
    cursor: pointer;
    border-radius: 50%;
    border: 1px solid #ddd;
    background: #ddd;
    width: 16px;
    height: 16px;
    box-sizing: border-box;
    text-align: center;
    line-height: 15px;
}

.detailTabWrap {
    text-align: center;
    padding: 10px 20px;
    border-bottom: 1px solid #d9e2e8;
}

.detailFoot {
    height: calc(100vh - 187px);
    overflow: auto;
    position: relative;
}

.detailTabWrap .tab-tit li {
    width: 85px;
    height: 28px;
}

.detailFoot>.leftP ul:hover {
    overflow: auto;
    overflow: overlay;
}

.detailFoot>.leftP ul>li {
    width: 50px;
    height: 50px;
    background: #a5b7c8;
    color: #fff;
    border-top: 1px solid #d9e2e8;
    text-align: center;
    line-height: 50px;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.detailFoot>.leftP ul>li:first-child {
    border-top: none;
}

.rightPp_floor {
    height: 35px;
    background: #f6f6f6;
    padding: 0 20px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border-bottom: 1px solid #d9e2e8;
    border-top: 1px solid #d9e2e8;
    font-size: 16px;
    line-height: 35px;
}

.rightP_part:first-child .rightPp_floor {
    border-top: none;
}

.rightP_part .checkFloor {
    background: #02a9d1 !important;
    color: #fff !important;
    border-bottom-style: solid !important;
    border-bottom-width: 0 !important;
}

.rightP_part .checkFloor .hou,
.rightP_part .checkFloor .yu,
.rightP_part .checkFloor .tan {
    border: 1px solid #fff;
}

.rightP {
    width: calc(100% - 51px);
    border-left: 1px solid #d9e2e8;
    height: calc(100vh - 187px);
    overflow: auto;
    overflow-x: hidden;
}

.leftP {
    height: calc(100vh - 187px);
    overflow: auto;
    overflow-x: hidden;
}

.leftP::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}

.leftP::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0);
    -webkit-border-radius: 3px;
}

.leftP::-webkit-scrollbar-thumb {
    -webkit-border-radius: 3px;
    background: #e6e6e6;
    border: 1px solid #e6e6e6;
}

.leftP::-webkit-scrollbar-thumb:hover {
    background: #e6e6e6;
    border: 1px solid #e6e6e6;
}

.rightP_part .rightPp_tent {
    overflow: auto;
    overflow: overlay;
}

.rightP_part .rightPp_tent ul li {
    height: 48px;
    border-bottom: 1px dashed #eeeeee;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
    padding: 7px 0 0 30px;
}

.rightP_part .rightPp_tent ul li .xxoverhidden {
    width: 138px;
}

.rightP_part .rightPp_tent ul li .xxoverhidden .tentName {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%
}

.rightP_part .rightPp_tent ul li:hover {
    background: #f8f8f8;
}

.rightP_part .rightPp_tent ul li:last-child {
    border-bottom: none;
}

.rightP_part .rightPp_tent .tentNO {
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
}

.tentNO {
    color: #6d6d6d;
}

.rightPp_tent .hou,
.rightPp_tent .yu,
.rightPp_tent .tan {
    border: 1px solid #e2e5ea;
}

.checkFloor {
    background: #02a9d1;
}

.checkFloor ._left,
.checkFloor ._left .tentNO {
    color: #fff;
}

#detailPage .timeBox {
    text-align: left;
    height: 45px;
    position: relative;
}

#detailPage .timeBox>.timeContent {
    display: inline-block;
    height: 28px;
    padding: 0 16px 0 32px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 28px;
    border: 1px solid #cacaca;
    border-radius: 3px;
    cursor: pointer;
}

#detailPage .timeBox>.timeChartBox {
    height: auto;
    background: #fff;
    position: absolute;
    top: 28px;
    left: 0px;
    z-index: 5;
    padding: 10px;
    border: 1px solid #ddd;
    display: none;
    width: 230px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

#detailPage .timeBox>.timeChartBox [p-type="time-chart"].calendar-horizontal .time-to,
#detailPage .timeBox>.timeChartBox [p-type="time-chart"] .calendar-box>[class*=-box] .calendar-text span>em.week {
    display: none;
}

#detailPage .timeBox>.timeChartBox [p-type="time-chart"] .calendar-box>.day-box .calendar-text span>em.cur,
#detailPage .timeBox>.timeChartBox [p-type="time-chart"] .calendar-box>.day-box .calendar-text span>em {
    height: 100%;
    line-height: 29px;
}

#detailPage .timeBox>.timeChartBox>div {
    line-height: 28px;
}

#detailPage .timeBox>.timeChartBox>div:last-child {
    text-align: center;
}

#detailPage.timeBox>.timeChartBox>div:last-child {
    text-align: center;
    margin-top: 10px;
}

#detailPage .timeBox>.timeChartBox>div:last-child>div {
    display: inline-block;
}

#detailPage .timeBox>.timeContent>span {
    position: relative;
}

#detailPage .timeBox>.timeContent>span:before {
    position: absolute;
    top: 1px;
    left: -20px;
    content: '';
    width: 14px;
    height: 14px;
    display: inline-block;
    background: url(../images/clock.png) no-repeat center center;
}

.dpRight_head {
    height: 80px;
    border-bottom: 1px solid #d9e2e8;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 20px;
    width: 100%;
    position: relative;
    z-index: 6;
}

.dpRight_head .closeDetailFloat {
    font-family: perficon;
    cursor: pointer;
    color: #637e99;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    margin-left: 10px;
    position: relative;
    margin-top: 20px;
}

.dpRight_head .closeDetailFloat:before {
    content: '';
    border-left: 1px solid #EDEDED;
    height: 25px;
    position: absolute;
    top: 6px;
    left: 0;
}

.dpRight_head .dpRight_head_title {
    font-size: 16px;
    padding-top: 31px;
}

.custromTementAlarm {
    margin-top: 27px;
    margin-left: 20px;
}

.dpRight_head_time {
    margin-top: 27px;
}

.dpRight_head .note {
    font-size: 16px;
}

.dpRight_head .detailtitle {
    max-width: 200px;
    display: inline-block;
    height: 16px;
    line-height: 16px;
    position: relative;
    top: 2px;
}

#chooseType {
    min-width: 100px;
    display: inline-block;
    position: relative;
    cursor: pointer;
    text-align: left;
}

#chooseType [p-type^="combobox"] .combobox-con.combobox-menu-bottom {
    top: 31px;
}

.dpRight_body {
    height: 100%;
}

.dpRight_body_title {
    text-align: center;
    position: relative;
    padding: 32px 0;
}

.dpRight_body_title .dName {
    font-size: 18px;
}

.dpRight_body_title .meterWraper {
    position: absolute;
    top: 57px;
    left: 50%;
    z-index: 20;
    height: 30px;
    width: 300px;
    margin-left: -102px;
    z-index: 1;
}

.dpRight_body_title .meterWraper>div {
    float: left;
    margin: 0 10px;
}

.dpRight_body_title .meterWraper .meterWrapername {
    line-height: 30px;
}

.chartTitle {
    font-size: 16px;
}

.chartPart {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.promptPart {
    display: none;
    position: relative;
    height: calc(100vh - 228px);
}

.promptPart [p-type="prompt-abnormalmess"] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
}

.inline-element:before {
    content: "\A";
    white-space: pre;
}

.promptPart .activeTime:after {
    content: '，';
}

.charthead {
    position: relative;
    height: 30px;
    line-height: 30px;
    text-align: center;
    z-index: 5;
}

.charthead .charTab {
    position: absolute;
}

.charthead>span {
    color: #637e99;
    font-size: 16px;
    padding-left: 25px;
}

.charthead .combobox-title>em {
    font-size: 16px;
}

.charthead>.changeWatch {
    display: inline-block;
    position: relative;
    top: 9px;
    left: -15px;
}

.chartMain {
    height: 100%;
}

.chartMain #updatachartLoading {
    background: white;
}

.footwrap {
    height: calc(100% - 250px);
    position: relative;
}

#chart_expend {
    height: 100%;
    padding-left: 8px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

/*二级下拉框*/
.combobox-level:hover {
    background: #f8f8f8;
    border: 1px solid #ccc;
}

.combobox-level {
    width: 100%;
    display: inline-block;
    height: 100%;
    font-size: 14px;
    line-height: 27px;
    border-radius: 3px;
    padding-left: 15px;
    box-sizing: border-box;
    background: #fff;
    border: 1px solid #cacaca;
    color: #637e99;
    position: relative;
    height: 28px;
}

.combobox-level span {
    float: right;
    width: 28px;
    font-family: "perficon";
    text-align: center;
    font-size: 12px;
}

.combobox-level span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 100%;
}

.combobox-level-menu {
    top: 56px;
    bottom: auto;
    width: 100%;
    position: absolute;
    box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e3e3;
    background: #fff;
    box-sizing: border-box;
    color: #333;
    display: none;
    z-index: 3;
    max-height: 233px;
}

.combobox-level-menu>ul {
    position: relative;
    z-index: 2;
}

.combobox-level-menu>ul>li {
    width: 100%;
    height: 33px;
    line-height: 33px;
    box-sizing: border-box;
    padding-left: 16px;
    padding-right: 16px;
    border-bottom: 1px solid #e3e3e3;
    position: relative;
    overflow: hidden;
}

.combobox-level-menu>ul>li:hover {
    overflow: visible;
}

.combobox-level-menu>ul>li:after {
    content: '>';
    font-family: perficon;
    font-size: 14px;
    color: #333;
    position: absolute;
    top: 0;
    right: 6px;
}

.combobox-content {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.combobox-level2 {
    position: absolute;
    top: -1px;
    left: 100%;
    z-index: 15;
    box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e3e3;
    border-radius: 3px;
    background: #fff;
    box-sizing: border-box;
    color: #333;
}

.combobox-level2>li {
    display: inline-block;
    min-width: 102px;
    width: 100%;
    height: 33px;
    line-height: 33px;
    box-sizing: border-box;
    padding-left: 16px;
    padding-right: 20px;
    border-bottom: 1px solid #e3e3e3;
    position: relative;
}

.combobox-level2>li>span {
    white-space: nowrap;
}

.combobox-level2>li:hover {
    background: #f8f8f8;
}

.combobox-level2>li.pitch:after {
    content: 'Z';
    font-family: perficon;
    text-align: center;
    color: #6d6d6d;
    font-size: 12px;
    float: right;
    position: absolute;
    right: 6px;
    top: 0;
}

.chartFoot>p span:first-child {
    font-size: 16px;
    color: #333;
}

.chartFoot>p span.checkMoth {
    font-size: 14px;
    color: #637e99;
    margin-left: 20px;
    cursor: pointer;
}

.chartFoot_main {
    height: 160px;
    display: flex;
    flex-direction: row;
    /*IE10*/
    display: -ms-flexbox;
    -ms-flex-direction: row;
    border-bottom: 1px solid #d9d9d9;
    margin: 0 20px;
}

.chartFoot_main>.items {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    /*IE10*/
    display: -ms-flexbox;
    -ms-flex-direction: column;
    -ms-flex-pack: center;
    -ms-flex-align: center;
    -ms-flex: 1;
}

.chartFoot_main>.items:first-child {
    border-left: none;
}

.chartFoot_main>.items>div>p {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.chartFoot_main>.items>div>p:first-child {
    height: 24px;
    flex: 0 0 24px;
    padding: 0 10px;
    background: #F4F5F7;
    border-radius: 12px;
    color: #333;
    display: inline-block;
    line-height: 24px;
    margin-bottom: 1px;
}

.chartFoot_main>.items>div>p:last-child em {
    font-size: 50px;
}

.chartFoot_main>.items>div>p:last-child span.isredColor {
    color: #ff7b7b;
}

.chartFoot_main>.items>div>p:last-child span.isgreenColor {
    color: #50E3C2;
}

.chartFoot_main>.items>div>p:last-child i {
    font-size: 12px;
}

.energyNo {
    font-size: 30px;
    color: #333;
    margin-top: 20px;
}

.compareLy {
    text-align: center;
    font-size: 12px;
    color: #6d6d6d;
}

.changeNo {
    font-size: 26px;
    color: #333;
}

.arrow_up {
    color: #ef6767;
}

.arrow_up:before {
    position: relative;
    bottom: -3px;
    content: 't';
    font-family: perficon;
    font-size: 20px;
}

.arrow_down {
    color: rgb(104, 197, 179);
}

.arrow_down:before {
    position: relative;
    top: -3px;
    content: 'b';
    font-family: perficon;
    font-size: 20px;
}

.chart_Moth {
    width: 100%;
}

.voiceSwitch_0:after {
    position: relative;
    top: -3px;
    content: 'y';
    width: 16px;
    height: 16px;
    display: inline-block;
    font-family: perficon;
    font-size: 16px;
    color: #7a94ad;
    cursor: pointer;
}

.voiceSwitch_1:after {
    position: relative;
    top: -3px;
    content: 'q';
    width: 16px;
    height: 16px;
    display: inline-block;
    font-family: perficon;
    font-size: 16px;
    color: #7a94ad;
    cursor: pointer;
}

.postilSwitch {
    width: 14px;
    height: 14px;
    display: inline-block;
    background: url(../images/postilswitch.png) no-repeat center center;
    cursor: pointer;
}

.postilSwitch0 {
    width: 14px;
    height: 14px;
    display: inline-block;
    background: url(../images/postilswitch.png) no-repeat center center;
}

.cursor {
    cursor: pointer;
}

.dpRight_head .goBack {
    margin-top: 10px;
    float: left;
}

.dpRight_day_title {
    text-align: center;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    height: 30px;
    line-height: 30px;
    font-size: 16px;
}

.dpRight_day_title-left {
    position: absolute;
    left: 10px;
    font-size: 14px;
}

.dpRight_day_title-right {
    position: absolute;
    right: 0;
    top: 10px;
}

.dpRight_day_title .note {
    color: #333;
}

.dayExpendRart .chartFoot {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 30px;
    position: absolute;
    bottom: 10px;
    width: 100%;
}

#dayExpend {
    position: absolute;
    top: 50px;
    bottom: 200px;
    left: 0;
    right: 0;
}

.wranColor {
    color: #ef6767;
}

.maxP_time {
    text-align: center;
    font-size: 12px;
    color: #6d6d6d;
}

.maxP_time_hour {
    margin-left: 10px;
}

.temperature {
    font-size: 30px;
    color: #333;
}

@media screen and (min-width: 1921px) {
    #detailPage {
        display: flex;
        display: -ms-flexbox;
    }

    #detailPage .dpLeft {
        flex: 1;
        order: -1;
        -ms-flex: 1;
        -ms-flex-order: -1;
    }

    #detailPage .dpRight {
        flex: 5;
        -ms-flex: 5;
    }
}

.space {
    padding-left: 1.5em;
}

.openTextColor {
    color: #40A1CC;
}

#monthlyEnergysNoData {
    display: none;
}

#detailFloat {
    left: 100px;
}

#detailFloat .per-madal-float {
    width: 100%;
}

#detailTimerWrap .per-calendar-title ._time-left,
#detailTimerWrap .per-calendar-title ._time-right {
    display: none;
}

#detailTimerWrap .per-calendar-title {
    padding: 0;
    margin-top: 26px;
}

.likeLink {
    color: #637E99;
    border-bottom: 1px solid #637e99;
}

.likeLink:hover {
    color: #029AC2;
}

#detailFloat .per-madal-float_operate>div {
    margin: 0 5px;
}

#detailFloat #alarmRecord,
#detailFloat #alarmRecordBack {
    margin-top: 26px;
}

#detailFloat .per-madal-float_x {
    width: 60px;
}

#detailFloat .per-madal-float_title {
    padding-right: 70px;
}

#chart_expend .highcharts-tooltip {
    z-index: 5;
}

#detailsetdiyAlarm {
    background: rgb(253, 253, 253) !important;
    margin-top: 26px !important;
}

#detailsetdiyAlarm:hover {
    background: #f8f8f8 !important;
}

#detailTimerAll .per-calendar-con._combobox_bottom,
#detailTimerOnlyD .per-calendar-con._combobox_bottom,
#detailTimerexceptY .per-calendar-con._combobox_bottom {
    top: 54px;
}