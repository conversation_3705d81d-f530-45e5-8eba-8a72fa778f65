#tenementMonitor {
    position: relative;
}

::-ms-clear,
::-ms-reveal {
    display: none;
}

.clearFloat:after {
    content: " ";
    display: table;
    clear: both;
}

.clearFloat:before {
    content: " ";
    display: table;
}

._left {
    float: left;
}

._right {
    float: right;
}

.blueColor {
    color: #637e99 !important;
}

#prompt {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%);
    -webkit-transform: translate(-50%);
    z-index: 9999;
}

.scroll_part::-webkit-scrollbar {
    width: 15px;
    height: 15px;
}

.scroll_part::-webkit-scrollbar-track:vertical {
    background-color: #f8f8f8;
    border: 1px solid #dddddd;
    border-bottom: none;
    border-top: none;
    border-right: none;
}

.mainBody {
    height: calc(100vh);
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    padding: 16px 10px 0 10px;
    background: #f0f3f6;
}

.detailpanel {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    background: rgba(79, 79, 79, 0.15);
    padding: 25px 40px 35px 25px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.detailpanel .detailpanel-main {
    width: 100%;
    height: 100%;
}

.detailpanel .detailpanel-switch {
    display: inline-block;
    position: absolute;
    top: -6px;
    right: 12px;
    width: 24px;
    height: 40px;
    font-size: 36px;
    color: rgb(153, 153, 153);
    cursor: pointer;
    font-family: perficon
}

.rightTop {
    position: absolute;
    top: 23px;
    right: 10px;
    display: inline-block;
    white-space: nowrap;
}

.rightTop .topSearch {
    display: inline-block;
    position: relative;
    z-index: 5;
}

.rightTop .topSearch>b {
    display: none;
    position: absolute;
    right: 8px;
    top: 8px;
    z-index: 12;
    width: 14px;
    height: 14px;
    line-height: 15px;
    text-align: center;
    font-family: perficon;
    font-size: 10px;
    color: #fff;
    border-radius: 50%;
    background: #D9D9D9;
    cursor: pointer;
}

.situationAlarm {
    display: inline-block;
    width: 133px;
    margin-right: 5px;
    margin-left: 10px;
}

.buildList_wrap {
    max-height: 200px;
    overflow: hidden;
    text-align: center;
    border-bottom: 1px solid #eee;
    border-top: 1px solid #eee;
}

.buildWarn {
    color: #f77c7c;
    text-align: center;
    height: 20px;
    margin-top: 10px;
}

.buildList_wrap:hover {
    overflow-x: hidden;
    overflow-y: auto;
    overflow-y: overlay;
}

.buildChangebtn {
    position: relative;
    height: 30px;
    background: #fff;
    box-sizing: border-box;
    border-radius: 3px;
    cursor: pointer;
    display: inline-block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.buildChangebtn .per-combobox-title,
.buildChangebtnround .per-combobox-title {
    padding-left: 38px;
}

.buildChangebtn:before,
.buildChangebtnround:before {
    content: 'g';
    font-family: perficon;
    position: absolute;
    top: 6px;
    left: 10px;
    font-size: 16px;
    z-index: 10;
    color: #7A94AD;
}

.buildChangebtnround {
    position: absolute;
    top: 9px;
    z-index: 20;
    height: 28px;
    background: #fff;
    box-sizing: border-box;
    border-radius: 3px;
    cursor: pointer;
    display: inline-block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.buildChangebtndataCompara {
    display: none;
}

.buildIcon {
    width: 14px;
    height: 14px;
    display: inline-block;
    font-family: perficon;
    color: #637e99;
    font-size: 12px;
    position: absolute;
    left: 15px;
    top: 6px;
}

.buildName {
    height: 28px;
    font-size: 14px;
    color: #637e99;
    line-height: 28px;
    display: inline-block;
}

.buildLIst {
    margin: 0 auto;
}

.buildLIst>li:nth-child(odd) {
    background: #F8F8FA;
}

.buildLIst>li:hover {
    cursor: pointer;
    background: #f8f8f8;
}

.buildLIst>li {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    text-align: left;
    padding-left: 100px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.buildLIst>li>b {
    position: relative;
}

.buildLIst>li .leftCross:before {
    content: 'Z';
    font-family: 'perficon';
    color: #6d6d6d;
    font-size: 12px;
    position: absolute;
    left: -22px;
}

[p-type="tab-navigation"]>.tab-tit>.tab-content {
    padding: 12px 20px 10px 20px;
}

[p-type="modal-custom"] .dialog-wrap-con,
[p-type="modal-warncustom"] .dialog-wrap-con {
    padding: 60px 25px 30px 25px !important;
    width: 442px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

#modal-gAlarmset [p-type="modal-custom"] .dialog-wrap-con,
[p-type="modal-warncustom"] .dialog-wrap-con {
    width: 640px;
    max-height: 710px;
}

.btnGroup {
    text-align: center;
    margin-top: 20px;
}

.btnGroup>div {
    display: inline-block;
    margin: 0 20px;
}

.lightBlueC {
    color: #02a9d1 !important;
}

.lightblackC {
    color: #6d6d6d !important;
}

/*禁用样式*/
.disabled {
    opacity: .5;
    pointer-events: none;
}

.disabled .gabpBody {
    pointer-events: none;
}

.disabled1 {
    opacity: .5;
}

/*右侧抽屉*/
#float-diyAlarmset .longTxt [p-type="button-redBorder"] {
    padding: 0 !important;
}

.float_w {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    display: none;
    bottom: 0;

}

#float-diyAlarmset {
    margin-right: 43px !important;
    width: 640px;
}

#float-diyAlarmset .radioGroup {
    margin-top: 10px;
    text-align: right;
    height: 40px;
    line-height: 40px;
}

#float-diyAlarmset .radioGroup>div {
    display: inline-block;
}

#float-diyAlarmset .radioGroup>div:last-child {
    margin-left: 20px;
}

.gabpBody p .inputFalse {
    display: inline-block;
    width: 50px;
    height: 30px;
    margin: 0 5px;
    text-align: center;
}

#float-diyAlarmset .gaFoot {
    position: absolute;
    width: 100%;
    left: 0px;
    bottom: 0px;
    border-top: 1px solid #eee;
    background: white;
    height: 50px;
}

#float-diyAlarmset .gaBody {
    overflow: auto;
    overflow: overlay;
    padding-right: 20px;
    padding-left: 20px;
    height: 100%;
}

#float-diyAlarmset [p-type="float-normal"] .float-con {
    padding-right: 10px;
    padding-top: 0px;
    padding-bottom: 0px;
    top: 0;
}

#float-checkMoth {
    width: calc(100% - 326px);
}

/*报警批注*/
#float-postil {
    width: 536px;
}

.postilWrap {
    padding: 0 25px;
}

.postilTitle {
    margin: 18px 0;
}

.postilBody {
    margin-bottom: 18px;
}

.postilWrap .historyPostil:first-child {
    border-top: none;
}

#float-postil [p-type="float-normal"] .float-title i {
    color: transparent !important;
    background: url(../images/postil.png) no-repeat center center;
}

.historyPostil {
    border-top: 1px dashed #d9e2e8;
}

.postilTitle>span {
    margin: 0 5px;
}

.addPicon>span {
    font-family: perficon;
    color: #333333;
    font-size: 16px;
    margin-right: 10px;
}

.addPostil>textarea {
    margin-top: 20px;
    width: 436px;
    height: 135px;
}

#modal-voice .dialog-wrap-con h4 {
    font-size: 16px;
    color: #333;
    text-align: center;
    font-weight: normal;
    line-height: 34px;
}

#modal-voice .btnG {
    margin-top: 40px;
    text-align: center;
}

#modal-voice .btnG>div {
    display: inline-block;
    margin: 0 20px;
}

#modal-voice .vbody {
    line-height: 24px;
    margin-bottom: 10px;
}

#modal-voice .combobox {
    position: relative;
    display: inline-block;
    top: 8px;
    display: inline-block;
}

.powerBubble {
    width: 14px;
    height: 14px;
    display: inline-block;
    position: relative;
    top: 3px;
    margin-left: 10px;
    background: url(../images/postilswitch.png) no-repeat center center;
    cursor: pointer;
}

#float-checkMoth [p-type="float-normal"] .float-title i {
    color: transparent !important;
    background: url(../images/trend.png) no-repeat center center;
}

#float-checkMoth .float-con div p {
    margin: 15px 0;
}

.pop-tips {
    border: 1px solid #ced0d0;
    background: #fff;
    display: inline-block;
    border-radius: 3px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    position: relative;
    padding: 8px;
}

.pop-tips:before {
    content: '';
    width: 8px;
    height: 8px;
    background: #fff;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
    position: absolute;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    transform: rotate(45deg);
    left: 50%;
    bottom: -3px;
    margin-left: -4px;
}

/*wyy*/
.scroll_part::-webkit-scrollbar-thumb {
    background: none;
}

.scroll_part::-webkit-scrollbar-thumb:hover {
    background: none;
}

.monitorNodata {
    position: absolute;
    left: 20px;
    top: 50px;
    bottom: 0;
    right: 20px;
}

.monitorNodata>div {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -150%);
}


[p-type="grid-dynamic"] .grid-titile>ul>li[checkbox],
[p-type="grid-dynamic"] .grid-content>ul>li>*[lck] {
    -webkit-flex-grow: 0;
    -webkit-flex-basis: 54px;
    -moz-flex-grow: 0;
    -moz-flex-basis: 54px;
    -ms-flex-grow: 0;
    -ms-flex-basis: 54px;
    flex-grow: 0;
    flex-basis: 54px;
}

[p-type="grid-dynamic"] .grid-content>ul>li>*[lck]>[p-type="switch-checkbox"] {
    width: 16px;
    margin: 10px auto 0;
}

.tentCelltitleWrap {
    position: absolute;
    height: 30px;
    line-height: 30px;
    width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.buildTitleW {
    height: 60px;
    font-size: 18px;
    text-align: center;
    margin-top: -26px;

}

.unitSize {
    font-size: 12px;
    line-height: 20px;
    color: #6d6d6d;
    margin-left: 2px;
}

.slh {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

/*component*/
.treeCont {
    border-right: 1px solid #dcdcdc;
}

.treeCont>li {
    position: relative;
    line-height: 40px;
    box-sizing: border-box;
    cursor: pointer;
    background: #ffffff;
}

.treeCont>li.isFirst {
    border-bottom: none;
}

.treeTitle.isFloor>.cont {
    font-size: 14px;
    color: #86A5C2;
}

.treeCont .treeTitle {
    position: relative;
    padding-left: 0px;
    height: 41px;
    line-height: 40px;
    border-bottom: 1px solid #dcdcdc;
    box-sizing: border-box;
    cursor: pointer;
    background: #ffffff;
}

.treeCont .treeTitle:hover {
    background: #f8f8f8;
}

.treeCont .treeTitle .arrow {
    float: left;
    height: 24px;
    width: 24px;
    border: 1px solid transparent;
    border-radius: 100%;
    line-height: 24px;
    text-align: center;
    font-family: "perficon";
    font-size: 14px;
    color: #637e99;
    cursor: pointer;
    box-sizing: border-box;
    margin: 8px 8px 0;
}

.treeCont .treeTitle .choose {
    color: #02A9D1;
}

.treeCont .treeTitle label {
    position: absolute;
    height: 100%;
    z-index: 1;
    left: 0;
    top: 0;
    right: 10px;
    cursor: pointer;
}

.treeCont .treeCont .treeTitle>.cont {
    width: calc(100% - 20px);
    display: inline-block;
}

.treeCont .treeTitle .arrow {
    position: relative;
    z-index: 2;
}

.treeCont .treeTitle b {
    position: absolute;
    top: 12px;
    right: 8px;
    width: 16px;
    height: 16px;
    line-height: 16px;
    font-size: 14px;
    text-align: center;
    font-family: "perficon";
    color: #02a9d1;
    font-weight: bolder;
    ;
}

.treeCont .treeTitle input[type="checkbox"]:checked+span+b {
    display: block;
}

.treeCont .treeTitle input[type="checkbox"]:checked+span {
    color: #02a9d1;
}

.treeCont .treeTitle input[type="radio"]:checked+span+b {
    display: block;
}

.treeCont .treeTitle input[type="radio"]:checked+span {
    color: #02a9d1;
}


.chooseType-wrapper .chooseType {
    min-width: 250px;
    display: inline-block;
    position: relative;
    cursor: pointer;
    text-align: left;
    margin-left: 20px;
    margin-top: 10px;
}

#chooseType .combobox-level {
    width: 100%;
    display: inline-block;
    font-size: 14px;
    line-height: 27px;
    border-radius: 3px;
    padding-left: 15px;
    box-sizing: border-box;
    background: #fff;
    border: 1px solid #cacaca;
    color: #637e99;
    position: relative;
    height: 30px;
    margin-top: 26px;
}

.chooseType .combobox-level {
    width: 100%;
    display: inline-block;
    font-size: 14px;
    line-height: 27px;
    border-radius: 3px;
    padding-left: 15px;
    box-sizing: border-box;
    background: #fff;
    border: 1px solid #cacaca;
    color: #637e99;
    position: relative;
    height: 30px;

}

.chooseType .combobox-level span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    height: 100%;
    float: right;
    width: 28px;
    font-family: "perficon";
    text-align: center;
    font-size: 12px;
}

.chooseType-wrapper .combobox-level-menu {
    top: 27px;
    bottom: auto;
    width: 100%;
    position: absolute;
    box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e3e3;
    background: #fff;
    box-sizing: border-box;
    color: #333;
    display: none;
    z-index: 6;
    max-height: 233px;
}

.combobox-level-menu>ul {
    position: relative;
    z-index: 2;
}

.combobox-level-menu>ul>li {
    width: 100%;
    height: 33px;
    line-height: 33px;
    box-sizing: border-box;
    padding-left: 16px;
    padding-right: 16px;
    border-bottom: 1px solid #e3e3e3;
    position: relative;
    overflow: hidden;
}

.combobox-level-menu>ul>li:after {
    content: '>';
    font-family: perficon;
    font-size: 14px;
    color: #333;
    position: absolute;
    top: 0;
    right: 6px;
}

.combobox-content {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.combobox-level2 {
    position: absolute;
    top: -1px;
    left: 100%;
    z-index: 15;
    box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    -webkit-box-shadow: 0 5px 7px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e3e3;
    border-radius: 3px;
    background: #fff;
    box-sizing: border-box;
    color: #333;
}

.combobox-level2>li {
    display: inline-block;
    min-width: 102px;
    width: 100%;
    height: 33px;
    line-height: 33px;
    box-sizing: border-box;
    padding-left: 16px;
    padding-right: 20px;
    border-bottom: 1px solid #e3e3e3;
    position: relative;
}

.combobox-level2>li.pitch:after {
    content: 'Z';
    font-family: perficon;
    text-align: center;
    color: #6d6d6d;
    font-size: 12px;
    float: right;
    position: absolute;
    right: 6px;
    top: 0;
}

.combobox-level2>li>span {
    white-space: nowrap;
}

._detailFloat {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 100px;
    background: #fff;
    z-index: 50;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: none;
}

.float_w_detail {
    position: absolute;
    top: 57px;
    right: 32px;
    z-index: 60;
    width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: .5s;
    background: #fff;
    height: calc(100vh - 108px);
    border: 1px solid #d9d9d9;
    display: none;
}

.float_w_detail .float-title {
    display: none;
}

.gAlarmsetWrap {
    height: 100%;
}

.errorTips {
    font-size: 12px;
    color: red;
    margin-left: 108px;
    margin-top: 5px;
}

.maskdetailFloat {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100px;
    background: transparent;
    z-index: 50;
    display: none;
}

#dataComparaTimer [p-type="time-chart"] .calendar-box>[class*=-box] .calendar-text span>em.week {
    display: none;
}

#dataComparaTimer [p-type="time-chart"] .calendar-box>.day-box .calendar-text span>em {
    height: 100%;
    line-height: 29px;
}

#dataComparaTimer [p-type="time-chart"] .calendar-box>.year-box .calendar-text span {
    pointer-events: none;
}

/*charttips样式*/
.charttips_alarm {
    color: #f77c7c;
    font-weight: bold;
}

.tabmain {
    padding: 20px;
    height: 100%;
    width: 100%;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding-top: 10px;
    padding-bottom: 10px;
}

.highcharts-tooltip * {
    font-family: Arial, "Microsoft Yahei", sans-serif;
}

.highcharts-tooltip>span {
    border: 1px solid #cdcdcd;
    border-radius: 3px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    background: #fff;
}

.highcharts-tooltip>span>span {
    display: block;
    height: 29px;
    line-height: 29px;
    padding: 0 8px;
    font-size: 14px;
    color: #333;
}

.topsearchWrap {
    width: 200px;
    height: 30px;
    float: left;
}