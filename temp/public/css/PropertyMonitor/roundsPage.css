.rpLeftp {
    width: 210px;
}

.rpRightp {
    width: 100%;
}

.rpLeftp_title {
    height: 50px;
    width: 100%;
    line-height: 50px;
    border-bottom: 1px solid #d9e2e8;
    padding: 10px 18px;
    box-sizing: border-box;
}

.rpLeftp_title .tab-tit li,
.rpRightp_title .tab-tit li {
    width: 85px;
    height: 28px;
}

.rpLeftp_footTitle {
    font-size: 16px;
    height: 45px;
    width: 100%;
    line-height: 45px;
    padding-left: 22px;
    border-bottom: 1px solid #d9e2e8;
    box-sizing: border-box;
}

.rpLeftp_footTitle>span {
    width: 34px;
    width: 34px;
    height: 18px;
    background: #f77c7c;
    color: #fff;
    border-radius: 9px;
    text-align: center;
    line-height: 18px;
    float: right;
    margin-top: 14px;
    margin-right: 11px;
    font-size: 14px;
}

.checkFloor {
    background: #02a9d1 !important;
    color: #fff !important;
    border-bottom-style: solid !important;
}

.checkFloor .alarmCellContent {
    background: #fff !important;
    color: #02a9d1 !important;
}

.rpLeftp_body .floorName {
    font-size: 16px;
}

.rpLeftp_body {
    overflow: auto;
    overflow-x: hidden;
    overflow-x: overlay;
    height: calc(100vh - 178px);
}

.rpLeftp_body li {
    height: 46px;
    border-bottom: 1px dashed #eeeeee;
    padding: 13px 22px;
    box-sizing: border-box;
    cursor: pointer;
    position: relative;
}

.rpLeftp_body li:first-child .alarmCellDetail {
    top: 20px;
}

.rpLeftp_body li:first-child .alarmCellDetail::after {
    display: none;
}

.rpLeftp_body li:hover {
    background: #f8f8f8;
}

.alarmCell {
    position: absolute;
    right: 11px;
    height: 18px;
    top: 14px;
}

.alarmCellContent {
    width: 34px;
    height: 18px;
    background: #f77c7c;
    color: #fff;
    border-radius: 9px;
    text-align: center;
    line-height: 18px;
}

.alarmCellDetail {
    display: none;
    position: absolute;
    top: -62px;
    right: -10px;
    width: 110px;
    height: 60px;
    border: 1px solid #cacccc;
    border-radius: 4px;
    background: #fff;
    color: #333;
    z-index: 6;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.1);
}

.alarmCellDetail>p {
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.alarmCellDetail>p:last-child {
    border-top: 1px solid #cacccc;
    font-size: 12px;
}

.rpRightp_title {
    height: 37px;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    position: relative;
}

.toggleFood-format {
    position: absolute;
    top: 9px;
    right: 17px;
    width: 140px;
    height: 30px;
    z-index: 1;
}

.rpRightp_title_couent {
    max-width: 510px;
    display: -webkit-box;
    display: flex;
    position: absolute;
    top: 13px;
    left: 50%;
    z-index: 5;
    transform: translate(-50%);
    -webkit-transform: translate(-50%);
    -moz-transform: translate(-50%);
    -ms-transform: translate(-50%);
    -o-transform: translate(-50%);
    /*IE10*/
    -ms-display: flexbox;
}

.rpRightp_title_couent>li {
    width: 34px;
    height: 18px;
    color: #fff;
    text-align: center;
    line-height: 18px;
    margin-left: 51px;
}

.rpRightp_body {
    padding: 10px;
    width: calc(100% - 20px);
    height: calc(100vh - 153px);
    position: relative;
    overflow: auto;
    border: 1px solid #d9e2e8;
    background: #f8f8f8;
    margin-top: 2px;
}

.rpRightp_body>ul>li {
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    flex-direction: row;
    /*IE10*/
    display: -ms-flexbox;
    -ms-flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-left: 54px;
    position: relative;
}

.tentCellwrap {
    position: relative;
    box-sizing: border-box;
    cursor: pointer;
}

.tentAcross {
    position: absolute;
    top: 4px;
    left: 4px;
    width: 350px;
    background: #fff;
    border: 1px solid #dae3ec;
    border-radius: 4px;
    box-sizing: border-box;
    z-index: 5;
}

.rpR_foolNo {
    width: 50px;
    min-height: 50px;
    background: #fff;
    color: #768EA5;
    border: 1px solid #DDE5EA;
    border-radius: 2px;
    margin-top: 4px;
    position: absolute;
    top: 0;
    left: 0;
    display: table;
}

.rpR_foolNo span:first-child {
    text-align: center;
    word-break: keep-all;
    margin-left: 10px;
}

.rpR_foolNo .alarmPoint {
    width: 10px;
    height: 10px;
    background: #F77C7C;
    border-radius: 50%;
    position: absolute;
    top: 8px;
    right: 2px;
}

.rpR_foolNo>span {
    width: 50px;
    min-height: 50px;
    font-size: 14px;
    font-weight: bold;
    display: table-cell;
    vertical-align: middle;
    text-align: center;
}

.tentCell {
    width: 242px;
    height: 130px;
    border: 1px solid #dae3ec;
    border-radius: 3px;
    box-sizing: border-box;
    margin: 5px;
    background: #fff;
    padding: 16px 20px 20px;
}

.tentCell>h4,
.isAcolumns>h4 {
    height: 30px;
    line-height: 30px;
    padding-left: 15px;
    box-sizing: border-box;
    background: #dae3ec;
}

.tentCell>h4 .rptentName,
.isAcolumns>h4 .rptentName {
    display: inline-block;
    font-size: 14px;
    height: 100%;
}

.tentCell_name {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.tentCell_name h5 {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.tentCell_name h5.name {
    color: #333;
    font-size: 14px;
    display: inline-block;
}

.tentCell_name h5.floor {
    color: #cacaca;
    font-size: 12px;
}

.tentCell_detail {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
}

.tentCell_detail .detail_left {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    width: 110px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 110px;
    flex: 0 0 110px;
}

.tentCell_detail .detail_right .detail_right_alarm,
.tentCell_detail .detail_right .detail_right_alarm em {
    color: #F77C7C !important;
}

.tentCell_detail .detail_left>h5 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.tentCell_detail .detail_left>h5.name {
    color: #6d6d6d;
    font-size: 12px;
}

.tentCell_detail .detail_left>h5.unit {
    color: #6d6d6d;
    font-size: 18px;
    margin-top: 3px;
    font-family: Arial;
}

.tentCell_detail .detail_left>h5.unit>em {
    color: #333;
}

.tentCell_detail .detail_left>h5.unit>em>em {
    color: #6d6d6d;
    font-size: 12px;
}

.tentCell_detail .detail_right>h5.unit {
    margin-top: 3px;
    font-family: Arial;
}

.tentCell_detail .detail_right>h5.unit>em {
    color: #333;
    font-size: 18px;
    width: 90px;
}

.tentCell_detail .detail_right>h5.unit>em>em {
    color: #6d6d6d;
    font-size: 12px;
}

.tentCell_detail .detail_right {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;

}

.tentCell_detail .detail_right>h5 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    color: #6d6d6d;
}

.tentCell_detail .detail_right>h5.name {
    font-size: 12px;
}

.tentCell>h4 .tentCellPic,
.isAcolumns>h4 .tentCellPic {
    display: inline-block;
}

.tentCell .tentCell_name {
    width: 200px;
    box-sizing: border-box;
    margin-bottom: 18px;
    height: 35px;
}

.tentCell .tentCell_detail {
    box-sizing: border-box;
    height: 43px;
}

.tentCell .expend,
.tentCellPic .expend {
    font-size: 16px;
    display: inline-block;
    color: #333;
    max-width: 112px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: left;
}

.tentCell .tentCell_no,
.isAcolumns .tentCell_no {
    color: #6d6d6d;
    margin: 0 5px;
    font-size: 12px;
}

.hou,
.yu,
.tan {
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 50%;
    background: #ffffff;
    color: #a7a7a7;
    font-size: 12px;
    margin: 5px;
    cursor: default;
}

.tentCellDetail {
    height: 60px;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    flex-direction: row;
    padding: 10px 0px;
    box-sizing: border-box;
    /*IE10*/
    display: -ms-flexbox;
    -ms-flex-direction: row;
}

.tentCellDetail>div {
    -webkit-box-flex: 1;
    flex: 1;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    /*IE10*/
    display: -ms-flexbox;
    -ms-flex-direction: column;
    -ms-flex-pack: center;
    -ms-flex-align: center;
    -ms-flex: 1;
}

.tentCellDetail>div>span {
    /*IE10*/
    display: -ms-flexbox;
    font-size: 12px;
}

.tentCellDetail>div>span:nth-child(2) {
    color: #a7a7a7;
}

.tentCellDetail>div:nth-child(2) {
    border-left: 1px dashed #cacaca;
    border-right: 1px dashed #cacaca;
    box-sizing: border-box;
}

.expend {
    font-size: 21px;
    color: #333;
}

.isAcolumns>.tentCellDetail {
    height: calc(100% - 30px) !important;
    padding: 0 0px !important;
    -webkit-box-orient: vertical !important;
    -webkit-box-direction: normal !important;
    flex-direction: column !important;
    /*IE10*/
    -ms-flex-direction: column !important;
}

.isAcolumns .tentCell {
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    /*IE10*/
    display: -ms-flexbox;
    -ms-flex-direction: column;
}

.isAcolumns .tentCellDetail>div {
    -webkit-box-orient: horizontal !important;
    -webkit-box-direction: normal !important;
    flex-direction: row !important;
    /*IE10*/
    -ms-flex-direction: row !important;
}

.isAcolumns .tentCellDetail>div:nth-child(2) {
    border-top: 1px dashed #cacaca;
    border-bottom: 1px dashed #cacaca;
    border-left: none;
    border-right: none;
}

.isAcolumns .tentCellDetail>div:last-child {
    border-bottom: none;
}

.isAcolumns .tentCellDetail>div>span {
    -webkit-box-flex: 1;
    flex: 1;
    padding-left: 20px;
    box-sizing: border-box;
    /*IE10*/
    -ms-flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.isAcolumns .tentCellDetail>div>span:last-child {
    -webkit-box-ordinal-group: 0;
    order: -1;
    /*IE10*/
    -ms-flex-order: -1;
}

.tentAlarmState .tentCell,
.tentAlarmState .tentAcross {
    border: 1px solid #ff7b7b;
}

.tentAlarmState h4 {
    background: #ff7b7b;
    color: #fff;
}

.tentAlarmState .tentCell_no {
    color: #fff;
}

.roundsAlarmColor {
    color: #ee5438;
}

.roundsAlarmColor>.expend {
    color: #ee5438;
}

.tab-buttondiy>.tab-tit {
    height: 30px;
    line-height: 30px;
    position: relative;
    border-radius: 3px;
    box-sizing: border-box;
    overflow: hidden;
    display: inline-block;
}

.tab-buttondiy>.tab-tit ul {
    position: relative;
    z-index: 2;
    border-radius: 3px;
    box-sizing: border-box;
    height: 100%;
    overflow: hidden;
}

.tab-buttondiy>.tab-tit ul li.cur {
    color: #fff;
    transition: all 200ms linear;
    cursor: default;
    pointer-events: none;
    background: #7a94ad;
    height: 100%;
    border-color: #7a94ad;
}

.tab-buttondiy>.tab-tit ul li:first-child {
    border-radius: 3px 0 0 3px;
}

.tab-buttondiy>.tab-tit ul li:last-child {
    border-right: 1px solid #cacaca;
    box-sizing: border-box;
    border-radius: 0 3px 3px 0;
}

.tab-buttondiy>.tab-tit ul li {
    min-width: 54px;
    text-align: center;
    border: 1px solid #cacaca;
    border-right: none;
    cursor: pointer;
    box-sizing: border-box;
    height: 100%;
    padding: 0 10px;
    display: block;
    float: left;
    color: #637e99;
    width: 85px;
}

.rpRightp_title .tab-buttondiy .tab-tit li>i {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #f77c7c;
    border-radius: 50%;
    position: relative;
    top: -6px;
    right: -15px;
}