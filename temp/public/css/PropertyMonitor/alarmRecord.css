html {
    height: 100%;
}

body {
    height: 100%;
    background: #f0f3f6;
}

#alarmLists,.dpRight_body {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

 .alarm-con-con {
    position: absolute;
    top: 50px;
    bottom: 10px;
    left: 0;
    right: 0;
    border: 1px solid #d9e2e9;
    box-sizing: border-box;
}
#alarmLists .alarm-con-con{
    top: 10px;
}
#alarmPart{
    top: 0;
    border-top: none;
}

 .alarm-con-con .alarm-con-nav {
    width: 249px;
    height: 100%;
    border-right: 1px solid #d9e2e8;
    box-sizing: border-box;
    overflow: auto;
}

.alarm-con-nav .alarm-nav-temp {
    margin-top: 10px;
}

.alarm-con-nav .alarm-nav-temp .nav-title {
    font-size: 14px;
    line-height: 52px;
    height: 52px;
    border-bottom: 1px solid #d9d9d9;
    border-top: 1px solid #d9d9d9;
    background: #f9fbfc;
    position: relative;
    box-sizing: border-box;
}

.alarm-con-nav .alarm-nav-temp ul {
    padding: 0 8px;
    border-bottom: 1px solid #d9e2e8;
}

.alarm-con-nav .alarm-nav-temp ul li {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px dashed #d9d9d9;
    padding-left: 30px;
    position: relative;
    cursor: pointer;
    box-sizing: border-box;
}

.alarm-con-nav .alarm-nav-temp ul li b {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    position: relative;
    z-index: 1;
}

.alarm-con-nav .alarm-nav-temp ul li:hover:after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: -8px;
    right: -8px;
    background: #f8f8f8;
}

.alarm-con-nav .alarm-nav-temp ul li.pitch:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: -8px;
    background: #02a9d1;
    width: 10px;
    z-index: 2;
}

.alarm-con-nav .alarm-nav-temp ul li:last-child {
    border-bottom: none;
}

.alarm-con-nav .alarm-nav-temp ul li.red {
    color: #dd5252;
}

.alarm-con-nav .alarm-nav-temp ul li.green {
    color: #49ab99;
}

.alarm-con-nav .alarm-nav-temp:first-child {
    margin-top: 0;
}

.alarm-con-nav .alarm-nav-temp:first-child .nav-title {
    border-top: none;
}

.alarm-con-nav .alarm-nav-temp .nav-title > em {
    display: block;
    float: left;
    margin-top: 18px;
    margin-left: 10px;
}

.alarm-con-nav .alarm-nav-temp .nav-title > em.state {
    width: 20px;
    height: 16px;
    background: url(/img/PropertyMonitor/alarm/nav-state.png) no-repeat center right;
    margin-top: 19px;
}

.alarm-con-nav .alarm-nav-temp .nav-title > em.alarm {
    width: 20px;
    height: 16px;
    background: url(/img/PropertyMonitor/alarm/nav-alarm.png) no-repeat center right;
}

.alarm-con-nav .alarm-nav-temp .nav-title > b {
    position: absolute;
    text-indent: 35px;
    left: 0;
    font-weight: bold;
}

.alarm-con-nav .alarm-type-tree .temp-title,.alarm-con-nav .alarm-type-tree .all {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px dashed #d9d9d9;
    position: relative;
    cursor: pointer;
    box-sizing: border-box;
    margin: 0 8px;
}

.alarm-con-nav .alarm-type-tree .temp-title b,.alarm-con-nav .alarm-type-tree .all b {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    position: relative;
    z-index: 2;
}


.alarm-con-nav .alarm-type-tree .temp-title:hover:after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: -8px;
    right: -8px;
    background: #f8f8f8;
}

.alarm-con-nav .alarm-type-tree .temp-title.pitch:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: -8px;
    background: #02a9d1;
    z-index: 2;
    width: 10px;
}

.alarm-con-nav .alarm-type-tree .temp-title .arrows {
    width: 26px;
    height: 26px;
    float: left;
    margin: 5px 0px 0 0px;
    line-height: 26px;
    position: relative;
    z-index: 3;
}
.alarm-con-nav .alarm-type-tree .temp-title b{
    margin-left: 30px;
}
.icon{
    font-family: "perficon";
    line-height: 14px;
    font-size: 14px;
    box-sizing: border-box;
    margin: 9px;
    border-radius: 50%;
    color: #7a94ad;
    font-weight: normal;
}
.alarm-con-nav .alarm-type-tree > .temp-title {
    padding-left: 33px;
}

.alarm-con-nav .alarm-type-tree ul {
    border-bottom: none;
    display: none;
}

.alarm-con-nav .alarm-type-tree ul li:last-child {
    border-bottom: 1px dashed #d9d9d9;
}

/*报警消息右侧内容*/
.alarm-con-wrap {
    position: absolute;
    left: 250px;
    right: 0;
    top: 0;
    bottom: 0;
}

.alarm-con-wrap .alarm-c-w-left {
    width: 60%;
    float: left;
    height: 100%;
    position: relative;
}

.alarm-con-wrap .alarm-c-w-left .c-left-title {
    height: 52px;
    border-bottom: 1px solid #d9e2e8;
    box-sizing: border-box;
}

.alarm-con-wrap .alarm-c-w-left .c-left-title .l-t-right {
    float: right;
    margin: 11px 11px 0 0;
    position: relative;
}
.alarm-con-wrap .alarm-c-w-left .c-left-title .l-t-right #tenantCalendar{
    max-width: 240px;
    height: 30px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-title .time-title {
    min-width: 82px;
    width: auto;
    padding: 0 16px;
    font-size: 14px;
    background: #fff;
    border: 1px solid #cecece;
    color: #637e99;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
    transition: all 200ms linear;
    box-sizing: border-box;
    height: 28px;
    line-height: 27px;
    display: block;
}

.alarm-con-wrap .alarm-c-w-left .c-left-title .time-title:hover {
    background: #f8f8f8;
    border: 1px solid #cacaca;
}

.alarm-con-wrap .alarm-c-w-left .c-left-title .time-title i {
    display: block;
    float: left;
    width: 14px;
    height: 14px;
    background: url(/images/alarm/time.png) no-repeat;
    margin-top: 6px;
    margin-right: 8px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-title .time-con {
    width: 220px;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
    position: absolute;
    top: 38px;
    right: 0;
    background: #fff;
    border: 1px solid #dedede;
    z-index: 7;
    padding: 20px;
    display: none;
    text-align: center;
}

.alarm-con-wrap .alarm-c-w-left .c-left-title .time-con .but {
    text-align: center;
    margin-top: 20px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-title .time-con .but > div {
    display: inline-block;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con {
    position: absolute;
    top: 52px;
    bottom: 0;
    width: 100%;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .nodata {
    width: 100%;
    height: 100%;
    text-align: center;
    padding-top: 20%;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .nodata > div {
    display: inline-block;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul {
    position: absolute;
    top: 0;
    bottom: 62px;
    overflow: auto;
    width: 100%;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .paga-wrap {
    border-top: 1px solid #d9e2e8;
    position: absolute;
    bottom: 0px;
    right: 0px;
    height: 62px;
    width: 100%;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .paga-wrap > div {
    float: right;
    margin-top: 15px;
    margin-right: 15px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul > li {
    height: 85px;
    padding: 0 9px;
    box-sizing: border-box;
    cursor: pointer;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul > li > div {
    border-bottom: 1px dashed #d9d9d9;
    height: 100%;
    padding-right: 11px;
    box-sizing: border-box;
    padding-top: 11px;
}
.alarm-con-wrap .alarm-c-w-left .c-left-con .div-top {
    height: 38px;
    line-height: 38px;
    overflow: hidden;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-top .alarm-state {
    width: 54px;
    text-align: right;
    float: left;
    margin-right: 15px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-top .alarm-state.red {
    color: #dd5252;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-top .alarm-state.green {
    color: #49ab99;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-top .alarm-name {
    font-size: 15px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-top .alarm-name > em:first-child {
    max-width: 60%; /*写给不支持calc()的浏览器*/
    max-width: -moz-calc(100% - 150px);
    max-width: -webkit-calc(100% - 150px);
    max-width: calc(100% - 150px);
    max-width: -webkit-calc(100% - 150px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    float: left;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul > li.bold .div-top .alarm-name > em:first-child {
    font-weight: bold;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-top .alarm-name .silence-icon {
    font-family: 'perficon';
    color: #7a94ad;
    margin-left: 5px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-bottom {
    height: 38px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-bottom > span:first-child {
    float: left;
    padding-left: 70px;
    box-sizing: border-box;
    max-width: 50%; /*写给不支持calc()的浏览器*/
    max-width: -moz-calc(100% - 320px);
    max-width: -webkit-calc(100% - 320px);
    max-width: calc(100% - 320px);
    max-width: -webkit-calc(100% - 320px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-bottom > span:last-child {
    float: right;
    color: #6d6d6d;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    max-width: 320px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .div-bottom > span:last-child .name {
    color: #333;
    margin-right: 15px;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul > li:hover {
    background: #f8f8f8;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul > li.active {
    background: #02a9d1;
    color: #fff;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul > li.active .div-bottom > span:last-child {
    color: #fff;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul > li.active .div-bottom > span:last-child .name {
    color: #fff;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul > li.active .div-top .alarm-name .silence-icon {
    color: #fff;
}

.alarm-con-wrap .alarm-c-w-left .c-left-con .c-left-con-wrap > ul > li.active .div-top .alarm-state {
    color: #fff;
}

.alarm-con-wrap .alarm-c-w-right {
    right: 0;
    position: absolute;
    left: 60%;
    top: 0;
    height: 100%;
    border-left: 1px solid #d9e2e8;
}

.alarm-con-wrap .alarm-c-w-right .nodata {
    width: 100%;
    height: 100%;
    text-align: center;
}

.alarm-con-wrap .alarm-c-w-right .nodata > div {
    display: inline-block;
}

.alarm-con-wrap .alarm-c-w-right-con {
    height: 100%;
    width: 100%;
}

.alarm-con-wrap .alarm-details-wrap {
    position: absolute;
    top: 100%;
    width: 100%;
    padding: 0 19px;
    box-sizing: border-box;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 164px;
    overflow: auto;
    padding: 0 20px;
    box-sizing: border-box;
    overflow-x: auto;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name {
    padding: 17px 0 10px 0;
    box-sizing: border-box;
    border-bottom: 1px solid #d9e2e8;
    min-height: 58px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name li {
    height: 26px;
    line-height: 26px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name li.li1 > span {
    font-size: 16px;
    font-weight: bold;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name li.li1 > a {
    color: #637e99;
    margin-left: 8px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name li.li1 > a:hover {
    color: #425f7c
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name li.li2 {
    color: #6d6d6d;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name .muteSet {
    position: absolute;
    right: 20px;
    top: 19px;
    color: #637e99;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name .muteSet span {
    cursor: pointer;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name .muteSet span:hover {
    color: #425f7c;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name .muteSet i {
    font-family: "perficon";
    margin-right: 5px;
    font-size: 18px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-name .muteSet em {
    color: #666;
    font-size: 12px;
    position: absolute;
    width: 200px;
    right: 0;
    text-align: right;
}

.muteSet-wrap {
    min-width: 414px;
    height: 220px;
}

.muteSet-wrap .inp {
    width: 92px;
    display: inline-block;
}

.muteSet-wrap .inp .error-tip {
    left: -112px;
    bottom: -32px;
}

.muteSet-wrap h1 {
    font-size: 16px;
    color: #333;
    text-align: center;
    font-weight: normal;
    line-height: 33px;
}

.muteSet-wrap .set-time {
    text-align: center;
    margin-top: 22px;
}

.muteSet-wrap .tip-con {
    color: #6d6d6d;
    font-size: 14px;
    text-align: center;
    margin-top: 36px;
}

.muteSet-wrap .muteSet-but {
    text-align: center;
    line-height: 50px;
    margin-top: 20px;
}

.muteSet-wrap .muteSet-but > div {
    display: inline-block;
    margin: 0 18px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-type {
    padding: 15px 0 10px 0;
    box-sizing: border-box;
    min-height: 52px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-type li {
    height: 26px;
    line-height: 26px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-type li .mar10 {
    margin-left: 10px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-type li .gray12 {
    color: #6d6d6d;
    font-size: 12px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-type li.tips {
    height: 25px;
    line-height: 25px;
    font-size: 12px;
    color: #fd9054;
    position: relative;
    white-space: nowrap;
    box-sizing: border-box;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-type li.tips:before {
    content: '!';
    width: 14px;
    height: 14px;
    background: #f28b37;
    margin: 3px 5px 5px 0;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 14px;
    border-radius: 50%;
    color: #fff;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con {
    border-top: 1px solid #d9e2e8;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con li span {
    height: 26px;
    line-height: 26px;
    display: block;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con li span > em:first-child {
    float: left;
    color: #02a9d1;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con li span > em:last-child {
    float: right;
    color: #6d6d6d;
    font-size: 12px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con li {
    margin: 10px 0;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con li > p {
    word-break: break-all;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con .alarm-d-title {
    min-height: 58px;
    overflow: hidden;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con .alarm-d-title > span {
    line-height: 60px;
    margin-right: 30px;
    float: left;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con .alarm-d-title > span em.val {
    margin-left: 10px;
    font-size: 30px;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con .alarm-d-title > span em.val.red {
    color: #dd5252;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con .alarm-d-title > span em.val.green {
    color: #49ab99;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con .chart-title {
    text-align: left;
    position: absolute;
}

.alarm-con-wrap .alarm-c-w-right-con-wrap .alarm-details-con .chart-con {
    width: 100%;
    position: relative;
}

#alarmLineChart {
    height: 380px;
    position: relative;
    z-index: 2;
}

#referenceLineVal {
    color: #7a94ad;
    position: absolute;
    right: 10px;
    margin-top: -19px;
    text-align: right;
    height: 20px;
    line-height: 20px;
    font-size: 12px;
    display: none;
}

.alarm-con-wrap .alarm-c-w-right-con .alarm-details-text {
    position: absolute;
    bottom: 0;
    height: 164px;
    width: 100%;
    border-top: 1px solid #d9e2e8;
    background: #f9fbfc;
    padding: 10px 10px 0 10px;
    box-sizing: border-box;
}

.alarm-con-wrap .alarm-c-w-right-con .alarm-details-text .textarea {
    height: 102px;
    background: #fff;
}

.alarm-con-wrap .alarm-c-w-right-con .alarm-details-text .textarea-but {
    float: right;
    margin-top: 12px;
}

/*推送设置*/

.v6s-alarmwrap .alarmPushSet .alarmPushSet-title {
    position: relative;
    height: 50px;
}

.v6s-alarmwrap .alarmPushSet .alarmPushSet-title .alarm-push-tab {
    display: block;
    height: 50px;
    text-align: center;
    line-height: 50px;
    padding-top: 11px;
}

.v6s-alarmwrap .alarmPushSet .alarmPushSet-title .alarmPush-set-but {
    position: absolute;
    left: 0px;
    top: 11px;
}

.v6s-alarmwrap .alarmPushSet .alarmPushSet-con {
    position: absolute;
    top: 50px;
    bottom: 10px;
    left: 0;
    right: 0;
    border: 1px solid #d9e2e9;
    box-sizing: border-box;
}

.v6s-alarmwrap .alarmPushSet .alarmPushSet-con .pushSchemeModel .nodata {
    position: absolute;
    width: 100%;
    height: 100%;
}

.v6s-alarmwrap .alarmPushSet .alarmPushSet-con .pushSchemeModel .nodata > div {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -70%);
}

.v6s-alarmwrap .alarmPushSet .alarmPushSet-con .pushSchemeModel .new-push-but {
    float: left;
    position: absolute;
    z-index: 3;
    top: 10px;
    left: 10px;
}

.v6s-alarmwrap .alarmPushSet .alarmPushSet-con .pushSchemeModel .pushSchemeModel-wrap {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 50px;
    overflow: auto;
    padding: 0 10px;
    box-sizing: border-box;
}

.v6s-alarmwrap .pushModelTemp {
    border: 1px solid #d9e2e8;
    box-sizing: border-box;
    width: 100%;
    overflow: hidden;
    margin-bottom: 15px;
    display: flex;
    align-items: stretch;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left {
    width: 35%;
    background: #f4f4f4;
    box-sizing: border-box;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left .temp-left-title {
    height: 52px;
    border-bottom: 1px solid #d9d9d9;
    box-sizing: border-box;
    position: relative;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left .temp-left-title > em {
    font-size: 16px;
    text-align: center;
    line-height: 52px;
    display: block;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left .but-wrap {
    position: absolute;
    right: 10px;
    top: 15px;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left .but-wrap > div {
    display: inline-block;
    margin-left: 10px;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left > ul {
    padding: 0 10px;
    box-sizing: border-box;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left > ul > li {
    min-height: 42px;
    border-bottom: 1px dashed #d9d9d9;
    padding: 10px 0;
    box-sizing: border-box;
    padding-left: 10px;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left > ul > li > b {
    float: left;
    font-weight: bold;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left > ul > li:last-child {
    border-bottom: none;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left > ul > li.push-way em {
    margin: 0 3px;
    width: 20px;
    height: 20px;
    display: inline-block;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left > ul > li.push-user em {
    margin-right: 10px;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left > ul > li.push-way em.wx {
    background: url(/img/alarm/xx.png) no-repeat center 4px;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left > ul > li.push-way em.mail {
    background: url(/img/alarm/mail.png) no-repeat center center;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-left > ul > li.push-way em.iphone {
    background: url(/img/alarm/iphone.png) no-repeat center center;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-right {
    width: 64.999%;
    border-left: 1px solid #d9d9d9;
    box-sizing: border-box;
    position: relative;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-right .temp-right-title {
    height: 52px;
    border-bottom: 1px solid #d9d9d9;
    box-sizing: border-box;
    text-align: center;
    line-height: 52px;
    font-size: 16px;
    background: #f9fbfc;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-right .temp-right-title > span {
    float: right;
    color: #637e99;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-right .temp-right-title > span:hover {
    color: #425f7c;
}

.v6s-alarmwrap .pushModelTemp .pushModelTemp-right > ul > li {
    height: 40px;
    line-height: 40px;
    margin: 0 20px;
    float: left;
}

.v6s-alarmwrap .pushModelTemp .nodata {
    position: absolute;
    width: 100%;
    height: 100%;
}

.v6s-alarmwrap .pushModelTemp .nodata > div {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -70%);
}

/*报警类型模式*/
.v6s-alarmwrap .alarmTypeModel {
    width: 100%;
    height: 100%;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left {
    height: 100%;
    width: 64.9999%;
    border-right: 1px solid #d9e2e8;
    float: left;
    box-sizing: border-box;
    overflow: auto;
    padding: 10px;
    position: relative;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp-wrap {
    position: relative;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .nodata {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-right {
    height: 100%;
    width: 35%;
    float: left;
    box-sizing: border-box;
    padding: 10px;
    overflow: auto;
    position: relative;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-right .nodata {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-right .pushModelTemp-left {
    width: 100%;
}

.v6s-alarmwrap .alarmTypeModel .newpushBut {
    margin: 0 0 11px 0;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp {
    border: 1px solid #d9e2e8;
    box-sizing: border-box;
    display: flex;
    min-height: 42px;
    margin-bottom: 10px;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-left {
    width: 193px;
    background: #f9fbfc;
    display: box;
    display: -webkit-box;
    display: -moz-box;
    -webkit-box-pack: center;
    -moz-box-pack: center;
    -webkit-box-align: center;
    -moz-box-align: center;
    font-size: 16px;
    box-sizing: border-box;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-right {
    border-left: 1px solid #d9d9d9;
    box-sizing: border-box;
    width: calc(100% - 193px);
    width: -webkit-calc(100% - 193px);
    width: -moz-calc(100% - 193px);
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-right .ATM-temp-line {
    height: 41px;
    line-height: 41px;
    border-bottom: 1px dashed #d9d9d9;
    width: 100%;
    position: relative;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-right .ATM-temp-line:hover {
    background: #f8f8f8;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-right .ATM-temp-line:last-child {
    border-bottom: none;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-right .ATM-temp-line .ATM-temp-name {
    width: 202px;
    float: left;
    text-indent: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 38px;
    box-sizing: border-box;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-right .ATM-temp-line .ATM-temp-but {
    float: left;
    margin-right: 5px;
    display: none;
    position: absolute;
    left: 173px;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-right .ATM-temp-line:hover .ATM-temp-but {
    display: block;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-right .ATM-temp-line .ulwrap {
    height: 41px;
    display: block;
    overflow: hidden;
}

.v6s-alarmwrap .alarmTypeModel .alarmTypeModel-left .alarmTypeModel-temp .ATM-temp-right .ATM-temp-line .ulwrap li {
    background: #02a9d1;
    border: 1px solid #0095b9;
    box-sizing: border-box;
    color: #fff;
    height: 28px;
    line-height: 28px;
    display: inline-block;
    padding: 0 10px;
    border-radius: 3px;
    float: left;
    cursor: default;
    margin: 6px;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop {
    width: 290px;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
    position: absolute;
    top: -1px;
    left: -120px;
    background: #fff;
    border: 1px solid #dedede;
    z-index: 6;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-title {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #dedede;
    background: #f0f3f6;
    font-size: 16px;
    padding-left: 13px;
    box-sizing: border-box;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-title > span {
    font-family: 'perficon';
    width: 53px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    border-left: 1px solid #e7e9ea;
    color: #a0a1a2;
    display: block;
    position: absolute;
    right: 0;
    top: 8px;
    font-size: 22px;
    cursor: pointer;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-nodata {
    text-align: center;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-nodata > div {
    display: inline-block;
    margin-top: 20px;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-con {
    max-height: 251px;
    overflow: auto;
    margin-bottom: 20px;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-con ul {
    border: 1px solid #e7e9ea;
    width: 250px;
    margin: 0 auto;
    box-sizing: border-box;
    margin-top: 10px;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-con ul li {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #e7e9ea;
    position: relative;
    color: #5f7a94;
    padding-left: 20px;
    box-sizing: border-box;
    cursor: pointer;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-con ul li:last-child {
    border-bottom: none;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-con ul li:hover {
    background: #f8f8f8;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-con ul li b {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 40px;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-con ul li em {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    color: #fff;
    font-family: 'perficon';
    background: #e6e6e6;
    position: absolute;
    right: 10px;
    top: 14px;
    line-height: 16px;
    text-align: center;
    font-size: 12px;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-con ul li.lipitch em {
    background: #7a94ad;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .error-tip {
    font-size: 12px;
    height: 25px;
    line-height: 25px;
    display: none;
    color: #fd9054;
    white-space: nowrap;
    position: absolute;
    position: absolute;
    bottom: 46px;
    left: 73px;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .error-tip i {
    width: 14px;
    height: 14px;
    background: #f28b37;
    margin: 5px 5px 5px 0;
    float: left;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 14px;
    border-radius: 50%;
    color: #fff;
    font-size: 14px;
    font-size: 14px;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-but {
    height: 50px;
    text-align: center;
    line-height: 50px;
}

.v6s-alarmwrap .alarmTypeModel .editPushPop .pop-but > div {
    display: inline-block;
}

/*新建推送方案*/
.v6s-alarmwrap .alarmPushSet .addPushSet-title {
    position: relative;
    height: 50px;
    border-bottom: 1px solid #d9e2e8;
    box-sizing: border-box;
}

.v6s-alarmwrap .alarmPushSet .addPush-set-but {
    position: absolute;
    left: 0px;
    top: 11px;
}

.v6s-alarmwrap .alarmPushSet .addPush-save-but {
    position: absolute;
    right: 0px;
    top: 11px;
    float: left;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-title h1 {
    text-align: center;
    line-height: 50px;
    font-size: 16px;
}

.v6s-alarmwrap .alarmPushSet .addPushModel {
    height: 100%;
    width: 100%;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con {
    position: absolute;
    top: 51px;
    bottom: 0;
    height: -webkit-calc(100% - 51px);
    height: -moz-calc(100% - 51px);
    width: 100%;
    overflow: auto;
    padding-bottom: 20px;
    box-sizing: border-box;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-wrap {
    width: 690px;
    margin: 0 auto;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-temp {
    border: 1px solid #d9e2e8;
    margin-top: 16px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-temp .title {
    height: 51px;
    border-bottom: 1px solid #d9d9d9;
    box-sizing: border-box;
    background: #f9fbfc;
    padding-left: 15px;
    font-size: 16px;
    line-height: 51px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-name .con {
    height: 58px;
    overflow: hidden;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-name .inp {
    width: 400px;
    margin-top: 15px;
    margin-left: 20px;
}

#divCaseName .reminder-tip, #divCaseName .error-tip {
    left: 411px;
    bottom: 0px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .select-wrap {
    border-bottom: 1px solid #d9d9d9;
    min-height: 57px;
    overflow: hidden;
    position: relative;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .select-wrap .error-tip {
    font-size: 12px;
    height: 25px;
    line-height: 25px;
    display: none;
    color: #fd9054;
    white-space: nowrap;
    position: absolute;
    bottom: 16px;
    left: 16px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .select-wrap .error-tip i {
    width: 14px;
    height: 14px;
    background: #f28b37;
    margin: 5px 5px 5px 0;
    float: left;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 14px;
    border-radius: 50%;
    color: #fff;
    font-size: 14px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .select-wrap ul li {
    height: 27px;
    line-height: 26px;
    padding: 0 10px;
    box-sizing: border-box;
    border-radius: 12px;
    border: 1px solid #e5e5e5;
    background: #f7f7f7;
    float: left;
    margin: 15px 10px 5px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 28px;
    position: relative;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .select-wrap ul li em {
    color: #7a94ad;
    font-family: 'perficon';
    font-size: 16px;
    margin-left: 10px;
    cursor: pointer;
    position: absolute;
    right: 5px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .tree-wrap {
    padding: 0 10px;
    box-sizing: border-box;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .tree-title {
    height: 40px;
    line-height: 40px;
    border-top: 1px dashed #d9d9d9;
    box-sizing: border-box;
    padding-left: 12px;
    cursor: pointer;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .tree-con {
    display: none;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .tree-title > b {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .tree-title > .arrows {
    width: 24px;
    height: 24px;
    float: left;
    line-height: 24px;
    margin: 7px 6px 0 0;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .tree-con .tree-title {
    padding-left: 37px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .tree-con .tree-con .tree-title {
    padding-left: 62px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-user .tree-wrap > .tree-temp > .tree-title {
    border-top: none;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-time {
    position: relative;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-time .con {
    height: 58px;
    line-height: 58px;
    margin-left: 20px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-time .error-tip {
    font-size: 12px;
    height: 25px;
    line-height: 25px;
    display: none;
    color: #fd9054;
    white-space: nowrap;
    position: absolute;
    bottom: 16px;
    left: 436px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-time .error-tip i {
    width: 14px;
    height: 14px;
    background: #f28b37;
    margin: 5px 5px 5px 0;
    float: left;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 14px;
    border-radius: 50%;
    color: #fff;
    font-size: 14px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-time .input {
    width: 50px;
    margin: 0 6px;
    display: inline-block;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-way .con {
    overflow: hidden;
    height: 58px;
    position: relative;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-way .error-tip {
    font-size: 12px;
    height: 25px;
    line-height: 25px;
    display: none;
    color: #fd9054;
    white-space: nowrap;
    position: absolute;
    bottom: 16px;
    left: 335px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-way .error-tip i {
    width: 14px;
    height: 14px;
    background: #f28b37;
    margin: 5px 5px 5px 0;
    float: left;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 14px;
    border-radius: 50%;
    color: #fff;
    font-size: 14px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-way ul li {
    float: left;
    height: 60px;
    margin-left: 30px;
    line-height: 60px;
    vertical-align: bottom;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-way ul li > div {
    float: left;
    margin-top: 4px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-way ul li > em {
    float: left;
    text-indent: 25px;
    margin-left: 8px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-way ul li.app em {
    background: url(/img/alarm/iphone.png) no-repeat left center;
    text-indent: 18px;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-way ul li.mail em {
    background: url(/img/alarm/mail.png) no-repeat left center;
}

.v6s-alarmwrap .alarmPushSet .addPushSet-con .addPush-way ul li.note em {
    background: url(/img/alarm/xx.png) no-repeat left center;
}

/*更改推送内容float*/
.v6s-alarmwrap .editPushContent-float {
    position: absolute;
    top: -66px;
    right: -580px;
    width: 556px;
    bottom: -10px;
    z-index: 3;
    transition: 0.5s;
    -webkit-transition: 0.5s;
    -moz-transition: 0.5s;
    -o-transition: 0.5s;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap {
    border: 1px solid #e5e5e5;
    width: 100%;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-title {
    height: 44px;
    border-top: 1px dashed #d9d9d9;
    line-height: 44px;
    cursor: pointer;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .alarm-type-temp:first-child .temp-title {
    border-top: none;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-title .arrows {
    float: left;
    width: 24px;
    height: 24px;
    line-height: 24px;
    margin: 9px 0 0 10px;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-title .checkbox {
    float: left;
    width: 16px;
    height: 16px;
    line-height: 16px;
    margin-top: 15px;
    margin: 15px 5px 0 7px;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-title b {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-con li {
    height: 44px;
    border-top: 1px dashed #d9d9d9;
    line-height: 44px;
    position: relative;
    padding-left: 75px;
    box-sizing: border-box;
    cursor: pointer;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-con li > span {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    color: #fff;
    font-family: 'perficon';
    background: #e6e6e6;
    position: absolute;
    right: 20px;
    top: 14px;
    line-height: 16px;
    text-align: center;
    font-size: 12px;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-con li.pitch > span {
    background: #7a94ad;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-con li > b {
    float: left;
    margin-right: 30px;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-con li > div {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    color: #6d6d6d;
    font-size: 12px;
    padding-right: 44px;
    margin-left: 15px;
    cursor: default;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .temp-con li > div .co {
    color: #fd9054;
}

.v6s-alarmwrap .editPushContent-float .alarm-type-wrap .alarm-type-temp:last-child .temp-con li:last-child {
    border-bottom: none;
}

.highcharts-tooltip > span > span {
    box-sizing: border-box;
    overflow: hidden;
    float: left;
    padding-right: 20px;
    width: 100%;
}

.highcharts-tooltip > span > span > em {
    width: 8px;
    height: 8px;
    border-radius: 100%;
    float: left;
    display: inline-block;
    margin-top: 10px;
    margin-left: 0px;
    margin-right: 7px;
}

.highcharts-tooltip > span > span > b {
    display: inline-block;

}


