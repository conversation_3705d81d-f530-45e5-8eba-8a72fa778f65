html {
    height: 100%;
}

body {
    height: 100%;
    overflow: hidden;
}

.report_level1 {
    width: 100%;
    overflow: hidden;
    height: 100%;
    display: flex;
    box-sizing: border-box;
    background: #f0f3f6;
}

.report_level2 {
    width: 100%;
    overflow: hidden;
    height: 100%;
    display: flex;
    box-sizing: border-box;
    border: 1px solid #d9e2e8;
    margin: 20px 10px 0px 10px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(13, 4, 8, 0.1);
}

/* 距离底部高度10px */
#report_printing {
    width: 100%;
    overflow: hidden;
    height: calc(100% - 10px);
    padding: 20px;
    display: flex;
    box-sizing: border-box;
    position: relative;
}

.reportl {
    width: 368px;
    border: 1px solid #d9e2e8;
    margin-right: 10px;
    box-sizing: border-box;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.reportl header {
    height: 49px;
    border-bottom: 1px solid #d9e2e8;
    padding: 0 20px;
    line-height: 49px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.reportl header p:nth-of-type(2) {
    width: 90px;
    height: 30px;
    background: #7a94ad;
    color: #fff;
    text-align: center;
    line-height: 30px;
    border-radius: 4px;
    cursor: pointer;
}

.reportl article {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: overlay;
    overflow: auto;
}

.reportl article p:nth-of-type(1) {
    min-height: 42px;
    line-height: 42px;
    padding-left: 20px;
    font-size: 14px;
}

/* 左右两边增加内边距 */
.reportl article aside {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.xiaojiantou {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-color: #7b92a9;
    border-width: 0 0 2px 2px;
    border-style: solid;
    transform: rotate(-140deg) skewX(-10deg);
}

.xiaojiantou.normal {
    border-color: #fff;
}

.xialajt {
    position: absolute;
    right: 4px;
    top: 14px;
    display: inline-block;
    width: 6px;
    height: 6px;
    border-color: #7a94ad;
    border-width: 0 0 2px 2px;
    border-style: solid;
    transform: rotate(-41deg) skewX(13deg);
}

.reportl article .articdiv1 {
    width: 100%;
    border-bottom: 1px solid #d9e2e8;
}

.reportl article .articdiv1 .articdiv1a1 {
    cursor: pointer;
    display: flex;
    flex-direction: row;
    height: 52px;
    border-top: 1px solid #d9e2e8;
    align-items: center;
    color: #c0c0c0;
    padding-left: 20px;
    position: relative;
    font-size: 14px;
}

.reportl article .articdiv1 .articdiv1a2 {
    border-bottom: 1px solid #d9e2e8;
}

.reportl article .articdiv1 .articdiv1a1:hover {
    background: #f8f8f8;
}

.reportl article .articdiv1 .articdiv1a1 i:nth-of-type(3) {
    position: absolute;
    right: 20px;
    top: 22px;
}

.reportlul li:hover {
    background: #f8f8f8;
}

.reportlul li {
    height: 56px;
    border: 1px solid #cacaca;
    border-radius: 4px;
    display: flex;
    align-items: center;
    margin: 0 20px 15px 20px;
    padding-left: 15px;
    cursor: pointer;
    position: relative;
}

.reportlul li b:nth-of-type(1) {
    display: inline-block;
    width: 27px;
    height: 27px;
    border-radius: 50%;
    background: #637e99;
    color: #fff;
    line-height: 27px;
    text-align: center;
    font-size: 14px;
    margin-right: 16px;
}

/* 超出两行增加省略号 */
.reportlul li b:nth-of-type(2) {
    color: #637e99;
    font-size: 14px;
    max-width: 228px;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    display: -webkit-box;
}

.reportlul li .guanbi:nth-of-type(1) {
    display: inline-block;
    width: 16px;
    height: 16px;
    color: #999;
    font-size: 13px;
    text-align: center;
    line-height: 14px;
    border-radius: 50%;
    position: absolute;
    right: 15px;
    top: 20px;
    cursor: pointer;
    display: none;
    background: url('../../img/TenantReport/close_normal_16x16.png');
}

.reportlul li .guanbi:nth-of-type(1):hover {
    background: url('../../img/TenantReport/close_hover_16x16.png');
}

.articdiv1ai1 {
    display: inline-block;
    text-align: center;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #abbbcb;
    color: #fff;
    font-size: 12px;
    line-height: 16px;
    margin-right: 8px;
    margin-top: 3px;
}

.reportl article .articdiv1 .articdiv1a1:nth-of-type(1) i:nth-of-type(2) {
    color: #5d5d5d;
}

.reportr {
    flex: 1;
    box-sizing: border-box;
    width: calc(100% - 378px);
    height: 100%;
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid #d9e2e8;
}

.reportr header {
    display: flex;
    position: relative;
    margin-left: -1px;
}

/* 顶部栏增加边框 */
.reportr header div:nth-of-type(1) {
    width: 100%;
    height: 32px;
    flex: 1;
    overflow: hidden;
    border: 1px solid #d9e2e8;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-right: 2px;
    border-bottom: none;
}

.reportr header div:nth-of-type(2) {
    width: 29px;
    height: 29px;
    border-radius: 3px;
    border: 1px solid #d9e2e8;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.reportr header div:nth-of-type(2):hover {
    background: #f8f8f8;
}

.reportr header div:nth-of-type(2) i {
    display: inline-block;
    width: 11px;
    height: 7px;
    background: url('../../img/TenantReport/xiala.png') no-repeat center;
}

.reportr article {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #d9e2e8;
    height: 100px;
    border-bottom: none;
}

.reportr article img:nth-of-type(1) {
    width: 273px;
}

.reportrcont {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(100% - 20px);
    overflow-y: scroll;
    padding: 20px 20px 0 20px;
}

.baobiaoshuju {
    flex: 1;
    width: 100%;
    position: relative;
    height: 100%;
}

.baobiaoshujunav {
    width: 100%;
    height: 30px;
    position: relative;
    overflow: hidden;
}

.baobiaoshujunav>ul {
    position: absolute;
    left: 0px;
}

.baobiaoshuju ul {
    text-align: center;
    height: 40px;
    display: flex;
    min-width: 100%;
}

.baobiaoshuju ul>li {
    flex: 1;
    min-width: 150px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    border-bottom: 1px solid #dddddd;
    border-right: 1px solid #ddd;
}

.baobiaoshuju ul>li:last-child {
    border-right: none;
    flex: 1
}

.baobiaoshujunav ul {
    background: #8b959a;
    height: 30px;
}

.baobiaoshujunav ul>li {
    color: #fff;
    font-size: 14px;
    line-height: 30px;
}

.baobiaoshujucon {
    overflow: overlay;
    max-height: calc(100% - 30px);
}

.mobanxuanzhong {
    background: #02a9d1 !important;
    color: #fff !important;
}

.mobanxuanzhong i {
    color: #fff
}

.mobanxuanzhong i:nth-of-type(1) {
    background: #fff !important;
    color: #02a9d1 !important;
}

.mobanxuanzhong i:nth-of-type(2) {
    color: #fff !important;
}

.mobanxuanzhong i:nth-of-type(3) {
    color: #fff !important;
}

.mobanxuanzhong .xiaojiantou {
    color: #fff !important;
}

.shijiankongjian {
    width: 240px;
    height: 220px;
    border-radius: 8px;
    background: #fff;
    z-index: 9;
    position: absolute;
    left: 378px !important;
    top: 0px;
    box-shadow: 0px 0px 8px #d1d1d1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.shijiankongdiv2 {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.shijiankongdiv2 p {
    width: 80px !important;
    height: 30px !important;
    background: #7a94ad;
    color: #fff !important;
    line-height: 30px;
    display: flex;
    padding-left: 0px !important;
    justify-content: center;
    cursor: pointer;
}

.shijianxzriqi {
    padding: 0px 20px;
    display: inline-block;
    width: 326px;
}

.shijianxzriqi .xuanzhongTxt {
    display: inline-block;
    width: 276px;
    padding: 0 44px 0 10px;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.shijianxzriqi b {
    font-size: 14px;
    display: inline-block;
    width: 326px;
    height: 38px;
    border: 1px solid #cacaca;
    text-align: center;
    position: relative;
    line-height: 38px;
    margin: 14px 0px;
    box-sizing: border-box;
}

.shijianxzriqi strong {
    border-top: 1px solid #f4f6f8;
    display: inline-block;
    width: 326px;
}

.shijianxzriqi b i {
    display: inline-block;
    width: 16px;
    height: 16px;
    color: #999;
    font-size: 13px;
    text-align: center;
    line-height: 14px;
    border-radius: 50%;
    position: absolute;
    right: 15px;
    top: 11px;
    cursor: pointer;
    background: url('../../img/TenantReport/close_normal_16x16.png');
}

.shijianxzriqi b i:hover {
    background: url('../../img/TenantReport/close_hover_16x16.png');
}

.seachjianzhu {
    position: absolute;
    z-index: 999;
    left: 376px !important;
    top: 0px;
    box-shadow: 0px 0px 2px #cacaca;
    width: 290px;
    display: flex;
    flex-direction: column;
}

.seachjianzhu .searchdiv {
    height: 42px !important;
    border: 1px solid #cacaca;
    background: #f5f8f8;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px 9px !important;
}

.shengchengbaobiao {
    height: 70px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 0px;
}

.shengchengbaobiao i:nth-of-type(1) {
    display: inline-block;
    width: 200px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
    background: #7a94ad;
    border-radius: 4px;
    font-size: 14px;
}

.daixialasearch {
    position: absolute;
    z-index: 999;
    left: 378px;
    width: 290px;
    height: 530px;
    box-shadow: 0px 0px 2px #cacaca;
}

.seachyibiao {
    position: absolute;
    z-index: 999;
    top: 0px;
    left: 378px;
    width: 290px;
    height: 530px;
    box-shadow: 0px 0px 2px #cacaca;
}

.seachzhilu {
    position: absolute;
    z-index: 999;
    bottom: 0px;
    left: 378px;
    width: 290px;
}

.scale {
    font-size: 12px;
    transform: scale(0.68);
    display: inline-block;
    white-space: nowrap;
    position: relative;
    left: -1px;
    top: -1px;
}

.guanbi:hover {
    background: red;
}

.searchdiv div:first-child {
    width: 100%;
}

.searchdiv [p-type="searchbox-promptly"]:before {
    border-radius: 0px;
}

.seachjianzhu [p-type="searchbox-promptly"]:before {
    border-radius: 0px;
}

.seachjianzhu [p-type="tree-combobox"] {
    height: 530px;
}

.seachjianzhu [p-type="tree-combobox"] .float-con {
    max-height: 428px;
}

.daixialasearch .xiala {
    padding: 10px;
    background: #f5f8f8;
    width: 270px;
    border: 1px solid #cacaca;
    border-bottom: none;
}

.daixialasearch .xialaliebiao [p-type="combobox-menu"] {
    max-width: 300px;
}

.xialasearch [p-type="tree-combobox"] {
    height: 478px;
}

.xuanzhewanchgeng i:nth-of-type(1) {
    background: #abbbcb;
}

.xuanzhewanchgeng i:nth-of-type(2) {
    color: #333
}

.xuanzhewanchgeng i:nth-of-type(3) {
    color: #637e99
}

.daixialasearch [p-type="searchbox-promptly"]:before {
    border-radius: 0px;
}

/* sheet页 */
.sheetPageBox {
    width: 100%;
    height: 28px;
    box-sizing: border-box;
    margin-bottom: 2px;
    display: flex;
    border: 1px solid #d9e2e8;
    border-top: none;
}

.sheetPageBox.show {
    display: block;
}

.optionBox {
    width: 50px;
    height: 26px;
    border: 1px solid #d9e2e8;
    float: left;
    line-height: 26px;
    background: #f8f8f8;
    margin-right: -1px;
    border-left: none;
}

.arrowLeft {
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 6px solid transparent;
    border-right: 6px solid #d9e2e8;
    margin-left: 2px;

}

.arrowLeft.show {
    border-right: 6px solid #7a94ad;
}

.arrowRight {
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-left: 6px solid #d9e2e8;
    border-bottom: 6px solid transparent;
    border-right: 6px solid transparent;
    margin-left: 14px;
}

.arrowRight.show {
    border-left: 6px solid #7a94ad;
}

.forbidClick {
    pointer-events: none;
}

.dianLeft {
    width: 34px;
    height: 26px;
    text-align: center;
    border: 1px solid #d9e2e8;
    float: left;
    background: #f8f8f8;
    display: none;
    margin-right: -1px;
}

.dianLeft.show {
    display: block;
}

.dianRight {
    width: 34px;
    height: 26px;
    text-align: center;
    border: 1px solid #d9e2e8;
    border-right: none;
    float: left;
    background: #f8f8f8;
    display: none;
}

.dianRight.show {
    display: block;
}

.sheetPageUl {
    width: 100%;
    height: 28px;
    overflow: hidden;
    display: flex;
    box-shadow: 0px 0.5px 2px rgba(217, 226, 232, 1) inset;
    background: #f8f8f8;
}

.sheetPageUl li {
    max-width: 122px;
    font-size: 14px;
    flex: 1;
    text-align: center;
    height: 26px;
    line-height: 26px;
    border: 1px solid #d9e2e8;
    float: left;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    padding-left: 14px;
    padding-right: 14px;
    background: #f8f8f8;
    margin-right: -1px;
}

.sheetPageUl li.show {
    border-top: 1px solid #fff;
    background: #ffffff;
}

.sheetPageUl li:hover {
    background: #ffffff;
}

/*右侧的tabcss*/

.reportrtab {
    display: flex;
    width: 100%;
    overflow: hidden;
}

.reportrtab li {
    flex: 1;
    min-width: 112px;
    max-width: 212px;
    background: #f7f7f7;
    position: relative;
    display: inline-block;
    height: 31px;
    padding: 0px 30px 0px 20px;
    margin-right: 2px;
    border: 1px solid #d9e2e8;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    line-height: 32px;
    cursor: pointer;
    font-size: 14px;
    color: #888888;
}

.reportrtab li:hover {
    background: #fff;
}

.reportrtab li i:nth-of-type(1) {
    animation: rodet 2s linear 0s infinite normal;
    position: absolute;
    left: 6px;
    display: inline-block;
    height: 32px;
    width: 14px;
    background: url('../../img/TenantReport/loading2.png') no-repeat center;
}

.reportrtab li i:nth-of-type(2) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    height: 100%;
    display: inline-block;
    margin-left: 6px;
}

.reportrtab li i:nth-of-type(3) {
    position: absolute;
    right: 8px;
    top: 9px;
    width: 16px;
    height: 16px;
    background: url('../../img/TenantReport/normal.png');
}

.reportrtab li:hover i:nth-of-type(3) {
    display: inline-block;
}

.reportrtab li i:nth-of-type(3):hover {
    background: url('../../img/TenantReport/hover.png');
}

.mobanxialakuang {
    position: absolute;
    right: 0px;
    top: 33px;
    border: 1px solid #d9d9d9;
    z-index: 9;
    background: #fff;
    max-height: 320px;
    overflow: overlay;
}

.mobanxialakuang li {
    padding: 0px 10px;
    cursor: pointer;
    height: 35px;
    line-height: 35px;
    font-size: 14px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.mobanxialakuang li.show {
    color: #02a9d1;
}

.mobanxialakuang li:last-child {
    border-bottom: none;
}

.mobanxialakuang li:hover {
    background: #f8f8f8;
}

.tabbeixuanzhong {
    background: #fff !important;
    border-bottom: 1px solid #fff !important;
    color: #333333 !important;
}

.shijianxzriqi strong b:hover {
    background: #f8f8f8;
}

.noborderbottom {
    border-bottom: none;
}