#template_manage {
	width: 100%;
	height: calc(100% - 40px);
	padding: 20px;
	display: flex;
	flex-direction: column;
}

.templateheader {
	height: 50px;
	width: calc(100% - 2px);
	border: 1px solid #d9e2e8;
	text-align: center;
	position: relative;
	line-height: 50px;
	font-size: 18px;
}

.returnreport {
	width: 78px;
	height: 28px;
	position: absolute;
	left: 22px;
	top: 11px;
	cursor: pointer;
	background: #fff;
	color: #7e93a9;
	font-size: 14px;
	line-height: 28px;
}

.scteplate {
	width: 90px;
	height: 30px;
	text-align: center;
	line-height: 30px;
	right: 22px;
	top: 11px;
	position: absolute;
	font-size: 14px;
	line-height: 30px;
}

.templatecon {
	flex: 1;
	border: 1px solid #d9e2e8;
	border-top: none;
	background: #f8fafb;
	padding: 30px 20px 0px;
	position: relative;
	display: flex;
	flex-direction: column;
	overflow: overlay;
}

.templaterongqi {
	width: 100%;
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
	margin: 0 auto;
	font-size: 18px;
}

.templatecon article {
	width: 340px;
	height: 168px;
	background: #fff;
	border: 1px solid #e9eef0;
	border-radius: 8px;
	position: relative;
	margin-bottom: 30px;
	margin-left: 26px;
	float: left;
}

.templatecond {
	display: flex;
	height: 130px;
}

.rihuozhou {
	width: 82px;
	padding-left: 30px;
	padding-top: 30px;
}

.rihuozhouxinxi {
	flex: 1;
	padding: 30px 10px 10px 0px;
	display: flex;
	flex-direction: column;

}

.rihuozhou p {
	width: 60px;
	height: 60px;
	background: #becfe0;
	color: #fff;
	border-radius: 50%;
	line-height: 60px;
	text-align: center;
}

.rihuozhouxinxi p {
	line-height: 30px;
}

.rihuozhouxinxi p:nth-of-type(1) {
	margin-bottom: 8px;
	color: #8fa0a9;
}

.bianjineirong {
	flex: 1;
	border: none;
	font-size: 14px;
	width: 96%;
	line-height: 18px;
}

textarea {
	padding: 0 !important;
}

.bianjineirong.error {
	border: 1px solid #ff7b7b;
}

.templzhiyuanshu:hover {
	box-shadow: 0px 0px 15px rgba(0, 86, 106, 0.08);
}

.templzhiyuanshu:hover .tempbianji {
	display: block;
}

.xianzhongtemp {
	background: #7a94ad !important;
}

.mingchengchuts,
.mingchengempty {
	color: red;
	font-size: 12px;
	width: 150px !important;
	display: inline-block;
	line-height: 37px;
}

.tempbianji {
	height: 36px;
	border-top: 1px solid #e9eef0;
	display: none;
}

.tempbianjip {
	display: flex;
	flex-direction: row-reverse;
}

.tempbianjibc {
	height: 36px;
	border-top: 1px solid #e9eef0;
	display: none;
}



.tempbianji .btn {
	position: absolute;
	right: 18px;
	top: 7px;
}


.tempbaocunp {
	display: flex;
	flex-direction: row-reverse;
	padding-right: 20px;
}

.tempbaocunp b {
	display: inline-block;
	width: 44px;
	height: 37px;
	cursor: pointer;
	margin-left: 14px;
}

.tempbaocunp .liangbaocun {
	background: url(/img/TenantReport/Save1.png) no-repeat center;
}

.tempbaocunp .disbaocun {
	background: url(/img/TenantReport/Save.png) no-repeat center;
}

.tempbianjip b {
	display: inline-block;
	width: 44px;
	height: 37px;
	cursor: pointer;
}

.tempbianjip b:nth-of-type(2) {
	background: url(/img/TenantReport/bianji.png) no-repeat center;
	margin-right: 20px;
}

.tempbianjip b:nth-of-type(1) {
	background: url(/img/TenantReport/shanchu.png) no-repeat center;
	margin-right: 20px;
}

.reportrfoot {
	height: 70px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: calc(100% - 2px);
	border-left: 1px solid #d9e2e8;
	border-right: 1px solid #d9e2e8;
	background: #fff;
	position: relative;
	z-index: 99;
}

.reportrfoot p {
	width: 200px;
	height: 30px;
	text-align: center;
	line-height: 30px;
	color: #fff;
	background: #7a94ad;
	font-size: 14px;
	border-radius: 4px;
	cursor: pointer;
}



.switch {
	width: 30px;
	height: 16px;
	position: relative;
	border: 1px solid #dfdfdf;
	background-color: #fdfdfd;
	box-shadow: #dfdfdf 0 0 0 0 inset;
	border-radius: 8px;
	background-clip: content-box;
	display: inline-block;
	-webkit-appearance: none;
	user-select: none;
	outline: none;
	margin-top: 10px;
	margin-right: 15px;
	padding-top: 2px;
}

.switch:before {
	content: '';
	width: 15px;
	height: 14px;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: 7px;
	background-color: #fff;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.switch:checked {
	border-color: #68c5b3;
	box-shadow: #68c5b3 0 0 0 16px inset;
	background-color: #68c5b3;
}

.switch:checked:before {
	left: 15px;
}

.switch.switch-anim {
	transition: border cubic-bezier(0, 0, 0, 1) 0.4s, box-shadow cubic-bezier(0, 0, 0, 1) 0.4s;
}

.switch.switch-anim:before {
	transition: left 0.3s;
}

.switch.switch-anim:checked {
	box-shadow: #68c5b3 0 0 0 16px inset;
	background-color: #68c5b3;
	transition: border ease 0.4s, box-shadow ease 0.4s, background-color ease 1.2s;
}

.switch.switch-anim:checked:before {
	transition: left 0.3s;
}