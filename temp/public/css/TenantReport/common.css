.cdisable {
	pointer-events: none;
	cursor: default !important;
	opacity: .3;
}

[p-type="time-chart"].calendar-vertical .time-lock {
	display: none;
}

/*删除弹窗*/
.shamchudamceng {
	width: 342px;
	height: 140px;
	background: #292e3e;
	opacity: 0.8;
	padding-top: 30px;
	border-radius: 8px;
	position: absolute;
	left: 0px;
	top: 0px;
}

.shamchudamceng div:nth-of-type(1) p:nth-of-type(1) {
	line-height: 30px;
	font-size: 18px;
	line-height: 30px;
	text-align: center;
	color: #fff;
}

.shamchudamceng div:nth-of-type(1) p:nth-of-type(2) {
	line-height: 12px;
	font-size: 12px;
	line-height: 30px;
	text-align: center;
	color: #fff;
}

.shamchudamceng div:nth-of-type(2) {
	height: 66px;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 14px;
}

.shamchudamceng div:nth-of-type(2) p {
	width: 80px;
	height: 30px;
	line-height: 30px;
	text-align: center;
	border-radius: 3px;
	cursor: pointer;
}

.shamchudamceng div:nth-of-type(2) p:nth-of-type(1) {
	color: #fff;
	background: #ff7b7b;
	margin-right: 20px;
}

.shamchudamceng div:nth-of-type(2) p:nth-of-type(2) {
	background: #fff;
	color: #90a5b9;
}

.cdisable {
	pointer-events: none;
	cursor: default !important;
	opacity: .3;
}

/*顶部提示信息css*/
#notice-con {
	position: fixed;
	top: 0px;
	width: 100%;
	z-index: 99;
}

.success {
	left: 50%;
	transform: translateX(-50%);
}

#notice2-con {
	position: fixed;
	top: 0px;
	width: 100%;
	z-index: 99;
}

.failure {
	left: 50%;
	transform: translateX(-50%);
}

/*上传弹窗css*/
.shangchuandc {
	position: fixed;
	left: 0px;
	top: 0px;
	width: 100vw;
	height: 100vh;
	background: rgba(79, 79, 79, 0.15);
	z-index: 9;
}

#yemianqiehuanloading {
	position: fixed;
	left: 0px;
	top: 0px;
	width: 100vw;
	height: 100vh;
	background: rgba(79, 79, 79, 0.15);
	z-index: 9;
}

#yemianqiehuanloading img {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.shangchuandccon {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 460px;
	height: 300px;
	border-radius: 8px;
	background: #fff;
	border-radius: 2px;
	box-shadow: 0px 0px 15px rgba(14, 19, 41, 0);
}

.shangchuandccon header {
	height: 50px;
	text-align: center;
	line-height: 50px;
	color: #fff;
	background: #6985a1;
}

.shangchuandccon section {
	height: 140px;
	padding-top: 20px;
	padding-left: 94px;
}

.shangchuandccon section div:nth-of-type(1) {
	height: 48px;
	display: flex;
	align-items: center;
	font-size: 14px;
}

.shangchuandccond1 label,
.shangchuandccond2 label {
	margin-right: 15px;
	display: inline-block;
	min-width: 65px;
}

.shangchuandccond1 p {
	width: 202px;
	height: 28px;
	line-height: 28px;
	color: #6985a1;
	cursor: pointer;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.shangchuandccond2 {
	height: 90px;
	padding-top: 25px;
	font-size: 14px;
}

.shangchuandccond2 [type="text"] {
	width: 202px;
	height: 28px;
	border: 1px solid #cacaca;
	border-radius: 3px;
	margin-left: -4px;
}

.shangchuandccon footer {
	height: 90px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
}

.shangchuandccon footer p {
	width: 80px;
	height: 28px;
	border-radius: 3px;
	line-height: 28px;
	text-align: center;
	cursor: pointer;
}

.shangchuandccon footer p:nth-of-type(1) {
	border: 1px solid #637e99;
	background: #7a94ad;
	color: #fff;
	margin-right: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.shangchuandccon footer p:nth-of-type(1):hover {
	background: #637e99;
}

.shangchuandccon footer p:nth-of-type(1) i {
	display: inline-block;
	width: 14px;
	height: 14px;
	background: url(/img/TenantReport/loading1.gif) no-repeat center;
	margin-right: 4px;
	display: none;
	animation: rodet 2s linear 0s infinite normal;
}

@keyframes rodet {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(720deg);
	}
}

.shangchuanzhong {
	pointer-events: none;
	cursor: default !important;
	opacity: .3;
}

.mingchengtishi,
.wangroutishi,
.nameEmpty {
	margin-top: 10px;
	color: red;
	font-size: 14px;
	width: 100%;
	text-indent: 64px;
}

.mingchengtishi {
	text-indent: 80px;
}

.wangroutishi,
.nameEmpty {
	text-indent: 80px;
	margin-top: 12px;
}

.shangchuanzhong i {
	display: inline-block !important;
}

.shangchuandccon footer p:nth-of-type(2) {
	border: 1px solid #cacaca;
	background: #fff;
	color: #7a94ad;
}

.shangchuandccon footer p:nth-of-type(2):hover {
	background: #f8f8f8;
}

.disabled {
	opacity: .3;
	pointer-events: none;
}