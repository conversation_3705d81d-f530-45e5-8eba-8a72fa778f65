.meter_detail{
    height:100%;
}
.meter_detail .header{
    height:50px;
    padding-left:20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}
.meter_detail .header .cancelBtn{
    float: left;
    margin-top:11px;
}
.meter_detail .header .title{
    width: 132px;
    margin: 0 auto;
    height: 50px;
    padding-top: 10px;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
}
.meter_detail .body{
    height:calc(100% - 50px);
    padding: 10px 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-top: 1px solid #d9e2e8;
    position: relative;
}
.meter_detail .body .time_wrapper{
    float: left;
}
.meter_detail .body .chart_wrapper{
    position: absolute;
    top: 50px;
    bottom: 0;
    left: 20px;
    right: 20px;
}
.chartTips{
    border:1px solid #cacaca;
    border-radius:5px;
    background: #fff;
}
.chartTips>span{
    display: inline-block;
    height:30px;
    line-height:30px;
    padding: 0 10px;
}
.chartTips>span:first-child{
    border-bottom:1px solid #cacaca;
}
