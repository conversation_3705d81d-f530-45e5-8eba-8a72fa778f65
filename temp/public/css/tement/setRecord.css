#setRecord {
	height: 100%;
}

.setRecord_con {
	width: 100%;
	height: 100%;
}

.setRecord_con>.top {
	width: 100%;
	height: 50px;
	position: relative;
	border-bottom: 1px solid #d9e2e8;
}

.setRecord_con>.top>.goBack {
	position: absolute;
	top: 10px;
	left: 20px;
}

.setRecord_con>.top>.title {
	width: 600px;
	position: absolute;
	left: 50%;
	top: 10px;
	margin-left: -300px;
	text-align: center;
	height: 40px;
	line-height: 40px;
}

.setRecord_con>.list {
	height: calc(100% - 20px);
	padding: 10px 20px 0 20px;
}

.setRecord_con>.list>.top {
	display: flex;
	flex-flow: row nowrap;
	justify-content: space-between;
	align-items: center;
}

.setRecord_con>.list>.top>.left {
	display: flex;
	flex-flow: row nowrap;
	justify-content: center;
}

.setRecord_con>.list>.top>.left>section {
	margin-right: 20px;
}

.setRecord_con>.list>.top>.left>section:nth-child(1) {
	width: 220px;
}

.setRecord_con>.list>.top>.left>section:nth-child(2) {
	width: 220px;
}

.setRecord_con>.list>.grid {
	margin-top: 10px;
	height: calc(100% - 80px);
}


/*  */
.setRecord_con .table {
	width: 100%;
	border: 1px solid #d9e2e8;
	width: calc(100% - 1px);
}

.setRecord_con .table>.title {
	display: flex;
	flex-flow: row nowrap;
	position: absolute;
	width: 100%;
	z-index: 1;
}

.setRecord_con .table>.title div {
	height: 30px;
	line-height: 30px;
	background: #8C959A;
	text-align: center;
	border-right: 1px solid #8C959A;
	border-bottom: 1px solid #d9e2e8;
	color: #fff;
	width: 16%;
}

.setRecord_con .table>.content {
	max-height: calc(100% - 200px);
	overflow-y: auto;
	margin-top: 30px;
}

.setRecord_con .table>.content section div {
	min-height: 30px;
	border-right: 1px solid #d9e2e8;
	border-bottom: 1px solid #d9e2e8;
}

.setRecord_con .table>.content section:last-child>div {
	border-bottom: none;
}

.setRecord_con .table>section {
	display: flex;
	flex-flow: row nowrap;
}

.setRecord_con .table>section:nth-child(odd) {
	background: #f7f9fb;
}

.setRecord_con .table>.content section {
	display: flex;
	flex-flow: row nowrap;
	flex: 1;
}

.setRecord_con .table>.content section div {
	position: relative;
	width: 20%;
}

.setRecord_con .table>.content section div>span {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.setRecord_con .table>.content section .time>span {
	display: inline-block;
	min-width: 160px;
	text-align: center;
}

.setRecord_con .table>.content section div:last-child>div {
	display: block;
	margin-top: 4px;
	border-bottom: 1px solid #d9e2e8;
	height: 32px;
	line-height: 32px;
	width: 100%;
	text-align: center;
}

.setRecord_con .table>.content section div:last-child>div:last-child {
	border-bottom: none;
}

.setRecord_con .page {
	display: flex;
	flex-flow: row nowrap;
	margin: 15px;
	float: right;
}

.setRecord_con .page section {
	display: flex;
	flex-flow: row nowrap;
	align-items: center;
	margin-left: 10px;
}

.setRecord_con .page section>i {
	margin-right: 10px;
}

#sr_page_text {
	width: 50px;
	margin: 0 10px 0 10px;
}

.setRecord_con .tableBox {
	position: relative;
	height: calc(100% - 160px);
	overflow-y: auto;
	overflow-x: hidden;
	margin: 7px;
	border-bottom: 1px solid #d9e2e8;
}

.setRecord_con .tableBox thead {
	position: fixed;
	top: 0;
}

.setRecord_con .tableBox tbody {
	margin-top: 30px;
}

.setRecord_con .table>.content section div:last-child>div {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
	height: 100%;
	width: 250px;
	cursor: pointer;
	padding: 0 20px;
}