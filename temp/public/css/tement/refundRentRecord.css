/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

.r_r_r_box div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.r_r_r_box {
    width: 100%;
    height: 100%;
}

.r_r_r_box .r_r_r_header {
    height: 50px;
    border-bottom: 1px solid #D9E2E8;
    padding: 10px 20px 9px;
    position: relative;
}

.r_r_r_box .r_r_r_header .r_r_r_go_back {
    position: absolute;
}

.r_r_r_box .r_r_r_header h1 {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    color: #333;
    text-align: center;
}

.r_r_r_box .r_r_r_gird_box {
    height: 100%;
    max-height: calc(100% - 50px);
    padding: 20px;
    padding-top: 0;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird_operation {
    padding: 10px 0;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird_operation>div>div {
    height: 30px;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird_operation .r_r_r_gird_operation_left {
    float: left;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird_operation .r_r_r_gird_operation_right {
    float: right;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird_operation .r_r_r_gird_operation_left>div {
    float: left;
    margin-right: 10px;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird_operation .r_r_r_gird_operation_left .energy_type_box {
    width: 222px;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird_operation .r_r_r_gird_operation_left .check_activate_time {
    line-height: 36px;
    margin-left: 20px;
}


.r_r_r_box .r_r_r_gird_box .r_r_r_gird {
    width: 100%;
    height: calc(100% - 50px);
    font-size: 14px;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_tit {
    display: flex;
    border: 1px solid #CCCCCC;
    border-bottom: none;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_tit>div {
    flex: 1;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    background-color: #8C959A;
    border-right: 1px solid #8C959A;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_tit>div:last-child {
    border-right: none;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body {
    height: calc(100% - 30px);
    overflow: auto;
    border: 1px solid #CCCCCC;
    border-top: none;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item {
    display: flex;
    text-align: center;
    color: #333333;
    border-top: 1px solid #CCCCCC;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item>div,
.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item>div>div {
    position: relative;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item>div>span,
.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item>div>div>span {
    display: block;
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item .r_r_r_gird_item_1 .r_r_r_gird_item_2 {
    border-bottom: 1px solid #CCCCCC;
    height: 36px;
    line-height: 36px;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item .r_r_r_gird_item_1 .r_r_r_gird_item_2:last-child,
.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item .r_r_r_gird_item_1:last-child {
    border-bottom: none;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item:first-child {
    border-top: none;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item>div {
    flex: 1;
    border-right: 1px solid #CCCCCC;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item>div:last-child {
    border-right: none;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item:nth-child(odd) {
    background-color: #FFFFFF;
}

.r_r_r_box .r_r_r_gird_box .r_r_r_gird .r_r_r_gird_body .r_r_r_gird_item:nth-child(even) {
    background-color: #f5f7fa;
}