body {
    width: 100%;
    height: 100%;
    position: relative;
    margin: 0;
    font-size: 14px;
    font-family: <PERSON><PERSON>, "Microsoft Yahei", sans-serif;
    color: #333
}

.pageBreak {
    page-break-before: always;
}

.pdfReport {
    height: 100%;
}

.pdfReport_head {
    height: 50px;
    padding: 0 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-bottom: 1px solid #d9e2e8;
}

.pdfReport .pdfReport_head .gobackBtn {
    float: left;
    margin-top: 11px;
}

.pdfReport .pdfReport_head .downloadGroup {
    float: right;
    margin-top: 11px;
}

.pdfReport .pdfReport_head .title {
    width: 300px;
    margin: 0 auto;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
}

.pdfReport .pdfReport_body {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    height: calc(100% - 50px);
}

.func_wrapper {
    height: 50px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50px;
    flex: 0 0 50px;
    margin-bottom: 10px;
    padding-top: 10px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.func_wrapper>div {
    float: left;
    margin-left: 10px;
}

.func_wrapper>div:first-child {
    margin-left: 20px;
}

.scroll_wrapper {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: auto;
}

.report_wrapper {
    width: 80%;
    margin: 0 auto;
    border: 1px solid #cacaca;
    min-height: 99%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border-radius: 3px;
    margin-bottom: 3px;
}

.report_wrapper .noReport {
    display: none;
    width: 150px;
    height: 150px;
    line-height: 330px;
    background: url("/img/tement/noReport.png") no-repeat center center;
    color: #6d6d6d;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -75px;
    margin-top: -75px;
    z-index: 0;
}

/*能耗费用报表 头部  */
.temp_head {
    height: 162px;
    background: #e3e7ee;
    padding: 40px 60px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.temp_head_left {
    float: left;
}

.temp_head_left>h2 {
    font-size: 36px;
    font-weight: 900;
    color: #575f73;
    position: relative;
    top: -6px;
}

.temp_head_left>h2>em>u {
    text-decoration: none;
    padding-bottom: 10px;
    border-bottom: 4px solid currentColor;
}

.temp_head_left>span {
    display: inline-block;
    margin-top: 20px;
    font-size: 14px;
    color: #6a7183;
}

.temp_head_right {
    float: right;
    font-size: 18px;
    font-weight: 600;
    color: #6b7284;
}

/*能耗费用报表 内容部分  */
.temp_body {
    padding: 40px 60px 10px 60px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

/*内容部分 头部  */
.temp_body_title {
    height: 70px;
    font-weight: bold;
    position: relative;
}

.temp_body_title_left {
    float: left;
    color: #333333;
    font-size: 14px;
    position: absolute;
    left: 30px;
    bottom: 0;
}

.temp_body_title_left h2 {
    margin: 5px 0;
}

.temp_body_title_right {
    float: right;
    position: absolute;
    right: 0;
    bottom: 0;
}

.temp_body_title_right2 {
    float: right;
    position: absolute;
    right: 178px;
    bottom: 0;
}

.temp_body_title_right h2 {
    text-align: right;
    margin-bottom: 10px;
    font-size: 14px;
    color: #333333;
}

.temp_body_title_right>span {
    color: #637E99;
    font-size: 18px;
}

.temp_body_title_right>span>em {
    font-size: 28px;
    margin: 0 5px;
    font-weight: bold;
}

.temp_body_title_right:first-child {
    margin-left: 34px;
}

.temp_body_title_right:first-child>span>em {
    margin-right: 0;
}

/*内容部分表格  */
.temp_body_grid {
    border: 1px solid #dddddd;
    margin-top: 30px;
}

.temp_body_grid_head {
    background: #F9FAFC;
    border-bottom: none;
}

.temp_body_grid_head .title {
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    border-bottom: 1px solid #dddddd;
    color: #333333;
}

.temp_body_grid_head .title_item {
    font-size: 14px;
    font-weight: bold;
    height: 30px;
    border-bottom: 1px solid #dddddd;
    display: table;
    width: 100%;
}

.temp_body_grid_head .title_item>div,
.temp_body_grid_body_list>div {
    display: table-cell;
    vertical-align: middle;
    width: 25%;
    text-align: center;
    border-right: 1px solid #dddddd;
}

.temp_body_grid_head .title_item>div:last-child,
.temp_body_grid_body_list>div:last-child {
    border-right: none;
}

.temp_body_grid_body {
    width: 100%;
    font-size: 12px;
}

.temp_body_grid_body_list {
    display: table;
    width: 100%;
    table-layout: fixed;
    background: #fff;
    border-bottom: 1px solid #dddddd;
}

.temp_body_grid_body_list>div {
    height: 40px;
}

.temp_body_grid_body_list:nth-child(even) {
    background: #F9FAFC;
}

.temp_body_grid_body_list:last-child {
    border-bottom: none;
}

.historyBillinggrid_head {
    width: 100%;
    height: 34px;
    display: table;
    table-layout: fixed;
}

.historyBillinggrid_head>div {
    display: table-cell;
    width: 40%;
    text-align: center;
    padding-right: 10px;
    vertical-align: middle;
    background: #DADEE5;
}

.historyBillinggrid_head>div:first-child {
    text-align: left;
    padding-right: 0;
    padding-left: 10px;
}

.historyBillinggrid_list {
    height: 70px;
    table-layout: fixed;
}

.historyBillinggrid_list>div:first-child {
    text-align: left;
    padding-left: 10px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.historyBillinggrid_list>div:first-child i {
    margin-left: 8px;
}

.historyBillinggrid_list>div i.nopay {
    background: #F87C7C;
    color: #fff;
    width: 60px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    border-radius: 3px;
    display: inline-block;
}

.isdownPdf {
    display: none;
}