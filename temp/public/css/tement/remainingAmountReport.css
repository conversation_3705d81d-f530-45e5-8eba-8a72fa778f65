/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

.r_a_r_box div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.r_a_r_box {
    width: 100%;
    height: 100%;
}

.r_a_r_box .r_a_r_header {
    height: 50px;
    border-bottom: 1px solid #D9E2E8;
    padding: 10px 20px 9px;
    position: relative;
}

.r_a_r_box .r_a_r_header .r_a_r_go_back {
    position: absolute;
}

.r_a_r_box .r_a_r_header h1 {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    color: #333;
    text-align: center;
}

.r_a_r_box .r_a_r_gird_box {
    height: 100%;
    max-height: calc(100% - 50px);
    padding: 20px;
}

.r_a_r_box .per-grid-normal {
    padding-bottom: 0;
}

.no_border_bottom {
    border-bottom: none;
}

.r_a_r_box .r_a_r_download {
    position: absolute;
    top: 10px;
    right: 20px;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird {
    width: 100%;
    height: 100%;
    font-size: 14px;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_tit {
    display: flex;
    border: 1px solid #CCCCCC;
    border-bottom: none;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_tit>div {
    flex: 1;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    background-color: #8C959A;
    border-right: 1px solid #8C959A;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_tit>div:last-child {
    border-right: none;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_body {
    height: calc(100% - 30px);
    overflow: auto;
    border: 1px solid #CCCCCC;
    border-top: none;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_body .r_a_r_gird_item {
    display: flex;
    height: 36px;
    line-height: 36px;
    text-align: center;
    color: #333333;
    border-top: 1px solid #CCCCCC;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_body .r_a_r_gird_item:first-child {
    border-top: none;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_body .r_a_r_gird_item>div {
    flex: 1;
    border-right: 1px solid #CCCCCC;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_body .r_a_r_gird_item>div:last-child {
    border-right: none;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_body .r_a_r_gird_item:nth-child(odd) {
    background-color: #FFFFFF;
}

.r_a_r_box .r_a_r_gird_box .r_a_r_gird .r_a_r_gird_body .r_a_r_gird_item:nth-child(even) {
    background-color: #F7F7F7;
}