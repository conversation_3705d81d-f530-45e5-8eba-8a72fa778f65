/*缴费记录-电  */

/*头部  */
.mainBody .payRecord_title {
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #d9e2e8;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 20px;
}

#payRecord_title_button1 {
    width: 80px;
    height: 30px;
    font-size: 14px;
    margin-top: 10px;
}

.payRecord_title_title {
    font-size: 16px;
}

#payRecord_title_button2 {
    width: 140px;
    height: 30px;
    font-size: 14px;
    margin-top: 10px;
}

/*时间栏  */
.mainBody .payRecord_time {
    padding: 10px 20px;
}

.mainBody .payRecord_time .per-calendar-text {
    width: 212px;
}

/*表格  */
.mainBody .payRecord_grid {
    margin: 0 20px;
    overflow: hidden;
    font-size: 12px;
    border: 1px solid #ccc;
    border-left: none;
}

/*表头  */
.mainBody .payRecord_grid .payRecord_grid_top {
    height: 30px;
    line-height: 30px;
    color: white;
    background-color: #8b959a;
    font-size: 12px;
}

.mainBody .payRecord_grid .payRecord_grid_top ul li>div {
    text-indent: 20px;
}

.mainBody .payRecord_grid .payRecord_grid_top ul li>div:nth-child(4) {
    padding-left: 0;
}

.payRecord_grid_name {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 150px;
    flex: 0 0 150px;
}

.payRecord_grid_namenum {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 73px;
    flex: 0 0 73px;
}

.payRecord_grid_home {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 85px;
    flex: 0 0 85px;
}

.payRecord_grid_con {
    width: 100%;
}

.payRecord_grid_con>div {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.payRecord_grid_body_right {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    min-height: 36px;
}

.payRecord_grid_body_right>div {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    min-height: 36px;
}

/*表格内容  */
.mainBody .payRecord_grid .payRecord_grid_body {
    max-height: calc(100vh - 160px);
    overflow: auto;
    background: white !important;
}

.mainBody .payRecord_grid .payRecord_grid_body ul li {
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
    min-height: 36px;
}

.mainBody .payRecord_grid .payRecord_grid_body ul li:last-child {
    border-bottom: none;
}

.mainBody .payRecord_grid .payRecord_grid_body ul li>div {
    border-right: 1px solid #ccc;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    text-indent: 20px;
}

.mainBody .payRecord_grid .payRecord_grid_body ul li>div:last-child {
    border-right: none;
}

.mainBody .payRecord_grid .payRecord_grid_body .payRecord_grid_body_right .inline>div {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    height: 36px;
    line-height: 36px;
}

.mainBody .payRecord_grid .payRecord_grid_body .payRecord_grid_body_right .inline>div:last-child {
    border-right: none;
}

.mainBody .payRecord_grid .payRecord_grid_body .payRecord_grid_body_right .inline:last-child>div {
    border-bottom: none;
}

/*隔行变色  */
.mainBody .payRecord_grid .payRecord_grid_body ul li:nth-child(2n) {
    background: #f5f7fa !important;
}

.mainBody .payRecord_grid .payRecord_grid_body ul li:nth-child(2n+1) {
    background: white !important;
}