/*结算*/


/*头部  */

.mainBody .settlement_title {
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #d9e2e8;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 20px;
    font-weight: 500;
    text-align: left;
}

.settlement_title .settlement_title_con {
    font-size: 16px;
    position: relative;
    right: 48px;
}

#settlement_title_button {
    width: 80px;
    height: 30px;
    font-size: 14px;
    margin-top: 10px;
}


/*结算 中央内容部分  */

.settlement_content {
    width: 1110px;
    height: calc(100% - 147px);
    margin: 0px auto;
}


/*结算 内容部分   标题  */

.settlement_content .settlement_content_title>div>div:nth-child(1) {
    font-size: 14px;
    color: #868686;
    margin-bottom: 12px;
}

.settlement_content .settlement_content_title>div>div.date_title {
    margin-bottom: 9px;
}

.settlement_content .settlement_content_title>div>div:nth-child(2) {
    font-size: 14px;
}

.settlement_content .settlement_content_title>div:nth-child(1) {
    margin: 36px 0;
}

.settlement_content .settlement_content_title>div:nth-child(2) {
    position: relative;
    margin-bottom: 40px;
}

.settlement_content .s_choose_date_error_msg {
    position: absolute;
    top: 58px;
    left: 0;
}

.settlement_content .s_choose_date_error_msg p {
    font-size: 12px;
    height: 28px;
    line-height: 28px;
    color: #ff8f5c;
    display: none;
}

.settlement_content .s_choose_date_error_msg i {
    display: block;
    height: 28px;
    width: 16px;
    background: url(/images/error_icon.png) no-repeat left;
    float: left;
}


/*结算内容部分  表格  */

.settlement_content .settlement_content_table {
    text-align: center;
    font-size: 12px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.settlement_num {
    -webkit-box-flex: 1.4;
    -ms-flex: 1.4;
    flex: 1.4;
}

.settlement_name {
    -webkit-box-flex: 1.7;
    -ms-flex: 1.7;
    flex: 1.7;
}

.settlement_home {
    -webkit-box-flex: 1.9;
    -ms-flex: 1.9;
    flex: 1.9;
}

.settlement_homeNum {
    -webkit-box-flex: 1.1;
    -ms-flex: 1.1;
    flex: 1.1;
}

.settlement_money {
    -webkit-box-flex: 1.6;
    -ms-flex: 1.6;
    flex: 1.6;
}

/*结算内容部分 表格表头  */

.settlement_content .settlement_content_table_top {
    border-bottom: 1px solid #eee;
}

.settlement_content .settlement_content_table_top ul li {
    height: 30px;
    line-height: 30px;
    background-color: #f8f8f8;
}


/*结算内容部分  表格内容  */

.settlement_content .settlement_content_table_content {
    max-height: calc(100vh - 490px);
    overflow: auto;
    cursor: pointer;
}

.settlement_content .settlement_content_table_content ul li {
    height: 36px;
    line-height: 36px;
}


/*隔行变色  */

.settlement_content .settlement_content_table_content ul li:nth-child(2n) {
    background: #f7f9fb;
}


/*表格  hover效果  */

.settlement_content .settlement_content_table_content ul li:hover {
    background: #f8f8f8;
}

/* 结算表格下方提示部分 */
.prompt_settlement {
    margin-top: 10px;
    width: 100%;
    height: 90px;
    overflow: auto;
}

.prompt_foot {
    padding-left: 18px;
}


/*结算失败  */


/*中央内容部分  */

.settlementErr_content {
    display: none;
    text-align: center;
}


/*内容上方  */

.settlementErr_content_top {
    margin-top: 100px;
}

.settlementErr_content_top>div {
    width: 24px;
    height: 24px;
    position: relative;
    top: 5px;
    right: 5px;
    display: inline-block;
    background: url(/images/erricon.png) no-repeat left;
    background-size: 100% 100%;
}


.settlementErr_content_top span {
    font-size: 24px;
    font-weight: 600;
    color: #f87c7c;
}


/*内容下方  */

.settlementErr_content_bottom {
    margin-top: 40px;
}

.settlementErr_content_bottom p {
    color: #6d6d6d;
    font-size: 14px;
}


/*结算成功  */


/*中央内容部分  */

.settlementSucc_content {
    display: none;
    text-align: center;
}

.settlementSucc_content_top {
    margin-top: 100px;
}

.settlementSucc_content_top>div {
    width: 24px;
    height: 24px;
    position: relative;
    top: 5px;
    right: 5px;
    display: inline-block;
    background: url(/images/succicon.png) no-repeat left;
    background-size: 100% 100%;
}

.settlementSucc_content_top span {
    font-size: 24px;
    font-weight: 600;
    color: #68c5b3;
}

.settlementSucc_content_bottom {
    margin-top: 40px;
}

.settlementSucc_content_bottom .per-button-grayBorder {
    width: 140px;
    height: 30px;
    font-size: 14px;
}


/*尾部  */

.settlement_foot {
    width: calc(100% - 20px);
    height: 59px;
    line-height: 59px;
    border-top: 1px solid #d9e2e8;
    background: white;
    text-align: center;
}

.settlement_foot .per-button-grayBg {
    width: 140px;
    height: 30px;
    font-size: 14px;
    margin: 15px auto;
}

#bat_re_settle_ok,
#bat_settle_back {
    display: none;
    width: 140px;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    margin: 15px auto;
}

/*结算 账单明细  大体样式 */

#billWindow .per-madal-float {
    width: 608px;
    font-size: 12px;
}


/*结算 账单明细  头部设置 */

#billWindow .per-madal-float .per-madal-float_title {
    width: 568px;
    height: 60px;
    margin-left: 20px;
}

#billWindow .per-madal-float .per-madal-float_title>div {
    margin-top: -10px;
}

#billWindow .per-madal-float .per-madal-float_title .per-madal-float_titcon {
    font-size: 18px !important;
    font-weight: 600;
    margin-left: -20px;
}




/*结算 账单明细  内容部分设置 */

.billCon {
    width: 528px;
    margin: 40px auto;
    font-size: 14px;
}

.billCon_title>div {
    margin-bottom: 20px;
    line-height: 30px;
}


/*结算 账单明细  内容部分  表格 */

.bill {
    width: 526px;
    max-height: calc(100% - 266px);
    border: 1px solid #ccc;
    border-bottom: none;
    font-size: 12px;
}


/*表头  */

.bill_top {
    width: 526px;
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #ccc;
    background: #8b959a;
    color: white;
}

.bill_top ul li {
    width: 526px;
    height: 30px;
    text-align: center;
}


/*表格内容  */

.bill_con {
    width: 528px;
    max-height: calc(100vh - 305px);
    overflow: auto;
    border-bottom: 1px solid #ccc;
}

.bill_con ul li {
    height: 36px;
    line-height: 36px;
    text-align: center;
}

.bill_con ul li:nth-child(2n) {
    background: #f5f7fa;
}

.bill_con ul li div {
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
}

.bill_con ul li:last-child div {
    border-bottom: none;
}