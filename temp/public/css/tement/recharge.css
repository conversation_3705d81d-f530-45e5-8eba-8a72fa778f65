/*充值记录-电-软件充软件和  */

/*头部  */
.mainBody .recharge_title{
    height:50px;
    line-height:50px;
    border-bottom:1px solid #d9e2e8;
    display:-webkit-box;
    display:-ms-flexbox;
    display:flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    padding:0 20px;
}
#recharge_title_button1{
    width:80px;
    height:30px;
    font-size:14px;
    margin-top:10px;
}
.recharge_title_title{
    font-size:16px;
}
#recharge_title_button2{
    width:140px;
    height:30px;
    font-size:14px;
    margin-top:10px;
}
#page_simple1{
    margin-right: 5%;
}
#page_simple2{
    margin-right: 5%;
}
/*时间栏  */
.mainBody .recharge_time{
    padding:10px 20px;
}
.mainBody .recharge_time .per-calendar-text{
    width:212px;
}
/*表格  */
.mainBody .recharge_table{
    margin:0 20px;
    border:1px solid #ccc;
    border-left:none;
    overflow:hidden;
    font-size:12px;
}

/*表头  */
.mainBody .recharge_table .recharge_table_top{
    height:30px;
    line-height:30px;
    color:white;
    background-color:#8b959a;
    text-align:center;
    font-size:12px;
    font-weight:900;
}

.recharge_table_name {
    width: 286px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 286px;
            flex: 0 0 286px;
}
.recharge_table_num {
    width: 240px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 240px;
            flex: 0 0 240px;
}
.recharge_table_home {
    width: 252px;
    -webkit-box-flex: 0;
        -ms-flex: 0 0 252px;
            flex: 0 0 252px;
}
.recharge_table_con {
    width:100%;
}
.recharge_table_con_time {
    -webkit-box-flex: 4;
        -ms-flex: 4;
            flex: 4;
}
.recharge_table_con_num {
    -webkit-box-flex: 3;
        -ms-flex: 3;
            flex: 3;
}
.recharge_table_con_money {
    -webkit-box-flex: 2;
        -ms-flex: 2;
            flex: 2;
}

/*表格内容  */
.mainBody .recharge_table .recharge_table_body{
    max-height:calc(100vh - 160px);
    overflow:auto;
}
.mainBody .recharge_table .recharge_table_body ul li{
    border-bottom:1px solid #ccc; 
    border-left:1px solid #ccc;
    min-height:36px;
}
.mainBody .recharge_table .recharge_table_body ul li:last-child{
    border-bottom:none; 
}
.mainBody .recharge_table .recharge_table_body ul li>div{
    border-right:1px solid #ccc; 
    display:-webkit-box; 
    display:-ms-flexbox; 
    display:flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
}
.mainBody .recharge_table .recharge_table_body ul li>div:last-child{
    border-right:none;
}

/*隔行变色  */
.mainBody .recharge_table .recharge_table_body ul li:nth-child(2n){
    background:#f5f7fa;  
}

.recharge_table_body_right{
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1; 
    -webkit-box-orient: vertical; 
    -webkit-box-direction: normal; 
        -ms-flex-direction: column; 
            flex-direction: column; 
    min-height: 36px;
}
.recharge_table_body_right>div{
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1; 
    min-height: 36px;
} 
.mainBody .recharge_table .recharge_table_body .recharge_table_body_right .inline>div{
    border-bottom:1px solid #ccc;
    border-right:1px solid #ccc; 
    height:36px;
    line-height:36px; 
    text-align:center;
}
.mainBody .recharge_table .recharge_table_body .recharge_table_body_right .inline>div:last-child{
    border-right:none;
}
.mainBody .recharge_table .recharge_table_body .recharge_table_body_right .inline:last-child >div{
    border-bottom:none;
}
