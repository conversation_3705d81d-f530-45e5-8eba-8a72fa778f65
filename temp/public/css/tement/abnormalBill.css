#abnormalBill{
    width: 100%;
    height: 100%; 
}

#abnormalBill .per-noBorderButton-gray{
    font-size: 12px;
}
#abnormalBill .per-noBorderButton-red{
    font-size: 12px;
}
#abnormalBill > .head{
    padding: 10px 20px 10px;
    width: calc(100% - 40px);
    height: 30px;
    line-height: 30px;
}
#abnormalBill > .head > div{
    margin-right: 10px;
    float: left;
}
#abnormalBill > .title{
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    margin: 0 20px 0;
    border-bottom: 1px solid #eee;
    width: calc(100% - 40px);
    height: 30px;
    font-size: 12px;
}
#abnormalBill > .title span{
    padding: 0 10px 0;
    flex: 1;
}
#abnormalBill > .grid{
    padding: 0 20px 0;
    width: calc(100% - 40px);
    height: calc(100% - 130px);
    overflow-y: auto;
    font-size: 12px !important;
}
#abnormalBill > .grid li{
    line-height: 38px;
    height: 38px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
}
#abnormalBill > .grid li:nth-of-type(odd){
    background: #f7f9fb;
}
#abnormalBill > .grid li span{
    padding: 0 10px 0;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    flex: 1;
}
#abnormalBill > .page{
    padding: 10px 20px 10px;
    width: calc(100% - 40px);
    height: 30px;
    line-height: 30px;
}
#abnormalBill > .page > div{
    float: right;
}

.abnormalBillChargeAgainWrap{
    padding: 50px;
    height: auto;
    font-size: 14px;
}
.abnormalBillChargeAgainWrap > .info{
    height: 60px;
    margin-bottom: 10px;
}
.abnormalBillChargeAgainWrap > .info > div{
    width: 100%;
    height: 30px;
    line-height: 30px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}
.abnormalBillChargeAgainWrap > .info > .title{
    color: #6d6d6d;
}

.abnormalBillChargeAgainWrap > .btn{
    margin-top: 30px;
}

.abnormalBillChargeAgainWrap > .btn > div{
    margin-left: 40px;
}