/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

.s_a_box div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.s_a_box div.per-prompt-abnormalmess {
    top: 60%;
}

.s_a_box {
    width: 100%;
    height: 100%;
}

.s_a_box .s_a_header {
    height: 50px;
    border-bottom: 1px solid #d9e2e8;
    padding: 10px 20px 9px;
    position: relative;
}

.s_a_box .s_a_header .s_a_cancel {
    position: absolute;
}

.s_a_box .s_a_header h1 {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    color: #333;
    text-align: center;
}

.s_a_box .s_a_content {
    height: calc(100% - 108px);
    overflow: auto;
}

.s_a_box .s_a_content .s_a_content_mid {
    width: 972px;
    height: 100%;
    margin: 0 auto;
    padding-top: 32px;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_energy {
    margin-bottom: 22px;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_choose_date {
    position: relative;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_energy .s_a_energy_title,
.s_a_box .s_a_content .s_a_content_mid .s_a_energy .s_a_energy_type,
.s_a_box .s_a_content .s_a_content_mid .s_a_choose_date .s_a_choose_date_title {
    font-size: 14px;
    color: #6d6d6d;
    height: 30px;
    line-height: 30px;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_choose_date .s_a_choose_date_controller {
    margin-top: 2px;
    margin-bottom: 40px;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_choose_date .s_a_choose_date_error_msg {
    position: absolute;
    top: 62px;
    left: 0;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_choose_date .s_a_choose_date_error_msg p {
    font-size: 12px;
    height: 28px;
    line-height: 28px;
    color: #ff8f5c;
    display: none;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_choose_date .s_a_choose_date_error_msg i {
    display: block;
    height: 28px;
    width: 16px;
    background: url(/images/error_icon.png) no-repeat left;
    float: left;
}


/* 干掉控件库样式 */

.s_a_box .s_a_content .s_a_content_mid .s_a_energy .s_a_energy_type,
.s_a_box .s_a_content .s_a_content_mid .s_a_gird .per-grid-normal_title {
    color: #333333;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_gird .per-grid-normal_title>ul>li {
    background: #eaeef0;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_gird .per-grid-normal_title>ul {
    border-bottom: 1px solid #ccc;
}

.s_a_box .s_a_content .s_a_content_mid .s_a_gird .per-grid-normal .per-grid-normal_r .per-grid-normal_con .per-grid-nomal-wrap ul li {
    cursor: pointer;
}

.s_a_box .s_a_footer {
    height: 58px;
    border-top: 1px solid #d9e2e8;
}

.s_a_box .per-grid-normal_main {
    border-bottom: none;
}

.s_a_box .s_a_footer .s_a_settle_btn {
    padding: 14px 0;
    width: 240px;
    margin: 0 auto;
}

.s_a_box .s_a_footer .s_a_settle_btn .per-button-grayBg {
    width: 100%;
}

.s_a_box .per-grid-normal_con,
.s_a_box .per-grid-normal_r,
.s_a_box .per-grid-nomal-wrap {
    position: static;
}

.prompt_settleAccounts {
    margin-top: -60px;
}

.prompt_con {
    color: #fd9054 !important;
    font-size: 14px;
    line-height: 20px;
}

.prompt_icon {
    width: 14px;
    height: 14px;
    font-size: 12px;
    line-height: 14px;
    border-radius: 50%;
    color: #fff;
    background: #f28b37;
    display: inline-block;
    text-align: center;
}