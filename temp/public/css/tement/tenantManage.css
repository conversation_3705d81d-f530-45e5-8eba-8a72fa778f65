/* 解决初始化大白板问题，千万不要干掉 */
.mainBodyShow {
    display: none;
}

.clearFloat:after {
    content: " ";
    display: table;
    clear: both;
}

.clearFloat:before {
    content: " ";
    display: table;
}

._left {
    float: left;
}

._right {
    float: right;
}

#remotetopup_building_cbx {
    width: 200px;
}

#addtenant_building_cbx {
    width: 200px;
}


/* 修改webkit中input的placeholder样式 */

input::-webkit-input-placeholder {
    color: #cacaca;
}

#RemotetopupCount .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar_wrap {
    height: 265px !important
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
    #RemotetopupCount .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar {
        max-height: 400px !important;

    }
}

#RemotetopupCount .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar {
    max-height: 400px !important;

}


/* 表格上方默认隐藏按钮 */

#Pbutton-white1,
#Pbutton-white2,
#Pbutton-white3,
#Pbutton-white4,
#Pbutton-white5,
#Pbutton-white6,
#Pbutton-white7 {
    display: none;
}

body {
    width: 100%;
    height: 100%;
    min-width: 1280px;
    overflow: auto;
}

#tenantManage {
    position: relative;
    padding: 14px 10px 0;
    height: 100vh;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #eef2f5;
}

.setPage {
    position: absolute;right:400px
}
.setPage span {
    cursor:pointer;
    margin-left:10px;
}
.setPage span:hover {
    color:#7a94ad;
}

.mainBody {
    height: 100%;
    background: #fff;
    -webkit-border-radius: 6px 6px 0 0;
    -webkit-box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}


/*头部  */

.mainBody .main_title {
    height: 50px;
    padding: 0 20px;
}

.mainBody .main_title>div {
    margin-top: 10px;
}


/*头部选择栏 两个  */

.mainBody .main_title .main_title_combobox {
    float: left;
    width: 200px;
}


/*头部第一个状态栏  */

.mainBody .main_title .main_title_combobox:first-child {
    width: 160px;
    margin-right: 10px;
}


/* 头部右侧搜索栏 */

.mainBody .main_title .main_title_right {
    float: right;
    width: 200px;
    height: 30px;
    position: relative;
}


/*右侧搜索提示栏  */

.searchHint {
    width: 180px;
    max-height: 200px;
    overflow: auto;
    padding: 5px 10px;
    background: white;
    border: 1px solid #cacaca;
    border-top: none;
    position: absolute;
    top: 31px;
    z-index: 2;
    cursor: pointer;
    display: none;
}

/*提示栏下方列表样式  */
.searchHint ul {
    line-height: 20px;
    font-size: 14px;
}

.searchHint ul li p {
    font-size: 12px;
    color: #cacaca;
}

.searchHint ul li div {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


/*头部右侧按钮  */

.mainBody .main_title .main_title_button {
    float: right;
    width: 86px;
    height: 30px;
    margin-left: 10px;
}

.mainBody .main_title .main_title_button_D {
    width: 114px;
}

/*中央表格  */

.mainBody .main_grid {
    width: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: calc(100% - 40px);
    padding: 0 20px;
}

.mainBody .main_grid .per-grid-dynamic_header {
    border-top: 1px solid #d9e2e8;
    border-bottom: 1px solid #d9e2e8;
}

.per-scrollbar_actual .per-grid-dynamic_li:last-child {
    padding-right: 0;
}

.mainBody .main_grid #grid1 .per-grid-dynamic_header_left .per-combobox-basic {
    margin-left: 10px;
}


/*表格上方按钮位置  */
.per-grid-dynamic_header_right {
    margin-right: 10px;
}
#absoluteLeft {
    position: absolute;
    left: 145px;
} 
#absoluteLeft .per-combobox-title{
    width: 140px;
    margin: 0 10px 0 0;
} 
.per-grid-dynamic_header .per-combobox-title{
    padding: 0 8px;
    width: 130px;
}
#t_feetype_cbx,
#t_showdata_cbx,
#t_feetype_cbx_hou {
    width: 150px;
}
.per-grid-dynamic_header_right .per-bIcon{
    margin: 0;
}
#absoluteLeft>div {
    float: left;
}

#Pbutton-white1,
#Pbutton-white2 {
    text-align: center;
    border: 1px solid #cacaca;
    border-radius: 2px;
    margin-right: 10px;
    background-color: #eaeef0;
    color: #637e99;
    padding: 0 10px;
    height: 24px;
    line-height: 24px;
    font-size: 14px;
    margin-top: 3px;
    cursor: pointer;
}

#Pbutton-white3,
#Pbutton-white4,
#Pbutton-white5,
#Pbutton-white6,
#Pbutton-white7 {
    text-align: center;
    border: 1px solid #cacaca;
    border-radius: 2px;
    background-color: #eaeef0;
    color: #637e99;
    padding: 0 15px;
    height: 24px;
    line-height: 24px;
    font-size: 14px;
    margin-top: 3px;
    cursor: pointer;
}

/*点击按钮时的样式  */
.btnVisited {
    background: #d6dfe3 !important;
    border: 1px solid #7a94ad !important;
}

.mainBody .main_grid .per-grid-dynamic_li .per-grid-dynamic_item:last-child .per-notice_successicon {
    background: transparent;
    width: 24px;
    height: 24px;
    line-height: 24px;
    position: relative;
    left: -20px;
    border-radius: 0;
    text-align: center;
    color: #7a94ad;
    font-size: 14px;
}

/*表格最后一栏操作栏  */
.mainBody .main_grid .per-grid-dynamic_li .per-grid-dynamic_item:last-child>div .operationButton {
    width: 22px;
    height: 22px;
    position: relative;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid transparent;
    display: inline-block;
    font-family: "perficon";
    line-height: 22px;
    font-size: 18px;
    margin: 7px auto;
    color: #7a94ad;
    font-weight: normal;
}

.mainBody .main_grid .per-grid-dynamic_li .per-grid-dynamic_item:last-child>div .operationButton:hover {
    border: 1px solid #cacaca;
}

/*表格最后一栏操作栏  操作列表  */
.mainBody .main_grid .per-grid-dynamic_li .per-grid-dynamic_item:last-child>div ul {
    display: none;
    z-index: 2;
    width: 150px;
    border: 1px solid #ccc;
    background: #ffffff;
    position: absolute;
    left: -150px;
    top: 8px;
    -webkit-box-shadow: 0px -2px 16px #d9d9d9;
    box-shadow: 0px -2px 16px #d9d9d9;
    border-radius: 2px;
}

.mainBody .main_grid .per-grid-dynamic_li .per-grid-dynamic_item:last-child>div ul li {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    font-weight: 600;
    color: #637e99;
    text-align: center;
}

.mainBody .main_grid .per-grid-dynamic_li .per-grid-dynamic_item:last-child>div ul li:hover {
    background: #f8f8f8;
}


/*尾部  */

.mainBody #grid1 .per-grid-paging {
    width: 100%;
    height: 59px;
    background: white;
    border-top: 1px solid #eee;
}


/*尾部 左边按钮  */

.mainBody #grid1 .per-grid-paging .main_floot_left {
    display: table-cell;
    float: left;
}

.mainBody #grid1 .per-grid-paging .main_floot_left>div {
    height: 30px;
    margin: -5px 5px 15px 0;
    padding: 0 18px;
    font-size: 14px;
}


/*尾部 右边 分页 */

#page_simple {
    position: relative;
    top: -5px;
    left: -239px;
}

.main_foot_right {
    height: 59px;
    display: table-cell;
    float: right;
    position: absolute;
    right: 0;
}

.main_foot_right .page_con {
    height: 30px;
    float: right;
    margin-top: -5px;
}

.main_foot_right .page_con p {
    display: table-cell;
    font-size: 14px;
    line-height: 30px;
    padding: 0 10px;
}

.main_foot_right .page_con #page_text {
    width: 50px;
    height: 18px;
    border-radius: 2px;
    display: table-cell;
    font-size: 12px;
    color: #000;
}

.main_foot_right .page_con #page_determine {
    width: 54px;
    height: 28px;
    line-height: 28px;
    border: 1px solid #cecece;
    border-radius: 2px;
    display: table-cell;
    font-size: 14px;
}

.left {
    float: left;
    margin-right: 13px;
}

.overflowVisble {
    width: 100% !important;
    overflow: visible !important;
}


/*设置报警门限 头部标题  */

#policeWindow .per-modal-custom_title {
    font-size: 16px;
}


/*设置报警门限中   输入框的属性  */

#policeWindow .per-input-basic {
    width: 70px;
    height: 30px;
    display: inline-block;
    margin: 0 5px;
}


/*设置报警门限中   确认按钮大小  */
#determine_custom,
#determine_globle {
    float: left;
    width: 80px;
    height: 30px;
    font-size: 14px;
}


/*设置报警门限中   取消按钮大小  */
#cancel_custom,

#cancel_global {
    float: right;
    width: 80px;
    height: 30px;
    font-size: 14px;
}

#sure_global {
    margin-left: 230px;
    margin-right: 60px;
}


/*设置报警门限中  字体，位置的设置  */
.police {
    box-sizing: border-box;
    line-height: 30px;
    font-size: 14px;
}

.police div {
    box-sizing: border-box;
}

.police .plice_sel {
    width: 700px;
    padding: 50px 72px 16px;
    max-height: 590px;
    overflow: auto;
}

.police .plice_sel>div>div {
    padding-left: 18px;
}

.police .police_tit {
    position: relative;
    height: 30px;
    line-height: 30px;
    background-color: #F6F6F6;
    border-radius: 3px 3px 0 0;
}

.police .police_tit>div {
    height: 16px;
    position: absolute;
    right: 18px;
    top: 7px;
}

.police .police_tit>div>div {
    vertical-align: top;
}

.police .plice_sel>div {
    margin-bottom: 10px;
    border: 1px solid #CCCCCC;
    border-radius: 4px;
}

.police .plice_sel>div:last-child {
    margin-bottom: 0;
}

.police .police_con {
    position: relative;
    height: 49px;
    padding: 9px 0;
    border-top: 1px solid #CCCCCC;

}

.police .police_con .btn {
    position: absolute;
    right: 18px;
    top: 7px;
}

.police .police_con>div {
    line-height: 30px;
    float: left;
}

.police .police_con .police_con_mid {
    margin-left: 12px;
    margin-right: 10px;
}

.police .police_con .police_con_mid>div {
    width: 50px !important;
}

.police .police_con .police_con_mid>div>input {
    text-align: center;
}

.police .police_con .police_con_mid .error-tip {
    left: 130px;
    bottom: 4px;
}

.police .plice_ope {
    height: 109px;
    padding: 38px 248px 41px;
}

/* 自动发送短信20180606wp++ */
.autoMsgBox .autoMsg {
    padding: 30px 40px 0px;
}

.autoMsgBox .autoMsgCol {
    position: relative;
    height: 40px;
    line-height: 40px;
}

.autoMsgBox .autoMsg .btnS {
    position: absolute;
    right: 18px;
    top: 7px;
}

.autoMsgBox .autoMsg .btnS.btnLeft {
    right: 100px;
}

.autoMsgBox .plice_ope {
    width: 220px;
    padding-bottom: 40px;
    padding-left: 200px;
}

#autoTime2,
#autoTime1 {
    display: inline-block;
    width: 120px;
    margin: 5px 20px;
}

.autoMsg .plice_ope {
    margin-top: 0px;
}

/*修改价格方案  大体样式设置 */

#floatWindow .per-madal-float {
    width: 430px;
    font-size: 14px;
    padding: 0 20px;
}


/*修改价格方案  头部设置 */

#floatWindow .per-madal-float .per-madal-float_title {
    height: 80px;
    padding-top: 0;
    font-size: 18px;
    font-weight: 700;
}

#floatWindow .per-madal-float .per-madal-float_title .per-madal-float_titcon {
    margin-left: -20px;
}


/*修改价格方案  内容  */

.float {
    padding: 30px 20px;
}

/*当前价格方案输入框  */
.float #floatText {
    width: 390px;
}

.float #floatText .per-combobox-title {
    padding-left: 5px;
}

/*修改价格方案  保存按钮 */

#buttonCenter {
    line-height: 30px;
    width: 120px;
    height: 30px;
    font-size: 12px;
    margin: 15px auto;
    display: block;
}


/*字体颜色变灰  */

.colorGray {
    color: #868686;
    line-height: 30px;
}


/*字体颜色变蓝  */

.colorBlue {
    color: #7e93a9;
    padding: 20px 0;
}

.colorBlue:hover {
    cursor: pointer;
    color: #637e99;
}

/*字体缩小  */
.textSmall {
    font-size: 12px;
    color: #b8b8b8;
    margin-top: 5px;
}

/*发送充值提醒  */
.modalwindow {
    width: 500px;
    max-height: 380px;
    line-height: 30px;
    padding: 40px 80px 30px;
    font-size: 14px;
}

/*发送充值提醒  表格 */
.modalwindow .modalwindow_table #reminder {
    padding: 0;
    max-height: 234px;
    overflow: hidden;
    font-size: 12px;
}

.modalwindow .modalwindow_table #reminder .per-scrollbar_actual ul .per-grid-dynamic_li {
    cursor: default !important;
}

.modalwindow .modalwindow_table #reminder .per-scrollbar_actual ul li:hover {
    background: white;
}

.modalwindow .modalwindow_table #reminder .per-scrollbar_actual ul li:nth-child(even):hover {
    background: #f7f9fb;
}

/*发送充值提醒  表格控件头部隐藏  */
.modalwindow .per-grid-dynamic_header {
    display: none;
}


/*发送充值提醒 按钮位置  */
.modalwindow .modalwindow_button {
    margin-left: 130px;
    margin-top: 20px;
    z-index: 2;
}


/*发送充值提醒 按钮大小 */
#send_msg_bat_ok {
    width: 100px;
    height: 30px;
    font-size: 14px;
    margin-right: 40px;
}

#send_msg_bat_cancel {
    width: 100px;
    height: 30px;
    font-size: 14px;
}

.sortGray {
    color: #d1d7e3 !important;
}

#tenantManage .per-input-textarea .textareawrap {
    height: 100px;
}

.flexbox{
    display: flex;
}
.spaceAround{
    justify-content: space-around;
}
.spaceBetween{
    justify-content: space-between;
}
.searchBox section{
    margin-right: 20px;
}
.success_green{
    color: green;
}
.error_red{
    color: red;
}