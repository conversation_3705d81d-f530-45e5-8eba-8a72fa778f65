/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

.t_d_box div,
.t_d_box li,
.t_d_box h1,
.t_d_box h2,
.t_d_box h3,
.t_d_box p {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}


/* 未激活 */

.t_d_box .t_d_content .t_d_content_info span.no_activate {
    font-size: 12px;
    color: #fd9055;
    border: 1px solid #fd9055;
    background-color: #faeae3;
}


/* 已激活 */

.t_d_box .t_d_content .t_d_content_info span.activate {
    font-size: 12px;
    color: #02a9d1;
    border: 1px solid #02a9d1;
    background-color: #d4eef6;
}


/* 已退租 */

.t_d_box .t_d_content .t_d_content_info h2 span.surrender {
    font-size: 12px;
    color: #f87c7c;
    border: 1px solid #f87c7c;
    background-color: #f9e7e9;
}


/* 缴费状态右上角图标 */

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item>i.wait_pay_icon {
    background: url("/img/tement/wait_pay_icon.png");
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item>i.not_sufficient_icon_money {
    background: url("/img/tement/not_sufficient_icon.png");
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item>i.not_sufficient_icon_account {
    background: url("/img/tement/not_surplus_icon.png");
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item>i.no_icon {
    background: transparent;
}

.t_d_box {
    width: 100%;
    height: 100%;
    color: #333;
}

.t_d_box .t_d_header {
    height: 50px;
    border-bottom: 1px solid #d9e2e8;
    padding: 10px 20px 9px;
}

.t_d_box .t_d_header h1 {
    width: 800px;
    margin: 0 auto;
}

.t_d_box .t_d_header .t_d_go_back {
    float: left;
}

.t_d_box .t_d_header .t_d_delete,
.t_d_box .t_d_header .t_d_activate,
.t_d_box .t_d_header .t_d_surrender {
    float: right;
    margin-left: 10px;
}

.t_d_box .t_d_header h1 {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    color: #333;
    text-align: center;
}

.t_d_box .t_d_content {
    height: calc(100% - 50px);
    padding: 10px 20px;
}

.t_d_box .t_d_content .t_d_content_info,
.t_d_box .t_d_content .t_d_content_energy {
    height: 100%;
    border: 1px solid #d9e2e8;
    position: relative;
}

.t_d_box .t_d_content .t_d_content_info {
    width: 470px;
    float: left;
    margin-right: 10px;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_title,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_title {
    background-color: #f9fafc;
    border-bottom: 1px solid #d9e2e8;
    padding: 0 20px;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_title span.edit_icon {
    position: relative;
    padding-left: 18px;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_title span.edit_icon i {
    display: block;
    height: 16px;
    line-height: 16px;
    width: 16px;
    font-family: perficon;
    margin-right: 8px;
    position: absolute;
    top: 0;
    left: 0;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_title span:hover,
.t_d_box .t_d_content .t_d_content_info .t_d_content_info_title span.edit_icon:hover {
    color: #42607c;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_title div,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_title div {
    float: right;
    height: 40px;
    line-height: 40px;
    color: #7a94ad;
    margin-left: 28px;
    cursor: pointer;
}

.t_d_box .t_d_content .t_d_content_info h2,
.t_d_box .t_d_content .t_d_content_energy h2 {
    font-size: 16px;
    height: 40px;
    line-height: 40px;
    float: left;
}

.t_d_box .t_d_content .t_d_content_info .status {
    display: block;
    width: 60px;
    height: 20px;
    position: absolute;
    text-align: center;
    line-height: 20px;
    top: 9px;
    left: 93px;
    border-radius: 3px;
    border: 1px solid #f36469;
    background: #f7e0e4;
    color: #f36469;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box {
    padding: 20px;
    height: calc(100% - 41px);
    overflow: auto;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_details {
    padding-bottom: 10px;
    border-bottom: 1px dashed #cacaca;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_person {
    padding: 10px 0;
    border-bottom: 1px dashed #cacaca;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_person p,
.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_details p {
    height: 32px;
    line-height: 32px;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_details p:first-child {
    height: 22px;
    line-height: 1;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird h3,
.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird .t_d_content_info_box_gird_title div {
    margin-top: 7px;
    font-size: 14px;
    color: #6d6d6d;
    height: 40px;
    line-height: 40px;
    float: left;
    cursor: pointer;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird .t_d_content_info_box_gird_title span:hover {
    color: #42607c;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird .t_d_content_info_box_gird_title div {
    float: right;
    color: #637e99;
    margin-left: 16px;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird_all {
    border: 1px solid #cccccc;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird_all .t_d_content_info_box_gird_head {
    background-color: #eaeef0;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird_all .t_d_content_info_box_gird_head>div {
    display: table;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird_all .t_d_content_info_box_gird_head>div>div {
    height: 29px;
    line-height: 30px;
    font-size: 12px;
    display: table-cell;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird_all .t_d_content_info_box_gird_head>div>div:first-child {
    padding-left: 18px;
    width: 130px;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird_all .t_d_content_info_box_gird_head>div>div:nth-child(2) {
    padding-left: 22px;
    width: 120px;
}

.t_d_box .t_d_content .t_d_content_info .t_d_content_info_box_gird_all .t_d_content_info_box_gird_head>div>div:last-child {
    padding-left: 20px;
}

.t_d_content_info_box_gird_body_item,
.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter,
.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter {
    display: table;
    width: 100%;
}

.t_d_content_info_box_gird_body_item .room,
.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .energy {
    display: table-cell;
    vertical-align: middle;
}

.t_d_content_info_box_gird_body_item .room {
    width: 130px;
}

.t_d_content_info_box_gird_body_item .energy_and_meter_box {
    display: table;
    width: 100%;
}

.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .energy {
    width: 120px;
    border: 1px solid #dddddd;
    border-bottom: 0;
}

.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter {
    height: 37px;
    line-height: 37px;
}

.t_d_content_info_box_gird_body_item .room,
.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .meter_item {
    border-top: 1px solid #dddddd;
}

.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .meter_item {
    color: #637e99;
}

/*把hover效果和鼠标手形效果另行定义 未激活时不添加这个样式 */
.meter_other {
    cursor: pointer;
}

.meter_other:hover {
    background-color: #fcfdfe;
}

.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .meter_item:hover .change_meter,
.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .meter_item:hover .set_meter {
    display: block;
}

.t_d_content_info_box_gird_body_item .room,
.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .energy,
.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .meter_item {
    padding-left: 18px;
    position: relative;
}

.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .change_meter,
.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .set_meter {
    color: #637e99;
    position: absolute;
    top: 10px;
    height: 16px;
    line-height: 16px;
    width: 30px;
    text-align: center;
    margin-top: 0;
    display: none;
    cursor: pointer;
}

.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .change_meter:hover,
.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .set_meter:hover {
    color: #42607c;
}

.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .set_meter {
    right: 58px;
}

.t_d_content_info_box_gird_body_item .energy_and_meter_box .energy_and_meter .meter .change_meter {
    right: 14px;
}

.t_d_box .t_d_content .t_d_content_energy {
    height: 100%;
    overflow: auto;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box {
    padding: 20px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item {
    padding: 0 24px 9px;
    border: 1px solid #d9e2e8;
    margin-bottom: 10px;
    position: relative;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item:last-child {
    margin-bottom: 0;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item>i {
    display: block;
    position: absolute;
    width: 70px;
    height: 70px;
    top: 0;
    right: 0;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_title {
    border-bottom: 1px dashed #d9e2e8;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_title h3,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_title p {
    font-size: 16px;
    height: 48px;
    line-height: 48px;
    float: left;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_title p {
    color: #6d6d6d;
    font-size: 12px;
    margin-left: 18px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .no_activate_tip,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .add_up {
    height: 28px;
    line-height: 28px;
    margin-top: 14px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .no_activate_tip {
    color: #cacaca;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .price_and_money,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .check_payment_records {
    height: 34px;
    line-height: 34px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .price_and_money div {
    float: left;
    margin-right: 40px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_title {
    margin-top: 10px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_title p {
    margin-right: 50px;
    height: 32px;
    line-height: 32px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content {
    display: table;
    width: 100%;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_title {
    height: 31px;
    line-height: 31px;
    border-top: 1px solid #ccc;
    background-color: #eaeef0;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con {
    height: 36px;
    line-height: 36px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_title,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    border-bottom: 1px solid #ccc;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_title .energy_this_time,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con .energy_this_time_con {
    width: calc(100% / 3);
    border-left: 1px solid #ccc;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_title .money_this_time,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con .money_this_time_con {
    width: calc(100% / 3);
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_title .operation,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con .operation_con {
    width: calc(100% / 3);
    border-right: 1px solid #ccc;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con .money_this_time_con {
    border-right: 1px solid #ccc;
    border-left: 1px solid #ccc;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_title .energy_this_time,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con .energy_this_time_con,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_title .money_this_time,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con .money_this_time_con,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_title .operation,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con .operation_con {
    padding-left: 18px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con .operation_con span {
    cursor: pointer;
    color: #7189a1;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_content .bills_girds_content_con .operation_con span:hover,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .meter_meter_bills_girds_item .meter_meter_operation_con span:hover {
    color: #42607c;
}


.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .price_and_money .price span:first-child,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .check_payment_records,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .download_and_checked_bills,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .pay_btns .check_record,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .pay_btns .refund_record {
    color: #7189a1;
    cursor: pointer;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .price_and_money .price span:first-child:hover {
    color: #42607c;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .add_up p,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .download_and_checked_bills p,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .bills_girds .bills_girds_title p {
    float: left;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .download_and_checked_bills p,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .check_payment_records span {
    cursor: pointer;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .check_payment_records span:hover {
    color: #42607c;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .add_up .add_up_consumption,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .download_and_checked_bills .download_bills {
    margin-right: 40px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .download_and_checked_bills {
    height: 52px;
    line-height: 52px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .download_and_checked_bills p span:hover {
    color: #42607c;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .close_account>div {
    width: 120px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .pay_all_money {
    height: 44px;
    line-height: 44px;
    text-align: right;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .pay_all_money span {
    font-size: 20px;
    font-weight: 700;
    color: #ff7b7b;
}

.t_d_content_energy_item .t_d_content_energy_item_box .pay_btns .check_record>div,
.t_d_content_energy_item .t_d_content_energy_item_box .pay_btns .refund_record>div {
    height: 30px;
    line-height: 30px;
}

.t_d_content_energy_item .t_d_content_energy_item_box .pay_btns .check_record>div span:hover,
.t_d_content_energy_item .t_d_content_energy_item_box .pay_btns .refund_record>div span:hover {
    color: #42607c;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .pay_btns {
    margin-bottom: 10px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .pay_btns>div {
    float: right;
    margin-left: 20px;
}

.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .pay_btns .check_record>div,
.t_d_box .t_d_content .t_d_content_energy .t_d_content_energy_box .t_d_content_energy_item .t_d_content_energy_item_box .pay_btns .refund_record>div {
    text-align: center;
}


/* 租户详情--点击发送缴费信息提醒弹窗 */

.send_message_confirm {
    width: 520px;
    min-height: 180px;
    padding: 32px 80px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.send_message_confirm p:first-child {
    font-size: 14px;
    color: #7a7a7a;
    height: 28px;
    line-height: 28px;
}

.send_message_confirm p:last-child {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
}

.send_message_confirm>div {
    padding: 39px 60px 0;
}

.send_message_confirm>div>div {
    width: 100px;
}

.send_message_confirm>div .go_send>div,
.send_message_confirm>div .cancel_send>div {
    width: 100%;
}

.send_message_confirm>div .go_send {
    float: left;
}

.send_message_confirm>div .cancel_send {
    float: right;
}


/* 租户详情--点击激活按钮弹窗 */


/* 覆盖控件库样式，解决时间空间太长无法出来的问题 */

.t_p_box .start_lessee_from_list_btn_box .per-modal-custom_con,
.t_d_box .start_lessee_btn_box .per-modal-custom_con {
    overflow: visible;
}

.start_lessee_confirm {
    width: 382px;
    height: 194px;
    padding: 30px 68px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.start_lessee_confirm .start_lessee_text {
    position: relative;
    margin-bottom: 40px;
}

.start_lessee_confirm .start_lessee_text .choose_date_controller {
    height: 30px;
}

.start_lessee_confirm p:first-child {
    height: 34px;
    line-height: 34px;
    font-size: 14px;
    color: #6d6d6d;
    position: relative;
}

.start_lessee_confirm p:first-child span {
    font-size: 12px;
    display: block;
    height: 34px;
    line-height: 34px;
    color: #cacaca;
    position: absolute;
    top: 0;
    left: 66px;
}

.start_lessee_confirm p {
    height: 22px;
    line-height: 22px;
    font-size: 14px;
    color: #a9a9a9;
}

.t_p_box .t_d_choose_date_error_msg,
.t_d_box .t_d_choose_date_error_msg {
    position: absolute;
    top: 64px;
    left: 0;
}

.t_p_box .t_d_choose_date_error_msg p,
.t_d_box .t_d_choose_date_error_msg p {
    font-size: 12px;
    height: 28px;
    line-height: 28px;
    color: #ff8f5c;
    display: none;
}

.t_p_box .t_d_choose_date_error_msg i,
.t_d_box .t_d_choose_date_error_msg i {
    display: block;
    height: 28px;
    width: 16px;
    background: url(/img/tement/error_icon.png) no-repeat left;
    float: left;
}

.start_lessee_confirm .start_lessee_btns {
    width: 240px;
    margin: 0 auto;
}

.start_lessee_confirm .start_lessee_btns .go_start,
.start_lessee_confirm .start_lessee_btns .cancel_start {
    width: 100px;
}

.start_lessee_confirm .start_lessee_btns .go_start>div,
.start_lessee_confirm .start_lessee_btns .cancel_start>div {
    width: 100%;
}

.start_lessee_confirm .start_lessee_btns .go_start {
    float: left;
}

.start_lessee_confirm .start_lessee_btns .cancel_start {
    float: right;
}


/* 点击换表侧弹窗 */

.t_d_box .right_side_change_meter_window {
    width: 400px;
    padding: 30px 0;
    height: 100%;
}

.t_d_box .right_side_change_meter_window .choose_date_controller {
    height: 30px;
}

.t_d_box .right_side_change_meter_window .right_side_change_meter_window_box {
    width: 298px;
    margin: 0 auto;
}

.t_d_box .right_side_change_meter_window h3 {
    font-size: 14px;
    color: #6d6d6d;
    height: 34px;
    line-height: 34px;
}

.t_d_box .right_side_change_meter_window p {
    font-size: 12px;
    color: #333333;
    height: 26px;
    line-height: 26px;
}

.t_d_box .right_side_change_meter_window .meter_number,
.t_d_box .right_side_change_meter_window .energy_types {
    margin-bottom: 22px;
}

.t_d_box .right_side_change_meter_window .change_metter_time {
    margin-bottom: 40px;
}

/*换表中的电表样式  */

.change_meter_number>li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background: #fff;
    height: 31px;
    line-height: 30px;
    border: 1px solid #D9D9D9;
    border-top: none;
    font-size: 12px;
}

.change_meter_number>li .per-inputwrap input:not(.input-error),
.change_meter_number>li .per-inputborder {
    border: none;
}

.change_meter_number>li:first-child {
    border-top: 1px solid #D9D9D9;
    height: 32px;
}

.change_meter_number>li:nth-child(even) {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background: #EAEEF0;
}

.change_meter_number>li>div:first-child {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 130px;
    flex: 0 0 130px;
    text-align: center;
    border-right: 1px solid #D9D9D9;
}

.change_meter_number>li>div:last-child {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

/*改变仪表读数中控件中错误提示位置  */
.change_metter_text .change_meter_number .error-tip {
    top: 5px;
}

/*改变仪表读数中下方错误提示信息样式  */
.change_metter_prompt {
    width: 100%;
    height: 30px;
}


.t_d_box .right_side_change_meter_window .change_meter_btn_box {
    width: 120px;
    margin: 50px auto;
}

.t_d_box .right_side_change_meter_window .change_meter_btn_box>div {
    width: 120px;
}


/* 点击缴费充值缴费控件样式 */

.font_color_blue {
    color: #02a9d1;
}

.font_color_orange {
    color: #fd9055;
}

.font_color_green {
    color: #4aab9a;
}

.font_color_red {
    color: #ff7b7b;
}

.font_color_black {
    color: #333333;
}

.font_color_gray {
    color: #cacaca;
}

.font_color_dark_blue {
    color: #7a94ad;
    cursor: pointer;
}

.refresh {
    width: 12px;
    height: 12px;
    position: relative;
    top: 2px;
    right: 3px;
    display: inline-block;
    background: url(/img/tement/refreshicon.png) no-repeat left;
    background-size: 100% 100%;
    cursor: pointer;
}


.font_color_dark_gray {
    color: #6d6d6d;
}

.window_instrument_set .per-loading-nomal_pic,
.refresh_title .per-loading-nomal_pic {
    width: 12px;
    height: 12px;
    margin: -2px auto;
    display: inline-block;
}


.pay_the_fees_error,
.pay_the_fees_success,
.pay_the_fees_loading,
.pay_the_fees_post_paid,
.t_d_box .top_up_recharge_error,
.t_d_box .top_up_recharge_success,
.t_d_box .top_up_recharge_loading,
.t_d_box .top_up_recharge {
    width: 480px;
    height: 567px;
}

.t_d_box .top_up_recharge_success,
.t_d_box .top_up_recharge {
    height: 587px;
}

.pay_the_fees_error,
.pay_the_fees_success,
.pay_the_fees_loading,
.pay_the_fees_post_paid,
.pay_the_fees_error div,
.pay_the_fees_success div,
.pay_the_fees_loading div,
.pay_the_fees_post_paid div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.pay_the_fees_error>div:first-child,
.pay_the_fees_success>div:first-child,
.pay_the_fees_loading>div:first-child,
.pay_the_fees_post_paid>div:first-child,
.t_d_box .top_up_recharge_error>div:first-child,
.t_d_box .top_up_recharge_success>div:first-child,
.t_d_box .top_up_recharge_loading>div:first-child,
.t_d_box .top_up_recharge>div:first-child {
    padding: 32px 60px;
    overflow: auto;
    height: calc(100% - 80px);
}

.t_d_box .top_up_recharge>div.top_up_recharge_has_tip {
    height: calc(100% - 108px);
}

.t_d_box .top_up_recharge>div>div>p.font_size_big {
    margin-bottom: 14px;
}

.t_d_box .top_up_recharge>div>div>p.special_tips {
    height: 12px;
    line-height: 12px;
    font-size: 12px;
    margin-bottom: 0;
}

.pay_the_fees_loading {
    position: relative;
}

.pay_the_fees_loading p,
.t_d_box .top_up_recharge_loading p {
    width: 100%;
    text-align: center;
    z-index: 100;
    position: absolute;
    top: 325px;
    left: 0;
}

.pay_the_fees_error>div>div,
.pay_the_fees_success>div>div,
.pay_the_fees_post_paid>div>div,
.t_d_box .top_up_recharge_error>div>div,
.t_d_box .top_up_recharge_success>div>div,
.t_d_box .top_up_recharge>div>div {
    font-size: 14px;
    margin-bottom: 15px;
}

.pay_the_fees_post_paid>div>div.prev_bills {
    margin-bottom: 20px;
}

.pay_the_fees_error>div>div:last-child,
.pay_the_fees_success>div>div:last-child,
.pay_the_fees_post_paid>div>div:last-child,
.t_d_box .top_up_recharge_error>div>div:last-child,
.t_d_box .top_up_recharge_success>div>div:last-child,
.t_d_box .top_up_recharge>div>div:last-child {
    margin-bottom: 0;
}

.pay_the_fees_error .bills p,
.pay_the_fees_success .bills p,
.pay_the_fees_post_paid .bills p,
.t_d_box .top_up_recharge_error>div>div>p,
.t_d_box .top_up_recharge_success>div>div>p,
.t_d_box .top_up_recharge>div>div>p {
    margin-bottom: 22px;
}

.pay_the_fees_error .bills p:last-child,
.pay_the_fees_success .bills p:last-child,
.pay_the_fees_post_paid .bills p:last-child,
.t_d_box .top_up_recharge_error>div>div>p:last-child,
.t_d_box .top_up_recharge_success>div>div>p:last-child,
.t_d_box .top_up_recharge>div>div>p:last-child {
    margin-bottom: 0;
}

.pay_the_fees_error .bills h3,
.pay_the_fees_error .bills p,
.pay_the_fees_error>div>div>h3,
.pay_the_fees_success .bills h3,
.pay_the_fees_success .bills p,
.pay_the_fees_success>div>div>h3,
.pay_the_fees_post_paid .bills h3,
.pay_the_fees_post_paid .bills p,
.pay_the_fees_post_paid>div>div>h3,
.t_d_box .top_up_recharge_error>div>div>h3,
.t_d_box .top_up_recharge_error>div>div>p,
.t_d_box .top_up_recharge_success>div>div>h3,
.t_d_box .top_up_recharge_success>div>div>p,
.t_d_box .top_up_recharge>div>div>h3,
.t_d_box .top_up_recharge>div>div>p {
    height: 30px;
    line-height: 30px;
}

.pay_the_fees_error>div>div p.font_size_big,
.pay_the_fees_success>div>div p.font_size_big,
.pay_the_fees_post_paid>div>div p.font_size_big,
.t_d_box .top_up_recharge_error>div>div p.font_size_big,
.t_d_box .top_up_recharge_success>div>div p.font_size_big,
.t_d_box .top_up_recharge>div>div p.font_size_big {
    height: 35px;
    line-height: 46px;
    font-size: 30px;
}

.pay_the_fees_error>div>div p.font_size_big span,
.pay_the_fees_success>div>div p.font_size_big span,
.pay_the_fees_post_paid>div>div p.font_size_big span,
.t_d_box .top_up_recharge_error>div>div p.font_size_big span,
.t_d_box .top_up_recharge_success>div>div p.font_size_big span,
.t_d_box .top_up_recharge>div>div p.font_size_big span {
    font-size: 14px;
}

.pay_the_fees_error .btns,
.pay_the_fees_success .btns,
.pay_the_fees_post_paid .btns,
.t_d_box .top_up_recharge_success .btns,
.t_d_box .top_up_recharge_error .btns,
.t_d_box .top_up_recharge .btns {
    margin: 20px 0 27px;

}

.pay_the_fees_error .btns>div,
.pay_the_fees_success .btns>div,
.pay_the_fees_post_paid .btns>div,
.t_d_box .top_up_recharge_success .btns>div,
.t_d_box .top_up_recharge_error .btns>div,
.t_d_box .top_up_recharge .btns>div {
    width: 240px;
    margin: 0 auto;
}

.pay_the_fees_error .btns>div>div>div,
.pay_the_fees_success .btns>div>div>div,
.pay_the_fees_post_paid .btns>div>div>div,
.t_d_box .top_up_recharge_success .btns>div>div>div,
.t_d_box .top_up_recharge_error .btns>div>div>div,
.t_d_box .top_up_recharge .btns>div>div>div {
    width: 100px;
}

.t_d_box .top_up_recharge .btns>div>.print>div,
.t_d_box .top_up_recharge_success .btns>div>.print>div {
    position: absolute;
    bottom: 46px;
    left: 160px;
    width: 160px;
}

.pay_the_fees_error .btns>div>div:first-child,
.pay_the_fees_post_paid .btns>div>div:first-child,
.t_d_box .top_up_recharge_success .btns>div>div:first-child,
.t_d_box .top_up_recharge_error .btns>div>div:first-child,
.t_d_box .top_up_recharge .btns>div>div:first-child {
    float: left;
}

.pay_the_fees_error .btns>div>div:last-child,
.pay_the_fees_post_paid .btns>div>div:last-child,
.t_d_box .top_up_recharge_success .btns>div>div:last-child,
.t_d_box .top_up_recharge_error .btns>div>div:last-child,
.t_d_box .top_up_recharge .btns>div>div:last-child {
    float: right;
}

.t_d_box .top_up_recharge_tip p {
    height: 28px;
    line-height: 28px;
    background-color: #fff9f5;
    text-align: center;
}

.t_d_box .top_up_recharge .ipt {
    margin-bottom: 30px;
}

.pay_the_fees_success .btn_1>div>div,
.t_d_box .top_up_recharge_success .btn_1>div>div {
    padding: 0 70px !important;
    float: none;
}

.t_d_box .top_up_recharge_error p.ptext,
.t_d_box .top_up_recharge_success p.ptext {
    height: 48px !important;
    line-height: 24px !important;
    margin-bottom: 32px !important;
}

.pay_the_fees_post_paid .bills {
    padding: 10px 0;
    border-top: 1px dashed #cacaca;
}

.pay_the_fees_post_paid .bills>div {
    margin-bottom: 4px;
}

.pay_the_fees_post_paid .bills>div:last-child {
    margin-bottom: 0;
}

.t_d_box .software_software_bills_girds_item,
.t_d_box .meter_meter_bills_girds_item {
    margin-top: 20px;
}

.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_title,
.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_con {
    border-left: 1px solid #ccc;
}

.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_title,
.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_con {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_title>div,
.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_con>div {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding-left: 18px;
}

.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_title>div {
    height: 32px;
    line-height: 32px;
    background-color: #eaeef0;
    border-right: 1px solid #ccc;
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
}


.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_con>div {
    height: 36px;
    line-height: 36px;
    border-bottom: 1px solid #cccccc;
    border-right: 1px solid #cccccc;
}

.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_con>div:last-child {
    border-right: 1px solid #cccccc;
}

.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_con>div:last-child>span {
    cursor: pointer;
    margin-right: 10px;
}

.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_con>div:last-child>span:hover {
    color: #42607c;
}

.t_d_box .meter_meter_bills_girds_item .meter_meter_bills_girds_content_title>div.bor_l_none {
    border-left: 0;
}

.pay_the_fees_loading_box .per-modal-custom_title,
.t_d_box .top_up_recharge_loading_box .per-modal-custom_title,
.t_d_box .refund_cost_loading_box .per-modal-custom_title {
    background-color: #44b3ce;
}

.pay_the_fees_success_box .per-modal-custom_title,
.t_d_box .top_up_recharge_software_success_box .per-modal-custom_title,
.t_d_box .refund_cost_success_box .per-modal-custom_title {
    background-color: #68c5b3;
}

.pay_the_fees_success_box .per-modal-custom_title .per-notice_successicon {
    position: relative;
    top: -2px;
}

.pay_the_fees_error_box .per-notice_failureicon {
    position: relative;
    top: -2px;
}

.pay_the_fees_error_box .per-modal-custom_title,
.t_d_box .top_up_recharge_software_error_box .per-modal-custom_title,
.t_d_box .refund_cost_error_box .per-modal-custom_title {
    background-color: #ff7b7b;
}

.t_d_box .police_box {
    position: absolute;
    top: 114px;
    right: 154px;
}

.t_d_box .police_box {
    width: 640px;
    box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.12);
    background-color: #fff;
}

.t_d_box .police_box .plice_radio {
    padding: 0 40px;
}

.t_d_box .police_box .plice_radio>div {
    float: right;
    padding: 10px 0;
    margin-left: 30px;
}

.t_d_box .police_box .plice_radio>div>div {
    vertical-align: middle;
}

.t_d_box .police_box .police>div {
    padding: 0 40px;
}

.t_d_box .police_box .police .plice_sel {
    width: 100%;
    padding: 16px 0;
    border-top: 1px dashed #E3E3E3;
    border-bottom: 1px dashed #E3E3E3;
}

.t_d_box .police_box .police .plice_ope {
    padding: 38px 218px 41px;
}

.t_d_box .police_box .police .static_font {
    font-weight: 700;
    font-size: 14px;
    margin: 0 6px;
}

.allAmount {
    color: #02a9d1;
    cursor: pointer;
}