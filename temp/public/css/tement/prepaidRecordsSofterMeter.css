/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

.p_r_s_m_box div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.p_r_s_m_box {
    width: 100%;
    height: 100%;
}

.p_r_s_m_box .p_r_s_m_header {
    height: 50px;
    border-bottom: 1px solid #D9E2E8;
    padding: 10px 20px 9px;
    position: relative;
}

.p_r_s_m_box .p_r_s_m_header .p_r_s_m_go_back {
    position: absolute;
}

.p_r_s_m_box .p_r_s_m_header h1 {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    color: #333;
    text-align: center;
}

.p_r_s_m_box .p_r_s_m_gird_box {
    height: 100%;
    max-height: calc(100% - 50px);
    padding: 20px;
    padding-top: 0;
    /* overflow: auto; */
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird_operation {
    padding: 10px 0;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird_operation>div>div {
    height: 30px;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird_operation .p_r_s_m_gird_operation_left {
    float: left;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird_operation .p_r_s_m_gird_operation_right {
    float: right;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird_operation .p_r_s_m_gird_operation_left>div {
    float: left;
    margin-right: 10px;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird_operation .p_r_s_m_gird_operation_left .energy_type_box {
    width: 222px;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird_operation .p_r_s_m_gird_operation_left .check_activate_time {
    line-height: 36px;
    margin-left: 20px;
}


.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird {
    width: 100%;
    height: calc(100% - 50px);
    font-size: 14px;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_tit {
    display: flex;
    border: 1px solid #CCCCCC;
    border-bottom: none;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_tit>div {
    flex: 1;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    background-color: #8C959A;
    border-right: 1px solid #8C959A;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_tit>div:last-child {
    border-right: none;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body {
    height: calc(100% - 30px);
    overflow: auto;
    border: 1px solid #CCCCCC;
    border-top: none;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item {
    display: flex;
    text-align: center;
    color: #333333;
    border-top: 1px solid #CCCCCC;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item>div,
.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item>div>div {
    position: relative;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item>div>span,
.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item>div>div>span {
    display: block;
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item .p_r_s_m_gird_item_1 .p_r_s_m_gird_item_2,
.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item .p_r_s_m_gird_item_1 {
    border-bottom: 1px solid #CCCCCC;
    height: 36px;
    line-height: 36px;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item .p_r_s_m_gird_item_1 .p_r_s_m_gird_item_2:last-child,
.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item .p_r_s_m_gird_item_1:last-child {
    border-bottom: none;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item:first-child {
    border-top: none;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item>div {
    flex: 1;
    border-right: 1px solid #CCCCCC;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item>div:last-child {
    border-right: none;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item:nth-child(odd) {
    background-color: #FFFFFF;
}

.p_r_s_m_box .p_r_s_m_gird_box .p_r_s_m_gird .p_r_s_m_gird_body .p_r_s_m_gird_item:nth-child(even) {
    background-color: #f5f7fa;
}