/* 能耗费用报表-电 */

/*头部  */
.mainBody .energyCost_title {
    height: 50px;
    line-height: 50px;
    border-bottom: 1px solid #d9e2e8;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 20px;
}

/*头部左侧返回按钮  */
#energyCost_title_button {
    width: 80px;
    height: 30px;
    font-size: 14px;
    margin-top: 10px;
}

/*头部中央标题  */
#energyCost_title_con {
    font-size: 16px;
}

/*头部右侧下载列表按钮  */
#energyCost_title_button2 {
    width: 174px;
    height: 30px;
    font-size: 14px;
    margin-top: 10px;
}

/*时间栏  */
.mainBody .energyCost_time {
    padding: 10px 20px;
    font-size: 14px;
}

.mainBody .energyCost_time .per-calendar-text {
    width: 212px;
}

/*表格部分  */
.energyCost_wrap {
    width: 100%;
    overflow: auto;
}

.mainBody .energyCost_table {
    padding: 0 20px;
    border-left: none;
    font-size: 12px;
}

/*表格部分  表头  */
.mainBody .energyCost_table .energyCost_table_top {
    height: 60px;
    line-height: 60px;
    text-align: center;
    background: #8b959a;
    color: white;
    font-weight: 600;
}

.mainBody .energyCost_table .energyCost_table_top ul li>div.slh>div {
    border-right: 1px solid #9faaaf;
}

/*表头右边  */
.mainBody .energyCost_table .energyCost_table_top .energyCost_table_top_right {
    -webkit-box-flex: 5;
    -ms-flex: 5;
    flex: 5;
}

.mainBody .energyCost_table .energyCost_table_top .energyCost_table_top_right>div {
    line-height: 30px;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-right: 1px solid #9faaaf;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.mainBody .energyCost_table .energyCost_table_top .energyCost_table_top_right>div:last-child {
    border-right: none;
}

.mainBody .energyCost_table .energyCost_table_top .energyCost_table_top_right>div>._timer {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

/*租户编号  */
.energyCost_table_top .tenentNo,
.tenentNo {
    min-width: 158px;
    height: 60px;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

/*租户名称  */
.energyCost_table_top .tenentName,
.tenentName {
    min-width: 370px;
    height: 60px;
    -webkit-box-flex: 2;
    -ms-flex: 2;
    flex: 2;
}

.tenentBodyName {
    min-width: 369px;
    height: 60px;
    -webkit-box-flex: 1.99;
    -ms-flex: 1.99;
    flex: 1.99;
}

/*右下  */
.energyCost_table_top_right_bottom {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border-top: 1px solid #9faaaf;
    min-width: 201px;
}

.energyCost_table_top_right_bottom>div {
    width: 50%;
    float: left;
    border-right: 1px solid #9faaaf;
    height: 30px;
    overflow: hidden;
    box-sizing: border-box;
}

.energyCost_table_top_right_bottom>div:last-child {
    border-right: none
}


/*表格  合计部分  */
.mainBody .energyCost_table .energyCost_table_center {
    height: 36px;
    line-height: 36px;
    text-align: center;
    background: #e9f0f4;
    font-weight: 900;
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
}

/*合计  */
.energyCost_table_center .total {
    min-width: 529px;
    -webkit-box-flex: 3;
    -ms-flex: 3;
    flex: 3;
}

.mainBody .energyCost_table .energyCost_table_center ul li>div {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.mainBody .energyCost_table .energyCost_table_center .energyCost_table_center_right {
    -webkit-box-flex: 5;
    -ms-flex: 5;
    flex: 5;
}

.mainBody .energyCost_table .energyCost_table_center .energyCost_table_center_right div {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-right: 1px solid #ccc;
    min-width: 100px;
}

.mainBody .energyCost_table .energyCost_table_center .energyCost_table_center_right div:last-child {
    border-right: none;
}

/*表格  内容部分  */
.mainBody .energyCost_table .energyCost_table_body {
    max-height: calc(100vh - 227px);
    overflow: hidden;
    border-bottom: 1px solid #ccc;
}

.mainBody .energyCost_table .energyCost_table_body ul li>div {
    border-right: 1px solid #ccc;
    text-align: center;
    height: 36px;
    line-height: 36px;
}

.mainBody .energyCost_table .energyCost_table_body ul li>div:last-child {
    border-right: none;
}

.mainBody .energyCost_table .energyCost_table_body ul li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
}

.mainBody .energyCost_table .energyCost_table_body ul li:last-child {
    border-bottom: none;
}

.mainBody .energyCost_table .energyCost_table_body ul li:nth-child(2n) {
    background: #f5f7fa;
}


/*部分位置调节  */
.mainBody .energyCost_table .energyCost_table_body .energyCost_table_body_bottom {
    -webkit-box-flex: 5;
    -ms-flex: 5;
    flex: 5;
}

.mainBody .energyCost_table .energyCost_table_body .energyCost_table_body_bottom div {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-right: 1px solid #ccc;
    min-width: 100px;
}


.inline {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}