._disable {
    opacity: .3;
    pointer-events: none;
}

.slh {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.addTementPage {
    height: 100%;
    position: relative;
}

.addTementPage .header {
    height: 52px;
}

.addTementPage .header .cancelBtn {
    float: left;
    margin-left: 20px;
    margin-top: 11px;
}

.addTementPage .header .title {
    font-size: 16px;
    margin: 0 auto;
    text-align: center;
    display: block;
    width: 300px;
    color: #333;
    height: 52px;
    line-height: 52px;
}

.stylenextpageFor {
    margin-left: 58%;
    margin-bottom: 4%;
}


.addTementPage .body {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: calc(100% - 110px);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    position: absolute;
    left: 20px;
    right: 20px;
    border: 1px solid #d9e2e8;
}

.addTementPage .body .step_detail {
    border-top: none;
    position: absolute;
    top: 92px;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: auto;
}

.addTementPage .body .step_detail .basic_information {
    width: 520px;
    margin: auto;
    padding: 0 20px;
    height: 100%;
}

.addTementPage .body .step_detail .basic_information .input_comboxPart {
    margin: 34px 0;
}

.addTementPage .body .step_detail .basic_information .input_comboxPart label {
    margin-bottom: 5px;
    display: inline-block;
}

.addTementPage .body .step_detail .basic_information .input_comboxPart .remindind {
    color: #fd9054;
    margin-left: 10px;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup {
    margin: 34px 0;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup li {
    margin-top: 30px;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup li .minusBtn {
    display: none;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .contact {
    float: left;
    width: 220px;
    margin-right: 10px;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .contact label {
    display: inline-block;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .contact_number {
    float: left;
    width: 290px;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .contact_number label {
    display: inline-block;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .newAddList {
    background: #f8f8f8;
    height: 84px;
    border: 1px solid #eee;
    padding: 10px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .newAddList .minusBtn {
    display: block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    color: #cacaca;
    position: absolute;
    top: 3px;
    right: 3px;
    cursor: pointer;
    font-family: perficon;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .newAddList .contact {
    float: left;
    width: 210px;
    margin-right: 8px;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .contact label {
    display: inline-block;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .newAddList .contact_number {
    float: left;
    width: 280px;
}

.addTementPage .body .step_detail .basic_information .input_comboxPartGroup .contact_number label {
    display: inline-block;
}

.input_comboxPartGroup .addContact {
    color: #637E99;
    margin-top: 16px;
    cursor: pointer;
    width: 88px;
}

.input_comboxPartGroup .addContact em {
    font-family: perficon;
}

.floot {
    border: 1px solid #d9e2e8;
    position: absolute;
    bottom: 10px;
    height: 48px;
    left: 20px;
    right: 20px;
    border-top: none;
}

.floot .floot_btnWrapper {
    width: 400px;
    margin: 0 auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.floot .floot_btnWrapper>div {
    width: 180px;
    margin: 9px 10px 0;
}

.basic_information2 {
    width: 640px;
    margin: auto;
    padding: 0 20px;
    height: 100%;
}

.basic_information2 .roomPart {
    min-width: 640px;

}

.basic_information2 .roomPart label {
    margin: 10px 0;
    display: inline-block;
}

.basic_information2 .roomPart .roomPart_list>li {
    margin: 10px 0;
    position: relative;
    padding: 10px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.basic_information2 .roomPart .roomPart_list>li:first-child {
    margin-top: 0;
}

.basic_information2 .roomPart .roomPart_list>li .minusroomBtn,
.minusformulaBtn {
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    color: #cacaca;
    position: absolute;
    top: 3px;
    right: 3px;
    cursor: pointer;
    font-family: perficon;
}

.basic_information2 .roomPart .roomPart_list>li .roomNo {
    color: #6d6d6d;
    margin-bottom: 10px;
}

.basic_information2 .roomPart .roomPart_list>li .roomNo em {
    color: #333;
}

.basic_information2 .roomPart .roomPart_list .nodata {
    width: 640px;
    height: 112px;
    background: #f8f8f8;
    line-height: 92px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    color: #ccc;
}

.addRoomBtn,
.addMeterformula {
    margin: 10px 0;
    cursor: pointer;
    color: #637E99;
}

.addRoomBtn em,
.addMeterformula em {
    font-family: perficon;
}

.addRoomBtn {
    width: 76px;
}

.addMeterformula {
    width: 154px;
}

.addMeterformula .formulaIcon {
    border: 1px solid currentcolor;
    display: inline-block;
    width: 14px;
    height: 14px;
    font-size: 14px;
    text-align: center;
    border-radius: 50%;
    margin-left: 5px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    top: 2px;
    overflow: hidden;
}

.addMeterformula .formulaIcon:hover {
    overflow: visible;
}

.addMeterformula .formulaIcon>b {
    position: relative;
    top: -2px;
}

.addMeterformula .tips {
    color: #333333;
    position: absolute;
    bottom: 21px;
    left: -100px;
    display: inline-block;
    border: 1px solid #d9e2e8;
    background: #fff;
    width: 200px;
    text-align: left;
    padding: 5px;
    line-height: 24px;
    z-index: 20;
}

.addMeterformula .tips em {
    color: #999999;
}

.roomMeter_list {
    background: #f8f8f8;
    border: 1px solid #eee;
    height: 112px;
    position: relative;
}

.meter_list {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.meter_list>li {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-top: 1px solid #d9e2e8;
    border-bottom: 1px solid #d9e2e8;
    height: 60px;
    overflow: hidden;
}

.meter_list>li:last-child {
    border-right: 1px solid #d9e2e8;
}

.meter_list>li:first-child {
    border-left: 1px solid #d9e2e8;
}

.meter_list>li div:first-child {
    background: #eaeef0;
}

.meter_list>li div:last-child {
    background: #fff;
}

.meter_list>li div {
    height: 30px;
    line-height: 30px;
    padding-left: 10px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}


.formula_title {
    margin: 10px 0;
}

.formulaList>li {
    padding: 20px 10px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background: #f8f8f8;
    border: 1px solid #eee;
    height: 140px;
    position: relative;
    margin-bottom: 10px;
}

.formulaList>li .formula_combox {
    width: 206px;
}

.choosesystem>li {
    border: 1px solid #D9D9D9;
    min-height: 48px;
    margin: 10px 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 20px;

}

.choosesystem_remindind>i,
.choosesystem_remind>i,
.plan_price_remind>i,
.plan_price_space>i {
    width: 14px;
    height: 14px;
    background: #f28b37;
    margin: 4px 5px 5px 0;
    float: left;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    line-height: 14px;
    border-radius: 50%;
    color: #fff;
}

.choosesystem_remind {
    padding-top: 20px;
    color: #FD9054;
}

.system_list_head {
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid #eee;
}

.system_list_body {
    display: none;
}

.system_list_body>div {
    margin: 10px 0;
    position: relative;
}

.system_list_body>div>label {
    margin: 10px 0;
    display: inline-block;
}

.managePricePlan {
    position: absolute;
    top: 12px;
    right: 0;
    color: #637e99;
    cursor: pointer;
}

.checkPriceDetail {
    display: inline-block;
    margin-top: 10px;
    color: #637e99;
    cursor: pointer;
}

.chooseRoomTemp_wrap {
    padding: 20px 20px 20px;
    width: 520px;
    height: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.chooseRoomTemp_wrap .chooseRoomTemp_wrap_top {
    height: 50px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50px;
    flex: 0 0 50px;
    background: #eaeef0;
    padding: 10px 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.chooseRoomTemp_combox,
.chooseRoomTemp_search {
    float: left;
    width: calc(50% - 10px);
}

.chooseRoomTemp_combox {
    margin-right: 20px;
}

.chooseRoomTemp_wrap_foot {
    height: 50px;
    line-height: 50px;
    -webkit-box-flex: 0;
    -ms-flex: 0 0 50px;
    flex: 0 0 50px;
    text-align: center;
}

.chooseRoomTemp_wrap_foot>div {
    width: 180px;
}

.chooseRoomTemp_wrap_body li {
    height: 30px;
    line-height: 36px;
    background: #fff;
    padding-left: 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.chooseRoomTemp_wrap_body li:nth-child(even) {
    background: #f4f6f9;
}

.price_plan_wrapper {
    padding: 20px 20px 0;
    width: 520px;
    height: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.price_plan_wrapper>div {
    margin-bottom: 30px;
}

.price_plan_wrapper .plan_name lable {
    margin: 10px 0;
    display: inline-block;

}

.price_plan_wrapper .electricitypriceType>div {
    float: left;
    margin-right: 20px;
    margin-bottom: 10px;
}

.time_electricity_price {
    margin-top: 30px
}

.time_electricity_price>li {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background: #fff;
    height: 30px;
    line-height: 30px;
    border: 1px solid #D9D9D9;
    border-top: none;
}

.time_electricity_price>li .per-inputwrap input:not(.input-error),
.time_electricity_price>li .per-inputborder {
    border: none;
}

.time_electricity_price>li:first-child {
    border-top: 1px solid #D9D9D9;
}

.time_electricity_price>li:nth-child(even) {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    background: #EAEEF0;
}

.time_electricity_price>li>div:first-child {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 100px;
    flex: 0 0 100px;
    text-align: center;
    border-right: 1px solid #D9D9D9;
}

.time_electricity_price>li>div:last-child {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.time_electricity_price>li>div .textState {
    padding-left: 20px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.time_electricity_price .averagePrice {
    background: #eaeef0;
}

.plan_price_remind,
.plan_price_space {
    color: #fd9054;
    margin-bottom: 10px !important;
}

.plan_price_space {
    display: none;
}

.managePrivcePlanPage .body {
    height: calc(100% - 50px);
    bottom: 0;
    top: 50px;
    border-left: none;
    border-right: none;
    border-bottom: none;
}

.managePrivcePlanPage .newPricePlanBtn {
    float: right;
    margin-right: 20px;
    margin-top: 11px;
}

.managePrivce_grid_wrapper {
    height: 100%;
    font-size: 12px;
}

.managePrivce_grid_title {
    height: 29px;
    color: #000000;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border-bottom: 1px solid #eee;
}

.managePrivce_grid_title>li {
    line-height: 29px;
    padding-left: 20px;
    color: #000000;
}

.managePrivce_grid_title>li.name,
.managePrivce_grid_body>li>div.name {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 96px;
    flex: 0 0 96px
}

.managePrivce_grid_title>li.type,
.managePrivce_grid_body>li>div.type {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 96px;
    flex: 0 0 96px
}

.managePrivce_grid_title>li.plandetail,
.managePrivce_grid_body>li>div.plandetail {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.managePrivce_grid_body {
    border-bottom: 1px solid #eee;
    max-height: 100%;
}

.managePrivce_grid_body>li {
    height: 36px;
    background: #f4f6f9;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    line-height: 36px;
}

.managePrivce_grid_body>li:nth-child(even) {
    background: #fff;
}

.managePrivce_grid_body>li>div {
    padding-left: 20px;
    color: #333;
}

.managePrivce_grid_body_wraper {
    height: calc(100% - 34px);
    overflow: auto;
}

.managePrivce_grid_body>li>div.plandetail>span {
    margin-right: 10px;
}

.managePrivce_grid_body>li>h2 {
    cursor: pointer;
    display: none;
}

.managePrivce_grid_body>li:hover>h2 {
    display: block;
}

.managePrivce_grid_body>li>h2>em {
    font-family: perficon;
    font-size: 14px;
}

.managePrivce_grid_body>li .managePrivce_grid_edit {
    color: #637e99;
    margin-right: 40px;
}

.managePrivce_grid_body>li .managePrivce_grid_delete {
    color: #FF7B7B;
    margin-right: 20px;
}

.moveTement_wrapper {
    width: 458px;
    height: 274px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 50px 90px 0;
}

.moveTement_wrapper .title {
    margin-bottom: 10px;
}

.moveTement_wrapper .btn_wrapper {
    margin-top: 46px;
    text-align: center;
}

.moveTement_wrapper .btn_wrapper>div {
    width: 130px;
}

.moveTement_wrapper .btn_wrapper>div:first-child {
    margin-right: 18px;
}