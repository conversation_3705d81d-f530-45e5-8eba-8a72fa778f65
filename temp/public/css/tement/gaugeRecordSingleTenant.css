/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

.g_r_s_t_box div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.g_r_s_t_box {
    width: 100%;
    height: 100%;
}

.g_r_s_t_box .g_r_s_t_header {
    height: 50px;
    border-bottom: 1px solid #D9E2E8;
    padding: 10px 20px 9px;
    position: relative;
}

.g_r_s_t_box .g_r_s_t_header .g_r_s_t_go_back {
    position: absolute;
}

.g_r_s_t_box .g_r_s_t_header h1 {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    color: #333;
    text-align: center;
}

.g_r_s_t_box .g_r_s_t_gird_box {
    height: 100%;
    max-height: calc(100% - 50px);
    padding: 20px;
    padding-top: 0;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation {
    padding: 10px 0;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation>div>div {
    height: 30px;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_left {
    float: left;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_right {
    float: right;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_left>div {
    float: left;
    margin-right: 10px;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_left .energy_type_box {
    width: 222px;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_left .check_activate_time {
    line-height: 36px;
    margin-left: 20px;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_left .time_hour_box,
.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_left .time_controller_box {
    width: 160px;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_left .time_controller_box ._time-left,
.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_left .time_controller_box ._time-right {
    display: none;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird_operation .g_r_s_t_gird_operation_left .time_controller_box .per-calendar-title {
    padding: 0;
    width: 160px;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird {
    width: 100%;
    height: calc(100% - 50px);
    font-size: 14px;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_tit {
    display: flex;
    border: 1px solid #CCCCCC;
    border-bottom: none;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_tit>div {
    flex: 1;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #FFFFFF;
    background-color: #8C959A;
    border-right: 1px solid #8C959A;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_tit>div:last-child {
    border-right: none;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body {
    height: calc(100% - 30px);
    overflow: auto;
    border: 1px solid #CCCCCC;
    border-top: none;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item {
    display: flex;
    text-align: center;
    color: #333333;
    border-top: 1px solid #CCCCCC;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item>div,
.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item>div>div {
    position: relative;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item>div>span,
.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item>div>div>span {
    display: block;
    width: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item .g_r_s_t_gird_item_1 .g_r_s_t_gird_item_2,
.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item .g_r_s_t_gird_item_1 {
    border-bottom: 1px solid #CCCCCC;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item .g_r_s_t_gird_item_1 .g_r_s_t_gird_item_2 {
    height: 36px;
    line-height: 36px;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item .g_r_s_t_gird_item_1 .g_r_s_t_gird_item_2:last-child,
.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item .g_r_s_t_gird_item_1:last-child {
    border-bottom: none;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item:first-child {
    border-top: none;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item>div {
    flex: 1;
    border-right: 1px solid #CCCCCC;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item>div:last-child {
    border-right: none;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item:nth-child(odd) {
    background-color: #FFFFFF;
}

.g_r_s_t_box .g_r_s_t_gird_box .g_r_s_t_gird .g_r_s_t_gird_body .g_r_s_t_gird_item:nth-child(even) {
    background-color: #f5f7fa;
}