/*清除浮动*/
.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

.p_r_box div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.p_r_box {
    width: 100%;
    height: 100%;
}

.p_r_box .p_r_header {
    height: 50px;
    border-bottom: 1px solid #d9e2e8;
    padding: 10px 20px 9px;
    position: relative;
}

.p_r_box .p_r_header .p_r_go_back,
.p_r_box .p_r_header .p_r_download {
    position: absolute;
}

.p_r_box .p_r_header .p_r_download {
    right: 20px;
    top: 10px;
}

.p_r_box .p_r_header h1 {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    color: #333;
    text-align: center;
}

.p_r_box .p_r_content {
    height: calc(100% - 60px);
    padding: 10px 20px;
}

.p_r_box .p_r_content .p_r_choose_time {
    margin-bottom: 9px;
}

.p_r_box .p_r_content .p_r_gird {
    height: calc(100% - 30px);
}

.p_r_box .per-grid-normal {
    padding-bottom: 0;
}