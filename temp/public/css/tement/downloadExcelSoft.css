.report_wrapper {
    font-size: 12px;
    width: calc(100%-40px);
    min-width: 1220px;
    margin: 10px 20px;
    border: 1px solid #ccc;
}

.recharge_table_top ul li.inline,
.recharge_table_body ul li.inline {
    font-size: 12px;
    width: 100%;
    min-height: 30px;
    display: flex;
    color: #fff;
    min-width: 1220px;
    border-bottom: 1px solid #ccc;
    box-sizing: border-box;
    background-color: #8C959A;
}

.recharge_table_body ul li.inline {
    color: #000;
    background-color: #fff;
}

ul li .recharge_table_name,
ul li .recharge_table_num,
ul li .recharge_table_home,
ul li .recharge_table_con_time,
ul li .recharge_table_con_num,
ul li .recharge_table_con_money {
    display: flex;
    justify-content: center;
    align-items: center;
}

ul li .recharge_table_name {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: 256px;
    border-right: 1px solid #ccc;
}

ul li .recharge_table_num {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: 180px;
    border-right: 1px solid #ccc;
}

ul li .recharge_table_home {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: 190px;
    border-right: 1px solid #ccc;
}

ul li .recharge_table_con,
.recharge_table_body_right {
    flex: 1;
    display: flex;
    flex-direction: column;
}

ul li .recharge_table_con,
.recharge_table_body_right .recharge_table_con {
    display: flex;
    flex-direction: row;
    min-height: 30px;
}

ul li .recharge_table_con .recharge_table_con_time {
    flex: 4;
    border-right: 1px solid #ccc;
}

ul li .recharge_table_con .recharge_table_con_num {
    flex: 3;
    border-right: 1px solid #ccc;
}

ul li .recharge_table_con .recharge_table_con_money {
    flex: 2;
    border-right: 1px solid #ccc;
}

.recharge_table_body_right .recharge_table_con {
    border-bottom: 1px solid #ccc;
}

ul li .recharge_table_con div:nth-last-child(1) {
    border-right: none;
}

.recharge_table_body ul li:nth-last-child(1),
.recharge_table_body_right .recharge_table_con:nth-last-child(1) {
    border-bottom: none;
}

/* 隔行变色 */
.recharge_table_body ul li:nth-of-type(2n) {
    background-color: #F5F7FA;
}