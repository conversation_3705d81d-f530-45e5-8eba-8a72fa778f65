/* 初始化样式 */

body {
    width: 100%;
    height: 100%;
    min-width: 1280px;
    font-family: "MicrosoftYaHei";
    position: relative;
}

input {
    color: #333;
}

input::-webkit-input-placeholder {
    color: #cacaca;
}


/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

._left {
    float: left;
}

._right {
    float: right;
}

._hide {
    display: none !important;
}

.disabled {
    opacity: 0.3;
    pointer-events: none;
}

.noClick {
    pointer-events: none;
    cursor: default;
}

.slh {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}


/* 正常样式 */

.tab_page {
    height: 100%;
    padding: 14px 10px 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.tab_select {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
}


/* 后台配置 */

.tab_select #backstage_config {
    padding: 20px;
    height: 100%;
}

.tab_select #backstage_config>div {
    width: 100%;
}

.tab_select #backstage_config>div>h2 {
    color: #333333;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
}

.tab_select #backstage_config .del_btn {
    color: #ff7b7b;
}

.tab_select #backstage_config .del_btn:hover {
    color: #ef6767;
}


/* 操作记录 */

.tab_select #operating_record {
    padding: 0 20px;
    height: 100%;
}

.tab_select #operating_record div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.tab_select #operating_record .operating_record_header {
    padding: 10px 0;
    border-bottom: 1px solid #eeeeee;
}

.tab_select #operating_record .operating_record_header_left>div {
    width: 196px;
    float: left;
    margin-right: 10px;
}

.tab_select #operating_record .operating_record_header_left>div:last-child {
    margin-right: 0;
}

.tab_select #operating_record .down_load_record {
    height: 30px;
}

.tab_select #operating_record .operating_record_body,
.tab_select .content_body {
    height: calc(100% - 50px);
}

.tab_select #operating_record .page_con_box {
    margin-left: 20px;
}

.tab_select #operating_record .page_con_box>div {
    float: left;
}

.tab_select #operating_record .page_con_box p {
    height: 31px;
    line-height: 31px;
}

.tab_select #operating_record .page_con_box .page_ipt {
    margin: 0 8px;
}

.tab_select #operating_record .page_con_box .page_ipt>div {
    width: 60px;
}

.tab_select #operating_record .page_con_box .page_ipt>div>input {
    text-align: center;
}

.tab_select #operating_record .page_con_box .page_set_btn {
    margin-left: 20px;
}


/* 覆盖原有控件的样式 */

.no_border_bottom {
    border-bottom: none;
    min-height: 30px;
}

.tab_select #backstage_config ._dynamic-title-height {
    border-top: 1px solid #eee;
}

.tab_select #backstage_config .per-grid-dynamic_item {
    padding: 0 20px;
    -webkit-box-flex: 0;
    -ms-flex: none;
    flex: none;
    width: 218px;
}

.tab_select #backstage_config .per-grid-dynamic_item:last-child {
    width: 464px;
}

.tab_select #backstage_config .per-grid-dynamic_item>b {
    color: #000000;
    font-size: 12px;
}

.tab_select #backstage_config .per-grid-dynamic_item:nth-child(2)>div,
.tab_select #backstage_config .per-grid-dynamic_item:last-child>div {
    color: #637e99;
}

.tab_select #backstage_config .per-grid-dynamic_item>div {
    font-size: 12px;
}

.tab_select #backstage_config .per-grid-dynamic_item:last-child div.btn {
    margin-right: 68px;
    cursor: pointer;
    float: left;
}

.tab_select #backstage_config .per-grid-dynamic_item:last-child div.btn:hover {
    color: #425f7c;
}

.tab_select #backstage_config .per-grid-dynamic_item:last-child div.btn:last-child {
    margin-right: 0;
}

.tab_select #backstage_config .per-grid-dynamic_item:last-child i {
    display: block;
    width: 14px;
    height: 14px;
    float: left;
    font-family: "perficon";
    font-size: 14px;
    text-align: center;
    line-height: 14px;
    margin-top: 12px;
    margin-right: 8px;
}

.tab_select #backstage_config .per-grid-dynamic_wrap {
    padding-bottom: 16px;
}

.tab_select #backstage_config .per-grid-dynamic_wrap .per-scrollbar {
    max-height: none !important;
}

.tab_select #backstage_config .per-combobox-con .per-combobox_item {
    width: 194px;
}

.tab_select #backstage_config .per-grid-dynamic_header_right {
    float: left;
    margin-left: 20px;
}

.tab_select #backstage_config .per-grid-dynamic_header_right>div {
    width: 196px;
    margin: 10px 0;
}

.tab_select #backstage_config .meter_config .per-grid-nodata,
.tab_select #backstage_config .basics_config .per-grid-nodata {
    min-height: 319px;
}

.tab_select #backstage_config .per-combobox-wrap,
.tab_select #backstage_config .per-combobox-con {
    width: 100%;
}

.tab_select #backstage_config .per-grid-dynamic_con {
    max-height: none !important;
}

.tab_select #backstage_config .per-tab-navigation_icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    line-height: 20px;
}

.tab_select #backstage_config .per-grid-dynamic_li {
    cursor: default;
}

.tab_select #operating_record ._time-left,
.tab_select #operating_record ._time-right {
    display: none;
}

.tab_select #operating_record .time_controller_box .per-calendar-title {
    padding: 0;
    width: 196px;
}

.tab_select #operating_record .search_record_box .per-searchbox-promptly,
.tab_select #operating_record .search_record_box .per-searchbox-promptly input {
    border-radius: 2px;
}

.tab_select #operating_record .per-grid-dynamic_con,
.tab_select #operating_record .per-grid-dynamic_wrap {
    height: 100%;
}

.tab_select #operating_record .per-grid-paging>div {
    float: left;
}

.tab_select #operating_record .per-grid-dynamic_title .per-grid-dynamic_item:nth-child(10),
.tab_select #operating_record .per-grid-dynamic_title .per-grid-dynamic_item:nth-child(11),
.tab_select #operating_record .per-grid-dynamic_con .per-grid-dynamic_li .per-grid-dynamic_item:nth-child(10),
.tab_select #operating_record .per-grid-dynamic_con .per-grid-dynamic_li .per-grid-dynamic_item:nth-child(11) {
    flex: 2;
}

/* 20180608wp++ */
#operating_record_gird.msg .per-grid-dynamic_title ul li .per-grid-dynamic_item:nth-of-type(29),
#operating_record_gird.msg .per-grid-dynamic_con ul li .per-grid-dynamic_item:nth-of-type(29) {
    -webkit-box-flex: 6.9;
    -ms-flex: 6.9;
    flex: 6.9;
}

/*项目管理Tab页*/
.tab_select #projectManagement,
.tab_select #buildManagement,
.tab_select #userManagement {
    padding: 0 20px;
    width: 100%;
    height: 100%;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.tab_select #projectManagement .listModel,
.tab_select #buildManagement .listModel,
.tab_select #userManagement .listModel {
    width: 100%;
    height: 100%;
}

.tab_select #projectManagement .addProjectBtn,
.tab_select #userManagement .addUserBtn,
.tab_select #buildManagement .addProjectBtn {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    height: 50px;
    justify-content: flex-end;
    align-items: center;
}

.tab_select .addPageHeader {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    height: 50px;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #cacaca;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.tab_select .addPageHeader div:nth-child(2) {
    font-size: 18px
}

.tab_select .addPageBody {
    width: 486px;
    height: calc(100% - 50px);
    padding-top: 30px;
    margin: 0 auto;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.tab_select .addPageBody .projectMessage .message {
    font-size: 18px;
    color: #6d6d6d;
    height: 18px;
    line-height: 18px;
    margin-bottom: 10px;
}

.tab_select .addPageBody .projectMessage .item {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    line-height: 30px;
    margin-bottom: 20px
}

.tab_select .addPageBody .item .inputname {
    width: 100px;
    text-align: right;
    height: 30px;
    line-height: 30px;
    margin-right: 10px;
    color: #666666;
    font-size: 16px;
}

.tab_select .addPageBody .item .inputwrap {
    line-height: 30px;
    width: 260px;
}

.tab_select .addPageBody .addBuildInfo,
.tab_select .watchEdit .addBuildInfo.message {
    margin-bottom: 20px;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    padding: 0 20px;
    width: 400px;
    height: 50px;
    line-height: 50px;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #CACACA;
    border-radius: 2px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    background-color: lightgray;
    color: #333333;
    font-size: 16px;
}

/*建筑详情弹框*/
.buildFloat .detailMessage .item {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    padding: 0 20px;
    margin-bottom: 25px;
    font-size: 16px;
    line-height: 30px;
}

.buildFloat .detailMessage .item:first-child {
    margin-top: 15px;
}

.buildFloat .detailMessage .item .inputname {
    margin-right: 10px;
    width: 100px;
    min-height: 30px;
    text-align: right;
    line-height: 30px;
}

.buildFloat .detailMessage .item .inputwrap {
    position: relative;
    width: 260px;
}

.buildFloat .detailMessage .item .inputwrap .resetPassword {
    position: absolute;
    right: 0;
    color: #637E99;
}

.buildFloat .detailMessage .item .inputwrap .resetPassword:hover {
    cursor: pointer;
}

.buildFloat .position {
    border-top: 1px solid #ccc;
    margin-top: 10px;
}

.buildFloat .position .positionName {
    margin-top: 10px;
    padding-left: 30px;
    font-size: 16px;
    color: #333333;
}

.menuPermission {
    margin-left: 30px;
    margin-top: 10px;
}

.menuPermission .menuPermissionchild {
    margin-left: 30px;
    margin-top: 5px;
}

.menuPermission .menuPermissionchild2 {
    margin-left: 15px;
}

.menuPermission .menuPermissionchild3 {
    margin-top: 5px
}

.forul {
    width: 90%;
    margin-left: 60px;
}

.forul .forli {
    float: left;
    margin-left: 9px;
    margin-top: 9px;
}

.buildFloat .position ul {
    margin-top: 15px;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: inline-flex;
    padding-left: 70px;
}

.buildFloat .position ul li {
    margin-right: 10px;
    font-size: 14px;
}

.buildFloat .position ul li:hover {
    cursor: pointer;
}

.buildFloat .position ul li input {
    vertical-align: middle;
}

.buildFloat {
    width: 600px;
    overflow: auto;
}

.buildFloat .butWrap {
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    padding: 10px 20px 0;
    position: absolute;
    bottom: 0;
    border-top: 1px solid #ccc;
    height: 60px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.buildFloat .butWrap button {
    width: 80px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border: 1px solid #7a94ad;
    background-color: #7a94ad;
    border-radius: 2px;
    color: #FFF;
    cursor: pointer;
}

.buildFloat .butWrap button:last-child {
    background: #f06f6f;
    border: 1px solid #f06f6f
}

/* 编辑项目 输入框错误提示 */
#editProjectName,
#editProjectAddress,
#editBuildName,
#editBuildAddress {
    white-space: nowrap;
    text-overflow: ellipsis;
}

#editProjectName+p,
#editProjectAddress+p ,
#editBuildName+p,
#editBuildAddress+p {
    display: block;
    position: absolute;
    font-size: 12px;
    height: 22px;
    line-height: 24px;
    white-space: nowrap;
    color: #666;
}