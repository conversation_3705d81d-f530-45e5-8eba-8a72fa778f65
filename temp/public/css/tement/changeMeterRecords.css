/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

.c_m_r_box div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.c_m_r_box {
    width: 100%;
    height: 100%;
}

.c_m_r_box .c_m_r_header {
    height: 50px;
    border-bottom: 1px solid #D9E2E8;
    padding: 10px 20px 9px;
    position: relative;
}

.c_m_r_box .c_m_r_header .c_m_r_go_back {
    position: absolute;
}

.c_m_r_box .c_m_r_header h1 {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    color: #333;
    text-align: center;
}

.c_m_r_box .c_m_r_gird {
    height: 100%;
    max-height: calc(100% - 40px);
    padding: 20px;
}

.c_m_r_box .per-grid-normal {
    padding-bottom: 0;
}

.no_border_bottom {
    border-bottom: none;
}