/*清除浮动*/

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    line-height: 0;
    clear: both;
    visibility: hidden;
}

.clear {
    zoom: 1;
}

/* 干掉ie浏览器input的框的x */
::-ms-clear,
::-ms-reveal {
    display: none;
}

.s_t_box div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.s_t_box input {
    outline: none;
    border: none;
}

.s_t_box {
    width: 100%;
    height: 100%;
}

.s_t_box .s_t_header {
    height: 50px;
    border-bottom: 1px solid #d9e2e8;
    padding: 10px 20px 9px;
    position: relative;
}

.s_t_box .s_t_header .s_t_cancel {
    position: absolute;
}

.s_t_box .s_t_header h1 {
    font-size: 16px;
    height: 30px;
    line-height: 30px;
    color: #333;
    text-align: center;
}

.s_t_box .s_t_content {
    height: calc(100% - 108px);
    overflow: auto;
}

.s_t_box .s_t_content .s_t_content_mid {
    width: 1340px;
    height: 100%;
    margin: 0 auto;
    padding-top: 28px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_choose_date .s_t_date_title {
    position: relative;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_choose_date .s_t_date_title,
.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_title {
    font-size: 14px;
    color: #6d6d6d;
    height: 34px;
    line-height: 34px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content_box {
    margin-bottom: 16px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content_box:last-child {
    margin-bottom: 20px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_choose_date .s_t_date_date_controller {
    height: 30px;
    margin-bottom: 40px;
    position: relative;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_choose_date .s_t_choose_date_error_msg {
    position: absolute;
    top: 30px;
    left: 0;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_choose_date .s_t_choose_date_error_msg p {
    font-size: 12px;
    height: 28px;
    line-height: 28px;
    color: #ff8f5c;
    display: none;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_choose_date .s_t_choose_date_error_msg i {
    display: block;
    height: 28px;
    width: 16px;
    background: url(/images/error_icon.png) no-repeat left;
    float: left;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_choose_date .s_t_date_tip {
    display: block;
    color: #cacaca;
    font-size: 12px;
    height: 34px;
    line-height: 34px;
    position: absolute;
    left: 68px;
    top: 0;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content {
    border: 1px solid #d9e2e8;
    padding: 0 0 19px;
    margin-bottom: 10px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content:last-child {
    margin-bottom: 0;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content>div {
    padding: 0 24px;
    margin-bottom: 12px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content>div:last-child {
    margin-bottom: 0;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content>div>div {
    padding-bottom: 20px;
    margin-top: 13px;
    border-bottom: 1px dashed #d9e2e8;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content>div:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content .s_t_not_pay_list_content_title {
    border-bottom: 1px solid #d9e2e8;
    padding: 0 24px;
    height: 48px;
    line-height: 50px;
    font-size: 16px;
    background-color: #f9fbfc;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content h4 {
    height: 28px;
    line-height: 28px;
    font-size: 16px;
    font-weight: 700;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content .price span:first-child {
    color: #7189a1;
    cursor: pointer;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content .price span:first-child:hover {
    color: #42607c;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content_sub_box {
    margin-bottom: 10px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content_sub_box:last-child {
    margin-bottom: 0;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_content .s_t_not_pay_list_content_info p {
    width: 240px;
    height: 32px;
    line-height: 32px;
    float: left;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border: 1px solid #d9e2e8;
    background-color: #eaeef0;
    height: 30px;
    line-height: 30px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_title>div {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    padding-left: 20px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: 1px solid #d9e2e8;
    border-top: none;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div>div,
.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div>div>input {
    padding-left: 20px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div>div>input {
    position: absolute;
    top: 0;
    left: 0;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    border-left: 1px solid #d9e2e8;
    padding-left: 0;
    color: #637e99;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more>div:last-child>div {
    height: 35px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more.more_no_edit>div {
    padding-left: 20px;
    color: #333333;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div:nth-child(4) {
    border-right: 1px solid #d9e2e8;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more>div {
    height: 36px;
    line-height: 36px;
    border-top: 1px solid #d9e2e8;
    padding-left: 0;
    position: relative;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more>div>div {
    padding-left: 20px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more>div>input {
    display: none;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more>div .blue_border {
    border: 1px solid #0fabd1;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more i {
    font-family: perficon;
    display: block;
    width: 16px;
    height: 16px;
    line-height: 16px;
    color: #7a94ad;
    position: absolute;
    top: 10px;
    right: 12px;
    display: none;
    cursor: pointer;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more>div .hide_edit {
    display: none;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more .check {
    right: 38px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box>div.more>div:first-child {
    border-top: none;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box .balanced_account_type {
    height: 36px;
    line-height: 36px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box .balanced_account_type>div {
    border-right: 1px solid #d9e2e8;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list_content_gird .s_t_not_pay_list_content_gird_box .border_none>div {
    border-right: none;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_money {
    margin-bottom: 18px;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_money p {
    font-size: 14px;
    text-align: right;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_money p.residue {
    padding: 16px 0;
}

.s_t_box .s_t_content .s_t_content_mid .s_t_not_pay_list .s_t_not_pay_list_money span {
    font-size: 26px;
    font-weight: 700;
}

.s_t_box .s_t_footer {
    height: 58px;
    border-top: 1px solid #d9e2e8;
}

.s_t_box .s_t_footer .s_t_settle_btn {
    padding: 14px 0;
    width: 240px;
    margin: 0 auto;
}

.s_t_box .s_t_footer .s_t_settle_btn .per-button-grayBg {
    width: 100%;
}