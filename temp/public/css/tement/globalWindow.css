.p_u_b-global ._hide {
    display: none;
}

.p_u_b-global ._show {
    display: block;
}

.p_u_b-global .pay_the_fees_model_block {
    width: 480px;
    height: 565px;
    padding: 60px 60px 30px 60px;
    position: relative;
    box-sizing: border-box;
}

.p_u_b-global .pay_the_fees_model_tit {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
}

.p_u_b-global .pay_the_fees_model_tip {
    color: #888;
    font-size: 14px;
    margin-bottom: 30px;
}

.p_u_b-global .pay_the_fees_password_tit {
    color: #888;
    font-size: 14px;
    margin-bottom: 10px;
}

.p_u_b-global .pay_the_fees_password_input>input {
    height: 30px;
}

.p_u_b-global .pay_the_fees_password_input>input.red {
    border: 1px solid #ff5553;
}

.p_u_b-global .pay_the_fees_password_input>section {
    color: #ff5553;
    margin-top: 6px;
}

.p_u_b-global .pay_the_fees_operat {
    width: 480px;
    position: absolute;
    bottom: 27px;
    left: 50%;
    margin-left: -240px;
    text-align: center;
}

.p_u_b-global .pay_the_fees_operat>div:nth-child(1) {
    margin-right: 40px;
    text-align: center;
}

.p_u_b-global .pay_the_fees_operat>div {
    width: 100px;
}

/* 缴费全部 */

.p_u_b-global .pay_the_fees_model_all_block {
    width: 520px;
    height: 300px;
    padding: 30px 40px 0 40px;
    box-sizing: border-box;
}

.p_u_b-global .pay_the_fees_model_all_tit {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
}

.p_u_b-global .pay_the_fees_model_all_tip {
    color: #888;
    font-size: 14px;
    margin-bottom: 30px;
}

.p_u_b-global .pay_the_fees_model_all_password_tit {
    color: #888;
    font-size: 14px;
    margin-bottom: 10px;
}

.p_u_b-global .pay_the_fees_model_all_password_input>input {
    height: 30px;
}

.p_u_b-global .pay_the_fees_model_all_password_input>input.red {
    border: 1px solid #ff5553;
}

.p_u_b-global .pay_the_fees_model_all_password_input>section {
    color: #ff5553;
    margin-top: 6px;
}

.p_u_b-global .pay_the_fees_model_all_operat {
    width: 520px;
    position: absolute;
    bottom: 27px;
    left: 50%;
    margin-left: -260px;
    text-align: center;
}

.p_u_b-global .pay_the_fees_model_all_operat>div {
    width: 100px;
}

.p_u_b-global .pay_the_fees_model_all_operat>div:nth-child(1) {
    margin-right: 40px;
    text-align: center;
}

/* 仪表设置 */
.p_u_b-global .instrument_set_model_block {
    width: 520px;
    height: 600px;
    padding: 30px 60px 0 60px;
    box-sizing: border-box;
    position: relative;
}

.p_u_b-global .window_instrument_set #close_meter_setting {
    display: none;
    width: 28px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    font-family: "perficon";
    font-size: 14px;
    color: #fff;
    position: fixed;
    cursor: pointer;
    top: calc(50% - 325px + 25px);
    left: calc(50% + 240px);
    z-index: 99;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.p_u_b-global .instrument_set_model_topList {
    width: 400px;
    height: 80px;
}

.p_u_b-global .instrument_set_model_topList>ul {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    border: 1px solid #e5e5e5;
}

.p_u_b-global .instrument_set_model_topList>ul:nth-child(2) {
    border-top: none;
}

.p_u_b-global .instrument_set_model_topList>ul>li {
    display: flex;
    flex-flow: row nowrap;
    align-items: center;

}

.p_u_b-global .instrument_set_model_topList>ul>li>h4 {
    width: 118px;
    background: #f8f8f8;
    padding: 10px 16px;
    border-right: 1px solid #e5e5e5;
    box-sizing: border-box;
    color: #6d6d6d;
}

.p_u_b-global .instrument_set_model_topList>ul>li>p {
    width: 80px;
    border-right: 1px solid #e5e5e5;
    padding: 10px 16px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.p_u_b-global .instrument_set_model_topList>ul>li:nth-child(2)>p {
    border-right: none;
}

.p_u_b-global .instrument_set_main .instrument_set_model_title {
    width: 400px;
    height: 30px;
    margin: 0 auto;
    text-align: center;
    margin-top: 30px;
}

.p_u_b-global .instrument_set_main .instrument_set_model_title span {
    display: block;
    font-size: 14px;
    color: #aaa;
    position: relative;
}

.p_u_b-global .instrument_set_main .instrument_set_model_title span:before,
.p_u_b-global .instrument_set_main .instrument_set_model_title span:after {
    content: '';
    position: absolute;
    top: 52%;
    background: #e6e6e6;
    width: 40%;
    height: 1px;
}

.p_u_b-global .instrument_set_main .instrument_set_model_title span:before {
    left: 0%;
}

.p_u_b-global .instrument_set_main .instrument_set_model_title span:after {
    right: 0%;
}

.p_u_b-global .instrument_set_main .instrument_set_model_item {
    margin-top: 22px;
}

.p_u_b-global .instrument_set_main .instrument_set_model_item>ul {
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
}

.p_u_b-global .instrument_set_main .instrument_set_model_item>ul>li {
    width: 400px;
    height: 35px;
    border: 1px solid #d6d6d6;
    text-align: center;
    line-height: 35px;
    margin-bottom: 16px;
    color: #7d93aa;
    cursor: pointer;
}

.p_u_b-global .instrument_set_main .instrument_set_model_item>ul>li:hover {
    background-color: rgb(248, 248, 248);
}

.p_u_b-global .instrument_set_main .instrument_set_model_item>ul>li.gray {
    color: #ccc;
}

.p_u_b-global .instrument_reset_block_loading>p {
    position: absolute;
    top: 360px;
    left: 50%;
    width: 220px;
    margin-left: -110px;
    z-index: 200;
    color: #7b7b7b;
    text-align: center;
}


.p_u_b-global .instrument_reset_block_success>.dashed {
    width: 400px;
    border-top: 1px dashed #ccc;
    margin-top: 30px;
}

.p_u_b-global .instrument_reset_block_success>.top {
    display: flex;
    flex-flow: row nowrap;
    margin-top: 26px;
}

.p_u_b-global .instrument_reset_block_success>.top>h4 {
    color: #6d6d6d
}

.p_u_b-global .instrument_reset_block_success>.top>h4 span:last-child {
    color: #888888
}

.p_u_b-global .instrument_reset_block_success>.top>i {
    color: #d9d9d9;
}

.p_u_b-global .instrument_reset_block_success>.number {
    margin-top: 18px;
    color: #03a9d3;
}

.p_u_b-global .instrument_reset_block_success>.number div {
    float: left;
    width: 50%;
}

.p_u_b-global .instrument_reset_block_success>.number i {
    font-size: 30px;
    margin-right: 10px;
}

.p_u_b-global .instrument_reset_block_success>.number span {
    font-size: 14px;
}


.p_u_b-global .instrument_reset_block_success>.operat {
    width: 400px;
    position: absolute;
    bottom: 30px;
    left: 50%;
    margin-left: -200px;
    text-align: center;
}

.p_u_b-global .instrument_reset_block_success>.operat>div {
    text-align: center;
    width: 100px;
}

.p_u_b-global .instrument_reset_block_success>.operat>div:first-child {
    margin-right: 20px;
}

.p_u_b-global .instrument_reset_block_success>.operat>div:last-child {
    text-align: left;
    margin-left: 20px;
}

.p_u_b-global .instrument_reset_block_success>.operat .instrument_icon {
    width: 14px;
    height: 14px;
    font-size: 12px;
    line-height: 14px;
    border-radius: 50%;
    color: #fff;
    background: #D1D2D1;
    display: inline-block;
    text-align: center;
    position: absolute;
    top: 9px;
    right: 91px;
    cursor: pointer;
}

.p_u_b-global .instrument_reset_block_notResponse {
    width: 400px;
}

.p_u_b-global .instrument_reset_block_notResponse>.button {
    width: 200px;
    position: absolute;
    left: 50%;
    margin-left: -100px;
    bottom: 30px;
    text-align: center;
}

.p_u_b-global .instrument_reset_block_notResponse>.button>div {
    width: 100px;
}

.p_u_b-global .instrument_reset_block_notResponse .per-prompt-abnormalmess {
    top: calc(50% - 63px);
}



.p_u_b-global .instrument_reset_block_paulElectric_input {
    margin-top: 30px;
}

.p_u_b-global .instrument_reset_block_paulElectric_input>.dashed {
    width: 400px;
    border-top: 1px dashed #ccc;
}

.p_u_b-global .instrument_reset_block_paulElectric_input>.title {
    font-size: 16px;
    color: #333;
    margin-bottom: 16px;
    margin-top: 30px;
}

.p_u_b-global .instrument_reset_block_paulElectric_input>.password {
    color: #888;
    font-size: 14px;
    margin-bottom: 10px;
}

.p_u_b-global .instrument_reset_block_paulElectric_input>.content>input {
    height: 30px;
}

.p_u_b-global .instrument_reset_block_paulElectric_input>.content>input.red {
    border: 1px solid #ff5553;
}

.p_u_b-global .instrument_reset_block_paulElectric_input>.content>section {
    color: #ff5553;
    margin-top: 6px;
}

.p_u_b-global .instrument_reset_block_paulElectric_input>.operat {
    width: 400px;
    position: absolute;
    bottom: 30px;
    left: 50%;
    margin-left: -200px;
    text-align: center;
}


.p_u_b-global .instrument_reset_block_paulElectric_input>.operat>div {
    text-align: center;
    width: 100px;
}

.p_u_b-global .instrument_reset_block_paulElectric_input>.operat>div:first-child {
    margin-right: 20px;
}

.p_u_b-global .instrument_reset_block_paulElectric_input>.operat>div:last-child {
    margin-left: 20px;
}


.p_u_b-global .instrument_reset_block_Make_overdraft_updata {
    margin-top: 30px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.dashed {
    width: 400px;
    border-top: 1px dashed #ccc;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.top {
    display: flex;
    flex-flow: row nowrap;
    margin-top: 26px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.top>h4 {
    color: #6d6d6d
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.top>h4 span:last-child {
    color: #888888
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.top>i {
    color: #d9d9d9;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.number {
    margin-top: 18px;
    color: #03a9d3;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.number div {
    float: left;
    width: 50%;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.number i {
    font-size: 30px;
    margin-right: 10px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.number span {
    font-size: 14px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.operat {
    width: 400px;
    position: absolute;
    bottom: 30px;
    left: 50%;
    margin-left: -200px;
    text-align: center;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.change {
    display: flex;
    flex-flow: column nowrap;
    margin-top: 26px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.change>h4 {
    color: #6d6d6d
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.change>section {
    color: #aaa;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.change>div {
    margin-top: 6px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore {
    display: flex;
    flex-flow: column nowrap;
    margin-top: 26px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>h4 {
    color: #6d6d6d
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>section {
    color: #aaa;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>div {
    margin-top: 6px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>div>ul>li {
    display: flex;
    flex-flow: row nowrap;
    border: 1px solid #ccc;
    border-bottom: none;
    position: relative;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>div>ul>li:last-child {
    border-bottom: 1px solid #ccc;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>div>ul>li>h4 {
    width: 80px;
    height: 30px;
    background: #f8f8f8;
    text-align: center;
    line-height: 30px;
    border-right: 1px solid #ccc;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>div>ul>li>span {
    position: absolute;
    color: #ff0000;
    top: 5px;
    left: 100px;
    z-index: 50;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>div>ul>li>div {
    width: calc(100% - 81px);
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>div>ul>li>div input {
    border: none;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.changeMore>div>ul>li .per-inputborder {
    border: none;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.operat>div {
    text-align: center;
    width: 100px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.operat>div:first-child {
    margin-right: 20px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata>.operat>div:last-child {
    margin-left: 20px;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata .changeMore li>div {
    position: relative;
}

.p_u_b-global .instrument_reset_block_Make_overdraft_updata .changeMore li>div .error-tip {
    position: absolute;
    top: 4px;
    z-index: 99;
    right: 80px;
    height: 30px;
    font-size: 14px;
}