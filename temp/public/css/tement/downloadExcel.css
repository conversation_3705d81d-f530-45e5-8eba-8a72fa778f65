.report_wrapper {
    font-size: 12px;
    width: 100%;
    min-width: 1220px;
    box-sizing: border-box;
}

/* 表头 */
.report_wrapper .p_r_s_m_gird_tit {
    width: 100%;
    height: 30px;
    display: flex;
    color: #fff;
    min-width: 1220px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    background-color: #8C959A;
}

.report_wrapper .p_r_s_m_gird_tit div {
    flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    border-right: 1px solid #ccc;
}

.report_wrapper .p_r_s_m_gird_tit div:nth-last-child(1) {
    border-right: none;
}

/* 内容 */
.p_r_s_m_gird_item_box {
    width: 100%;
    color: #000;
    min-width: 1220px;
    box-sizing: border-box;
}

.p_r_s_m_gird_body .p_r_s_m_gird_item {
    min-height: 30px;
    display: flex;
    flex-direction: row;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc !important;
    border-left: 1px solid #ccc;
}

.p_r_s_m_gird_body .p_r_s_m_gird_item:nth-of-type(2n) {
    background: #f5f7fa;
}

.p_r_s_m_gird_body .p_r_s_m_gird_item:nth-last-child(1) {
    border-bottom: none;
}

.p_r_s_m_gird_body .p_r_s_m_gird_item>div {
    flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    min-height: 36px;
    display: flex;
    flex-direction: column;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #ccc;
}

.p_r_s_m_gird_body .p_r_s_m_gird_item>div:nth-last-child(1) {
    border-right: none;
}

.p_r_s_m_gird_item_1 {
    display: flex;
    flex-direction: column;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    align-items: center;
    justify-content: center;
}

.p_r_s_m_gird_item_1 .p_r_s_m_gird_item_2 {
    width: 100%;
    text-align: center;
}

.p_r_s_m_gird_body .p_r_s_m_gird_item_1.sort_code_1 {
    display: flex;
    align-items: center;
    justify-content: center;
}

.p_r_s_m_gird_item .room_code_2 .p_r_s_m_gird_item_1,
.p_r_s_m_gird_item .room_code_3 .p_r_s_m_gird_item_1,
.p_r_s_m_gird_item .room_code_4 .p_r_s_m_gird_item_1,
.p_r_s_m_gird_item .room_code_5 .p_r_s_m_gird_item_1,
.p_r_s_m_gird_item .room_code_6 .p_r_s_m_gird_item_1,
.p_r_s_m_gird_item .room_code_7 .p_r_s_m_gird_item_1 {
    display: flex;
    flex-direction: column;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    align-items: center;
    justify-content: center;
}

.p_r_s_m_gird_item .p_r_s_m_gird_item_1 .p_r_s_m_gird_item_2 {
    flex: 1;
    height: 36px;
    border-bottom: 1px solid #ccc;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
}

.p_r_s_m_gird_item .p_r_s_m_gird_item_1 .p_r_s_m_gird_item_2:nth-last-child(1) {
    border-bottom: none;
}

.p_r_s_m_gird_item .p_r_s_m_gird_item_1.sort_code_7 .p_r_s_m_gird_item_2 {
    border-right: none;
}

.p_r_s_m_gird_item_1 {
    width: 100%;
}

.per-scrollbar_wrap {
    width: 100% !important;
}