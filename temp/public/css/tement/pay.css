/*缴费-电页面  */

/*头部  */
.mainBody .pay_title{
    height:50px;
    line-height:50px;
    border-bottom:1px solid #d9e2e8;
    display:-webkit-box;
    display:-ms-flexbox;
    display:flex;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    padding:0 20px;
}
/*头部左侧按钮  */
#pay_title_button1{
    width:80px;
    height:30px;
    font-size:14px;
    margin-top:10px;
}
#pay_title_text{
    font-size:16px;
}
/*头部右侧按钮  */
#pay_title_button2{
    width:120px;
    height:30px;
    font-size:14px;
    margin-top:10px;
}

/*表格  */
.mainBody .pay_table{
    margin:10px 20px;
    overflow:hidden;
    font-size:12px;
}
.pay_table_top ul>li {
    width:100%;
}

.payname {
   -webkit-box-flex: 1.4;
       -ms-flex: 1.4;
           flex: 1.4;
}

.paynum {
   -webkit-box-flex: 1;
       -ms-flex: 1;
           flex: 1;
}

.payoper {
    -webkit-box-flex: 1.1;
        -ms-flex: 1.1;
            flex: 1.1;
}

.pay_table_con {
    -webkit-box-flex: 3.5;
        -ms-flex: 3.5;
            flex: 3.5;
    width: 100%;
}
.pay_table_con>div:first-child {
    -webkit-box-flex: 2;
        -ms-flex: 2;
            flex: 2;
}
.pay_table_con>div:nth-child(2) {
    -webkit-box-flex: 1.8;
        -ms-flex: 1.8;
            flex: 1.8;
}
.pay_table_con>div:nth-child(3) {
    -webkit-box-flex: 1.5;
        -ms-flex: 1.5;
            flex: 1.5;
}
.pay_table_con>div:nth-child(4) {
    -webkit-box-flex: 1.5;
        -ms-flex: 1.5;
            flex: 1.5;
}
.pay_table_con>div:last-child {
    -webkit-box-flex: 1;
        -ms-flex: 1;
            flex: 1;
}
.pay_table_body_right {
    -webkit-box-flex: 3.5;
        -ms-flex: 3.5;
            flex: 3.5;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    min-height: 34px;
}
.pay_table_body_right>div {
    -webkit-box-flex: 3.5;
        -ms-flex: 3.5;
            flex: 3.5;
    min-height: 34px;
    border-bottom: 1px solid #ccc;
}
.pay_table_body_right>div:last-child {
    border-bottom: none;
}
/*头部 */
.mainBody .pay_table .pay_table_top{
    height:32px;
    line-height:30px;
    color:white;
    background-color:#8b959a;
    text-align:center;
    font-size:12px;
}
/*表格主要内容  */
.mainBody .pay_table .pay_table_body{
    max-height:calc(100vh - 180px);
    overflow:auto;
    overflow-x: hidden;
    border-bottom:1px solid #ccc;
    border-right:1px solid #ccc;
}

.mainBody .pay_table .pay_table_body ul li{
    min-height:34px;
    border-left:1px solid #ccc;
    width: 100%;
}

.mainBody .pay_table .pay_table_body ul li>div{
    border:1px solid #ccc;
    border-bottom:none;
    border-left:none;
    display:-webkit-box;
    display:-ms-flexbox;
    display:flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
}
.mainBody .pay_table .pay_table_body ul li>div:last-child {
    border-right: none;
}
/*隔行变色  */
.mainBody .pay_table .pay_table_body ul li:nth-child(2n){
    background:#f5f7fa;
}

.mainBody .pay_table .pay_table_body .pay_table_body_right .inline>div{
    border-right:1px solid #ccc;
    height:34px;
    line-height:34px;
    text-align:center;
}
.mainBody .pay_table .pay_table_body .pay_table_body_right>div>div:last-child{
     border-right:none;
}
.mainBody .pay_table .pay_table_body .pay_table_body_right>div:last-child>div{
     border-bottom:none;
}
/*尾部  */
.mainBody .pay_floot {
    width:calc(100% - 20px);
    height:58px;
    background:white;
    border-top:1px solid #d9e2e8;
    text-align:center;
    position:absolute;
    bottom:0;
}
/*尾部  按钮  */
.mainBody .pay_floot .per-button-grayBg{
    width:140px;
    height:30px;
    font-size:14px;
    margin-top:15px;
    background: #7a94ad !important;
}

/*缴费中 头部标题  */
#payInWindow .per-modal-custom_title{
    font-size:18px;
    background:#02a9d1;
    height:50px;
    font-weight:550;
}
#payInWindow #partLoading {
    text-align:center;
}
/*缴费中 加载中位置 */
#payInWindow #partLoading .loading-con{
    position:relative;
    margin-top:-60px;
}

/*缴费中  加载中大小  */
#payInWindow #partLoading .loading-con .per-loading-nomal_pic{
    width:56px;
    height:56px;
}
/*缴费中  加载中 下方提示  */
#payInWindow .loading_title{
    font-size:14px;
    text-align: center;
    padding-top:300px;
}

/*全部缴费中 头部标题  */
#payBothInWindow .per-modal-custom_title{
    font-size:18px;
    background:#02a9d1;
    height:50px;
    font-weight:550;
}
#payBothInWindow #partLoading {
    text-align:center;
}
/*全部缴费中 加载中位置 */
#payBothInWindow #partLoading .loading-con{
    position:relative;
    margin-top:-30px;
}

/*全部缴费中  加载中大小  */
#payBothInWindow #partLoading .loading-con .per-loading-nomal_pic{
    width:56px;
    height:56px;
}
/*全部缴费中  加载中 下方提示  */
#payBothInWindow .loading_title{
    font-size:14px;
    text-align: center;
    padding-top: 170px;
}

/*缴费成功 头部标题  */
#paySuccessWindow .per-modal-custom_title{
    font-size:18px;
    background:#68c5b3;
    height:50px;
    font-weight:550;
}
#paySuccessWindow .per-modal-custom_title .per-notice_successicon {
    position: relative;
    top: -2px;
}
#paySuccessWindow #paySuccessButton{
    width:100px;
    height:30px;
    font-size:14px;
}

/*全部缴费成功标题  */
#payBothSuccWindow .per-modal-custom_title{
    font-size:18px;
    background:#68c5b3;
    height:50px;
    font-weight:550;
}
#payBothSuccWindow .per-modal-custom_title .per-notice_successicon {
    position: relative;
    top: -2px;
}

/*缴费中通用样式  */
.pay{
    width: 420px;
    padding: 10px 100px;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
}
.pay .pay_top{
    height:calc(100% - 100px);
    overflow: auto;
    border-bottom:1px dashed #b0b0b0;
}
.pay .pay_top>div{
    margin-bottom:10px;
    line-height:26px;
}
.pay .pay_top>div p:nth-child(1){
    color:#868686;
    font-size:14px;
}
.pay .pay_top>div p:nth-child(2){
    color:black;
    font-size:14px;
}
.pay .pay_bottom{
    padding: 10px 0;
    overflow:auto;
}
.pay .pay_bottom>div{
    margin-bottom:10px;
    line-height:26px;
}
.pay .pay_bottom>div p:nth-child(1){
    color:#868686;
    font-size:14px;
}
.pay .pay_bottom>div p:nth-child(2){
    color:black;
    font-size:14px;
}
.pay_button{
    text-align:center;
    margin: 20px 0;
}

/*缴费  尾部按钮*/
#pay_button1{
    width:100px;
    height:30px;
    font-size:14px;
}
#pay_button2{
    width:100px;
    height:30px;
    font-size:14px;
}

/*全部缴费  */
.payBoth{
    height:222px;
}
.payBoth .payBoth_top{
    width:400px;
    height:74px;
    margin:40px auto;
    border-bottom:1px dashed #b0b0b0;
}
.payBoth .payBoth_top>div{
    margin-bottom:18px;
}
.payBoth .payBoth_top>div p:nth-child(1){
    color:#868686;
    font-size:14px;
    height: 14px;
    line-height: 14px;
}
.payBoth .payBoth_top>div p:nth-child(2){
    color:black;
    font-size:14px;
}

.payBoth_bottom{
    width:400px;
    height:52px;
    line-height:52px;
    margin:-40px auto;
}
.payBoth_bottom>div{
    display:inline-block;
    color:#7a7a7a;
    font-size:14px;
}
.payBoth_bottom>div:last-child{
    margin-left:40px;
}

/*全部缴费成功中的文本  */
.paySucc_text{
    width:100%;
    height:20px;
    margin-top:35px;
    color:#4aab9a;
    text-align:center;
    font-size:14px;
}
 .paySucc_text .per-notice_successicon{
    background:#4aab9a;
    color:white;
    font-size:12px;
}

/*全部缴费失败中的文本  */
.payErr_text{
    width:100%;
    height:20px;
    margin-top:35px;
    color:#ff7b7b;
    text-align:center;
    font-size:14px;
}
.payErr_text .per-notice_failureicon{
    background:#ff7b7b;
    color:white;
    font-size:8px;
}

.payBoth_button{
    text-align:center;
    margin:-22px auto;
}
#payBoth_button1{
    width:100px;
    height:30px;
    font-size:14px;
    margin-right: 40px;
}
#payBoth_button2{
    width:100px;
    height:30px;
    font-size:14px;
}
#payBothSucc_button1{
    width:200px;
    height:30px;
    font-size:14px;
}
#payBothErr_button1{
    width:100px;
    height:30px;
    font-size:14px;
}
#payBothErr_button2{
    width:100px;
    height:30px;
    font-size:14px;
}

/*缴费失败 头部标题  */
#payErrorWindow .per-modal-custom_title{
    font-size:14px;
    background:#ff7b7b;
    height:50px;
    font-weight:550;
}

#payErrorWindow .per-notice_failureicon{
    line-height:18px;
    font-size:12px;
}
#payErrorButton1{
    width:100px;
    height:30px;
    font-size:14px;
}
#payErrorButton2{
    width:100px;
    height:30px;
    font-size:14px;
}

/*字体放大  */
.textlarge{
    font-size:36px;
    font-size:700;
    padding-right:12px;
}

#colorGreen{
    padding-top:10px;
    color:#4aab9a;
}
#colorRed{
    padding-top:10px;
    color:#ff7b7b;
}
.colorff7b7b{
    color:#ff7b7b;
}
#colorBlue{
    padding-top:10px;
    color:#02a9d1;
}
