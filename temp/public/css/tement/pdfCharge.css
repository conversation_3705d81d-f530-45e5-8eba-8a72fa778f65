/*充值记录  */
/*  */
/*头部  */
.charge_head {
    height: 162px;
    background: #e3e7ee;
    padding: 50px 60px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.charge_head_left {
    float: left;
}

.charge_head_left>h2 {
    font-size: 36px;
    font-weight: 900;
    color: #575f73;
    position: relative;
    top: -6px;
}

.charge_head_left>h2>em>u {
    text-decoration: none;
    border-bottom: 4px solid currentColor;
}

.charge_head_left>span {
    display: inline-block;
    margin-top: 10px;
    font-size: 14px;
    color: #6a7183;
}

.charge_head_right {
    float: right;
    font-size: 18px;
    font-weight: 600;
    color: #6b7284;
}

/*内容部分  */
.charge_body {
    padding: 60px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

/*内容部分头部  */
.charge_body_title {
    height: 80px;
    padding: 0 30px;
}

.charge_body_title_left {
    float: left;
    color: #333333;
    font-size: 14px;
    font-weight: 600;
}

.charge_body_title_left h2 {
    margin-bottom: 10px;
}

/*内容部分表格  */
.charge_body_grid {
    border-top: 1px solid #dddddd;
    border-bottom: 1px solid #dddddd;
}

/*内容部分表格 表头  */
.charge_body_grid_head .title_item {
    height: 60px;
    line-height: 60px;
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid #dddddd;
    color: #333333;
    width: 100%;
}

.charge_body_grid_head .title_item>div,
.charge_body_grid_body_list>div {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding-left: 20px;
    height: 100%;
    float: left;
}

.charge_body_grid_body_list>div {
    line-height: 70px;
}

.charge_body_grid_head .title_item>.colspan_1,
.charge_body_grid_body_list>.colspan_1 {
    width: 20%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.charge_body_grid_head .title_item>.colspan_2,
.charge_body_grid_body_list>.colspan_2 {
    width: 20%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.charge_body_grid_head .title_item>.colspan_3,
.charge_body_grid_body_list>.colspan_3 {
    width: 15%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.charge_body_grid_head .title_item>.colspan_4,
.charge_body_grid_body_list>.colspan_4 {
    width: 13%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.charge_body_grid_head .title_item>.colspan_5,
.charge_body_grid_body_list>.colspan_5 {
    width: 15%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.charge_body_grid_head .title_item>.colspan_6,
.charge_body_grid_body_list>.colspan_6 {
    width: 20%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.charge_body_grid_head .title_item>.colspan_copy_1,
.charge_body_grid_body_list>.colspan_copy_1 {
    width: 20%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
    text-align: center;
}

.charge_body_grid_head .title_item>.colspan_copy_2,
.charge_body_grid_body_list>.colspan_copy_2 {
    width: 20%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.charge_body_grid_head .title_item>.colspan_copy_3,
.charge_body_grid_body_list>.colspan_copy_3 {
    width: 20%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.charge_body_grid_head .title_item>.colspan_copy_4,
.charge_body_grid_body_list>.colspan_copy_4 {
    width: 20%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

/* colspan_copy_6 */
.charge_body_grid_head .title_item>.colspan_copy_6,
.charge_body_grid_body_list>.colspan_copy_6 {
    width: 20%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
}

.charge_body_grid_head .title_item div:nth-last-child(2),
.charge_body_grid_head .title_item div:last-child {
    text-align: center;
    padding-left: 0;
}

/*内容部分表格 表格内容  */
.charge_body_grid_body {
    width: 100%;
}

.charge_body_grid_body_list {
    display: table;
    width: 100%;
    height: 70px;
    font-size: 14px;
    table-layout: fixed;
    display: flex;
    justify-content: space-around;
}

.charge_body_grid_body_list div:nth-last-child(2),
.charge_body_grid_body_list div:last-child {
    font-size: 15px;
    text-align: center;
    padding-left: 0;
}

.charge_body_grid_body_list:nth-child(odd) {
    background: #f5f7fb;
}