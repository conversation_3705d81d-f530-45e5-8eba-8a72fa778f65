(function () {;function persagy_tool(){this.enterName="controlInit",this.persagyCreateType="p-create",this.typeSeparator="-",this.persagyRely="p-rely",this.persagyCreateBind="p-bind",this.pbindEventFnName="_pbindEventForOtherLibary",this.pstaticEventFnName="_pstaticEventForOtherLibary",this.attrPrefix="{{",this.attrSuffix="}}",this.cssStart=' class="',this.cssAttrEnd='"',this.libraryToPro="_ppro",this.libraryTypeToHtml="_pt",this.libraryIdToHtml="_id",this.eventCurrTargetName="_currentTarget",this.eventOthAttribute="pEventAttr",this.registeredEventRcord="_peventrecord",this.timerNameToElement="_timer",this.titleSourceAttr="ptitle",this.isRenderedToProName="_rendered",this.controlPrivateToProName="_other",this.maxDivMarker="pc",this.verifyResult="verify",this.pcontrolState={on:"on",off:"off",success:"success",failure:"failure"},this.shape={rectangle:"rectangle",ellipse:"ellipse"},this.orientation={up:"up",down:"down",left:"left",right:"right"},this.align={center:"center",left:"left",right:"right"},this.arrangeType={horizontal:"horizontal",vertical:"vertical"},this.verifyType={space:{name:"space",stringFnName:"pisSpace"},length:{name:"length",stringFnName:"pvalidLength"},chinese:{name:"chinese",stringFnName:"pisChinese"},email:{name:"email",stringFnName:"pisEmail"},idcard:{name:"idcard",stringFnName:"pisCard"},negativeint:{name:"negativeint",stringFnName:"pisNegativeInt"},positiveint:{name:"正整数",stringFnName:"pisPositiveInt"},"int":{name:"int",stringFnName:"pisInt"},negativenumber:{name:"negativenumber",stringFnName:"pisNegativeNumber"},positivenumber:{name:"positivenumber",stringFnName:"pisPositiveNumber"},number:{name:"number",stringFnName:"pisNumber"},greaterequalzero:{name:"greaterequalzero",stringFnName:"pisGreaterEqualZero"},natural:{name:"natural",stringFnName:"pisNatural"},tel:{name:"tel",stringFnName:"pisTel"},mobile:{name:"mobile",stringFnName:"pisMobile"}},this.sortType={asc:"asc",desc:"desc"}}persagy_tool.prototype.constructorCon=function(e){return persagy_tool.instanceFactory(e)},persagy_tool.prototype.createControl=function(e,t,n,r){var i=this.constructorCon(e);return i[this.enterName]?i[this.enterName](t,n,r):""},persagy_tool.prototype.getTypeByPcreate=function(e){var t=ptool.getJqElement(e);if(!t)return console.error("获取容器对象失败");var n=t.attr(this.persagyCreateType)||"";return this.getTypeAndChildType(n)},persagy_tool.prototype.getTypeAndChildType=function(e){var t=(e||"").split(this.typeSeparator),n=t[0],r=t[1];return{controlType:n,childType:r}},persagy_tool.prototype.getTypeAndChildTypeFromEle=function(e){var t=$(e).attr(this.libraryTypeToHtml);return this.getTypeAndChildType(t)},persagy_tool.prototype.oldGetBind=function(e){function o(e){var n=[],r={};while(e.length>0){var i=e.substring(0,1);switch(i){case"[":if(e.substring(1,2)!="{"){var s=e.substring(0,e.indexOf("]")+1);return e=e.substring(e.indexOf("]")+1),{val:s,str:e}}e=e.substring(1);while(e.length>0){var o=arguments.callee(e);n.push(o.val),e=o.str.substring(1);if(o.str.substring(0,1)=="]")break}return{val:n,str:e};case"{":case",":e=e.substring(1),i=e.substring(0,1);if(i=="}")return arguments.callee(e);var u="";i=="'"||i=='"'?(e=e.substring(1),u=e.substring(0,e.indexOf(i))):u=e.substring(0,e.indexOf(":")),e=e.substring(e.indexOf(":")+1);var a=arguments.callee(e);e=a.str;var f=a.val;r[u.ptrimHeadTail()]=f;break;case"}":case"]":return e=e.substring(1),{val:r,str:e};default:var l=e.indexOf(","),c=e.indexOf("}"),h=-1;h=l>c||l==-1?c:l;var s=e.substring(0,h),p=t.valueParse(s);return e=e.substring(h),{val:p,str:e}}}return{val:r,str:e}}var t=this,n=/(\?|\:|==)/g;e=ptool.getJqElement(e);if(!e)return!1;var r=e.attr(t.persagyCreateBind)||"";if(!r)return!1;r.indexOf("{")!==0&&(r="{"+r+"}");var i=o(r).val;i.attr=i.attr||{};var s=e.attr(t.persagyRely);return i.attr.bind=s==="true"?!0:!1,e.removeAttr(t.persagyCreateType),e.removeAttr(t.persagyCreateBind),e.removeAttr(t.persagyRely),i},persagy_tool.prototype.valueParse=function(e){var t=e==="true"?!0:e==="false"?!1:null;return t!=null?t:(t=parseFloat(e),isNaN(t)?e||"":t)},persagy_tool.prototype.elementAppendPattr=function(e,t,n,r){var i=t+this.typeSeparator+n;e[this.libraryToPro]={type:i,objBind:r}},persagy_tool.prototype.getInstanceFromDom=function(e){var t=this.getTypeAndChildTypeFromEle(e);return this.constructorCon(t.controlType)},persagy_tool.prototype.appendProToEvent=function(e,t){return e[this.eventOthAttribute]=t,e},persagy_tool.prototype.createDynamicTemplate=function(e,t){t=t||ptool.produceId();var n='<script type="text/html" id="'+t+'">'+e+"</script>";return $(document.body).append(n),t},persagy_tool.prototype.verifying=function(e,t,n){var r=(this.verifyType[e]||{}).stringFnName;return!r&&t[e]?t[e]():t[r](n)},persagy_tool.prototype.splitStrByKey=function(e,t){for(var n=0;n<t.length;n++){var r=t.substr(n,e.length);if(r==e){var i=t.substring(0,n),s=t.substr(n+e.length);return{start:i,key:e,end:s}}}return!1},persagy_tool.prototype.hideFormLibrary=function(){(new pcombobox).slideUp(),(new pcombobox).hideNotFirstLevelMenu(),(new psearch).slideUpFriend(),(new ptime).hideCalendar()},persagy_tool.prototype.isIe=function(){var e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;return t||n},persagy_tool.getInstance=function(){return persagy_tool.instanceFactory(persagy_tool)},persagy_tool.instanceFactory=function(name){var fn=typeof name=="string"?eval(name):name;return fn?fn._instance||(fn._instance=new fn)||fn._instance:!1};function persagy_event(){this.constructor=arguments.callee}persagy_event.prototype.createEvent=function(e,t,n,r){if(!e)return;$(e).on(n,r)},persagy_event.prototype.staticEventExecute=function(eventCallName){return function(fnName){return function(event){if(typeof fnName=="function")return fnName(event);var fn=eval(fnName);if(typeof fn=="function")return fn(event)}}(eventCallName)},persagy_event.getInstance=function(){return persagy_tool.instanceFactory(persagy_event)};function persagy_toBind(){this.ptool=persagy_tool.getInstance(),this.frameTypes={ko:"ko",Vue:"Vue"},this.currFrameType=null,this.attr={id:{bind:' id="',position:1},name:{bind:' name="',position:1},bind:{bind:"",position:3},disabled:{bind:' pdisabled="',position:1},value:{bind:' value="',position:1},text:{bind:"",position:2},errtext:{bind:"",position:2},tiptext:{bind:"",position:2},title:{bind:' ptitle="',position:1},name:{bind:' name="',position:1},icon:{bind:"",position:2},isborder:{bind:"",position:3},shape:{bind:"",position:3},state:{bind:"",position:3},subtitle:{bind:"",position:2},templateid:{bind:"",position:2},orientation:{bind:"",position:3},align:{bind:"",position:3},istree:{bind:"",position:3},datasource:{bind:"",position:3},placeholder:{bind:' placeholder="',position:1},isshade:{bind:"",position:3},accept:{bind:' accept="',position:1},number:{bind:"",position:3},arrange:{bind:"",position:3},visible:{bind:"",position:1}},this.event={click:"click",mousedown:"mousedown",mouseup:"mouseup",mouseover:"mouseover",mouseout:"mouseout",mouseenter:"mouseenter",mouseleave:"mouseleave",mousewheel:"mousewheel",change:"change",beforechange:"",input:"input",focus:"focus",blur:"blur",sel:"",beforesel:"",scroll:"",hide:"",beforehide:""},this.css={overallcss:'class="',iconcss:'class="'},this.getFrameName()}function persagy_ko(){this.constructor=arguments.callee,this.attr={id:{bind:"id:",position:1},name:{bind:" name:",position:1},bind:{bind:"",position:3},disabled:{bind:"pdisabled:",position:1},value:{bind:" value:",position:2},text:{bind:"text:",position:2},errtext:{bind:"text:",position:2},tiptext:{bind:"text:",position:2},title:{bind:"title:",position:1},name:{bind:" name:",position:1},icon:{bind:"text:",position:2},isborder:{bind:"",position:3},shape:{bind:"",position:3},state:{bind:"",position:3},subtitle:{bind:"text:",position:2},orientation:{bind:"",position:3},align:{bind:"",position:3},istree:{bind:"",position:3},datasource:{bind:"",position:3},placeholder:{bind:" placeholder:",position:1},isshade:{bind:"",position:3},accept:{bind:" accept:",position:1},number:{bind:"",position:3},arrange:{bind:"",position:3},templateid:{bind:"",position:2},visible:{bind:" visible:",position:1}},this.event={click:"click:",mousedown:"mousedown:",mouseup:"mouseup:",mouseover:"mouseover:",mouseout:"mouseout:",mouseenter:"mouseenter:",mouseleave:"mouseleave:",mousewheel:"mousewheel:",change:"",beforechange:"",input:"input:",focus:"focus:",blur:"blur:",sel:"",beforesel:"",scroll:"",hide:"",beforehide:""},this.css={}}function persagy_Vue(){this.attr={id:{bind:' :id="',position:1},name:{bind:' :name="',position:1},bind:{bind:"",position:3},disabled:{bind:' :pdisabled="',position:1},value:{bind:' v-model="',position:1},text:{bind:' v-text="',position:1},errtext:{bind:' v-text="',position:1},tiptext:{bind:' v-text="',position:1},title:{bind:' :title="',position:1},name:{bind:' :name="',position:1},icon:{bind:' v-text="',position:1},isborder:{bind:"",position:3},shape:{bind:"",position:3},state:{bind:"",position:3},subtitle:{bind:' v-text="',position:2},orientation:{bind:"",position:3},align:{bind:"",position:3},istree:{bind:"",position:3},datasource:{bind:"",position:3},placeholder:{bind:' :placeholder="',position:1},isshade:{bind:"",position:3},accept:{bind:' :accept="',position:1},number:{bind:"",position:3},arrange:{bind:"",position:3},templateid:{bind:"",position:2},visible:{bind:' v-show="',position:1}},this.event={click:' @click="',mousedown:' @mousedown="',mouseup:' @mouseup="',mouseover:' @mouseover="',mouseout:' @mouseout="',mouseenter:' @mouseenter="',mouseleave:' @mouseleave="',mousewheel:' @mousewheel="',change:"",beforechange:"",input:' @input="',focus:' @focus="',blur:' @blur="',sel:"",beforesel:"",scroll:"",hide:"",beforehide:""},this.css={}}persagy_toBind.prototype=persagy_event.getInstance(),persagy_toBind.getInstance=function(){return persagy_tool.instanceFactory(persagy_toBind)},persagy_toBind.prototype.getFrameName=function(){for(var frame in this.frameTypes){if(this.frameTypes.hasOwnProperty(frame)==0)continue;try{var currFrame=eval(this.frameTypes[frame]);return this.currFrameType=frame}catch(exception){}}},persagy_toBind.prototype.joinHtmlByAttrCss=function(e,t,n){t=t||{},n=n||{};for(var r in this.attr){if(this.attr.hasOwnProperty(r)==0)continue;var i=this.attr[r];if(i.position==3)continue;var s=this.ptool.attrPrefix+r+this.ptool.attrSuffix,o=e.indexOf(s);if(o==-1)continue;var u=o+s.length;if(!t[r]&&t[r]!==!1){e=e.substring(0,o)+e.substring(u);continue}switch(i.position){case 1:var a=i.bind+t[r]+this.ptool.cssAttrEnd;e=e.substring(0,o)+a+e.substring(u);break;case 2:var f=e.substring(0,o),l=e.substring(u),c=l.indexOf(">"),h=l.substring(0,c+1),p=l.substring(c+1),d=t[r];d=r.toLocaleLowerCase()!="templateid"?d:document.getElementById(d).innerHTML,e=f+h+d+p}}for(var v in n){if(n.hasOwnProperty(v)==0)continue;var m=this.ptool.attrPrefix+v+this.ptool.attrSuffix,g=e.indexOf(m);if(g==-1)continue;var y=g+m.length;if(!n[v]){e=e.substring(0,g)+e.substring(y);continue}var f=e.substring(0,g),h=e.substring(y),b=f.lastIndexOf("<"),w=f.lastIndexOf(this.ptool.cssStart),E=w+this.ptool.cssStart.length,S=h.indexOf(">"),x=h.indexOf(this.ptool.cssStart),T=x+this.ptool.cssStart.length;if(w>b){var l=f.substring(0,E),p=f.substring(E);e=l+n[v]+" "+p+h}else if(x<S){var l=h.substring(0,T),p=h.substring(T);e=f+l+n[v]+" "+p}else e=f+this.ptool.cssStart+n[v]+this.ptool.cssAttrEnd+h}for(var N in this.event){if(this.event.hasOwnProperty(N)==0)continue;var C=this.ptool.attrPrefix+N+this.ptool.attrSuffix,k=e.indexOf(C);if(k==-1)continue;var L=k+C.length;e=e.substring(0,k)+e.substring(L)}return e},persagy_toBind.prototype.joinHtmlToBindByAttrCss=function(e,t,n,r,i){t=t||{},n=n||{},r=r||{},i=i===!0?!0:!1,this.currFrameType||this.getFrameName();var s=persagy_tool.instanceFactory("persagy_"+this.currFrameType);return s.createBind(e,t,n,r,i)},persagy_toBind.prototype.createForBind=function(e,t,n){n=n===!0?!0:!1,this.currFrameType||this.getFrameName();var r=persagy_tool.instanceFactory("persagy_"+this.currFrameType);return r.createForBind(e,t,n)},persagy_toBind.prototype.createStyleBind=function(e,t){this.currFrameType||this.getFrameName();var n=persagy_tool.instanceFactory("persagy_"+this.currFrameType);return n.createStyleBind(e,t)},persagy_toBind.getInstance=function(){return persagy_tool.instanceFactory(persagy_toBind)},persagy_ko.prototype=new persagy_toBind,persagy_ko.prototype.createBind=function(e,t,n,r){function o(e){var n=e.indexOf(">");if(n==-1)return e;var o=e.substring(0,n),u=e.substring(n);if(n==0)return o=e.substring(0,1),u=e.substring(1),o+arguments.callee(u);var a="",f="";for(var l in i.attr){if(i.attr.hasOwnProperty(l)==0)continue;var c=i.attr[l];if(c.position==3)continue;var h=i.ptool.attrPrefix+l+i.ptool.attrSuffix,p=o.indexOf(h);if(p==-1)continue;var d=p+h.length;o=o.substring(0,p)+o.substring(d);var v=t[l];if(v==null)continue;if(l=="templateid"){u=u.substr(0,1)+document.getElementById(v).innerHTML+u.substr(1);continue}var m=c.bind+(v||"$data");switch(c.position){case 1:a+=(a?",":"")+m;break;case 2:f+=(f?",":"")+m}}a=a?"attr:{"+a+"}":"";var g=a+(a?",":"")+f,y="";for(var b in r){if(r.hasOwnProperty(b)==0)continue;var w=i.ptool.attrPrefix+b+i.ptool.attrSuffix,E=o.indexOf(w);if(E==-1)continue;var S=E+w.length;o=o.substring(0,E)+o.substring(S);if(!r[b])continue;y+=(y.length>0?",":"")+"'"+b+"':"+r[b]}y="css:{"+y+"}",y=g&&y?","+y:y;var x="";for(var T in i.event){if(i.event.hasOwnProperty(T)==0)continue;if(!i.event[T])continue;var N=i.ptool.attrPrefix+T+i.ptool.attrSuffix,C=o.indexOf(N);if(C==-1)continue;var k=C+N.length;o=o.substring(0,C)+o.substring(k),x+=(x?",":"")+i.event[T]+s.pbindEventFnName}x=x?"event:{"+x+"}":"",x=(g||y)&&x?","+x:x;var L=g||y||x?' data-bind="'+g+y+x+'"':"";return u=arguments.callee(u),o+L+u}var i=this,s=persagy_tool.getInstance();return o(e)},persagy_ko.prototype.createForBind=function(e,t){return"<!-- ko foreach: "+t+" -->"+e+"<!-- /ko -->"},persagy_ko.prototype.createStyleBind=function(e,t){t=t||{};var n="";for(var r in t){if(t.hasOwnProperty(r)==0)continue;var i=r,s=t[r];n+=(n.length>0?",":"")+"'"+i+"':"+s}n="style:{"+n+"}";var o=$(e),u=o.attr("data-bind");return u!=null&&(n=u+","+n),o.attr("data-bind",n),o[0].outerHTML},persagy_ko.getInstance=function(){return persagy_tool.instanceFactory(persagy_ko)},persagy_Vue.prototype=new persagy_toBind,persagy_Vue.prototype.createBind=function(e,t,n,r,i){function l(e,n){var i=n.indexOf(">");if(i==-1)return e+n;var f=n.substring(0,i),c=n.substring(i);if(i==0)return f=n.substring(0,1),c=n.substring(1),function(){return l(e+f,c)};var h="";for(var p in u.attr){if(u.attr.hasOwnProperty(p)==0)continue;var d=u.attr[p];if(d.position==3)continue;var v=u.ptool.attrPrefix+p+u.ptool.attrSuffix,m=f.indexOf(v);if(m==-1)continue;var g=m+v.length;f=f.substring(0,m)+f.substring(g);var y=t[p];if(y==null)continue;if(p=="templateid"){c=c.substr(0,1)+document.getElementById(y).innerHTML+c.substr(1);continue}var b=d.bind+s+(y?o+y:"")+u.ptool.cssAttrEnd;h+=b}var w="";for(var E in r){if(r.hasOwnProperty(E)==0)continue;var S=u.ptool.attrPrefix+E+u.ptool.attrSuffix,x=f.indexOf(S);if(x==-1)continue;var T=x+S.length;f=f.substring(0,x)+f.substring(T);if(!r[E])continue;w+=(w?",":"")+"'"+E+"':"+s+r[E]}w=w?' v-bind:class="{'+w+'}"':"";var N="";for(var C in u.event){if(u.event.hasOwnProperty(C)==0)continue;if(!u.event[C])continue;var k=u.ptool.attrPrefix+C+u.ptool.attrSuffix,L=f.indexOf(k);if(L==-1)continue;var A=L+k.length;f=f.substring(0,L)+f.substring(A),N+=u.event[C]+a.pbindEventFnName+"(model,$event)"+u.ptool.cssAttrEnd}var O=h+w+N;return function(){return l(e+f+O,c)}}function c(e){var n=e.indexOf(">");if(n==-1)return e;var i=e.substring(0,n),f=e.substring(n);if(n==0)return i=e.substring(0,1),f=e.substring(1),i+arguments.callee(f);var l="";for(var c in u.attr){if(u.attr.hasOwnProperty(c)==0)continue;var h=u.attr[c];if(h.position==3)continue;var p=u.ptool.attrPrefix+c+u.ptool.attrSuffix,d=i.indexOf(p);if(d==-1)continue;var v=d+p.length;i=i.substring(0,d)+i.substring(v);var m=t[c];if(m==null)continue;if(c=="templateid"){f=f.substr(0,1)+document.getElementById(m).innerHTML+f.substr(1);continue}var g=h.bind+s+(m?o+m:"")+u.ptool.cssAttrEnd;l+=g}var y="";for(var b in r){if(r.hasOwnProperty(b)==0)continue;var w=u.ptool.attrPrefix+b+u.ptool.attrSuffix,E=i.indexOf(w);if(E==-1)continue;var S=E+w.length;i=i.substring(0,E)+i.substring(S);if(!r[b])continue;y+=(y?",":"")+"'"+b+"':"+s+r[b]}y=y?' v-bind:class="{'+y+'}"':"";var x="";for(var T in u.event){if(u.event.hasOwnProperty(T)==0)continue;if(!u.event[T])continue;var N=u.ptool.attrPrefix+T+u.ptool.attrSuffix,C=i.indexOf(N);if(C==-1)continue;var k=C+N.length;i=i.substring(0,C)+i.substring(k),x+=u.event[T]+a.pbindEventFnName+"(model,$event)"+u.ptool.cssAttrEnd}var L=l+y+x;return f=arguments.callee(f),i+L+f}var s=i===!0?"model":"",o=i===!0?".":"",u=this,a=persagy_tool.getInstance(),f=l("",e);while(typeof f=="function")f=f();return f},persagy_Vue.prototype.createForBind=function(e,t,n){var r=e.indexOf(">"),i=e.substring(0,r),s=e.substring(r);return i=i+" "+'v-for="model in '+(n===!0?"model.":"")+t+'"',i+s},persagy_Vue.prototype.createStyleBind=function(e,t){t=t||{};var n="";for(var r in t){if(t.hasOwnProperty(r)==0)continue;var i=r,s=t[r];n+=(n.length>0?",":"")+"'"+i+"':"+s}n="{"+n+"}";var o=$(e);return o.attr("v-bind:style",n),o[0].outerHTML},persagy_Vue.getInstance=function(){return persagy_tool.instanceFactory(persagy_Vue)};function persagyElement(){this.constructor=arguments.callee,this.extendFn={pinit:function(e,t,n){persagy_tool.getInstance().createControl(e,t,this,n)},prender:function(){var e=persagy_tool.getInstance(),t=persagyElement.getInstance(),n=t.controlTypes,r="";for(var i in n){if(n.hasOwnProperty(i)==0)continue;var s=n[i].types;for(var o=0;o<s.length;o++){var u=s[o];r=r+(r.length>0?",":"")+i+e.typeSeparator+u}}var a=$(this).find(r);a.length>0&&a.each(function(){var t=e.getTypeAndChildType(this.tagName.toLowerCase());e.createControl(t.controlType,t.childType,this,null)})},psel:"",ptoggle:"",pshow:"",phide:"",psetScroll:"",pcss:"",pval:"",phideTextTip:function(){var e=$(this),t=new ptext_template;e.find("["+t.errMarker+"],["+t.friendlyMarker+"]").hide(),e.find("["+t.textMarker+"]").removeClass(t.errCss),e.attr(t.ptool.verifyResult,"true")},pshowTextTip:function(e){var t=$(this),n=new ptext_template;t.find("["+n.errTextMarker+"]").text(e),t.find("["+n.errMarker+"]").show(),t.find("["+n.textMarker+"]").addClass(n.errCss),t.attr(n.ptool.verifyResult,"false")},pverifi:function(){var e=$(this),t=persagy_tool.getInstance(),n=persagyElement.getInstance().controlTypes,r=e.find("["+t.maxDivMarker+"]");e.attr(t.maxDivMarker)!=null?r.push(e[0]):"";var i=new ptext_template,s=!0;for(var o=0;o<r.length;o++){var u=r.eq(o);if(u.attr(t.verifyResult)=="false"){s=!1;continue}var a=t.getTypeAndChildTypeFromEle(u),f,l;a.controlType==n.ptext.name?(f=u.pval(),l=ptext):a.controlType==n.pcombobox.name?(f=u.psel().text,l=pcombobox):(f="",l="");if(!l)continue;var c=u.attr(t.libraryIdToHtml),h=l[c],p=h.attr,d=p.verify;d instanceof Array||(d=[p.verify]);var v=JSON.stringify(d);if(v.indexOf(':"space"')==-1&&!f)continue;for(var m=0;m<d.length;m++){var g=d[m];if(!g.verifytype)continue;var y=g.verifytype===t.verifyType.length.name?parseInt(g.length):"",b=t.verifying(g.verifytype,f,y);g.verifytype==i.ptool.verifyType["space"].name&&(b=!b);if(!b){u.pshowTextTip(g.errtip||""),s=!1;break}u.phideTextTip()}}return s},precover:function(){var e=$(this),t=persagy_tool.getInstance(),n=persagyElement.getInstance(),r=e.find("["+t.maxDivMarker+"]");e.attr(t.maxDivMarker)!=null?r.push(e[0]):"";for(var i=0;i<r.length;i++){var s=r[i],o=t.getInstanceFromDom(s);if(!o)continue;if(typeof o.precover!="function")continue;[].unshift.call(arguments,s),o.precover.apply(o,arguments)}},pcount:"",plock:"",psetHeaderSort:"",pslideUp:"",pslideDown:""},this.controlTypes={pbutton:{name:"pbutton",types:["white","blue","borderred","backred","menumain","menuminor","whiteloading","blueloading"]},pcombobox:{name:"pcombobox",types:["normal","custom","menumain","menuminor","text","page","time"]},pswitch:{name:"pswitch",types:["checkbox","radio","slide"]},pnotice:{name:"pnotice",types:["message","nodata"]},ploading:{name:"ploading",types:["global","part"]},pscroll:{name:"pscroll",types:["small"]},ptab:{name:"ptab",types:["button","text","navigation"]},ptext:{name:"ptext",types:["text","textarea","combobox"]},pwindow:{name:"pwindow",types:["global","modal","confirm","float","bubble"]},pupload:{name:"pupload",types:["attachment","img"]},ppage:{name:"ppage",types:["simple","full"]},psearch:{name:"psearch",types:["delay","promptly"]},ptree:{name:"ptree",types:["normal"]},ptime:{name:"ptime",types:["form","calendar"]},pgrid:{name:"pgrid",types:["normal","multifunction"]},plogin:{name:"plogin",types:["normal"]},pframe:{name:"pframe",types:["normal"]}}}persagyElement.prototype=persagy_toBind.getInstance(),persagyElement.prototype.controlInit=function(e,t,n){var r=n||this.ptool.oldGetBind(t)||!1,i=ptool.getJqElement(t);if(!r){var s,o={},u={},a={};for(var f in this.attr){if(this.attr.hasOwnProperty(f)==0)continue;s=i[0].getAttribute(f);if(!s)continue;o[f]=this.ptool.valueParse(s),i.removeAttr(f)}for(var l in this.event){if(this.event.hasOwnProperty(l)==0)continue;s=i.attr(l);if(!s)continue;u[l]=s,i.removeAttr(l)}for(var c in this.css){if(this.css.hasOwnProperty(c)==0)continue;s=i.attr(c);if(!s)continue;a[c]=s,i.removeAttr(c)}var h=ptool.getDomElement(t),p=h.attributes,d=null;for(var v=0;v<p.length;v++){var m=p[v];d||(d={}),d[m.name]=m.value}r={attr:o,event:u,css:a,customAttr:d}}return r.attr.bind=r.attr.bind===!0?!0:!1,this.init(e,r,i)},persagyElement.prototype.renderView=function(e,t,n,r,i,s){var o="<div>"+e+"</div>",u=$(o);u.prender(),e=u.html().replace(/(\}\}\=\"\")/g,"}}");var a=ptool.produceId(),f=this.createHtml(e,r,t,n);return this.constructor[a]=r,this.htmlReplace(i,f,r,t,n,a,s)},persagyElement.prototype.createHtml=function(e,t,n,r){var i=t.attr,s=t.customAttr,o=t.event,u=t.css;if(s){var a=e.indexOf(">"),f=e.substring(0,a),l=e.substring(a);for(var c in s){if(s.hasOwnProperty(c)==0)continue;f+=" "+c+'="'+s[c]+'"'}e=f+l}return i.bind===!1?this.joinHtmlByAttrCss(e,i,u):this.joinHtmlToBindByAttrCss(e,i,o,u)},persagyElement.prototype.htmlReplace=function(e,t,n,r,i,s,o){var u=ptool.getJqElement(e),a=$(t),f=r+this.ptool.typeSeparator+i;return a.attr(this.ptool.libraryTypeToHtml,f),a.attr(this.ptool.libraryIdToHtml,s),typeof o=="function"&&(a=o(a)),a.replaceAll(u)},persagyElement.prototype.eventHandout=function(e,t){var n=t.currentTarget,r=$(n),i=this.ptool.getInstanceFromDom(n),s=r.attr(this.ptool.libraryIdToHtml),o=i.constructor[s],u=o.event||{},a=t.type,f=u[a];return f?this.executeEventCall(e,t,f):null},persagyElement.prototype.executeEventCall=function(model,event,fnName){if(!fnName)return null;if(typeof fnName=="function")return model?fnName(model,event):fnName(event);var fn=eval(fnName);if(typeof fn=="function")return model?fn(model,event):fn(event)},persagyElement.getInstance=function(){return persagy_tool.instanceFactory(persagyElement)};var persagy_control={};void function(){function n(){$(function(){var n=$(document.body);t.createEvent(n,null,"DOMNodeInserted",function(n){function i(n){function h(t){var n=t.children();for(var r=0;r<n.length;r++){var s=n.eq(r);if(s.attr(e.maxDivMarker)!=null){i(s[0]);continue}arguments.callee(s)}}var r=$(n),s=e.getTypeAndChildTypeFromEle(n),o=s.controlType;h(r);var u=t.controlTypes;if(!u[o])return;var a=n[e.libraryToPro]||{};if(a[e.isRenderedToProName]===!0)return;if(r.attr(e.maxDivMarker)==null)return;var f=e.getInstanceFromDom(n),l=r.attr(e.libraryIdToHtml),c=f.constructor[l];f.rendered(n,c,s.childType),a=n[e.libraryToPro]||{},a[e.isRenderedToProName]=!0,n[e.libraryToPro]=a}var r=n.srcElement||n.target;i(r)}),t.createEvent(n,null,"mousemove",function(e){(new pscroll).mouseMoveForScroll(e)}),t.createEvent(n,null,"mouseup",function(t){var n=document[e.libraryToPro]||{};n._vIsDown=!1,n._prevY=null,n._hIsDown=!1,n._prevX=null,n._ele=null,document[e.libraryToPro]=n}),t.createEvent(n,null,"click",function(){(new pwindow).bodyClick(),e.hideFormLibrary()}),t.createEvent(n,null,"mouseover",function(){(new pcombobox).hideNotFirstLevelMenu()}),$('script[type="text/html"],script[type="text/template"]').each(function(){var e=$(this),t="<div>"+e.html().replace(/\s+|\n/g," ").replace(/>\s</g,"><").ptrimHeadTail()+"</div>",n=$(t);n.prender(),e.html(n.html())}),n.find("["+e.persagyCreateType+"]").each(function(){var t=e.getTypeByPcreate(this);e.createControl(t.controlType,t.childType,this,null)}),$(document.body).prender()})}var e=persagy_tool.getInstance(),t=persagyElement.getInstance();n(),Object.freeze(persagy_control)}();(function(){function n(e,n){return function(e,n){return function(){var r=ptool.getDomElement(this),i=t.getInstanceFromDom(r);while(i===!1){r=$(r).children()[0];if(!r)break;i=t.getInstanceFromDom(r)}return!i&&typeof n!="function"?!1:i&&typeof i[e]=="function"?((new Array).unshift.call(arguments,r),i[e].apply(i,arguments)):typeof n=="function"?n.apply(this,arguments):!1}}(e,n)}function u(e,n){n.stopPropagation&&n.stopPropagation();var r=n.srcElement||n.target;n.srcElement=n.target=r;var i=n.currentTarget,s=t.getInstanceFromDom(i),o=0;while(!s){++o;if(o>100)break;i=$(i).parent()[0],s=t.getInstanceFromDom(i)}if(!s)return;n[t.eventCurrTargetName]=i,s.eventHandout(e,n)}var e=persagyElement.getInstance(),t=persagy_tool.getInstance(),r=e.extendFn;for(var i in r){if(r.hasOwnProperty(i)===!1)continue;var s=r[i],o=n(i,s);HTMLElement.prototype[i]=o,jQuery&&(jQuery.fn[i]=o)}window.model||(window.model={}),window[t.pbindEventFnName]=function(e,t){u(e,t)},window[t.pstaticEventFnName]=function(e){u(null,e)}})();function pbutton_template(){this.constructor=arguments.callee,this.start="<div "+this.ptool.maxDivMarker+' {{id}}{{disabled}}{{click}}{{mousedown}}{{mouseup}}{{mouseover}}{{mouseout}}{{mouseenter}}{{mouseleave}}{{mousewheel}} class="',this.textIconEnd='"><i {{icon}} class="per-bIcon"></i><em {{text}}{{title}}></em></div>',this.white=this.start+"per-button-grayBorder"+this.textIconEnd,this.blue=this.start+"per-button-grayBg"+this.textIconEnd,this.borderred=this.start+"per-button-redBorder"+this.textIconEnd,this.backred=this.start+"per-button-redBg"+this.textIconEnd,this.textEnd='"{{title}}><em {{text}}></em></div>',this.whiteText=this.start+"per-button-grayBorder"+this.textEnd,this.blueText=this.start+"per-button-grayBg"+this.textEnd,this.borderredText=this.start+"per-button-redBorder"+this.textEnd,this.backredText=this.start+"per-button-redBg"+this.textEnd,this.iconEnd='"{{title}}><i {{icon}}></i></div>',this.whiteIconRectangle=this.start+"per-squarebutton-grayBorder"+this.iconEnd,this.blueIconRectangle=this.start+"per-squarebutton-grayBg"+this.iconEnd,this.borderredIconRectangle=this.start+"per-squarebutton-redBorder"+this.iconEnd,this.backredIconRectangle=this.start+"per-squarebutton-redBg"+this.iconEnd,this.whiteIconEllipse=this.start+"per-iconbutton-grayBorder"+this.iconEnd,this.blueIconEllipse=this.start+"per-iconbutton-grayBg"+this.iconEnd,this.borderredIconEllipse=this.start+"per-iconbutton-redBorder"+this.iconEnd,this.backredIconEllipse=this.start+"per-iconbutton-redBg"+this.iconEnd,this.whiteNoBorder=this.start+"per-noBorderButton-white"+this.textIconEnd,this.blueNoBorder=this.start+"per-noBorderButton-gray"+this.textIconEnd,this.borderredNoBorder=this.backRedNoBorder=this.start+"per-noBorderButton-red"+this.textIconEnd,this.whiteTextNoBorder=this.start+"per-noBorderButton-white"+this.textEnd,this.blueTextNoBorder=this.start+"per-noBorderButton-gray"+this.textEnd,this.borderredTextNoBorder=this.backRedNoBorder=this.start+"per-noBorderButton-red"+this.textEnd,this.whiteIconNoBorder=this.start+"per-noBorderButton-white"+this.iconEnd,this.blueIconNoBorder=this.start+"per-noBorderButton-gray"+this.iconEnd,this.redIconNoBorder=this.start+"per-noBorderButton-red"+this.iconEnd}pbutton_template.prototype=new persagyElement,pbutton_template.prototype.getTemplateStr=function(e,t){return"<div "+this.ptool.maxDivMarker+' {{id}}{{disabled}}{{click}}{{mousedown}}{{mouseup}}{{mouseover}}{{mouseout}}{{mouseenter}}{{mouseleave}}{{mousewheel}} class="'+(e==this.controlTypes.pbutton.types[6]?"per-button-grayBorder":e==this.controlTypes.pbutton.types[7]?"per-button-grayBg":"")+'"><i class="per-bloading"><svg class="per-loading-verysmall_svg" viewBox="25 25 50 50">'+'<circle cx="50" cy="50" r="22" fill="none" class="path"></circle></svg></i><em {{text}}></em></div>'};function pbutton(){this.constructor=arguments.callee}pbutton.prototype=new pbutton_template,pbutton.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css;if(e==this.controlTypes.pbutton.types[4]||e==this.controlTypes.pbutton.types[5])return(new pcombobox).init(e,t,n);var o="";if(e==this.controlTypes.pbutton.types[6]||e==this.controlTypes.pbutton.types[7])o=this.getTemplateStr(e,t);else{r.isborder=r.isborder===!1?!1:!0,r.shape=r.shape||this.ptool.shape.rectangle;var u=e,a=r.text&&r.icon?"":r.text&&!r.icon?"Text":r.icon&&!r.text?"Icon":"",f=r.isborder==0?"NoBorder":r.text?"":r.icon&&r.shape===this.ptool.shape.rectangle?"Rectangle":r.icon&&r.shape===this.ptool.shape.ellipse?"Ellipse":"",l=u+a+f;o=this[l]}this.renderView(o,this.controlTypes.pbutton.name,e,t,n)},pbutton.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e),o=s.find("em");o=o.length>0?o:s,s.registerEventForTitle(o,this.ptool.titleSourceAttr),r.isborder===!1?r.text&&r.icon?(s.registerEventForColorChange(s.find("i"),1),s.registerEventForColorChange(s.find("em"),1)):s.registerEventForColorChange(null,1):(n==this.controlTypes.pbutton.types[0]||n==this.controlTypes.pbutton.types[1]||n==this.controlTypes.pbutton.types[2]||n==this.controlTypes.pbutton.types[3])&&s.registerEventForColorChange(null,0);if(r.bind===!0)return;for(var u in i){if(i.hasOwnProperty(u)==0)continue;this.createEvent(s,this.controlTypes.pbutton.name,u,window[this.ptool.pstaticEventFnName])}};function pswitch_template(){this.constructor=arguments.callee,this.selCss={checkbox:"per-checkbox-checked",radio:"per-radio-checked",slide:"per-slide-checked"},this.start="<div "+this.ptool.maxDivMarker+' {{id}}{{title}}{{disabled}}{{click}}{{mousedown}}{{mouseup}}{{mouseover}}{{mouseout}}{{mouseenter}}{{mouseleave}} class="',this.checkbox=this.start+'per-switch-checkbox">',this.checkboxSpan1="<span{{"+this.selCss.checkbox+'}} class="per-checkbox_input',this.checkboxSpan2='"></span>',this.checkbox3='<span class="per-switch_label"{{text}}></span></div>',this.radio=this.start+'per-switch-radio"{{name}}>',this.radioSpan1="<span{{"+this.selCss.radio+'}} class="per-radio_input',this.radioSpan2='"></span>',this.radio3='<span class="per-switch_label" {{text}}></span></div>',this.slide=this.start+'per-switch-slide">',this.slideSpan1="<span{{"+this.selCss.slide+'}} class="per-slide-Bg',this.slideSpan2='"></span>',this.slide3='<span class="per-switch_round"></span></div>'}pswitch_template.prototype=new persagyElement,pswitch_template.prototype.getTemplateStr=function(e,t){var n=e.attr,r=this.selCss[t],i={},s;if(n.bind!==!0){i[r]="";var o=n.state===!0?" "+r:"";return s=this[t+"Span1"]+o+this[t+"Span2"],s=this.joinHtmlByAttrCss(s,{},i),this[t]+s+this[t+"3"]}var u="";return s=this[t+"Span1"]+this[t+"Span2"],i[r]=n.state||"",s=this.joinHtmlToBindByAttrCss(s,{},{},i),this[t]+s+this[t+"3"]};function pswitch(){this.constructor=arguments.callee}pswitch.prototype=new pswitch_template,pswitch.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css||{},o=this.getTemplateStr(t,e);this.renderView(o,this.controlTypes.pswitch.name,e,t,n)},pswitch.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e),o=new MutationObserver(function(e,t){var n=e[0];n.attributeName=="class"&&(n.target[pconst.targetHoverCssSourcePro]=[],n.target.style.backgroundColor="")});o.observe(s.find("span")[0],{attributes:!0}),r.bind===!1&&this.createEvent(s,this.controlTypes.pswitch.name,"click",window[this.ptool.pstaticEventFnName]);var u=s.find("span:last");s.registerEventForTitle(u,this.ptool.titleSourceAttr),s.registerEventForColorChange(s.find("span:first"),0);if(r.bind===!0)return;for(var a in i){if(i.hasOwnProperty(a)==0)continue;if(a=="click")continue;this.createEvent(s,this.controlTypes.pswitch.name,a,window[this.ptool.pstaticEventFnName])}},pswitch.prototype.eventHandout=function(model,event){var domTarget=event.currentTarget,jqElement=$(domTarget),_id=jqElement.attr(this.ptool.libraryIdToHtml),objBind=pswitch[_id],attr=objBind.attr,orginEvent=objBind.event||{},eventName=event.type,typeObj=this.ptool.getTypeAndChildTypeFromEle(domTarget),selCss=this.selCss[typeObj.childType],selCssToElement=jqElement.find("span").eq(0),currState=selCssToElement.hasClass(selCss)?!0:!1;event=this.ptool.appendProToEvent(event,{state:currState});if(eventName=="click"){var beforeChangeFn=orginEvent.beforechange,beforeResult=!0;beforeChangeFn&&(beforeResult=this.executeEventCall(model,event,beforeChangeFn));if(beforeResult!==!1){var isToSel=!0;typeObj.childType==this.controlTypes.pswitch.types[1]&&(currState==0?this.pgroupSelToClear(domTarget):isToSel=!1);if(isToSel){this.psetSelState(domTarget,selCssToElement,selCss),currState=selCssToElement.hasClass(selCss)?!0:!1,event=this.ptool.appendProToEvent(event,{state:currState});if(model&&attr.state&&attr.bind==1){var evalStr=attr.state+"="+currState;eval(evalStr)}var changeFn=orginEvent.change;changeFn&&this.executeEventCall(model,event,changeFn)}}}var eventFn=orginEvent[eventName];if(!eventFn)return;this.executeEventCall(model,event,eventFn)},pswitch.prototype.pgroupSelToClear=function(e){var t=ptool.getDomElement(e),n=this.ptool.getTypeAndChildTypeFromEle(t),r=this.selCss[n.childType],i=$(e).attr("name");$('[name="'+i+'"]').find("span").removeClass(r)},pswitch.prototype.psetSelState=function(e,t,n){t.toggleClass(n)},pswitch.prototype.psel=function(e,t){var n=arguments[0],r=$(n),i=this.ptool.getTypeAndChildTypeFromEle(n),s=this.selCss[i.childType],o=r.find("span").eq(0),u=o.hasClass(s)?!0:!1;if(arguments.length==1)return u;e=arguments[1],t=arguments[2],t=t===!1?!1:!0;if(u===e)return u;if(t)return n.click();switch(e){case!0:i.childType==this.controlTypes.pswitch.types[1]&&this.pgroupSelToClear(n),this.psetSelState(n,o,s);break;case!1:if(i.childType==this.controlTypes.pswitch.types[1])return u;o.removeClass(s)}},pswitch.prototype.ptoggle=function(e){var t=arguments[0];e=arguments[1];var n=$(t),r=this.ptool.getTypeAndChildTypeFromEle(t),i=this.selCss[r.childType],s=n.find("span").eq(0),o=s.hasClass(i)?!1:!0;this.psel(t,o,e)};function pnotice_template(){this.constructor=arguments.callee,this.messCss={success:["per-notice-success","per-notice_successicon"],failure:["per-notice-failure","per-notice_failureicon"]},this.message="<div "+this.ptool.maxDivMarker+' class="per-notice-common" {{id}}><i></i><em></em></div>',this.nodata1="<div "+this.ptool.maxDivMarker+' {{id}} class="per-prompt-abnormalmess">',this.nodataImgStart='<img src="',this.nodataImgEnd='">',this.nodataStr='<span class="per-prompt_icon"></span>',this.nodata2='<span class="per-prompt_title" {{text}}></span><span class="per-prompt_subtitle" {{subtitle}}></span></div>'}pnotice_template.prototype=new persagyElement,pnotice_template.prototype.getTemplateStr=function(e,t){switch(e){case this.controlTypes.pnotice.types[0]:return this.message;default:var n=t.attr,r;return n.icon?r=this.nodataImgStart+n.icon+this.nodataImgEnd:r=this.nodataStr,this.nodata1+r+this.nodata2}};function pnotice(){this.constructor=arguments.callee,this.timeoutTime=2e3,this.showCss={name:"margin-top",value:"0px"},this.hideCss={name:"margin-top",value:"-70px"},this.animateTime=500}pnotice.prototype=new pnotice_template,pnotice.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css||{},o=this.getTemplateStr(e,t);this.renderView(o,this.controlTypes.pnotice.name,e,t,n)},pnotice.prototype.rendered=function(e,t,n){n==this.controlTypes.pnotice.types[0]&&(this.createEvent(e,this.controlTypes.pnotice.name,"mouseenter",window[this.ptool.pstaticEventFnName]),this.createEvent(e,this.controlTypes.pnotice.name,"mouseleave",window[this.ptool.pstaticEventFnName]))},pnotice.prototype.eventHandout=function(e,t){var n=this,r=$(t[n.ptool.eventCurrTargetName]),i=r[0],s=t.type,o=n.ptool.getTypeAndChildTypeFromEle(r);switch(s){case"mouseenter":if(o.childType==n.controlTypes.pnotice.types[0]){var u=i[n.ptool.libraryToPro]||{},a=u[n.ptool.timerNameToElement];a&&clearTimeout(a)}break;case"mouseleave":o.childType==n.controlTypes.pnotice.types[0]&&n.phideMessage(r)}},pnotice.prototype.phideMessage=function(e){var t={};t[this.hideCss.name]=this.hideCss.value,e.animate(t,this.animateTime),e[0][this.ptool.libraryToPro][this.ptool.timerNameToElement]=null},pnotice.prototype.pshow=function(e){if(arguments.length==1)return;var t=this,n=arguments[0];e=arguments[1]||{};var r=$(n),i=t.ptool.getTypeAndChildTypeFromEle(n);if(i.childType==t.controlTypes.pnotice.types[0]){var s=t.messCss[e.state||t.ptool.pcontrolState.success]||[],o=t.messCss[t.ptool.pcontrolState.success][0]+" "+t.messCss[t.ptool.pcontrolState.failure][0];r.removeClass(o),r.addClass(s[0]||""),o=t.messCss[t.ptool.pcontrolState.success][1]+" "+t.messCss[t.ptool.pcontrolState.failure][1];var u=r.find("i");u.removeClass(o),u.addClass(s[1]||""),r.find("em").text(e.text||"");var a=n[t.ptool.libraryToPro]||{},f=a[t.ptool.timerNameToElement];f&&clearTimeout(f);var l={};l[t.showCss.name]=t.showCss.value,r.animate(l,t.animateTime,function(){a[t.ptool.timerNameToElement]=setTimeout(function(){t.phideMessage(r)},t.timeoutTime)})}else{var c=r.find("span");e.text&&c.eq(1).text(e.text),e.subTitle&&c.eq(2).text(e.subTitle),r.show()}},pnotice.prototype.phide=function(){var e=arguments[0],t=$(e),n=this.ptool.getTypeAndChildTypeFromEle(e);if(n.childType==this.controlTypes.pnotice.types[0])return;t.hide()};function ploading_template(){this.constructor=arguments.callee,this.global="<div "+this.ptool.maxDivMarker+' class="per-loading-overall" {{id}}><div class="loading-con">'+'<div class="per-loading-overall_pic"></div>'+'<div class="per-loading-overall_text" {{text}}></div></div></div>',this.part="<div "+this.ptool.maxDivMarker+' class="per-loading-nomal" {{id}}><div class="loading-con">'+'<svg class="per-loading-nomal_svg" viewBox="25 25 50 50"><circle cx="50" cy="50" r="22" fill="none" class="path" /></svg>'+'<div class="per-loading-nomal_text" {{text}}></div></div></div>'}ploading_template.prototype=new persagyElement;function ploading(){this.constructor=arguments.callee}ploading.prototype=new ploading_template,ploading.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css||{},o=this[e];this.renderView(o,this.controlTypes.ploading.name,e,t,n)},ploading.prototype.rendered=function(e,t,n){},ploading.prototype.pshow=function(e){var t=this,n=arguments[0];e=arguments[1];var r=$(n);e&&r.find("div").text(e),r.show()},ploading.prototype.phide=function(){var e=arguments[0],t=$(e);t.hide()};function pscroll_template(){this.constructor=arguments.callee,this.externalDivMarker="scrollmax",this.scrollDivMarker="scroll",this.scrollDivParentMarker="scrollparent",this.horizontalDivMarker="horizontal",this.horizontalBarMarker="horizontalbar",this.verticalDivMarker="vertical",this.verticalBarMarker="verticalbar",this.small="<div "+this.ptool.maxDivMarker+' class="per-scrollbar" '+this.externalDivMarker+'{{id}}><div class="per-scrollbar_wrap"'+this.scrollDivParentMarker+'><div class="per-scrollbar_actual" '+this.scrollDivMarker+"{{templateid}}></div></div>"+'<div class="per-scrollbar__bar per-is-horizontal" '+this.horizontalDivMarker+">"+'<div class="per-scrollbar__thumb" '+this.horizontalBarMarker+"></div></div>"+'<div class="per-scrollbar__bar per-is-vertical" '+this.verticalDivMarker+">"+'<div class="per-scrollbar__thumb" '+this.verticalBarMarker+"></div></div></div>"}pscroll_template.prototype=new persagyElement;function pscroll(){this.constructor=arguments.callee}pscroll.prototype=new pscroll_template,pscroll.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css||{};r.bind=!1;var o=this[e];this.renderView(o,this.controlTypes.pscroll.name,e,t,n)},pscroll.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e),o=s.find("["+this.scrollDivMarker+"]"),u=s.find("["+this.scrollDivParentMarker+"]"),a=s.find("["+this.horizontalDivMarker+"]"),f=s.find("["+this.verticalDivMarker+"]"),l=s.find("["+this.verticalBarMarker+"]"),c=s.find("["+this.horizontalBarMarker+"]");this.createEvent(s,this.controlTypes.pscroll.name,"mouseenter",this.setEventCall(e)),this.createEvent(s,this.controlTypes.pscroll.name,"mouseleave",this.setEventCall(e)),this.createEvent(u,this.controlTypes.pscroll.name,"scroll",this.setEventCall(e)),this.createEvent(a,this.controlTypes.pscroll.name,"click",this.setEventCall(e)),this.createEvent(f,this.controlTypes.pscroll.name,"click",this.setEventCall(e)),this.createEvent(l,this.controlTypes.pscroll.name,"mousedown",this.setEventCall(e)),this.createEvent(c,this.controlTypes.pscroll.name,"mousedown",this.setEventCall(e))},pscroll.prototype.eventHandout=function(e,t){function D(){var e=0,n=0;if(x>0){e=p.scrollTop();var r=Math.division(e,E);r=Math.multiplication(r,100),g.css({transform:" translateY("+r+"%)"})}if(w>0){n=p.scrollLeft();var i=Math.division(n,y);i=Math.multiplication(i,100),v.css({transform:" translateX("+i+"%)"})}a.scroll&&(t=s.ptool.appendProToEvent(t,{maxScrollTop:x,currScrollTop:e,maxScrollLeft:w,scrollLeft:n}),s.executeEventCall(null,t,a.scroll))}var n=t.currentTarget,r=$(n),i=$(t.srcElement||t.target),s=this,o=r.attr(s.ptool.libraryIdToHtml),u=pscroll[o],a=u.event||{},f=t.type;if(f=="mouseenter"){var l=r.parent().height(),c=r.parent().width();r.css({"max-width":c+"px","max-height":l+"px"}),s.setClientSize(r),s.setScrollbarSize(r)}var h=r.find("["+s.scrollDivMarker+"]"),p=r.find("["+s.scrollDivParentMarker+"]"),d=r.find("["+s.horizontalDivMarker+"]"),v=r.find("["+s.horizontalBarMarker+"]"),m=r.find("["+s.verticalDivMarker+"]"),g=r.find("["+s.verticalBarMarker+"]"),y=r.width(),b=h.width(),w=b-y,E=r.height(),S=h.height(),x=S-E,T=document[this.ptool.libraryToPro]||{};switch(f){case"mousedown":i.attr(this.verticalBarMarker)!=null&&(T._vIsDown=!0,T._prevY=t.pageY,T._ele=n),i.attr(this.horizontalBarMarker)!=null&&(T._hIsDown=!0,T._prevX=t.pageX,T._ele=n);break;case"click":if(i.attr(this.verticalDivMarker)!=null){var N=t.offsetY,C=g.height(),k=N/C*100,L=k/100*r.height();r.psetScroll(L)}if(i.attr(this.horizontalDivMarker)!=null){var A=t.offsetX,O=v.width(),M=A/O*100,_=M/100*r.width();r.psetScroll(_,this.ptool.arrangeType.horizontal)}break;case"scroll":D();break;case"mouseenter":x>0?m.show():m.hide(),w>0?d.show():d.hide(),a.mouseenter&&s.executeEventCall(null,t,a.mouseenter);break;case"mouseleave":m.hide(),d.hide(),a.mouseleave&&s.executeEventCall(null,t,a.mouseleave);break;case"DOMSubtreeModified":if(!m.is(":hidden")||!d.is(":hidden"))return;p.scrollTop(0),p.scrollLeft(0),p.width(""),p.height(""),v.css({transform:" translateX(0%)"}),g.css({transform:" translateY(0%)"}),r.css({"max-width":"","max-height":""})}document[this.ptool.libraryToPro]=T},pscroll.prototype.setEventCall=function(e){return function(e){return function(t){t.currentTarget=e,window[persagy_tool.getInstance().pstaticEventFnName](t)}}(e)},pscroll.prototype.mouseMoveForScroll=function(e){var t=document[this.ptool.libraryToPro]||{};if(t._vIsDown!==!0&&t._hIsDown!==!0)return;var n=$(t._ele),r=n.find("["+this.scrollDivMarker+"]"),i=n.find("["+this.horizontalBarMarker+"]"),s=n.find("["+this.verticalBarMarker+"]"),o=n.width(),u=n.height(),a=r.height(),f=r.width(),l=Math.division(u,a),c=Math.division(o,f),h=Math.subtraction(a,u),p=Math.subtraction(f,o);if(t._vIsDown){var d=e.pageY,v=Math.subtraction(d,t._prevY||0),m=n.find("["+this.scrollDivParentMarker+"]").scrollTop(),g=Math.summation(m,Math.division(v,l));t._prevY=d,g=Math.min(g,h),g=Math.max(g,0),n.find("["+this.scrollDivParentMarker+"]").scrollTop(g)}if(t._hIsDown){var y=e.pageX,b=Math.subtraction(y,t._prevX||0),w=n.find("["+this.scrollDivParentMarker+"]").scrollLeft(),E=Math.summation(w,Math.division(b,c));t._prevX=y,E=Math.min(E,p),E=Math.max(E,0),n.find("["+this.scrollDivParentMarker+"]").scrollLeft(E)}document[this.ptool.libraryToPro]=t},pscroll.prototype.psetScroll=function(e,t){var n=arguments[0];e=arguments[1],t=arguments[2]||this.ptool.arrangeType.vertical;var r=$(n),i=r.find("["+this.scrollDivParentMarker+"]"),s=t==this.ptool.arrangeType.horizontal?"scrollLeft":"scrollTop";i[s](e);if(e==0){var o=r.find("["+this.horizontalBarMarker+"]"),u=r.find("["+this.verticalBarMarker+"]");i.width(""),i.height(""),o.css({transform:" translateX(0%)"}),u.css({transform:" translateY(0%)"}),r.css({"max-width":"","max-height":""})}},pscroll.prototype.setClientSize=function(e){var t=e.find("["+this.scrollDivParentMarker+"]"),n=e.width(),r=e.height(),i=17,s=t.width(),o=t.height();if(s-n!=i||o-r!=i)t.width(n+i),t.height(r+i)},pscroll.prototype.setScrollbarSize=function(e){var t=e.find("["+this.scrollDivMarker+"]"),n=e.find("["+this.horizontalBarMarker+"]"),r=e.find("["+this.verticalBarMarker+"]"),i=e.width(),s=e.height(),o=t.height(),u=t.width(),a=e[0][this.ptool.libraryToPro]||{};a[this.ptool.controlPrivateToProName]=!1;if(o>s||u>i){a[this.ptool.controlPrivateToProName]=!0;var f=Math.division(s,o);f=Math.multiplication(f,100);var l=Math.division(i,u);l=Math.multiplication(l,100),r.height(f+"%"),n.width(l+"%")}e[0][this.ptool.libraryToPro]=a};function ptext_template(){this.constructor=arguments.callee,this.errMarker="err",this.errTextMarker="errtext",this.friendlyMarker="friendly",this.textMarker="text",this.currCharLengthMarker="charlength",this.errCss="input-error",this.start="<div "+this.ptool.maxDivMarker+' {{id}}{{disabled}} class="',this.basicTextStart='"><input type="text" '+this.textMarker+"{{placeholder}}{{value}}{{focus}}{{blur}}{{click}}{{mousedown}}{{mouseup}}{{mouseover}}{{mouseout}}>",this.unitTextStart='"><div{{text}} class="',this.unitTextStart2='"></div><div class="',this.unitTextStart3='"><input type="text" '+this.textMarker+'{{placeholder}}{{value}}{{focus}}{{blur}}{{click}}{{mousedown}}{{mouseup}}{{mouseover}}{{mouseout}}><div class="per-inputborder"></div></div>',this.textareaStart='"><div class="per-input-textarea"><div class="textareawrap ',this.textareaStart2='"><textarea '+this.textMarker+"{{placeholder}}{{value}}{{focus}}{{blur}}{{click}}{{mousedown}}{{mouseup}}{{mouseover}}{{mouseout}}{{input}}></textarea>"+'<div class="per-textareaborder"></div>',this.textareaStart3='<div class="per-textarea-length"><b '+this.currCharLengthMarker+">0</b>/<b {{text}}></b></div>",this.end="/div>",this.end2='<div class="reminder-tip" '+this.friendlyMarker+"><i>*</i><em {{text}}></em></div>",this.end3='<div class="error-tip" '+this.errMarker+"><em "+this.errTextMarker+"></em></div></div>",this.noUnitStartClass="per-input-basic",this.unitStartClass="per-input-unit",this.textareaStartClass="per-input-textarea",this.unitLeftTextStartClass="per-inputunitL",this.unitRightTextStartClass="per-inputunit",this.unitLeftTextStartClass2="per-inputwrapR",this.unitRightTextStartClass2="per-inputwrap",this.textareaLengthStartClass="",this.textareaNoLengthStartClass="per-textareawra-nolength"}ptext_template.prototype=new persagyElement,ptext_template.prototype.getTemplateStr=function(e,t){var n=e.attr,r=n.bind,i=n.friendly||{},s=n.text,o=n.align||this.ptool.align.right,u=n.maxLength,a=this.start+(t===this.controlTypes.ptext.types[1]?this.textareaStartClass:s?this.unitStartClass:this.noUnitStartClass),f="";switch(t){case this.controlTypes.ptext.types[0]:s?f=this.unitTextStart+(o==this.ptool.align.right?this.unitRightTextStartClass:this.unitLeftTextStartClass)+this.unitTextStart2+(o==this.ptool.align.right?this.unitRightTextStartClass2:this.unitLeftTextStartClass2)+this.unitTextStart3:f=this.basicTextStart;break;case this.controlTypes.ptext.types[1]:f=this.textareaStart+(u?this.textareaLengthStartClass:this.textareaNoLengthStartClass)+this.textareaStart2+(u?this.textareaStart3:""),u&&(n.text=u)}var l=t===this.controlTypes.ptext.types[0]&&s?this.end:"",c=this.end2,h=this.end3,p=r===!0?"joinHtmlToBindByAttrCss":"joinHtmlByAttrCss";return a=this[p](a,n),f=r===!0?this[p](f,n,e.event):this[p](f,n),c=this[p](c,{text:i.info}),a+f+c+h};function ptext(){this.constructor=arguments.callee}ptext.prototype=new ptext_template,ptext.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css,o=this.ptool.isIe();o&&(r.placeholder=null,delete r.placeholder);var u=n.find("tip");r.friendly={info:u.attr("friendlytip")};var a=n.find("verify"),f=[];for(var l=0;l<a.length;l++){var c=a.eq(l),h=c.attr("length");f.push({verifytype:c.attr("verifytype"),length:h,errtip:c.attr("errtip")}),h&&(r.maxLength=parseInt(h))}r.verify=f;if(e==this.controlTypes.ptext.types[2])return(new pcombobox).init(this.controlTypes.pcombobox.types[4],t,n);var p=this.getTemplateStr(t,e);this.renderView(p,this.controlTypes.ptext.name,e,t,n)},ptext.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e);if(r.bind===!0)return;var o=s.find("["+this.textMarker+"]");i.blur||this.createEvent(o,this.controlTypes.ptext.name,"blur",window[this.ptool.pstaticEventFnName]),i.focus||this.createEvent(o,this.controlTypes.ptext.name,"focus",window[this.ptool.pstaticEventFnName]),!i.input&&n===this.controlTypes.ptext.types[1]&&r.maxLength&&this.createEvent(o,this.controlTypes.ptext.name,"input",window[this.ptool.pstaticEventFnName]);for(var u in i){if(i.hasOwnProperty(u)==0)continue;this.createEvent(o,this.controlTypes.ptext.name,u,window[this.ptool.pstaticEventFnName])}},ptext.prototype.eventHandout=function(e,t){var n=t[this.ptool.eventCurrTargetName],r=$(n),i=r.attr(this.ptool.libraryIdToHtml),s=this.ptool.getTypeAndChildTypeFromEle(n),o=ptext[i],u=o.attr,a=u.verify||[],f=u.friendly||{},l=o.event,c=t.type,h=l[c],p=r.find("["+this.errMarker+"]"),d=r.find("["+this.friendlyMarker+"]"),v=r.find("["+this.textMarker+"]"),m=v.val();switch(c){case"focus":r.phideTextTip(),f.info&&d.show();break;case"blur":d.hide(),r.pverifi();break;case"input":s.childType===this.controlTypes.ptext.types[1]&&u.maxLength&&r.find("["+this.currCharLengthMarker+"]").text(m.length)}this.executeEventCall(e,t,h)},ptext.prototype.pval=function(e){var t=arguments[0],n=$(t);e=arguments[1];var r=n.find("["+this.textMarker+"]");if(e==null)return r.val();r.val(e);var i=n.attr(this.ptool.libraryIdToHtml),s=this.ptool.getTypeAndChildTypeFromEle(t),o=ptext[i];s.childType===this.controlTypes.ptext.types[1]&&o.attr.maxLength&&n.find("["+this.currCharLengthMarker+"]").text(e.length)},ptext.prototype.precover=function(){var e=arguments[0],t=$(e);t.pval(""),t.phideTextTip();var n=t.attr(this.ptool.libraryIdToHtml),r=this.ptool.getTypeAndChildTypeFromEle(e),i=ptext[n];r.childType===this.controlTypes.ptext.types[1]&&i.attr.maxLength&&t.find("["+this.currCharLengthMarker+"]").text("0")};function pcombobox_template(){this.ptextTemplate=new ptext,this.constructor=arguments.callee,this.toggleTime=0,this.headerSplitor=" / ",this.comboxMaxHeight=212,this.headerTextMarker="cheadertext",this.headerIconMarker="cheadericon",this.headerMarkder="cheader",this.contentMaxDivMarker="con",this.contentFlexDivMarker="flex",this.comboxConMarker="cobcon",this.contentUlMarker="conul",this.levelMarker="level",this.headerOrigionTextMarker="_text",this.selCss="per-pitch",this.basicHeaderClassName="per-combobox-title",this.noborderHeaderClassName="per-nobordercombobox-title",this.menuHeaderClassName="per-combobox-button",this.pageComboboxTitleClassName="per-paging-combobox_title",this.orientationCss={up:"_combobox_top",down:"_combobox_bottom"},this.alignCss={left:"_combobox_left",right:"_combobox_right"},this.multiLevelCss="_combobox_level",this.multiLevelBackCss="per-combobox-multilevelback",this.contentExtenDivStart='<div class="per-combobox-wrap',this.contentExtenDivStart2='" style="display: none;" '+this.contentMaxDivMarker+'><div class="_combobox_flex" '+this.contentFlexDivMarker+">",this.contentDivStartStr1='<div class="per-combobox-con " style="display: inline-block;" '+this.comboxConMarker+" "+this.levelMarker+'="',this.contentDivStartStr2=">",this.contentDivEndStr="</div>",this.contentUlStartStr="<ul "+this.contentUlMarker+">",this.contentUlEndStr="</ul>",this.itemTemplateStr1='<li{{id}} class="per-combobox_item ',this.itemLiTemplateStr2_='" '+this.levelMarker+'="',this.itemLiTemplateStr2=">",this.itemIconTemplateStr='<span class="per-combobox_item_icon"{{text}}></span>',this.itemTextTemplateStr="<b {{text}}></b>",this.itemTemplateStr2='<i class="',this.itemTemplateStr4=">",this.itemTemplateStr3="</i></li>",this.start="<div "+this.ptool.maxDivMarker+' class="per-combobox-basic',this.end1='" {{id}}{{disabled}}><div ',this.end3=this.headerMarkder+' class="',this.endMiddle='"><span class="',this.endMiddle4='" '+this.headerTextMarker,this.endMiddle5='{{text}}></span><span class="',this.endMiddle2='">',this.endMiddle3="</span></div>",this.errStr='<div class="error-tip" '+this.ptextTemplate.errMarker+"><em "+this.ptextTemplate.errTextMarker+"{{text}}></em></div>",this.tipStr="",this.end2="</div></div>",this.end2_="</div>",this.selIcon={sel:"Z",nextItem:">"},this.itemTemplateClass={Z:"per-combobox_item_iconR",">":"per-combobox_level_iconR"}}pcombobox_template.prototype=new persagyElement,pcombobox_template.prototype.getTemplateStr=function(childType,objBind){var attr=objBind.attr,itemObj=attr.item,verifyObj=attr.verify,headerObj=attr.header,friendlyObj=attr.friendly,itemTextName=itemObj.text||"",itemIconName=itemObj.icon||"",isSelToProName=itemObj.pronametoissel,childProName=itemObj.child,conItemStr="",iconStr="",textStr="",selIcon="",itemSourceStr=itemObj.datasource,end2=this.end2,errStr=this.errStr,tipStr=this.tipStr,contentExtenDivStart=this.contentExtenDivStart,contentMaxDivCss=" "+this.orientationCss[attr.orientation]+" "+this.alignCss[attr.align]+(attr.istree?" "+this.multiLevelCss:"");contentExtenDivStart+=contentMaxDivCss+" "+this.contentExtenDivStart2;var endMiddle4=this.endMiddle4,endMiddle5=this.endMiddle5;endMiddle5=attr.bind==1?this.joinHtmlToBindByAttrCss(endMiddle5,{text:headerObj.placeholder}):this.joinHtmlByAttrCss(endMiddle5,{text:headerObj.placeholder});var end1=this.end1,endMiddle=this.endMiddle;end1+=(childType==this.controlTypes.pcombobox.types[2]?"":"{{click}}")+this.end3,endMiddle+=childType==this.controlTypes.pcombobox.types[2]?"per-comboboxButton_name":"per-combobox_name",endMiddle+=endMiddle4+(childType==this.controlTypes.pcombobox.types[2]?"{{click}}":"")+endMiddle5,endMiddle+=childType==this.controlTypes.pcombobox.types[2]?"per-comboboxButton_icon":"per-combobox_icon",endMiddle+=this.endMiddle2+(childType==this.controlTypes.pcombobox.types[2]?"b":childType==this.controlTypes.pcombobox.types[5]?"":"v")+this.endMiddle3,endMiddle+=contentExtenDivStart;var ptStr=" "+this.ptool.libraryTypeToHtml+'="'+this.controlTypes.pcombobox.name+this.ptool.typeSeparator+childType+'"',selIcon,itemTemplateStr2=this.itemTemplateStr2;if(attr.bind!==!0){end1=this.joinHtmlByAttrCss(end1,{id:attr.id,disabled:attr.disabled}),endMiddle=this.joinHtmlByAttrCss(endMiddle);var itemsArr=eval(itemSourceStr)||[];conItemStr=this.joinLi(itemsArr,objBind,childType,1),errStr=this.joinHtmlByAttrCss(errStr,{text:verifyObj.errtip}),tipStr=this.joinHtmlByAttrCss(tipStr,{text:friendlyObj.info})}else{errStr=this.joinHtmlByAttrCss(errStr,{text:verifyObj.errtip}),tipStr=this.joinHtmlByAttrCss(tipStr,{text:friendlyObj.info}),end1=this.joinHtmlToBindByAttrCss(end1,{id:attr.id,disabled:attr.disabled}),endMiddle=this.joinHtmlToBindByAttrCss(endMiddle);if(childType!==this.controlTypes.pcombobox.types[0]&&childType!==this.controlTypes.pcombobox.types[4])selIcon="",itemTemplateStr2+='"',attr.istree=!1;else if(attr.istree===!1)selIcon=this.selIcon.sel,itemTemplateStr2+=this.itemTemplateClass[selIcon]+'"';else{selIcon="";var bindCssObj={};bindCssObj[this.itemTemplateClass[this.selIcon.sel]]="model."+isSelToProName,bindCssObj[this.itemTemplateClass[this.selIcon.nextItem]]="model."+childProName+"&&model."+childProName+".length>0",itemTemplateStr2=this.joinHtmlToBindByAttrCss(itemTemplateStr2+'"'+"{{text}}{{"+this.itemTemplateClass[this.selIcon.sel]+"}}{{"+this.itemTemplateClass[this.selIcon.nextItem]+"}}"+this.itemTemplateStr4,{text:"model."+isSelToProName+"==true?'"+this.selIcon.sel+"':model."+childProName+"&&model."+childProName+".length>0?'"+this.selIcon.nextItem+"':''"},{},bindCssObj)}iconStr=itemIconName?this.joinHtmlToBindByAttrCss(this.itemIconTemplateStr,{text:itemIconName},{},{}):"",textStr=this.joinHtmlToBindByAttrCss(this.itemTextTemplateStr,{text:itemTextName},{},{},!0),conItemStr=this.itemTemplateStr1+this.itemLiTemplateStr2_+'1"{{click}}'+(childType===this.controlTypes.pcombobox.types[0]?"{{mouseenter}}":"")+this.itemLiTemplateStr2+iconStr+textStr+itemTemplateStr2+(attr.istree===!0?"":this.itemTemplateStr4)+selIcon+this.itemTemplateStr3,conItemStr=this.joinHtmlToBindByAttrCss(conItemStr,{id:itemObj.id},{},{},!0),conItemStr=this.createForBind(conItemStr,itemSourceStr)}var templateStartStr=this.start+end1,headerClassName=childType==this.controlTypes.pcombobox.types[5]?this.pageComboboxTitleClassName:childType==this.controlTypes.pcombobox.types[2]?this.menuHeaderClassName:childType!=this.controlTypes.pcombobox.types[0]&&childType!=this.controlTypes.pcombobox.types[6]||attr.isborder!==!1?this.basicHeaderClassName:this.noborderHeaderClassName,three=this.createDownList(conItemStr,1);end2+=errStr+tipStr+this.end2_;switch(childType){case this.controlTypes.pcombobox.types[1]:return templateStartStr+headerClassName+endMiddle+this.contentDivStartStr1+'1"{{templateid}}'+this.contentDivStartStr2+this.contentDivEndStr+end2;case this.controlTypes.pcombobox.types[4]:var valueStr='<input type="text"{{value}}{{placeholder}} '+this.ptextTemplate.textMarker+'><div class="per-inputborder"></div></div></div>';return valueStr=attr.bind==1?this.joinHtmlToBindByAttrCss(valueStr,{value:attr.value,placeholder:headerObj.placeholder}):this.joinHtmlByAttrCss(valueStr,{value:attr.value,placeholder:headerObj.placeholder}),'<div class="per-input-select" '+this.ptool.maxDivMarker+'><div class="per-inputcombobox-title" '+this.headerMarkder+">"+'<div class="per-inputIcon" '+this.headerIconMarker+'>b</div><div class="per-inputwrap">'+valueStr+contentExtenDivStart+three+"</div></div>"+errStr+tipStr+"</div>";default:return templateStartStr+headerClassName+endMiddle+three+end2}},pcombobox_template.prototype.joinLi=function(e,t,n,r,i,s){var o=t.attr||{},u=o.item||{},a=o.header||{},f=u.step,l=u.text||"",c=u.icon||"",h=u.pronametoissel,p=u.child,d="",v=n===this.controlTypes.pcombobox.types[5]?o.number||1:n===this.controlTypes.pcombobox.types[6]?u.end+1:e.length,m=n===this.controlTypes.pcombobox.types[6]?u.start:0;for(m;m<v;m++){var g=e[m],y="";switch(n){case this.controlTypes.pcombobox.types[5]:y=m+1;break;case this.controlTypes.pcombobox.types[6]:if(!f)y=m,y=y<10?"0"+y:y,y+=a.prefix||"";else{var b=m+f-1;b=Math.min(v-1,b),y=m+"~"+b}break;default:y=g[l]||""}var w=c?this.joinHtmlByAttrCss(this.itemIconTemplateStr,{text:g[c]||""},{}):"",E=this.joinHtmlByAttrCss(this.itemTextTemplateStr,{text:y},{}),S=n!==this.controlTypes.pcombobox.types[0]&&n!==this.controlTypes.pcombobox.types[4]?"":o.istree===!1?this.selIcon.sel:g[h]===!0?this.selIcon.sel:g[p]&&g[p].length>0?this.selIcon.nextItem:"",x=(this.itemTemplateClass[S]||"")+'"',T="";u.id&&s?g[u.id]==s&&(T=this.selCss):T=i?i==(g||{})[l]?this.selCss:"":"";var N=this.itemTemplateStr1+T+this.itemLiTemplateStr2_+r+'"'+this.itemLiTemplateStr2+w+E+this.itemTemplateStr2+x+this.itemTemplateStr4+S+this.itemTemplateStr3;N=this.joinHtmlByAttrCss(N,{id:g?g[u.id]||"":""},{}),d+=N,n===this.controlTypes.pcombobox.types[6]&&f&&(m=m+f-1)}return d},pcombobox_template.prototype.createDownList=function(e,t){var n=this.contentUlStartStr+e+this.contentUlEndStr,r=this.ptool.createDynamicTemplate(n),i=$('<div><pscroll-small templateid="'+r+'"></pscroll-small></div></div>');return n=i.html(),this.contentDivStartStr1+t+'"'+this.contentDivStartStr2+n+this.contentDivEndStr},pcombobox_template.prototype.joliToTime=function(e,t,n,r){var i="";for(var s=e;s<=t;s++){var o=r?function(){var e=s,n=Math.min(e+r-1,t);return e+"~"+n}():(s<10?"0"+s:s)+(n||""),u=this.itemTemplateStr1+'"'+this.itemLiTemplateStr2+this.itemTextTemplateStr+this.itemTemplateStr2+'"'+this.itemTemplateStr4+this.itemTemplateStr3;u=this.joinHtmlByAttrCss(u,{text:o}),i+=u,r&&(s=s+r-1)}return i};function pcombobox(){this.constructor=arguments.callee}pcombobox.prototype=new pcombobox_template,pcombobox.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css;r.number=r.number||1;var o=n.find("header");r.header={placeholder:e===this.controlTypes.pcombobox.types[5]?1+this.headerSplitor+(r.number||1):o.attr("placeholder"),prefix:o.attr("prefix"),click:o.attr("click")};var u=n.find("item");r.item={datasource:u.attr("datasource"),text:u.attr("text"),id:u.attr("id"),icon:u.attr("icon"),child:u.attr("child"),pronametoissel:u.attr("pronametoissel"),start:parseInt(u.attr("start"))||0,end:parseInt(u.attr("end"))||0,step:parseInt(u.attr("step"))||0,orientation:u.attr("orientation")||this.ptool.orientation.left};var a=n.find("verify");r.verify={verifytype:a.attr("verifytype"),errtip:a.attr("errtip"),friendlytip:a.attr("friendlytip"),length:a.attr("length")};var f=n.find("tip");r.friendly={info:f.attr("friendlytip")},r.isborder=r.isborder===!1?!1:!0,r.istree=r.istree===!0?!0:!1,r.orientation=r.orientation?r.orientation:this.ptool.orientation.down,r.align=r.align?r.align:this.ptool.align.left;var l=this.getTemplateStr(e,t),c=this.renderView(l,this.controlTypes.pcombobox.name,e,t,n)},pcombobox.prototype.rendered=function(element,objBind){if(!objBind)return;var attr=objBind.attr,event=objBind.event,jqElement=ptool.getJqElement(element),typeObj=this.ptool.getTypeAndChildTypeFromEle(jqElement),headerJqEle=jqElement.find("["+this.headerMarkder+"]");attr.bind!==!0&&(typeObj.childType!==this.controlTypes.pcombobox.types[2]&&typeObj.childType!==this.controlTypes.pcombobox.types[4]?this.createEvent(headerJqEle,this.controlTypes.pcombobox.name,"click",window[this.ptool.pstaticEventFnName]):this.createEvent(headerJqEle.find("span:first"),this.controlTypes.pcombobox.name,"click",window[this.ptool.pstaticEventFnName]));switch(typeObj.childType){case this.controlTypes.pcombobox.types[2]:this.createEvent(headerJqEle.find("span:last"),this.controlTypes.pcombobox.name,"click",window[this.ptool.pstaticEventFnName]);break;case this.controlTypes.pcombobox.types[4]:this.createEvent(headerJqEle.find("["+this.headerIconMarker+"]"),this.controlTypes.pcombobox.name,"click",window[this.ptool.pstaticEventFnName])}headerJqEle.length>0&&headerJqEle.registerEventForTitle(jqElement.find("["+this.headerTextMarker+"]"));if(typeObj.childType==this.controlTypes.pcombobox.types[1])return;if(typeObj.childType===this.controlTypes.pcombobox.types[4]){var inputTarget=jqElement.find("["+this.ptextTemplate.textMarker+"]");(!event.blur||attr.bind!=1)&&this.createEvent(inputTarget,this.controlTypes.pcombobox.name,"blur",window[this.ptool.pstaticEventFnName]),(!event.focus||attr.bind!=1)&&this.createEvent(inputTarget,this.controlTypes.pcombobox.name,"focus",window[this.ptool.pstaticEventFnName])}var itemObj=attr.item,itemSourceStr=itemObj.datasource,itemsArr=attr.bind===!0?[]:eval(itemSourceStr)||[],_id=jqElement.attr(this.ptool.libraryIdToHtml),ulJqTarget=jqElement.find("["+this.contentUlMarker+"]");this.conUlSet(ulJqTarget,_id,itemsArr,!attr.bind,jqElement.find("["+this.comboxConMarker+"]["+this.levelMarker+"]"),typeObj.childType)},pcombobox.prototype.headerClick=function(e,t){var n=$(e.currentTarget);if(e.currentTarget.tagName=="SPAN"||$(e.currentTarget).attr(this.headerIconMarker)=="")n=n.parent();var r=n.next(),i=$("["+this.contentMaxDivMarker+"]").not(r);i.slideUp(this.toggleTime);if(t){var s=t.attr(this.ptool.libraryIdToHtml),o=pcombobox[s],u=o.attr;if(u.istree===!1){var a=t.find("["+this.headerTextMarker+"]"),f=a.text(),l=a.attr("id");t.psel({itemIndexOrText:f,itemId:l,isEvent:!1})}}r.is(":visible")?r.slideUp(this.toggleTime):(r.find("["+this.contentUlMarker+"]").children().removeClass(this.multiLevelBackCss),r.psetScroll(0),r.psetScroll(0,this.ptool.arrangeType.horizontal),r.pdisable(!0),r.slideDown(0,function(){r.pdisable(!1)}))},pcombobox.prototype.eventHandout=function(e,t){if(t.type!=="mouseover"){var n=t.currentTarget,r=$(n),i=$(t[this.ptool.eventCurrTargetName]),s=i,o=0;while(s.attr(this.ptool.maxDivMarker)==null||this.ptool.getTypeAndChildTypeFromEle(s).controlType!==this.controlTypes.pcombobox.name){if(o>=100)break;s=s.parent(),++o}t[this.ptool.eventCurrTargetName]=s[0];var u=i.attr(this.ptool.libraryIdToHtml),a=pcombobox[u],f=a.attr,l=f.header.prefix||"",c=this.ptool.getTypeAndChildTypeFromEle(i),h=f.item;if(n.tagName=="LI"){var p=$(t.target);p[0].tagName!="LI"&&(p=p.parent());var d=p[0],v=p.parent(),m=s.find("["+this.headerTextMarker+"]").text();l&&(m=m.substr(l.length));var g=p.index(),y=parseInt(p.attr(this.levelMarker)||1),b=y+1,w="["+this.comboxConMarker+"]["+this.levelMarker+'="'+b+'"]',E=f.bind===!0&&y===1?e||{}:(v[0][this.ptool.libraryToPro][this.ptool.controlPrivateToProName]||[])[g]||{};e=f.bind===!0?E:e;var S=E[f.item.child]||[]}switch(t.type){case"focus":case"blur":return ptext[u]=a,this.ptextTemplate.eventHandout(e,t);case"click":var x=f.header.click;if(r.attr(this.headerMarkder)==""){(new psearch).slideUpFriend(),this.executeEventCall(e,t,x),this.headerClick(t,i);return}if(r.attr(this.headerTextMarker)==""){this.executeEventCall(e,t,x);return}if(n.tagName=="SPAN"&&r.attr(this.headerTextMarker)==null)return this.headerClick(t);if(r.attr(this.headerIconMarker)=="")return this.headerClick(t);if(f.istree===!0&&E[f.item.pronametoissel]!==!0)return;t[this.ptool.eventOthAttribute]={index:g,currItem:E};if(c.childType===this.controlTypes.pcombobox.types[0]||c.childType===this.controlTypes.pcombobox.types[4]||c.childType===this.controlTypes.pcombobox.types[5]||c.childType===this.controlTypes.pcombobox.types[6])(f.istree!==!0||E[f.item.pronametoissel]==1)&&this.changeSelItem(s,p,a,c.childType),s.phideTextTip(),s.pverifi();this.slideUp();var T=a.event.sel,N=a.event.beforesel;if(N){var C=this.executeEventCall(e,t,N);if(C===!1)return}this.executeEventCall(e,t,T);break;case"mouseenter":if(n.tagName=="LI"){var k=v.parent().parent().parent()[0];(new pscroll).setEventCall(k)({type:"mouseenter"}),r.elementShowTitle(r.find("b"))}if(f.istree===!1)return;var L=b+1,A=s.find("["+this.comboxConMarker+"]["+this.levelMarker+'="'+L+'"]');while(A.length>0)A.hide(),L+=1,A=s.find("["+this.comboxConMarker+"]["+this.levelMarker+'="'+L+'"]');var O=s.find(w);if(S&&S.length>0){r.siblings().removeClass(this.multiLevelBackCss),r.addClass(this.multiLevelBackCss);var M=i.find("["+this.headerTextMarker+"]").attr("id"),_=this.joinLi(S,a,c.childType,b,m,M);if(O.length>0){var D=O.find("["+this.contentUlMarker+"]");D.empty(),D.append(_)}else{var P="["+this.comboxConMarker+"]",H=s.find(P+":first").width();s.find("["+this.contentMaxDivMarker+"]").width(H);var B=this.createDownList(_,b);s.find("["+this.contentFlexDivMarker+"]").append(B),s.prender(),O=s.find(w)}this.conUlSet(O.find("["+this.contentUlMarker+"]"),u,S,!0,O,c.childType),O.show(),O.psetScroll(0);var j=(H+1)*(b-1);j=(h.orientation==this.ptool.orientation.left?"-"+j:j)+"px";var F=(r.position().top||0)+(parseInt($(k).parent().css("top"))||0);O.css({left:j,top:F+"px"})}else O.hide()}}},pcombobox.prototype.psel=function(){var e=arguments[0],t=$(e),n=t.attr(this.ptool.libraryIdToHtml),r=this.ptool.getTypeAndChildTypeFromEle(t),i=pcombobox[n],s=i.attr,o=s.item,u=s.header.prefix||"",a,f,l,c={};if(arguments.length>1){if(typeof arguments[1]=="object"){var h=arguments[1];c.level=h.level,c.itemId=h.itemId,a=h.isEvent,f=h.itemIndexOrText}else f=arguments[1],a=arguments[2];c.isEvent=a,c.indexOrText=f}else{var p=t.find("["+this.headerTextMarker+"]");c.level=parseInt(p.attr(this.levelMarker)),c.itemId=p.attr("id"),c.indexOrText=p.attr(this.headerOrigionTextMarker);if(!c.indexOrText){var d=p.text();c.indexOrText=d.substr((s.prefix||"").length)}c.isEvent=!1}c.isEvent=c.isEvent===!1?!1:!0;var v=t.find("["+this.contentUlMarker+"]:first").children(),m,g;for(var y=0;y<v.length;y++){var b=!1,w=v.eq(y),E=w.attr("id"),S=w.find("b").text();if(E&&c.itemId)E==c.itemId&&(b=!0);else if(y===c.indexOrText||S===c.indexOrText)b=!0;if(b){f=S,m=v.eq(y),g=y;break}}return m?(c.isEvent?m[0].click():this.changeSelItem(t,m,i,r.childType),s.istree?{text:c.indexOrText,level:c.level,id:c.itemId}:{index:g,text:f,id:c.itemId}):s.istree?(this.setHeaderTextToIn(t,i,r.childType,c.indexOrText,c.level,c.itemId),{text:c.indexOrText,level:c.level,id:c.itemId}):!1},pcombobox.prototype.precover=function(e){var t=arguments[0],n=$(t),r=this.ptool.getTypeAndChildTypeFromEle(n);switch(r.childType){case this.controlTypes.pcombobox.types[4]:n.find("["+this.ptextTemplate.textMarker+"]").val("");break;case this.controlTypes.pcombobox.types[0]:var i=n.attr(this.ptool.libraryIdToHtml),s=pcombobox[i],o=s.attr;if(o.istree===!1){var u=n.psel().index;n.find("["+this.contentUlMarker+"]").children().eq(u).removeClass(this.selCss)}else n.find("["+this.contentUlMarker+"]").children().removeClass(this.selCss);var a=o.header;e=o.bind!==!0?a.placeholder||"":arguments[1]||"";var f=n.find("["+this.headerTextMarker+"]");f.text(e),f.attr(this.levelMarker,""),f.attr("id",""),f.attr(this.headerOrigionTextMarker,"")}n.phideTextTip()},pcombobox.prototype.pval=function(e){var t=arguments[0],n=$(t);e=arguments[1];var r=this.ptool.getTypeAndChildTypeFromEle(n);return r.childType===this.controlTypes.pcombobox.types[4]?(new ptext).pval(t,e):!1},pcombobox.prototype.changeSelItem=function(e,t,n,r){var i=n.attr.header.prefix||"",s=t.find("b").text();if(r!==this.controlTypes.pcombobox.types[4]){var o=e.find("["+this.contentUlMarker+"]");for(var u=0;u<o.length;u++)o.eq(u).find("li").removeClass(this.selCss);t.addClass(this.selCss),this.setHeaderTextToIn(e,n,r,s,t.attr(this.levelMarker),t.attr("id"))}else e.find("["+this.ptextTemplate.textMarker+"]").val(s)},pcombobox.prototype.txtValChange=function(e){var t=e.find("["+this.ptextTemplate.textMarker+"]");t.val(origionText),t[0].oninput()},pcombobox.prototype.setHeaderTextToIn=function(e,t,n,r,i,s){var o=t.attr.header.prefix||"",u=r;n!==this.controlTypes.pcombobox.types[6]&&(u=o+u),n===this.controlTypes.pcombobox.types[5]&&(u=u+this.headerSplitor+t.attr.number);var a=e.find("["+this.headerTextMarker+"]");a.text(u),a.attr(this.levelMarker,parseInt(i)),a.attr("id",s),a.attr(this.headerOrigionTextMarker,r)},pcombobox.prototype.conUlSet=function(e,t,n,r,i,s){var o=this,u=this.controlTypes.pcombobox.name+this.ptool.typeSeparator+s;e.attr(o.ptool.libraryIdToHtml,t),e.attr(o.ptool.libraryTypeToHtml,u),this.createEvent(i,o.controlTypes.pcombobox.name,"mouseover",window[o.ptool.pstaticEventFnName]);if(r){var a=e[0],f=a[o.ptool.libraryToPro]||{};f[o.ptool.controlPrivateToProName]=n,a[o.ptool.libraryToPro]=f,o.conliEvent(e)}},pcombobox.prototype.conliEvent=function(e){var t=this,n=e.find("li");n.each(function(){t.createEvent(this,t.controlTypes.pcombobox.name,"mouseenter",window[t.ptool.pstaticEventFnName]),t.createEvent(this,t.controlTypes.pcombobox.name,"click",window[t.ptool.pstaticEventFnName])})},pcombobox.prototype.slideUp=function(){var e=this,t="",n=e.controlTypes.pcombobox.types;n.forEach(function(n){var r=e.ptool.libraryTypeToHtml+'="'+e.controlTypes.pcombobox.name+e.ptool.typeSeparator+n+'"';t+=(t.length==0?"":",")+"["+r+"]["+e.ptool.maxDivMarker+"]"}),$(t).find("["+e.contentMaxDivMarker+"]").slideUp(this.toggleTime)},pcombobox.prototype.hideNotFirstLevelMenu=function(){var e=this,t="",n=e.controlTypes.pcombobox.types;n.forEach(function(n){var r=e.ptool.libraryTypeToHtml+'="'+e.controlTypes.pcombobox.name+e.ptool.typeSeparator+n+'"';t+=(t.length==0?"":",")+"["+r+"]["+e.ptool.maxDivMarker+"]"});var r=$(t);r.each(function(){var t="["+e.comboxConMarker+"]["+e.levelMarker+'!="1"]';$(this).find(t).hide()})},pcombobox.prototype.pcount=function(e){var t=arguments[0],n=$(t),r=n.attr(this.ptool.libraryIdToHtml),i=pcombobox[r],s=i.attr;e=arguments[1],s.number=e,i.attr=s,pcombobox[r]=i;var o=n.find("["+this.contentUlMarker+"]"),u=this.joinLi([],i,this.controlTypes.pcombobox.types[5],1);o.empty(),o.append(u),this.conliEvent(o)},pcombobox.prototype.resetList=function(e,t,n,r,i){var s=this.joliToTime(t,n,r,i),o=e.attr(this.ptool.libraryIdToHtml),u=e.find("["+this.ptool.libraryIdToHtml+'="'+o+'"]');u.empty(),u.append(s),this.conliEvent(u)},pcombobox.prototype.setHeaderText=function(e,t){e.find("["+this.headerTextMarker+"]").text(t)},pcombobox.prototype.pslideUp=function(){var e=arguments[0],t=$(e);t.find("["+this.contentMaxDivMarker+"]").slideUp(this.toggleTime)},pcombobox.prototype.pslideDown=function(){var e=arguments[0],t=$(e);t.find("["+this.contentMaxDivMarker+"]").slideDown(this.toggleTime)};function ptab_template(){this.constructor=arguments.callee,this.leftMarker="left",this.rightMarker="right",this.clientPadding=10,this.contentTagMarker="ulm",this.contentLiMarker="ctli",this.selCss={button:"per-tab-button_active",text:"per-tab-text_active",navigation:"active"},this.buttonStart="<div "+this.ptool.maxDivMarker+' class="per-tab-button"{{id}}><ul '+this.contentTagMarker+">",this.buttonItem='<li class="per-tab-button_item"{{disabled}}{{click}}><span class="per-tab-button_nav"{{text}}></span></li>',this.buttonEnd="</ul></div>",this.textStart="<div "+this.ptool.maxDivMarker+' class="per-tab-text"{{id}}><div class="per-tab-text_but _tab-text-left" '+this.leftMarker+'="'+this.leftMarker+'" disable="flase"><</div><div class="per-tab-text_wrap"><ul '+this.contentTagMarker+">",this.textItem='<li class="per-tab-text_item"{{text}}{{disabled}}{{click}}></li>',this.textEnd='</ul></div><div class="per-tab-text_but _tab-text-right" '+this.rightMarker+'="'+this.rightMarker+'" disable="flase">></div></div>',this.navigationStart="<div "+this.ptool.maxDivMarker+' class="per-tab-navigation" {{id}}><div class="per-tab-navigation_title"><ul '+this.contentTagMarker+">",this.navigationIcon='<i class="per-tab-navigation_icon"{{icon}}></i>',this.navigationItem='<li class="per-tab-navigation_item"{{disabled}}{{click}}>',this.navigationItemEnd="<em {{text}}></em></li>",this.navigationEnd='</ul></div><div class="per-tab-navigation_con" {{templateid}}></div></div>'}ptab_template.prototype=new persagyElement,ptab_template.prototype.joinLi=function(objBind,childType){var attr=objBind.attr,bind=attr.bind,datasource=attr.datasource,text=attr.text,icon=attr.icon,disabled=attr.disabled,conTemplateStr=childType==this.controlTypes.ptab.types[0]?this.buttonItem:childType==this.controlTypes.ptab.types[1]?this.textItem:childType==this.controlTypes.ptab.types[2]?this.navigationItem:"";childType===this.controlTypes.ptab.types[2]&&(conTemplateStr+=(icon?this.navigationIcon:"")+this.navigationItemEnd);var conItemStr="";if(bind!==!0){var itemsArr=eval(datasource)||[];for(var i=0;i<itemsArr.length;i++){var currItem=itemsArr[i],textStr=(text?currItem[text]:currItem)||"",iconStr=(text?currItem[icon]:"")||"",disabledStr=currItem[disabled]||"",currTemplate=conTemplateStr;conItemStr+=this.joinHtmlByAttrCss(currTemplate,{text:textStr,icon:iconStr,disabled:disabledStr},{},{})}}else conItemStr=this.joinHtmlToBindByAttrCss(conTemplateStr,{text:text,icon:icon,disabled:disabled},{click:""},{},!0),conItemStr=this.createForBind(conItemStr,datasource);return conItemStr},ptab_template.prototype.getTemplateStr=function(e,t){var n=this.joinLi(e,t),r=this[t+"Start"],i=this[t+"End"],s=r+n+i;return s};function ptab(){this.constructor=arguments.callee}ptab.prototype=new ptab_template,ptab.prototype.init=function(e,t,n){var r=this.getTemplateStr(t,e);this.renderView(r,this.controlTypes.ptab.name,e,t,n)},ptab.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e),o=s.find("["+this.contentTagMarker+"]").children(),u=n===this.controlTypes.ptab.types[1]?1:0;o.registerEventForColorChange(null,u);var a=s.find("["+this.contentTagMarker+"]");r.bind!==!0&&this.createEvent(a,this.controlTypes.ptab.name,"click",window[this.ptool.pstaticEventFnName]);if(n===this.controlTypes.ptab.types[1]){this.createEvent(a,this.controlTypes.ptab.name,"DOMSubtreeModified",window[this.ptool.pstaticEventFnName]);var f=s.find("["+this.leftMarker+"]"),l=s.find("["+this.rightMarker+"]");this.createEvent(f,this.controlTypes.ptab.name,"click",window[this.ptool.pstaticEventFnName]),this.createEvent(l,this.controlTypes.ptab.name,"click",window[this.ptool.pstaticEventFnName]),this.pisShowPage(s)}},ptab.prototype.eventHandout=function(e,t){function A(){var e=h,t=0;for(var r=0;r<e;r++){var i=n.getTextTypeLiWidth(l.eq(r));t+=i}return Math.min(t,d)}var n=this,r=$(t[this.ptool.eventCurrTargetName]),i=t.type;switch(i){case"click":var s=t.target,o=$(s),u=o.attr(this.leftMarker),a=o.attr(this.rightMarker);if(u||a){var f=r.find("["+this.contentTagMarker+"]"),l=f.children(),c=r[0][this.ptool.libraryToPro]||{},h=c[this.ptool.controlPrivateToProName]||0,p=f.parent().width()+this.clientPadding,d=f.width()-p,v=0,m=0;if(u){for(h;h>=0;h--){var g=this.getTextTypeLiWidth(l.eq(h));v+=g;if(v>=p)break}m=A(),f.animate({left:parseFloat("-"+m)},300),m==0?o.pdisable(!0):o.pdisable(!1),r.find("["+this.rightMarker+"]").pdisable(!1)}if(a){for(h;h<l.length;h++){var g=this.getTextTypeLiWidth(l.eq(h));v+=g;if(v>=p)break}m=A(),f.animate({left:parseFloat("-"+m)},300),m==d?o.pdisable(!0):o.pdisable(!1),r.find("["+this.leftMarker+"]").pdisable(!1)}c[this.ptool.controlPrivateToProName]=h,r[0][this.ptool.libraryToPro]=c;return}var y=o;y[0].tagName!="LI"&&(y=y.parent());var b=r.attr(this.ptool.libraryIdToHtml),w=ptab[b],E=w.event||{},S=this.ptool.getTypeAndChildTypeFromEle(r),x=this.selCss[S.childType],T=y.hasClass(x)?!0:!1,N=y.index();t=this.ptool.appendProToEvent(t,{state:T,index:N});var C=E.beforesel,k=!0;C&&(k=this.executeEventCall(e,t,C));if(k!==!1){this.cssChangeForSel(y,x),T=y.hasClass(x)?!0:!1,t=this.ptool.appendProToEvent(t,{state:T,index:N});var L=E.sel;L&&this.executeEventCall(e,t,L)}break;case"DOMSubtreeModified":this.pisShowPage(r)}},ptab.prototype.psel=function(e,t){var n=arguments[0],r=$(n),i=this.ptool.getTypeAndChildTypeFromEle(n),s=this.selCss[i.childType],o=r.find("["+this.contentTagMarker+"]").children();if(arguments.length===1)for(var u=0;u<o.length;u++)if(o.eq(u).hasClass(s))return u;e=arguments[1],t=arguments[2],t=t===!1?!1:!0;var a=o.eq(e),f=a.hasClass(s)?!0:!1;if(f)return e;if(t)return a[0].click();this.cssChangeForSel(a,s)},ptab.prototype.pisShowPage=function(e){var t=e.find("["+this.contentTagMarker+"]"),n=t.parent().width()+this.clientPadding,r=t.width();if(r>n){var i=e.find("["+this.leftMarker+"]"),s=e.find("["+this.rightMarker+"]");i.show(),s.show(),i.pdisable(!0)}},ptab.prototype.getTextTypeLiWidth=function(e){return e.width()+20+this.clientPadding},ptab.prototype.cssChangeForSel=function(e,t){e[0][pconst.targetHoverCssSourcePro]=[],e.css("backgroundColor",""),e.toggleClass(t),e.siblings().removeClass(t)};function pwindow_template(){this.constructor=arguments.callee,this.closeMarker="close",this.buttonsMarker="buttons",this.floatMarker="float",this.titleMarker="tit",this.subTitleMarker="subtit",this.contentMarker="wincontent",this.global="<div "+this.ptool.maxDivMarker+' class="per-modal-control" style="display:none;opacity:0;" {{id}}><div class="per-modal-mask">'+'<div class="per-modal-global_x" '+this.closeMarker+">x</div><div "+this.contentMarker+' class="per-modal-global" {{templateid}}></div></div></div>',this.modal="<div "+this.ptool.maxDivMarker+' class="per-modal-control" style="display:none;opacity:0;" {{id}}><div class="per-modal-mask">'+'<div class="per-modal-custom"><div class="per-modal-custom_title" '+this.titleMarker+" {{text}}></div>"+'<div class="per-modal-custom_con" {{templateid}}></div></div></div></div>',this.confirm="<div "+this.ptool.maxDivMarker+' class="per-modal-control" style="display:none;opacity:0;" {{id}}><div class="per-modal-mask">'+'<div class="per-modal-common"><div class="per-modal-common_con">'+'<div class="per-modal-common_title" '+this.titleMarker+'{{text}}></div><div class="per-modal-common_subtitle" '+this.subTitleMarker+"{{subtitle}}></div>"+'</div><div class="per-modal-common_button" '+this.buttonsMarker+">",this.confirm2="</div></div></div></div>",this.float1="<div "+this.ptool.maxDivMarker+" "+this.floatMarker+" "+this.contentMarker+' class="per-madal-float" style="',this.float2=">",this.float='<div class="per-madal-float_title"><div class="per-madal-float_x" '+this.closeMarker+">x</div>"+'<div class="per-madal-float_titcon"><b '+this.titleMarker+'{{text}}></b></div><div class="per-madal-float_operate"'+this.buttonsMarker+">",this.float3='</div></div><div class="per-madal-float_con"{{templateid}}></div></div>',this.floatShadeStart1="<div "+this.ptool.maxDivMarker+' class="per-modal-control" style="display:none;opacity:0;"',this.floatShadeStart2=">",this.floatShadeEnd="</div>",this.bubble="<div "+this.ptool.maxDivMarker+' class="per-modal-pop" style="display:none;"{{id}}>'+'<div class="per-modal-pop_title" '+this.titleMarker+"{{text}}></div>"+'<div class="per-modal-pop_subtitle" '+this.subTitleMarker+"{{subtitle}}></div>"+'<div class="per-modal-pop_button" '+this.buttonsMarker+">",this.bubble2="</div></div>"}pwindow_template.prototype=new persagyElement,pwindow_template.prototype.getTemplateStr=function(e,t){var n=e.attr;n.text=n.title;switch(t){case this.controlTypes.pwindow.types[3]:var r=n.animate||{},i=this.float+(n.buttons||"")+this.float3,s=this.float1+(r.orientation+":"+r.minpx+'px;"');return n.isshade?this.floatShadeStart1+"{{id}}"+this.floatShadeStart2+s+this.float2+i+this.floatShadeEnd:s+"{{id}}"+this.float2+i;case this.controlTypes.pwindow.types[2]:case this.controlTypes.pwindow.types[4]:return this[t]+(n.buttons||"")+this[t+"2"];default:return this[t]}};function pwindow(){this.constructor=arguments.callee}pwindow.prototype=new pwindow_template,pwindow.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css;r.buttons=n.find("button").html();var o=n.find("animate");r.animate={orientation:o.attr("orientation"),maxpx:o.attr("maxpx"),minpx:o.attr("minpx")},r.isshade=r.isshade===!1?!1:!0;var u=this.getTemplateStr(t,e);this.renderView(u,this.controlTypes.pwindow.name,e,t,n)},pwindow.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e),o=s.find("["+this.closeMarker+"]");o.length>0&&this.createEvent(o,this.controlTypes.pwindow.name,"click",window[this.ptool.pstaticEventFnName]),n==this.controlTypes.pwindow.types[0]||n==this.controlTypes.pwindow.types[3]&&r.isshade?this.createEvent(s.find("["+this.contentMarker+"]"),this.controlTypes.pwindow.name,"click",window[this.ptool.pstaticEventFnName]):this.createEvent(s,this.controlTypes.pwindow.name,"click",window[this.ptool.pstaticEventFnName])},pwindow.prototype.eventHandout=function(e,t){var n=this,r=$(t[this.ptool.eventCurrTargetName]),i=t.type,s=$(t.currentTarget),o=this.ptool.getTypeAndChildTypeFromEle(r),u=r.attr(this.ptool.libraryIdToHtml),a=pwindow[u];switch(i){case"click":s.attr(this.closeMarker)==""&&r.phide()}},pwindow.prototype.pshow=function(e){var t=this,n=arguments[0];e=arguments[1]||{};var r=$(n),i=t.ptool.getTypeAndChildTypeFromEle(n),s=r.attr(t.ptool.libraryIdToHtml),o=pwindow[s],u=o.attr;i.childType!=t.controlTypes.pwindow.types[0]&&e.title&&r.find("["+this.titleMarker+"]").text(e.title),(i.childType==t.controlTypes.pwindow.types[2]||i.childType==t.controlTypes.pwindow.types[4])&&e.subtitle&&r.find("["+this.subTitleMarker+"]").text(e.subtitle);switch(i.childType){case t.controlTypes.pwindow.types[0]:case t.controlTypes.pwindow.types[1]:case t.controlTypes.pwindow.types[2]:r.show(),r.animate({opacity:1},500);break;case t.controlTypes.pwindow.types[3]:var a=u.animate||{};e={},e[a.orientation]=a.maxpx+"px",u.isshade===!1?r.css(e):(r.show(),r.animate({opacity:1},200,function(){r.find("["+t.floatMarker+"]").css(e)}));break;case t.controlTypes.pwindow.types[4]:r.css(e),r.show()}},pwindow.prototype.phide=function(e){var t=this,n=arguments[0],r=arguments[1]||{},i=r.cssObj||{},s=r.isEvent===!1?!1:!0,o=$(n),u=t.ptool.getTypeAndChildTypeFromEle(n),a=o.attr(t.ptool.libraryIdToHtml),f=pwindow[a],l=f.attr,c=f.event||{};if(s&&c.beforehide){var h=this.executeEventCall(null,{},c.beforehide);if(!h)return}switch(u.childType){case t.controlTypes.pwindow.types[0]:case t.controlTypes.pwindow.types[1]:case t.controlTypes.pwindow.types[2]:o.animate({opacity:0},500,function(){o.hide()});break;case t.controlTypes.pwindow.types[3]:var p=l.animate||{};i={},i[p.orientation]=p.minpx+"px",l.isshade===!1?o.css(i):(o.find("["+t.floatMarker+"]").css(i),o.animate({opacity:0},500,function(){o.hide()}));break;case t.controlTypes.pwindow.types[4]:o.hide()}s&&this.executeEventCall(null,{},c.hide)},pwindow.prototype.bodyClick=function(){var e=this,t="",n=e.controlTypes.pwindow.types,r=e.controlTypes.pwindow.name;for(var i=0;i<n.length;i++){if(i!==0&&i!==3&&i!==4)continue;var s=r+e.ptool.typeSeparator+n[i];t+=(t.length>0?",":"")+"["+e.ptool.libraryTypeToHtml+'="'+s+'"]'}var o=$(t);o.each(function(){var t=$(this);if(!t.is(":hidden")){var n=t.find("["+e.closeMarker+"]")[0];n?n.click():this.phide({isEvent:!1})}})};function ppage_template(){this.constructor=arguments.callee,this.leftButtonMarker="ltp",this.rightButtonMarker="rtp",this.pageComboboxMarker="pagebox",this.fullUlMarker="fullul",this.fullTextMarker="fulltext",this.dotCss="per-paging-dot",this.fullSelCss="per-paging-pitch",this.dotStr="···",this.start="<div {{id}} "+this.ptool.maxDivMarker+' class="per-paging-normal"><div class="per-paging-concise">',this.end="</div></div>",this.leftButton='<pbutton-white icon="l" '+this.leftButtonMarker+' click="ppage.prevPageFn"></pbutton-white>',this.rightButton='<pbutton-white icon="r" '+this.rightButtonMarker+' click="ppage.nextPageFn"></pbutton-white>',this.fullPageNumberUlStart='<ul class="per-paging-page" '+this.fullUlMarker+">",this.fullPageNumberUlEnd="</ul>",this.fullPageInputDiv='<div class="per-paging-number">跳转到<ptext-text '+this.fullTextMarker+'><verify verifytype="positiveint" errtip="页码必须为正整数"></verify>'+'</ptext-text>页</div><pbutton-white text="确定" click="ppage.sureClick"></pbutton-white>'}ppage_template.prototype=new persagyElement,ppage_template.prototype.getTemplateStr=function(e,t){var n=e.attr,r=e.event;switch(t){case this.controlTypes.ppage.types[0]:var i="<pcombobox-page "+this.pageComboboxMarker+' orientation="'+(n.orientation||"")+'" number="'+(n.number||"")+'" sel="ppage.selPageFn"></pcombobox-page>';return this.start+this.leftButton+i+this.rightButton+this.end;default:var s=this.createFullPageCon(n.number,1);return this.start+this.leftButton+s+this.rightButton+this.fullPageInputDiv+this.end}},ppage_template.prototype.createFullPageCon=function(e,t){var n=this.createLiStr(e,t);return this.fullPageNumberUlStart+n+this.fullPageNumberUlEnd},ppage_template.prototype.createLiStr=function(e,t){function f(e,r){return'<li class="per-paging-item '+(r===!0?n.dotCss:"")+" "+(e===t?n.fullSelCss:"")+'">'+e+"</li>"}var n=this,r=f(t),i=t,s=t;for(var o=1;o<=2;o++){var u=t-o;if(u<=0)break;i=u;var a=f(u);r=a+r}i-1>1&&(r=f(n.dotStr,!0)+r),i-1>0&&(r=f(1)+r);for(o=1;o<=2;o++){u=t+o;if(u>e)break;s=u;var a=f(u);r+=a}return e-s>1&&(r+=f(n.dotStr,!0)),e-s>0&&(r+=f(e)),r};function ppage(){this.constructor=arguments.callee}ppage.prototype=new ppage_template,ppage.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css;r.number=r.number||1;var o=this.getTemplateStr(t,e);this.renderView(o,this.controlTypes.ppage.name,e,t,n)},ppage.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e);s.find("["+this.leftButtonMarker+"]").pdisable(!0),r.number==1&&(s.find("["+this.rightButtonMarker+"]").pdisable(!0),s.find("["+this.pageComboboxMarker+"]").pdisable(!0)),this.createEvent(s.find("["+this.fullUlMarker+"]"),this.controlTypes.ppage.name,"click",window[this.ptool.pstaticEventFnName])},ppage.prototype.eventHandout=function(e,t){var n=$(t.currentTarget).parents("["+this.ptool.maxDivMarker+"]").eq(0),r=$(t.target),i=r.text(),s=parseInt(i),o=n.find("["+this.fullTextMarker+"]");o.pval("");if(i==this.dotStr)if(r.index()==1){var u=r.next().text();s=parseInt(u)-1}else{var a=r.prev().text();s=parseInt(a)+1}this.pageChangedToData(n,s,t)},ppage.prototype.pageChangedToData=function(e,t,n){n[this.ptool.eventCurrTargetName]=e[0];var r=e.attr(this.ptool.libraryIdToHtml),i=ppage[r];if(t>i.attr.number)return;this.pageChanged(e,t);var s=n[this.ptool.eventOthAttribute]||{};s.pageIndex=t,n[this.ptool.eventOthAttribute]=s,this.executeEventCall(null,n,i.event.sel)},ppage.prototype.pageBefore=function(e){var t=$(e.currentTarget).parents("["+this.ptool.maxDivMarker+"]").eq(0),n=t[0],r=n[this.ptool.libraryToPro]||{},i=r[this.ptool.controlPrivateToProName]||1,s=t.attr(this.ptool.libraryIdToHtml),o=ppage[s],u=o.attr,a=u.number,f=t.find("["+this.leftButtonMarker+"]"),l=t.find("["+this.rightButtonMarker+"]");return a==1?(f.pdisable(!0),l.pdisable(!0),!1):{currPageIndex:i,leftTarget:f,_oldPpro:r,ele:n,rightTarget:l,pageCount:a}},ppage.prototype.pageChanged=function(e,t){var n=e[0],r=n[this.ptool.libraryToPro]||{};r[this.ptool.controlPrivateToProName]=t,n[this.ptool.libraryToPro]=r;var i=e.attr(this.ptool.libraryIdToHtml),s=ppage[i],o=s.attr,u=o.number,a=this.ptool.getTypeAndChildTypeFromEle(e),f=e.find("["+this.leftButtonMarker+"]"),l=e.find("["+this.rightButtonMarker+"]"),c=e.find("["+this.pageComboboxMarker+"]");u==1?(f.pdisable(!0),l.pdisable(!0),c.pdisable(!0)):(c.pdisable(!1),t==1?(f.pdisable(!0),l.pdisable(!1)):t==u?(l.pdisable(!0),f.pdisable(!1)):(l.pdisable(!1),f.pdisable(!1)));if(a.childType===this.controlTypes.ppage.types[1]){var h=this.createLiStr(o.number,t),p=e.find("["+this.fullUlMarker+"]");p.empty(),p.append(h)}},ppage.prototype.psel=function(e,t){var n=arguments[0],r=$(n),i=this.ptool.getTypeAndChildTypeFromEle(r);if(arguments.length==1){var s=n[this.ptool.libraryToPro]||{};return s[this.ptool.controlPrivateToProName]||1}e=parseInt(arguments[1]);if(!e)return!1;t=arguments[2],t=t===!1?!1:!0,t||this.pageChanged(r,e);if(i.childType===this.controlTypes.ppage.types[0])return r.find("["+this.pageComboboxMarker+"]").psel(e-1,t);t&&this.pageChangedToData(r,e,{})},ppage.prototype.pcount=function(e){var t=arguments[0],n=$(t),r=this.ptool.getTypeAndChildTypeFromEle(n),i=n.attr(this.ptool.libraryIdToHtml),s=ppage[i],o=s.attr,u=o.number;if(arguments.length==1)return u;e=arguments[1],e=parseInt(e),o.number=e,s.attr=o,ppage[i]=s;var a=t[this.ptool.libraryToPro]||{},f=a[this.ptool.controlPrivateToProName]||1;e<f&&(f=1),a[this.ptool.controlPrivateToProName]=f,t[this.ptool.libraryToPro]=a,this.pageChanged(n,f);if(r.childType===this.controlTypes.ppage.types[0]){var l=n.find("["+this.pageComboboxMarker+"]");l.pcount(e),l.psel(f-1,!1)}else{var c=n.find("["+this.fullUlMarker+"]"),h=this.createLiStr(e,f);c.empty(),c.append(h)}},ppage.prevPageFn=function(e){var t=new ppage,n=t.pageBefore(e);if(n==0)return;n.rightTarget.pdisable(!1);var r=n.currPageIndex,i=n._oldPpro,s=n.ele;if(r==1)return n.leftTarget.pdisable(!0);--r,s.psel(r)},ppage.nextPageFn=function(e){var t=new ppage,n=t.pageBefore(e);if(n==0)return;n.leftTarget.pdisable(!1);var r=n.currPageIndex,i=n._oldPpro,s=n.ele;if(r==n.pageCount)return n.rightTarget.pdisable(!0);++r,s.psel(r)},ppage.selPageFn=function(e){var t=new ppage,n=$(e[t.ptool.eventCurrTargetName]).parents("["+t.ptool.maxDivMarker+"]").eq(0),r=e[t.ptool.eventOthAttribute],i=r.index+1;t.pageChangedToData(n,i,e)},ppage.sureClick=function(e){var t=new ppage,n=$(e.currentTarget).parents("["+t.ptool.maxDivMarker+"]").eq(0),r=n.find("["+t.fullTextMarker+"]"),i=r.pval();if(!i)return;if(!r.pverifi())return;var s=parseInt(i);t.pageChangedToData(n,s,e)};function psearch_template(){this.constructor=arguments.callee,this.friendlyUlMarker="friul",this.inputMarker="schinput",this.friendlyMarker="fri",this.scrollMarker="scro",this.liTextMarker="litt",this.comboboxMarker="scom",this.clearMarker="clear",this.delaySearchMarker="dsm",this.friendlyMaxHeight=(new pcombobox_template).comboxMaxHeight,this.inputSelCss="per-searchbox-delay_border",this.maxDelayCss="per-searchbox-delay",this.maxPromptlyCss="per-searchbox-promptly",this.start="<div {{id}} "+this.ptool.maxDivMarker+' class="',this.start2='">',this.comboxStart='<div class="per-searchbox-combobox" '+this.comboboxMarker+">",this.comboxEnd="</div>",this.searchInputStart='<div class="per-searchbox-input">',this.promptlySearchIcon='<div class="per-searchbox-icon">f</div>',this.searchInputMiddle='<input {{placeholder}} type="text" '+this.inputMarker+'/><div class="per-searchbox-input_x" '+this.clearMarker+">x</div>",this.searchFriendlyStart='<div class="per-combobox-wrap" style="display: none;" '+this.friendlyMarker+'><div class="per-combobox-con" style="display:inline-block;"><pscroll-small '+this.scrollMarker+' templateid="',this.searchFriendlyStart2='"></pscroll-small>',this.searchFriendlyend="</div></div>",this.friendlyUlCon="<ul "+this.friendlyUlMarker+" "+this.ptool.libraryTypeToHtml+'="',this.friendlyUlCon2='"></ul>',this.searchInputEnd="</div>",this.delaySearchIcon='<div class="per-searchbox-icon" '+this.delaySearchMarker+">f</div>",this.end="</div>"}psearch_template.prototype=new persagyElement,psearch_template.prototype.getTemplateStr=function(e,t){var n=e.attr,r=n.tip,i="";if(n.comboboxHtml){var s="<div>"+this.comboxStart+n.comboboxHtml+this.comboxEnd+"</div>",o=$(s),u=o.children().children().eq(0);u.attr("sel","psearch.typeSel"),u.attr("isborder","false"),i=o.html()}var a="";if(r.suggestsource||r.advisesource){var f=this.controlTypes.psearch.name+this.ptool.typeSeparator+t,l=this.ptool.createDynamicTemplate(this.friendlyUlCon+f+this.friendlyUlCon2);a=this.searchFriendlyStart+l+this.searchFriendlyStart2+this.searchFriendlyend}return this.start+(t==this.controlTypes.psearch.types[0]?this.maxDelayCss:this.maxPromptlyCss)+this.start2+i+this.searchInputStart+(t==this.controlTypes.psearch.types[1]?this.promptlySearchIcon:"")+this.searchInputMiddle+a+this.searchInputEnd+(t==this.controlTypes.psearch.types[0]?this.delaySearchIcon:"")+this.end},psearch_template.prototype.createAdviseLiStr=function(objBind,val){var attr=objBind.attr,tipObj=attr.tip,source,type,textPro;val?(source=tipObj.suggestsource,type=1,textPro=tipObj.suggesttext):(source=tipObj.advisesource,type=2,textPro=tipObj.advisetext);if(!source)return;var liStr="",sourceArr=eval(source);for(var i=0;i<sourceArr.length;i++){var currItem=sourceArr[i],textVal=textPro?currItem[textPro]:currItem,bStr="";if(type==1){var splitObj=this.ptool.splitStrByKey(val,textVal);splitObj&&(bStr=splitObj.start+'<em class="per-searchbox-select_color">'+splitObj.key+"</em>"+splitObj.end)}else bStr=textVal;if(!bStr)continue;liStr+='<li class="per-combobox_item" '+this.liTextMarker+'="'+textVal+'"><b>'+bStr+"</b></li>"}return liStr};function psearch(){this.constructor=arguments.callee}psearch.prototype=new psearch_template,psearch.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css,o=n.find("tip");r.tip={suggestsource:o.attr("suggestsource"),suggesttext:o.attr("suggesttext"),advisesource:o.attr("advisesource"),advisetext:o.attr("advisetext")},!r.tip.suggestsource&&!r.tip.advisesource?r.friendly=!1:r.friendly=!0,r.comboboxHtml=n.find("combobox").html();var u=this.ptool.isIe();u&&(r.placeholder=null,delete r.placeholder);var a=this.getTemplateStr(t,e);this.renderView(a,this.controlTypes.psearch.name,e,t,n)},psearch.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e),o=s.find("["+this.inputMarker+"]");this.createEvent(o,this.controlTypes.psearch.name,"focus",window[this.ptool.pstaticEventFnName]),this.createEvent(o,this.controlTypes.psearch.name,"blur",window[this.ptool.pstaticEventFnName]),this.createEvent(o,this.controlTypes.psearch.name,"input",window[this.ptool.pstaticEventFnName]),this.createEvent(o,this.controlTypes.psearch.name,"click",window[this.ptool.pstaticEventFnName]);var u=s.attr(this.ptool.libraryIdToHtml),a=s.find("["+this.friendlyUlMarker+"]");a.attr(this.ptool.libraryIdToHtml,u),this.createEvent(a,this.controlTypes.psearch.name,"click",window[this.ptool.pstaticEventFnName]),this.createEvent(s.find("["+this.clearMarker+"]"),this.controlTypes.psearch.name,"click",window[this.ptool.pstaticEventFnName]),this.createEvent(s.find("["+this.delaySearchMarker+"]"),this.controlTypes.psearch.name,"click",window[this.ptool.pstaticEventFnName])},psearch.prototype.eventHandout=function(e,t){function m(){if(l.friendly===!0){var e=n.createAdviseLiStr(a,h.val());if(e){var t=n.controlTypes.pscroll.name+n.ptool.typeSeparator+n.controlTypes.pscroll.types[0];c.find("["+n.ptool.libraryTypeToHtml+'="'+t+'"]').psetScroll(0);var i=r.find("["+n.friendlyUlMarker+"]");i.empty(),i.append(e),c.show()}else c.hide()}}function g(r){var i={key:h.val(),type:s.psel()};t[n.ptool.eventOthAttribute]=i,n.executeEventCall(e,t,r)}function y(){var e=r.find("["+n.clearMarker+"]");h.val().length>0?e.show():e.hide()}var n=this,r=$(t[n.ptool.eventCurrTargetName]),i=r.attr(n.ptool.libraryIdToHtml);r[0].tagName=="UL"&&(r=r.parents("["+n.ptool.libraryIdToHtml+'="'+i+'"]').eq(0));var s=r.find("["+n.comboboxMarker+"]"),o=t.type,u=n.ptool.getTypeAndChildTypeFromEle(r),a=psearch[i],f=a.event||{},l=a.attr,c=r.find("["+n.friendlyMarker+"]"),h=r.find("["+n.inputMarker+"]");switch(o){case"focus":(new pcombobox).slideUp(),r.addClass(n.inputSelCss),m(),y(),g(f.focus);break;case"blur":r.removeClass(n.inputSelCss),g(f.blur);break;case"input":var p=h.val();m(),u.childType===this.controlTypes.psearch.types[1]&&g(f.change),y();break;case"click":if(t.currentTarget.tagName=="UL"){var d=$(t.target);t.target.tagName!="LI"&&(d=d.parent());var v=d.attr(n.liTextMarker);h.val(v),g(f.change),c.hide(),y()}$(t.currentTarget).attr(n.clearMarker)==""&&(h.val(""),y(),g(f.change)),$(t.currentTarget).attr(n.delaySearchMarker)==""&&g(f.change)}},psearch.prototype.pval=function(e){var t=arguments[0],n=$(t),r=n.find("["+this.inputMarker+"]"),i=n.find("["+this.comboboxMarker+"]");if(arguments.length==1){var s=r.val(),o=i.psel();return{key:s,type:o}}e=arguments[1]||{};var u=e.value,a=e.indexOrText,f=e.isEvent||!1;i.psel(a,!1),r.val(u||"");if(f){var l=n.attr(this.ptool.libraryIdToHtml),c=psearch[l],h=c.event||{},p={key:r.val(),type:i.psel()},d=this.ptool.appendProToEvent({},p);this.executeEventCall(null,d,h.change)}},psearch.prototype.precover=function(e,t){var n=arguments[0],r=$(n);r.find("["+this.comboboxMarker+"]").precover(arguments[1]),t=arguments[2]===!1?!1:!0;var i=r.find("["+this.clearMarker+"]");if(t)return i[0].click();i.hide(),r.find("["+this.inputMarker+"]").val("")},psearch.prototype.slideUpFriend=function(){var e=this,t="",n=e.controlTypes.psearch.types,r=e.controlTypes.psearch.name;n.forEach(function(n){var i=r+e.ptool.typeSeparator+n;t+=(t.length>0?",":"")+"["+e.ptool.libraryTypeToHtml+'="'+i+'"]'}),$(t).find("["+(new psearch_template).friendlyMarker+"]").hide()},psearch.typeSel=function(e,t){};function pupload_template(){this.constructor=arguments.callee,this.defaultText="点击上传",this.inputFileMarker="itfile",this.imgUploadingSrc="./imgs/uploadingImg.png",this.imgMarkr="timg",this.fileRegionUlMarker="fileul",this.fileLabelMarker="filelabel",this.clearMarker="clear",this.currNumberMarker="crnu",this.uploadBarMarker="uploadbar",this.fileNameMarker="fname",this.oneFileLiMarker="fileli",this.fileDisableMarker="fdis",this.imgUploadFailureMarker="iuflm",this.imgUploadingMarker="iuimk",this.uploadFailureClass="_bar-red",this.uploadFailureText="上传失败",this.imgUploadFailureHtml="<div "+this.imgUploadFailureMarker+' class="per-upload-picture_error"><span></span><b>上传失败</b></div>',this.img1="<div "+this.ptool.maxDivMarker+'{{id}} class="per-upload-picture ',this.img4='"><ul '+this.fileRegionUlMarker+' class="per-upload-picture-ul"></ul><div class="per-upload-picture-wrap"><label '+this.fileLabelMarker,this.img2=' class="per-upload-picture_label"><span>J</span><span>',this.img2_="</span></label><input "+this.inputFileMarker+' accept="image/jpeg,image/gif,image/jpg,image/png,image/bmp" type="file" id="',this.img3='" style="display: none;"></div></div>',this.imgHorizontalCss="per-upload_horizontal",this.imgVerticalCss="per-upload_vertical",this.lonelyFile1="<div "+this.ptool.maxDivMarker+'{{id}} class="per-upload-onlyfile"><pbutton-white click="pupload.uploadBtnClick" text="',this.lonelyFile1_='" icon="d" '+this.fileLabelMarker+"></pbutton-white><input "+this.inputFileMarker+' {{accept}} type="file" id="',this.lonelyFile3='" style="display: none;"><div class="per-upload-onlyfile_file" '+this.oneFileLiMarker+'><div class="per-upload-onlyfile_name" '+this.fileDisableMarker+"><b "+this.fileNameMarker+'></b></div><span class="per-upload-manyfile_x" '+this.clearMarker+">x</span>"+'<div class="per-upload-manyfile_bar" '+this.uploadBarMarker+"></div></div></div>",this.multipleFile1="<div "+this.ptool.maxDivMarker+'{{id}} class="per-upload-manyfile"><pbutton-white click="pupload.uploadBtnClick" text="',this.multipleFile1_='" icon="d" '+this.fileLabelMarker+'></pbutton-white><div class="per-upload-count"><em '+this.currNumberMarker+">0</em>/<em>",this.multipleFile2="</em></div><input "+this.inputFileMarker+' {{accept}} type="file" id="',this.multipleFile3='" style="display: none;"><ul '+this.fileRegionUlMarker+"></ul></div>"}pupload_template.prototype=new persagyElement,pupload_template.prototype.getTemplateStr=function(e,t){var n=e.attr,r=ptool.produceId();switch(t){case this.controlTypes.pupload.types[1]:return this.img1+(n.arrange==this.ptool.arrangeType.horizontal?this.imgHorizontalCss:this.imgVerticalCss)+this.img4+this.img2+n.text+this.img2_+r+this.img3;default:return n.number==1?this.lonelyFile1+n.text+this.lonelyFile1_+r+this.lonelyFile3:this.multipleFile1+n.text+this.multipleFile1_+n.number+this.multipleFile2+r+this.multipleFile3}},pupload_template.prototype.createImgRegion=function(e,t){return e==this.controlTypes.pupload.types[1]?'<li class="per-upload-picture-wrap" '+this.oneFileLiMarker+'><div class="per-upload-picture_x" '+this.clearMarker+">x</div><img "+this.imgMarkr+"/><div "+this.imgUploadingMarker+' class="per-upload-picture_uploading"></div></li>':'<li class="per-upload-manyfile_item" '+this.oneFileLiMarker+'><div class="per-upload-manyfile_name"'+this.fileDisableMarker+"><b "+this.fileNameMarker+">"+t+'</b></div><span class="per-upload-manyfile_x"'+this.clearMarker+'>x</span><div class="per-upload-manyfile_bar" '+this.uploadBarMarker+"></div></li>"};function pupload(){this.constructor=arguments.callee}pupload.prototype=new pupload_template,pupload.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css;r.number=r.number||1,r.arrange=r.arrange||this.ptool.arrangeType.horizontal,r.text=r.text||this.defaultText;var o=n.find("panel");r.panel={change:o.attr("change"),success:o.attr("success"),err:o.attr("err"),progress:o.attr("progress"),clear:o.attr("clear")};var u=this.getTemplateStr(t,e);this.renderView(u,this.controlTypes.pupload.name,e,t,n)},pupload.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e),o=s.find("["+this.inputFileMarker+"]");this.createEvent(o,this.controlTypes.pupload.name,"change",window[this.ptool.pstaticEventFnName]),n==this.controlTypes.pupload.types[0]?r.number==1&&(this.createEvent(s.find("["+this.clearMarker+"]"),this.controlTypes.pupload.name,"click",window[this.ptool.pstaticEventFnName]),this.createEvent(s.find("["+this.oneFileLiMarker+"]"),this.controlTypes.pupload.name,"click",window[this.ptool.pstaticEventFnName])):this.createEvent(s.find("["+this.fileLabelMarker+"]"),this.controlTypes.pupload.name,"click",window[this.ptool.pstaticEventFnName])},pupload.prototype.eventHandout=function(e,t){var n=this,r=$(t[this.ptool.eventCurrTargetName]),i=r[0],s=t.type,o=$(t.currentTarget),u=i[n.ptool.registeredEventRcord]||{},a=r.find("["+this.inputFileMarker+"]"),f=r.find("["+this.fileLabelMarker+"]"),l=r.find("["+this.currNumberMarker+"]"),c=r.find("["+this.oneFileLiMarker+"]"),h=r.find("["+this.fileRegionUlMarker+"]"),p=this.ptool.getTypeAndChildTypeFromEle(r),d=r.attr(this.ptool.libraryIdToHtml),v=pupload[d],m=v.event||{},g=v.attr,y=g.panel,b=n.createXmlhttpName(d);switch(s){case"click":if(o.attr(this.fileLabelMarker)!=null)u.fileLabelIndex=-1,pupload[d]=v,i[n.ptool.registeredEventRcord]=u,a[0].click();else if(o.attr(this.clearMarker)!=null){v[b]&&v[b].abort();if(p.childType==this.controlTypes.pupload.types[0]&&g.number==1)u.files=[],c.hide(),f.show();else{var w=o.parent(),E=w.index();w.remove(),(u.files||[]).splice(E,1);if(p.childType==this.controlTypes.pupload.types[1])f.parent().show();else{var S=parseInt(l.text())||0;--S,l.text(S),f.pdisable(!1)}}this.executeEventCall(e,t,y.clear)}else if(o.attr(this.oneFileLiMarker)!=null){if(o.find("["+this.fileDisableMarker+"]").attr("pdisabled")=="true")return;u.fileLabelIndex=p.childType==this.controlTypes.pupload.types[0]&&g.number==1?0:o.index(),pupload[d]=v,i[n.ptool.registeredEventRcord]=u,a[0].click()}break;case"change":var x=a[0].files[0];if(!x)return;var T;if(p.childType==this.controlTypes.pupload.types[0]&&g.number==1)f.hide(),c.find("["+this.uploadBarMarker+"]").show(),c.find("["+this.fileNameMarker+"]").text(x.name),c.show(),T=c;else if(u.fileLabelIndex==-1){var N=this.createImgRegion(p.childType,x.name);h.append(N),this.createEvent(r.find("["+this.clearMarker+"]:last"),this.controlTypes.pupload.name,"click",window[this.ptool.pstaticEventFnName]),this.createEvent(h.find("["+this.oneFileLiMarker+"]:last"),this.controlTypes.pupload.name,"click",window[this.ptool.pstaticEventFnName]),T=h.find("li:last")}else{var C=h.children().eq(u.fileLabelIndex);C.find("["+this.clearMarker+"]").show();switch(p.childType){case this.controlTypes.pupload.types[0]:C.find("["+this.uploadBarMarker+"]").show(),C.find("["+this.fileNameMarker+"]").text(x.name)}T=C}T.find("["+this.fileDisableMarker+"],["+this.uploadBarMarker+"]").removeClass(this.uploadFailureClass),T.find("["+this.imgUploadFailureMarker+"]").remove(),T.find("["+this.imgMarkr+"]").hide(),T.find("["+this.imgUploadingMarker+"]").show();var k=pajax.upload({file:x,success:this.psuccess(r,v,T,y.success),progress:this.pprogress(T,y.progress),error:this.perror(r,v,T,y.err)});v[b]=k,T.find("["+this.fileDisableMarker+"]").pdisable(!0),a[0].value="",t=this.ptool.appendProToEvent(t,{file:x}),this.executeEventCall(e,t,y.change)}pupload[d]=v,i[n.ptool.registeredEventRcord]=u},pupload.prototype.pprogress=function(e,t){return function(e,t){return function(n){var r=new pupload;e.find("["+r.uploadBarMarker+"]").css({width:n.probe+"%"}),r.executeEventCall(null,n,t)}}(e,t)},pupload.prototype.psuccess=function(e,t,n,r){return function(t,n,r,i){return function(s){s=s||{};var o=new pupload,u=e[0],a=u[o.ptool.registeredEventRcord]||{},f=t.find("["+o.currNumberMarker+"]"),l=t.attr(o.ptool.libraryIdToHtml),c=parseInt(f.text())||0,h=pupload[l],p=o.createXmlhttpName(l),d=o.ptool.getTypeAndChildTypeFromEle(t),p=o.createXmlhttpName(l);n[p]=null;var v=s.showUrl+"?ft="+(d.childType===o.controlTypes.pupload.types[1]?"1":"2"),m={url:v,name:s.name,suffix:s.suffix,isNewFile:!0},g=a.files||[];a.fileLabelIndex===-1?(g.push(m),++c):g.splice(a.fileLabelIndex,1,m),a.files=g,pupload[l]=n,u[o.ptool.registeredEventRcord]=a,f.text(c);var y=n.attr;if(d.childType==o.controlTypes.pupload.types[1]||d.childType==o.controlTypes.pupload.types[0]&&y.number>1)if(r.siblings().length+1==y.number){var b=t.find("["+o.fileLabelMarker+"]");d.childType==o.controlTypes.pupload.types[1]?b.parent().hide():b.pdisable(!0)}r.find("["+o.fileDisableMarker+"]").pdisable(!1),r.find("["+o.uploadBarMarker+"]").hide();switch(d.childType){case o.controlTypes.pupload.types[1]:r.find("["+o.imgUploadingMarker+"]").hide();var w=r.find("["+o.imgMarkr+"]");w.attr("src",v),w.show()}o.executeEventCall(null,{showUrl:m.url,name:m.name,suffix:m.suffix},i)}}(e,t,n,r)},pupload.prototype.perror=function(e,t,n,r){return function(e,t,n,r){return function(t){console.log("------------");var i=new pupload,s=i.ptool.getTypeAndChildTypeFromEle(e);switch(s.childType){case i.controlTypes.pupload.types[0]:n.find("["+i.fileDisableMarker+"],["+i.uploadBarMarker+"]").addClass(i.uploadFailureClass),n.find("["+i.fileNameMarker+"]").text(i.uploadFailureText);break;case i.controlTypes.pupload.types[1]:n.find("["+i.imgMarkr+"]").hide(),n.find("["+i.imgUploadingMarker+"]").hide(),n.append(i.imgUploadFailureHtml)}var o=e.attr(i.ptool.libraryIdToHtml),u=e[0],a=u[i.ptool.registeredEventRcord]||{},f=e.find("["+i.currNumberMarker+"]"),l=parseInt(f.text())||0,c=pupload[o],h=c.attr;a.fileLabelIndex===-1&&++l,f.text(l);if(s.childType==i.controlTypes.pupload.types[1]||s.childType==i.controlTypes.pupload.types[0]&&h.number>1)if(l==h.number){var p=e.find("["+i.fileLabelMarker+"]");s.childType==i.controlTypes.pupload.types[1]?p.parent().hide():p.pdisable(!0)}i.executeEventCall(null,null,r)}}(e,t,n,r)},pupload.prototype.createXmlhttpName=function(e){return e+"XHTTP"},pupload.prototype.pval=function(e){var t=this,n=arguments[0],r=$(n),i=r.attr(this.ptool.libraryIdToHtml),s=pupload[i],o=s.attr,u=n[t.ptool.registeredEventRcord]||{};if(arguments.length==1)return JSON.parse(JSON.stringify(u.files||[]));var a=[];e=arguments[1]||[];var f=r.find("["+this.currNumberMarker+"]");f.text(e.length);var l=this.ptool.getTypeAndChildTypeFromEle(n),c=r.find("["+this.fileRegionUlMarker+"]"),h=r.find("["+this.fileLabelMarker+"]"),p=r.find("["+this.oneFileLiMarker+"]");for(var d=0;d<e.length;d++){var v=e[d],m=v.url+(v.url.indexOf("ft=")==-1?"?ft="+(l.childType===this.controlTypes.pupload.types[1]?"1":"2"):""),g=v.name||"",y=v.suffix||"",b=g.indexOf(".");b>-1?(y=g.substring(b+1),g=g.substring(0,b)):"",a.push({url:m,name:g,suffix:y,isNewFile:!1});if(l.childType===this.controlTypes.pupload.types[0]&&o.number===1){h.hide(),p.find("["+this.fileNameMarker+"]").text(g),p.show();break}var w=this.createImgRegion(l.childType,g);c.append(w),l.childType===this.controlTypes.pupload.types[1]&&c.children().filter(":last").find("["+this.imgMarkr+"]").attr("src",m),this.createEvent(r.find("["+this.clearMarker+"]:last"),this.controlTypes.pupload.name,"click",window[this.ptool.pstaticEventFnName]),this.createEvent(r.find("["+this.oneFileLiMarker+"]:last"),this.controlTypes.pupload.name,"click",window[this.ptool.pstaticEventFnName])}r.find("["+this.uploadBarMarker+"]").hide(),u.files=a,pupload[i]=s,n[t.ptool.registeredEventRcord]=u;switch(l.childType){case this.controlTypes.pupload.types[0]:o.number>1&&(e.length===o.number?h.pdisable(!0):h.pdisable(!1));break;case this.controlTypes.pupload.types[1]:e.length===o.number?h.parent().hide():h.parent().show()}},pupload.prototype.precover=function(){var e=arguments[0],t=$(e),n=e[this.ptool.registeredEventRcord]||{},r=t.attr(this.ptool.libraryIdToHtml),i=pupload[r],s=i.attr,o=this.ptool.getTypeAndChildTypeFromEle(e),u=t.find("["+this.fileLabelMarker+"]");switch(o.childType){case this.controlTypes.pupload.types[0]:if(s.number===1){var a=t.find("["+this.oneFileLiMarker+"]");a.find("["+this.fileNameMarker+"]").text(""),a.hide(),u.show();break};case this.controlTypes.pupload.types[1]:t.find("["+this.fileRegionUlMarker+"]").empty(),u.pdisable(!1),u.parent().show()}n.files=[],e[this.ptool.registeredEventRcord]=n,t.find("["+this.currNumberMarker+"]").text(0)},pupload.uploadBtnClick=function(e){var t=new pupload,n=$(e.currentTarget).parent(),r=n[0],i=n.find("["+t.inputFileMarker+"]"),s=t.ptool.getTypeAndChildTypeFromEle(n),o=r[t.ptool.registeredEventRcord]||{};o.fileLabelIndex=-1,r[t.ptool.registeredEventRcord]=o,i[0].click()};function ptree_template(){this.constructor=arguments.callee,this.foldIconObj={b:"r",r:"b"},this.foldMarker="fold",this.treeRegionMarker="treeregion",this.bodyRegionMarker="bodymarker",this.itemTitleMarker="itemtext",this.itemTitleDisabledMarker="itdmar",this.itemTitleValMarker="itemtextval",this.resultUlMarker="rul",this.resultMarker="sresult",this.operMarker="operd",this.operUlMarker="operdul",this.treeSearchMarker="tsm",this.singleCss="per-structure-single",this.panelCss="per-structure-default",this.selCss="per-tree-ts_active",this.start="<div "+this.ptool.maxDivMarker+' class="per-structure-normal ',this.start2='"{{id}}>',this.searchStr='<div class="per-structure-title"><psearch-promptly '+this.treeSearchMarker+' change="ptree.searchPromptly"></psearch-promptly></div>',this.conout1='<div class="per-structure-con" '+this.bodyRegionMarker+'><pscroll-small templateid="',this.conout2='"></pscroll-small></div></div>',this.conSearchResultRegion="<div "+this.resultMarker+' class="per-tree-soso_result"><ul '+this.resultUlMarker+"></ul></div>",this.conOperStr='<div class="per-tree-ts"><span '+this.operMarker,this.conOperStr2=">n</span><ul "+this.operUlMarker+' style="display: none;"',this.conOperStr3="><li>选择所有子级</li><li>选择下一级</li><li>取消选择所有子级</li></ul></div>"}ptree_template.prototype=new persagyElement,ptree_template.prototype.getTemplateStr=function(e,t){var n=e.attr,r=n.panel,i=n.item,s=this.start+(r?this.panelCss:"")+this.start2+(n.issearch===!0?this.searchStr:""),o=this.conSearchResultRegion,u=ptool.produceId(),a="",f=ptool.produceId(),l=persagy_toBind.getInstance(),c="";switch(l.currFrameType){case l.frameTypes.ko:c=this.conOperStr+' data-bind="click:'+this.ptool.pbindEventFnName+'"'+this.conOperStr2+' data-bind="click:'+this.ptool.pbindEventFnName+'"'+this.conOperStr3,o+="<div "+this.treeRegionMarker+' class="per-tree-wrap" data-bind="template:{name:'+f+",foreach:"+n.datasource+'}"></div>';var h=",attr:{"+this.itemTitleMarker+":"+(i.itemid||"''")+(i.disabled?","+this.itemTitleDisabledMarker+":!!"+i.disabled:"")+"}";a='<div class="per-tree-temp"><div data-bind="click:'+this.ptool.pbindEventFnName+",event:{mouseenter:"+this.ptool.pbindEventFnName+"},style:{'padding-left':(level*15+20)+'px'}"+h+'" class="per-tree-title'+(n.number===1?" per-structure-single":"")+'"><div class="per-slh"><span '+this.foldMarker+' class="per-tree-arrow" data-bind="click:'+this.ptool.pbindEventFnName+",style:{visibility:"+i.child+"&&"+i.child+".length>0?'visible':'hidden'}\">"+this.foldIconObj.b+"</span><b "+this.itemTitleValMarker+' data-bind="attr:{'+(i.disabled?"pdisabled:!!"+i.disabled:"")+"},text:"+i.text+'"></b></div>'+(n.number===1?"":c)+'</div><div class="per-tree-con" style="display:none;" '+'data-bind="template:{name:'+f+",foreach:"+i.child+'}"></div></div>';break;case l.frameTypes.Vue:c=this.conOperStr+' @click="'+this.ptool.pbindEventFnName+'(model,$event)"'+this.conOperStr2+' @click="'+this.ptool.pbindEventFnName+'(model,$event)"'+this.conOperStr3;var p="vuetag"+f;o+='<div class="per-tree-wrap" '+this.treeRegionMarker+"><"+p+' v-for="item in '+n.datasource+'" :model="item"></'+p+"></div>";var h=" v-bind:"+this.itemTitleMarker+'="'+(i.itemid?"model."+i.itemid:"''")+'"'+(i.disabled?" v-bind:"+this.itemTitleDisabledMarker+'="!!model.'+i.disabled+'"':"");a='<div class="per-tree-temp"><div '+h+'  @click="'+this.ptool.pbindEventFnName+'(model,$event)" @mouseenter="'+this.ptool.pbindEventFnName+"(model,$event)\" v-bind:style=\"{'padding-left':(model.level*15+20)+'px'}\" class=\"per-tree-title"+(n.number===1?" per-structure-single":"")+'"><div class="per-slh"><span '+this.foldMarker+' class="per-tree-arrow" @click="'+this.ptool.pbindEventFnName+'(model,$event)" v-bind:style="{visibility:model.'+i.child+"&&model."+i.child+".length>0?'visible':'hidden'}\">"+this.foldIconObj.b+"</span><b "+this.itemTitleValMarker+' v-text="model.'+i.text+'"'+(i.disabled?' v-bind:pdisabled="!!model.'+i.disabled+'"':"")+"></b></div>"+(n.number===1?"":c)+'</div><div class="per-tree-con" style="display:none;" '+"><"+p+' v-for="item in model.'+i.child+'" :model="item"></'+p+"></div></div>"}this.ptool.createDynamicTemplate(o,u),this.ptool.createDynamicTemplate(a,f),l.currFrameType==l.frameTypes.Vue&&Vue.component(p,{template:"#"+f,props:{model:Object},methods:{}});var d=this.conout1+u+this.conout2;return s+d};function ptree(){this.constructor=arguments.callee}ptree.prototype=new ptree_template,ptree.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css;r.bind=!0,r.number=r.number>1?r.number:1;var o=n.find("panel");o.length>0&&(r.panel={width:o.attr("width"),height:o.attr("height")});var u=n.find("item");r.item={itemid:u.attr("itemid"),text:u.attr("text"),disabled:u[0].getAttribute("disabled"),child:u.attr("child"),issearch:u.attr("issearch")},r.issearch=r.item.issearch==="false"?!1:!0;var a=this.getTemplateStr(t,e);this.renderView(a,this.controlTypes.ptree.name,e,t,n)},ptree.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e),o=s.attr(this.ptool.libraryIdToHtml),u=this.controlTypes.ptree.name+this.ptool.typeSeparator+n,a=s.find("["+this.treeRegionMarker+"]");a.attr(this.ptool.libraryTypeToHtml,u),a.attr(this.ptool.libraryIdToHtml,o);var f=s.find("["+this.resultMarker+"]");f.attr(this.ptool.libraryTypeToHtml,u),f.attr(this.ptool.libraryIdToHtml,o),r.issearch&&this.createEvent(s.find("["+this.resultUlMarker+"]"),this.controlTypes.ptree.name,"click",window[this.ptool.pstaticEventFnName])},ptree.prototype.eventHandout=function(e,t){var n=this,r=$(t[this.ptool.eventCurrTargetName]),i=r.attr(this.ptool.libraryIdToHtml),s=t.type,o=$(t.currentTarget),u=this.ptool.getTypeAndChildTypeFromEle(r),a=ptree[i],f=a.attr,l=a.event||{};switch(s){case"click":if(o.attr(this.foldMarker)!=null){var c=o.parent().parent().next(),h=c.is(":hidden")?"slideDown":"slideUp";c[h](function(){var e=r;while(e.attr(n.ptool.maxDivMarker)==null)e=e.parent();(new pscroll).setEventCall(e)({type:"mouseenter"})}),o.text(this.foldIconObj[o.text()])}else if(o.attr(this.itemTitleMarker)!=null){if(o.attr(this.itemTitleDisabledMarker)=="true")return;f.number===1&&!o.hasClass(this.selCss)&&r.find("["+this.itemTitleMarker+"]").removeClass(this.selCss),o.toggleClass(this.selCss),t[this.eventOthAttribute]={state:o.hasClass(this.selCss)},this.executeEventCall(e,t,l.sel);var p=a[this.ptool.controlPrivateToProName]||[];if(o.hasClass(this.selCss))p.push(e);else for(var d=0;d<p.length;d++){var v=p[d];if(v.level===e.level&&v[f.item.text]===e[f.item.text]){p.splice(d,1);break}}a[this.ptool.controlPrivateToProName]=p,ptree[i]=a}else if(o.attr(this.resultUlMarker)!=null){var m=$(t.target);m[0].tagName!="LI"&&(m=m.parent()),m.toggleClass(this.selCss);var g=r[0],y=g[this.ptool.libraryToPro]||{},b=y[this.ptool.controlPrivateToProName],w=m.index();b[w][0].click()}else if(o.attr(this.operMarker)!=null)o.next().is(":visible")||$("["+this.operUlMarker+"]").hide(),o.next().toggle();else if(o.attr(this.operUlMarker)!=null){o.prev()[0].click();var E=$(t.target),S=E.index(),c=o.parent().parent().next(),x=c.children();if(S==1){var T=c.children();for(var N=0;N<T.length;N++){var C=T.eq(N).children().eq(0);C.hasClass(this.selCss)||C[0].click()}}else{var x=c.find("["+this.itemTitleMarker+"]");for(var k=0;k<x.length;k++){var L=x.eq(k);switch(S){case 0:L.hasClass(this.selCss)||L[0].click();break;case 2:L.hasClass(this.selCss)&&L[0].click()}}}}break;case"mouseenter":o.elementShowTitle(o.find("["+this.itemTitleValMarker+"]"))}},ptree.prototype.precover=function(e){var t=arguments[0],n=$(t);e=arguments[1]===!1?!1:!0,n.find("["+this.treeSearchMarker+"]").precover();if(e){n.find("["+this.itemTitleMarker+"]").removeClass(this.selCss);var r=n.attr(this.ptool.libraryIdToHtml),i=ptree[r];i[this.ptool.controlPrivateToProName]=[],ptree[r]=i}},ptree.prototype.psel=function(e){var t=arguments[0],n=$(t),r=n.attr(this.ptool.libraryIdToHtml),i=ptree[r],s=i[this.ptool.controlPrivateToProName]||[];if(arguments.length===1)return s;e=arguments[1]||{};var o=e.nodeId,u=e.isEvent===!1?!1:!0,a=e.type,f=n.find("["+this.itemTitleMarker+'="'+o+'"]');if(f.length==0)return!1;switch(a){case 0:u?(f.addClass(this.selCss),f[0].click()):f.removeClass(this.selCss);break;case 1:u?(f.removeClass(this.selCss),f[0].click()):f.addClass(this.selCss);break;case 2:u?f[0].click():f.toggleClass(this.selCss)}},ptree.searchPromptly=function(e){var t=new ptree,n=e[t.ptool.eventOthAttribute].key,r=$(e[t.ptool.eventCurrTargetName]),i=r.parent().next().find("["+t.treeRegionMarker+"]"),s=i.prev(),o=s.find("["+t.resultUlMarker+"]");if(!n){s.hide(),i.show();return}i.hide(),s.show();var u=i.find("["+t.itemTitleMarker+"]"),a=[],f="";for(var l=0;l<u.length;l++){var c=u.eq(l),h=c.find("["+t.itemTitleValMarker+"]").attr("pdisabled");if(!h){var p=c.find("["+t.itemTitleValMarker+"]").text();if(p.indexOf(n)==-1)continue;a.push(c);var d=t.ptool.splitStrByKey(n,p);d&&(f+='<li class="'+(c.hasClass(t.selCss)?t.selCss:"")+'">'+d.start+'<em class="per-searchbox-select_color">'+d.key+"</em>"+d.end+"</li>")}}o.empty(),o.append(f);var v=s[0],m=v[t.ptool.libraryToPro]||{};m[t.ptool.controlPrivateToProName]=a,v[t.ptool.libraryToPro]=m};function ptime_template(){this.constructor=arguments.callee,this.calendarDefaultTimeTypeArr=["d","w","M","y","h","m","s"],this.calendarTimeTypeShow={d:"日",w:"周",M:"月",y:"年",h:"时",m:"分",s:"秒"},this.calendarCommonTimeArr=["d","pd","w","pw","M","pM","y","py"],this.calendarCommonTimeShow={d:"今天",pd:"昨天",w:"本周",pw:"上周",M:"本月",pM:"上月",y:"今年",py:"去年"},this.calendarConTimeTypeCss={d:"per-calendar_isday",w:"per-calendar_isweek",M:"per-calendar_ismonth",y:"per-calendar_isyear"},this.lockObj={c:"s",s:"c"},this.orientationCss={up:"_combobox_top",down:"_combobox_bottom"},this.alignCss={left:"_combobox_left",right:"_combobox_right"},this.commonTimeLiMarker="commontimeli",this.contentNavigationLiMarker="navili",this.panelConMaxMarker="pcontm",this.panelConYearComboxMarker="yearbox",this.panelConCrossYearComboxMarker="crossyearbox",this.panelConMonthComboxMarker="monthbox",this.orientationMarker="orientationm",this.columnUlMarker="columnul",this.columnLiMarker="ival",this.selTimeShowRegionMarker="seltimeshowre",this.panelToggleMarker="arrowto",this.panelConHeaderQuickMarker="panelquickto",this.footLockMarker="footlock",this.contentMaxMarker="maxcon",this.headerWeekMarker="hdwkmr",this.stepUnitMarker="stepunit",this.stepValueMarker="stepval",this.tempSelTimeMarker="temptimeval",this.okBtnMarker="okt",this.timeTypeMarker="tmk",this.defaultMinYear=1900,this.defaultMaxYear=2099,this.oneDayMillSeconds=864e5,this.crossYearStep=12,this.weekUnit=".W",this.dateSperator="-",this.timeSperator=":",this.hourSperator=" ",this.showTextSperator="~",this.commonTimeSelCss="per-calendar_location_active",this.contentNavigationSelCss="per-calendar_details_active",this.columnSelCss="per-calendar_main_active",this.columnInCss="per-calendar_main_hover",this.toggleArrowCss="_calendar-arrows-avtive",this.calendarShowRegion='<div class="per-calendar-title"><div class="_time-left"><pbutton-white icon="l" click="ptime.headerQuickSelEvent" '+this.orientationMarker+'="'+this.ptool.orientation.left+'"></pbutton-white></div>'+'<div class="per-calendar-text" '+this.selTimeShowRegionMarker+">此处显示当前选择的时间</div>"+'<div class="_time-right"><pbutton-white '+this.orientationMarker+'="'+this.ptool.orientation.right+'" icon="r" click="ptime.headerQuickSelEvent"></pbutton-white></div></div>'}ptime_template.prototype=new persagyElement,ptime_template.prototype.getTemplateStr=function(e,t){var n=e.attr,r=n.panel;switch(t){case this.controlTypes.ptime.types[0]:var i=r.timetype||[],s="";for(var o=0;o<i.length;o++){var u=i[o],a=this.calendarTimeTypeShow[u],f,l,c="";switch(u){case this.calendarDefaultTimeTypeArr[3]:f=r.startyear,l=r.endyear;break;case this.calendarDefaultTimeTypeArr[2]:f=1,l=12;break;case this.calendarDefaultTimeTypeArr[0]:f=0,l=-2,c='click="ptime.formTimeDateHeaderEvent"';break;case this.calendarDefaultTimeTypeArr[4]:f=0,l=23;break;case this.calendarDefaultTimeTypeArr[5]:case this.calendarDefaultTimeTypeArr[6]:f=0,l=59}var h="<pcombobox-time "+this.timeTypeMarker+'="'+u+'" sel="ptime.formTimeSelEvent" isborder="true"><header prefix="'+a+'" '+c+'></header><item start="'+f+'" end="'+l+'"></item></pcombobox-time>';s+='<div class="time-box'+(u===this.calendarDefaultTimeTypeArr[4]?" hour-box":"")+'">'+h+"</div>"}return'<div {{id}}{{disabled}} class="per-time-chart" '+this.ptool.maxDivMarker+">"+s+"</div>";case this.controlTypes.ptime.types[1]:var p=" "+this.orientationCss[r.orientation]+" "+this.alignCss[r.align],d="";if(r.iscommontime){var v='<li class="per-calendar_location_tit">常用时间</li>';for(var o=0;o<r.commontime.length;o++)v+="<li "+this.commonTimeLiMarker+'="'+r.commontime[o]+'" class="per-calendar_location_item">'+this.calendarCommonTimeShow[r.commontime[o]]+"</li>";d='<div class="per-calendar_location"><ul>'+v+"</ul></div>"}var m="";for(var g=0;g<r.timetype.length;g++)m+='<li class="per-calendar_details_item" '+this.contentNavigationLiMarker+'="'+r.timetype[g]+'">'+this.calendarTimeTypeShow[r.timetype[g]]+"</li>";var y='<div class="per-calendar_details_nav"><ul>'+m+"</ul></div>",b=this.createPanel(this.ptool.orientation.left,e),w="",E="";r.double===!0&&(r.doubletoggle===!0&&(w='<div class="per-calendar-arrows"><div class="per-calendar-arrows_but" '+this.panelToggleMarker+">></div></div>"),E=this.createPanel(this.ptool.orientation.right,e));var S='<div class="per-calendar_details_con">'+b+w+E+"</div>",x='<div class="per-calendar_details_footer">'+(r.lock===!0?'<div class="per-calendar-lock"><em class="icon" '+this.footLockMarker+">"+this.lockObj.s+'</em><em style="display:none;">锁定步长为<em class="color countVal" '+this.stepValueMarker+'>0</em><em class="countDateType" '+this.stepUnitMarker+">日</em></em></div>":"")+'<div class="per-calendar-selecttime">已选时间：<em class="countTime" '+this.tempSelTimeMarker+">2016~2017</em></div>"+'<div class="per-calendar-but"><pbutton-blue text="确定" '+this.okBtnMarker+' click="ptime.timeOkEvent"></pbutton-blue></div></div>',T='<div class="per-calendar_details">'+y+S+x+"</div>",N='<div class="per-calendar-con '+p+'" '+this.contentMaxMarker+'><div class="per-calendar-con_wrap">'+d+T+"</div></div>";return"<div "+this.ptool.maxDivMarker+' class="per-time-calendar" {{id}}>'+this.calendarShowRegion+N+"</div>"}},ptime_template.prototype.createPanel=function(e,t){var n=t.attr,r=n.panel;return'<div class="per-calendar_main" '+this.orientationMarker+'="'+e+'" '+this.panelConMaxMarker+">"+'<div class="per-calendar_main_menu">'+'<div class="_menu-icon _menu-prev" '+this.panelConHeaderQuickMarker+'="'+this.ptool.orientation.left+'" '+this.orientationMarker+'="'+e+'"><</div>'+'<div class="_menu-icon _menu-next" '+this.panelConHeaderQuickMarker+'="'+this.ptool.orientation.right+'" '+this.orientationMarker+'="'+e+'">></div>'+'<div class="per-calendar_combobox" '+this.panelConCrossYearComboxMarker+">"+'<pcombobox-time sel="ptime.panelTimeComboxSelEvent" isborder="false" '+this.orientationMarker+'="'+e+'"><header prefix="年"></header><item start="'+r.startyear+'" end="'+r.endyear+'" step="'+this.crossYearStep+'"></item></pcombobox-time></div>'+'<div class="per-calendar_combobox" '+this.panelConYearComboxMarker+">"+'<pcombobox-time sel="ptime.panelTimeComboxSelEvent" isborder="false" '+this.orientationMarker+'="'+e+'"><header prefix="年"></header><item start="'+r.startyear+'" end="'+r.endyear+'"></item></pcombobox-time></div>'+'<div class="per-calendar_combobox" '+this.panelConMonthComboxMarker+">"+'<pcombobox-time sel="ptime.panelTimeComboxSelEvent" isborder="false" '+this.orientationMarker+'="'+e+'"><header prefix="月"></header><item start="1" end="12"></item></pcombobox-time></div>'+"</div>"+'<div class="per-calendar-day">'+'<div class="per-calendar_main_week" '+this.headerWeekMarker+">"+"<ul>"+'<li class="per-calendar_main_item _week-item">一</li>'+'<li class="per-calendar_main_item _week-item">二</li>'+'<li class="per-calendar_main_item _week-item">三</li>'+'<li class="per-calendar_main_item _week-item">四</li>'+'<li class="per-calendar_main_item _week-item">五</li>'+'<li class="per-calendar_main_item _week-item">六</li>'+'<li class="per-calendar_main_item _week-item">日</li>'+"</ul></div>"+'<ul class="per-calendar-wrap_main" '+this.orientationMarker+'="'+e+'" '+this.columnUlMarker+"></ul></div></div>"};function ptime(){this.constructor=arguments.callee}ptime.prototype=new ptime_template,ptime.prototype.init=function(childType,objBind,jqElement){var attr=objBind.attr,event=objBind.event,css=objBind.css,jqPanelTarget=jqElement.find("panel"),timeTypeArr=(jqPanelTarget.attr("timetype")||"").split(""),commonTimeArr=eval(jqPanelTarget.attr("commontime")),_double=jqPanelTarget.attr("double")==="false"?!1:!0;attr.panel={startyear:parseInt(jqPanelTarget.attr("startyear"))||this.defaultMinYear,endyear:parseInt(jqPanelTarget.attr("endyear"))||this.defaultMaxYear,"double":_double,doubletoggle:_double===!1?!1:jqPanelTarget.attr("doubletoggle")==="false"?!1:!0,lock:_double===!1?!1:jqPanelTarget.attr("lock")==="false"?!1:!0,timetype:timeTypeArr.length>0?timeTypeArr:this.calendarDefaultTimeTypeArr,commontime:commonTimeArr||this.calendarCommonTimeArr,iscommontime:jqPanelTarget.attr("iscommontime")==="false"?!1:!0,orientation:jqPanelTarget.attr("orientation")||this.ptool.orientation.down,align:jqPanelTarget.attr("align")||this.ptool.align.left};var templateStr=this.getTemplateStr(objBind,childType);this.renderView(templateStr,this.controlTypes.ptime.name,childType,objBind,jqElement)},ptime.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e);if(n===this.controlTypes.ptime.types[1]){this.createEvent(s.find("["+this.selTimeShowRegionMarker+"]"),this.controlTypes.ptime.name,"click",window[this.ptool.pstaticEventFnName]);var o=s.find("["+this.commonTimeLiMarker+"]");for(var u=0;u<o.length;u++)this.createEvent(o.eq(u),this.controlTypes.ptime.name,"click",window[this.ptool.pstaticEventFnName]);var a=s.find("["+this.contentNavigationLiMarker+"]");for(var u=0;u<a.length;u++){var f=a.eq(u);this.createEvent(f,this.controlTypes.ptime.name,"click",window[this.ptool.pstaticEventFnName])}var l=s.find("["+this.panelConHeaderQuickMarker+"]");for(var u=0;u<l.length;u++)this.createEvent(l.eq(u),this.controlTypes.ptime.name,"click",window[this.ptool.pstaticEventFnName]);var c=s.find("["+this.panelToggleMarker+"]");this.createEvent(c,this.controlTypes.ptime.name,"click",window[this.ptool.pstaticEventFnName]);var h=s.find("["+this.footLockMarker+"]");this.createEvent(h,this.controlTypes.ptime.name,"click",window[this.ptool.pstaticEventFnName]);var p=s.find("["+this.columnUlMarker+"]");for(var u=0;u<p.length;u++)this.createEvent(p.eq(u),this.controlTypes.ptime.name,"click",window[this.ptool.pstaticEventFnName]);var d=a.eq(0).attr(this.contentNavigationLiMarker),v=s.find("["+this.panelConMaxMarker+"]["+this.orientationMarker+'="'+this.ptool.orientation.right+'"]'),m=v.length>0?(new Date).getTime():null;s.psel({timeType:d,startTime:(new Date).getTime(),endTime:m},!1),this.createEvent(s.find("["+this.contentMaxMarker+"]"),this.controlTypes.ptime.name,"click",window[this.ptool.pstaticEventFnName])}else{var g=new Date,y=g.getMonthLength(),b=g.getFullYear(),w=g.getMonth()+1,E=s.find("["+this.timeTypeMarker+'="'+this.calendarDefaultTimeTypeArr[0]+'"]');if(E.length>0){var S=r.panel.timetype.indexOf("y")>-1?b:null,x=r.panel.timetype.indexOf("M")>-1?w:null;this.formTimeDateListReset(S,S,E)}s.psel({y:b,M:w,d:g.getDate(),h:g.getHours(),m:g.getMinutes(),s:g.getSeconds()},!1)}},ptime.prototype.eventHandout=function(e,t){function b(){(new pcombobox).slideUp();var e=o.attr(n.contentNavigationLiMarker);if(d.timeType==e)return;var t=r.find("["+n.panelConMaxMarker+"]");t.removeClass(n.calendarConTimeTypeCss[d.timeType]),t.addClass(n.calendarConTimeTypeCss[e]),n.isLock(r)&&r.find("["+n.footLockMarker+"]")[0].click(),o.addClass(n.contentNavigationSelCss).siblings().removeClass(n.contentNavigationSelCss);var s=r.find("["+n.panelConYearComboxMarker+"]"),u=r.find("["+n.panelConCrossYearComboxMarker+"]"),a=r.find("["+n.panelConMonthComboxMarker+"]"),f=r.find("["+n.headerWeekMarker+"]"),l=r.find("["+n.panelConMaxMarker+"]["+n.orientationMarker+'="'+n.ptool.orientation.right+'"]'),c=l.attr(n.panelConMaxMarker)==="false"?!0:!1,h=l.length>0&&c?d.startTimeMsec:d.startTimeMsec||d.endTimeMsec,p=l.length==0||c?null:d.endTimeMsec||d.startTimeMsec;switch(e){case n.calendarDefaultTimeTypeArr[0]:u.hide(),s.show(),a.show(),f.show();break;case n.calendarDefaultTimeTypeArr[1]:u.hide(),s.show(),a.show(),f.show();var v=new Date(h),m=v.getChineseWeekStartAndEnd();h=m.startTime;if(p){var g=new Date(p),y=g.getChineseWeekStartAndEnd();p=y.endTime}break;case n.calendarDefaultTimeTypeArr[2]:a.hide(),f.hide(),u.hide(),s.show();var v=new Date(h);v.setDate(1),h=v.getTime();if(p){var g=new Date(p),b=g.getMonthLength();g.setDate(b),p=g.getTime()}break;case n.calendarDefaultTimeTypeArr[3]:s.hide(),a.hide(),f.hide(),u.show();var v=new Date(h);v.setMonth(0),v.setDate(1),h=v.getTime();if(p){var g=new Date(p);g.setMonth(11),g.setDate(31),p=g.getTime()}}n.setStoreTime({timeType:e,startTimeMsec:h,endTimeMsec:p,ele:i}),n.setConHeadComboxVal(r),n.calendarItem(r,n.ptool.orientation.left),n.calendarItem(r,n.ptool.orientation.right),n.calendarItemColor(r,n.ptool.orientation.left),n.calendarItemColor(r,n.ptool.orientation.right),n.calendarCountVal(r)}function w(){var e=o.attr(n.orientationMarker),t=o.attr(n.panelConHeaderQuickMarker),i=r.find("["+n.panelConYearComboxMarker+"]").find("["+n.orientationMarker+'="'+e+'"]'),s=r.find("["+n.panelConMonthComboxMarker+"]").find("["+n.orientationMarker+'="'+e+'"]'),u=r.find("["+n.panelConCrossYearComboxMarker+"]").find("["+n.orientationMarker+'="'+e+'"]'),a=i.psel().index,f=s.psel().index,l=u.psel().index,c=p-h;switch(d.timeType){case n.calendarDefaultTimeTypeArr[0]:case n.calendarDefaultTimeTypeArr[1]:switch(t){case n.ptool.orientation.left:--f;if(f<0){f=11,--a;if(a<0)return}break;case n.ptool.orientation.right:++f;if(f>11){f=0,++a;if(a>c)return}}s.psel(f,!1),i.psel(a,!0);break;case n.calendarDefaultTimeTypeArr[2]:switch(t){case n.ptool.orientation.left:--a;if(a<0)return;break;case n.ptool.orientation.right:++a;if(a>c)return}i.psel(a,!0);break;case n.calendarDefaultTimeTypeArr[3]:var v=(p+1-h)/n.crossYearStep-1;switch(t){case n.ptool.orientation.left:--l;if(l<0)return;break;case n.ptool.orientation.right:++l;if(l>v)return}u.psel(l,!0)}}function E(){var e=$(t.target);if(e[0].tagName=="UL")return;var s=e.attr(n.columnLiMarker);s==null&&(e=e.eq(0).parent()),s=parseInt(e.attr(n.columnLiMarker));var u=o.attr(n.orientationMarker),a=r.find("["+n.panelConYearComboxMarker+"]").find("["+n.orientationMarker+'="'+u+'"]'),f=r.find("["+n.panelConMonthComboxMarker+"]").find("["+n.orientationMarker+'="'+u+'"]'),l,c,p;switch(d.timeType){case n.calendarDefaultTimeTypeArr[0]:l=h+a.psel().index,c=f.psel().index+1,p=s;break;case n.calendarDefaultTimeTypeArr[1]:l=h+a.psel().index,c=f.psel().index+1,p=u==n.ptool.orientation.right?s+6:s;break;case n.calendarDefaultTimeTypeArr[2]:l=h+a.psel().index,c=s,p=u==n.ptool.orientation.left?1:(new Date(l,c-1,1)).getMonthLength();break;case n.calendarDefaultTimeTypeArr[3]:l=s,c=u==n.ptool.orientation.left?1:12,p=u==n.ptool.orientation.left?1:31}var v=(new Date(l,c-1,p)).getTime(),m=u==n.ptool.orientation.left?v:d.startTimeMsec,g=u==n.ptool.orientation.right?v:d.endTimeMsec;n.isLock(r)||(u==n.ptool.orientation.left&&m>g&&(g=null),u==n.ptool.orientation.right&&m>g&&(m=null)),n.setStoreTime({timeType:d.timeType,startTimeMsec:m,endTimeMsec:g,ele:i}),n.calendarItemColor(r,n.ptool.orientation.left),n.calendarItemColor(r,n.ptool.orientation.right),n.calendarCountVal(r)}function S(){var e=o.parent(),t=e.next(),s=e.hasClass(n.toggleArrowCss);if(s){t.show(),t.attr(n.panelConMaxMarker,"true"),e.removeClass(n.toggleArrowCss);var u=d.endTimeMsec;u||n.setStoreTime({startTimeMsec:d.startTimeMsec,endTimeMsec:d.startTimeMsec,timeType:d.timeType,ele:i}),n.setConHeadComboxVal(r),n.calendarItem(r,n.ptool.orientation.right),u||n.setStoreTime({startTimeMsec:d.startTimeMsec,endTimeMsec:null,timeType:d.timeType,ele:i}),u&&n.calendarItemColor(r,n.ptool.orientation.right)}else t.hide(),t.attr(n.panelConMaxMarker,"false"),e.addClass(n.toggleArrowCss),d.endTimeMsec=null,n.setStoreTime(d),n.calendarItemColor(r,n.ptool.orientation.left),n.calendarCountVal(r)}function x(){var e=r.find("["+n.panelToggleMarker+"]"),t=o.text(),i=n.lockObj[t];o.text(i);switch(i){case n.lockObj.s:e.pdisable(!1),o.next().hide();break;default:e.pdisable(!0),o.next().show()}}function T(){n.isLock(r)&&r.find("["+n.footLockMarker+"]")[0].click();var e=o.attr(n.commonTimeLiMarker),t,s,u;switch(e){case n.calendarCommonTimeArr[0]:case n.calendarCommonTimeArr[1]:t=n.calendarDefaultTimeTypeArr[0],u=new Date,e===n.calendarCommonTimeArr[1]&&u.setDate(u.getDate()-1);break;case n.calendarCommonTimeArr[2]:case n.calendarCommonTimeArr[3]:t=n.calendarDefaultTimeTypeArr[1],u=new Date,s=u.getChineseWeekStartAndEnd().startTime,u=new Date(s),e===n.calendarCommonTimeArr[3]&&u.setDate(u.getDate()-7);break;case n.calendarCommonTimeArr[4]:case n.calendarCommonTimeArr[5]:t=n.calendarDefaultTimeTypeArr[2],u=new Date,e===n.calendarCommonTimeArr[5]&&u.setMonth(u.getMonth()-1),u.setDate(1);break;case n.calendarCommonTimeArr[6]:case n.calendarCommonTimeArr[7]:t=n.calendarDefaultTimeTypeArr[3],u=new Date,e===n.calendarCommonTimeArr[7]&&u.setFullYear(u.getFullYear()-1),u.setMonth(0),u.setDate(1)}u.setHours(0),u.setMinutes(0),u.setSeconds(0),u.setMilliseconds(0),s=u.getTime(),n.setStoreTime({timeType:"_temp",startTimeMsec:s,endTimeMsec:null,ele:i,isReal:!0});var a=r.find("["+n.panelConMaxMarker+"]["+n.orientationMarker+'="'+n.ptool.orientation.right+'"]'),f=a.attr(n.panelConMaxMarker)==="false"?!0:!1;!f&&a.length>0&&r.find("["+n.panelToggleMarker+"]")[0].click(),r.find("["+n.contentNavigationLiMarker+'="'+t+'"]')[0].click(),r.find("["+n.okBtnMarker+"]")[0].click()}var n=this,r=$(t[n.ptool.eventCurrTargetName]),i=r[0],s=t.type,o=$(t.currentTarget),u=n.ptool.getTypeAndChildTypeFromEle(r),a=r.attr(n.ptool.libraryIdToHtml),f=ptime[a],l=f.attr.panel,c=f.event||{},h=l.startyear,p=l.endyear,d=n.getStoreTime(i).temp;d.ele=i;switch(s){case"click":if(o.attr(n.selTimeShowRegionMarker)!=null){var v=r.find("["+n.contentMaxMarker+"]"),m=v.is(":hidden");n.ptool.hideFormLibrary();if(m){var g=i.psel(),y=r.find("["+n.selTimeShowRegionMarker+"]").text();i.psel({startTime:g.startTime,endTime:y.split(n.showTextSperator)[1]?g.endTime:null,timeType:g.timeType},!1),v.toggle()}}else o.attr(n.contentNavigationLiMarker)!=null?b():o.attr(n.panelConHeaderQuickMarker)!=null?w():o.attr(n.columnUlMarker)!=null?E():o.attr(n.panelToggleMarker)!=null?S():o.attr(n.footLockMarker)!=null?x():o.attr(n.commonTimeLiMarker)!=null&&T()}},ptime.prototype.isLock=function(e){var t=$(e).find("["+this.footLockMarker+"]");return t.text()==this.lockObj.c},ptime.prototype.setConHeadComboxVal=function(e){var t=e.find("["+this.panelConYearComboxMarker+"]"),n=e.find("["+this.panelConCrossYearComboxMarker+"]"),r=e.find("["+this.panelConMonthComboxMarker+"]"),i=t.find("["+this.orientationMarker+'="'+this.ptool.orientation.left+'"]'),s=n.find("["+this.orientationMarker+'="'+this.ptool.orientation.left+'"]'),o=r.find("["+this.orientationMarker+'="'+this.ptool.orientation.left+'"]'),u=t.find("["+this.orientationMarker+'="'+this.ptool.orientation.right+'"]'),a=n.find("["+this.orientationMarker+'="'+this.ptool.orientation.right+'"]'),f=r.find("["+this.orientationMarker+'="'+this.ptool.orientation.right+'"]'),l=this.getStoreTime(e[0]).temp,c=l.startTimeMsec?new Date(l.startTimeMsec):null,h=null;if(l.endTimeMsec){var p=l.timeType===this.calendarDefaultTimeTypeArr[1]?(new Date(l.endTimeMsec)).getChineseWeekStartAndEnd().startTime:l.endTimeMsec;h=new Date(p)}switch(l.timeType){case this.calendarDefaultTimeTypeArr[0]:case this.calendarDefaultTimeTypeArr[1]:if(c){var d=c.getMonth();o.psel(d,!1)}if(h){var v=h.getMonth();f.psel(v,!1)};case this.calendarDefaultTimeTypeArr[2]:if(c){var m=c.getFullYear()+"年";i.psel(m,!1)}if(h){var g=h.getFullYear()+"年";u.psel(g,!1)}break;case this.calendarDefaultTimeTypeArr[3]:var y=e.attr(this.ptool.libraryIdToHtml),b=ptime[y],w=b.attr.panel,E=w.startyear,S=w.endyear,x=this.crossYearStep,T=0,N=0,m=c?c.getFullYear():null,g=h?h.getFullYear():null,C=c?!1:!0,k=h?!1:!0;for(var L=E;L<=S;L++){var A=L;L=L+x-1;var O=L;m&&m>=A&&m<=O&&(C=!0),g&&g>=A&&g<=O&&(k=!0);if(C&&k)break;C||++T,k||++N}m&&s.psel(T,!1),g&&a.psel(N,!1)}},ptime.prototype.calendarItemColor=function(e,t,n){n=n||!1;var r=e.find("["+this.columnUlMarker+"]["+this.orientationMarker+'="'+t+'"]').children(),i,s,o=headerMinute=headerSecond=0,u=this.getYearMonthFromConHead(e),a=this.getStoreTime(e[0]).temp,f=a.startTimeMsec,l=a.endTimeMsec,c=a.timeType,h,p,d,v;switch(t){case this.ptool.orientation.left:i=u.startYear1,s=u.month1,c===this.calendarDefaultTimeTypeArr[1]&&(d=a.startTimeMsec,d&&(v=(new Date(d)).getChineseWeekStartAndEnd().endTime));break;case this.ptool.orientation.right:i=u.startYear2,s=u.month2,c===this.calendarDefaultTimeTypeArr[1]&&(v=a.endTimeMsec,v&&(d=(new Date(v)).getChineseWeekStartAndEnd().startTime))}if(c===this.calendarDefaultTimeTypeArr[1]){var m=(new Date(i,s-1,1)).getChinaWeekArr();f&&(h=(new Date(f)).getChineseWeekStartAndEnd().endTime),l&&(p=(new Date(l)).getChineseWeekStartAndEnd().startTime)}for(var g=0;g<r.length;g++){var y=r.eq(g);y.removeClass(this.columnSelCss+" "+this.columnInCss);if(n||t===this.ptool.orientation.left&&!f||t===this.ptool.orientation.right&&!l)continue;var b=null,w="";switch(c){case this.calendarDefaultTimeTypeArr[0]:b=(new Date(i,s-1,g+1,o,headerMinute,headerSecond,0)).getTime();if(b===f&&t===this.ptool.orientation.left||b===l&&t===this.ptool.orientation.right)w=this.columnSelCss;f&&l&&(t===this.ptool.orientation.left&&b>f&&b<=l&&(w=this.columnInCss),t===this.ptool.orientation.right&&b>=f&&b<l&&(w=this.columnInCss));break;case this.calendarDefaultTimeTypeArr[1]:var E=m[g];t===this.ptool.orientation.left&&f&&E.startTime==f&&E.endTime==h&&(w=this.columnSelCss),t===this.ptool.orientation.right&&l&&E.startTime==p&&E.endTime==l&&(w=this.columnSelCss),f&&l&&(t===this.ptool.orientation.left&&E.startTime>h&&E.endTime<=l&&(w=this.columnInCss),t===this.ptool.orientation.right&&E.startTime>=f&&E.endTime<p&&(w=this.columnInCss));break;case this.calendarDefaultTimeTypeArr[2]:var S=new Date(i,g,1);if(f){var x=new Date(f);t===this.ptool.orientation.left&&S.getFullYear()==x.getFullYear()&&S.getMonth()==x.getMonth()&&(w=this.columnSelCss)}if(l){var T=new Date(l);t===this.ptool.orientation.right&&S.getFullYear()==T.getFullYear()&&S.getMonth()==T.getMonth()&&(w=this.columnSelCss)}f&&l&&(S.setDate(1),S.setHours(0),S.setMinutes(0),S.setSeconds(0),S.setMilliseconds(0),x.setDate(1),x.setHours(0),x.setMinutes(0),x.setSeconds(0),x.setMilliseconds(0),T.setDate(1),T.setHours(0),T.setMinutes(0),T.setSeconds(0),T.setMilliseconds(0),t===this.ptool.orientation.left&&S.getTime()>x.getTime()&&S.getTime()<=T.getTime()&&(w=this.columnInCss),t===this.ptool.orientation.right&&S.getTime()>=x.getTime()&&S.getTime()<T.getTime()&&(w=this.columnInCss));break;case this.calendarDefaultTimeTypeArr[3]:var S=new Date(i+g,1,1);if(f){var x=new Date(f);t===this.ptool.orientation.left&&S.getFullYear()==x.getFullYear()&&(w=this.columnSelCss)}if(l){var T=new Date(l);t===this.ptool.orientation.right&&S.getFullYear()==T.getFullYear()&&(w=this.columnSelCss)}f&&l&&(t===this.ptool.orientation.left&&S.getFullYear()>x.getFullYear()&&S.getFullYear()<=T.getFullYear()&&(w=this.columnInCss),t===this.ptool.orientation.right&&S.getFullYear()>=x.getFullYear()&&S.getFullYear()<T.getFullYear()&&(w=this.columnInCss))}y.addClass(w)}},ptime.prototype.calendarItem=function(e,t){var n=this,r=e.find("["+this.columnUlMarker+"]["+this.orientationMarker+'="'+t+'"]');r.html(n.calendarItemHtml(e,t))},ptime.prototype.calendarItemHtml=function(e,t){var n,r,i,s=this.getYearMonthFromConHead(e);switch(t){case this.ptool.orientation.left:n=s.startYear1,r=s.endYear1,i=s.month1;break;case this.ptool.orientation.right:n=s.startYear2,r=s.endYear2,i=s.month2}var o=this.getStoreTime(e[0]).temp,u=o.timeType;if(u===this.calendarDefaultTimeTypeArr[0]||u===this.calendarDefaultTimeTypeArr[1])var a=new Date(n,i-1,1);var f="";switch(u){case this.calendarDefaultTimeTypeArr[0]:var l=a.getDay(),c=l===0?6:l-1,h=a.getMonthLength();for(var p=1;p<=h;p++){var d=p==1?' style = "margin-left:'+c*30+'px"':"";f+='<li class="per-calendar_main_item" '+this.columnLiMarker+'="'+p+'"'+d+" "+this.orientationMarker+'="'+t+'">'+p+"</li>"}return f;case this.calendarDefaultTimeTypeArr[1]:var v=a.getChinaWeekArr();for(p=0;p<v.length;p++){var m=v[p],g=new Date(m.startTime),y=new Date(m.endTime),b=g.getDate();b=b<10?"0"+b:b;var w=g.format("M.d")+"~"+y.format("M.d");f+='<li class="per-calendar_main_item _week_item" '+this.columnLiMarker+'="'+b+'" '+this.orientationMarker+'="'+t+'"><em>第'+(p+1)+"周</em>"+w+"</li>"}return f;case this.calendarDefaultTimeTypeArr[2]:for(p=1;p<=12;p++)f+='<li class="per-calendar_month_item" '+this.columnLiMarker+'="'+p+'" '+this.orientationMarker+'="'+t+'">'+p+"月</li>";return f;case this.calendarDefaultTimeTypeArr[3]:for(p=n;p<=r;p++)f+='<li class="per-calendar_month_item" '+this.columnLiMarker+'="'+p+'" '+this.orientationMarker+'="'+t+'">'+p+"年</li>";return f}},ptime.prototype.calendarCountVal=function(e){var t=this.getCurrTimeSpacing(e),n=e.find("["+this.stepUnitMarker+"]");n.text(this.calendarTimeTypeShow[t.timeType]);var r=e.find("["+this.stepValueMarker+"]");r.text(t.count);var i=e.find("["+this.tempSelTimeMarker+"]");i.text(t.timeStr)},ptime.prototype.getYearMonthFromConHead=function(e){var t=this.getStoreTime(e[0]).temp,n=e.attr(this.ptool.libraryIdToHtml),r=ptime[n],i=r.attr.panel,s=i.startyear,o=i.endyear,u,a,f,l,c,h,p=e.find("["+this.panelConYearComboxMarker+"]"),d=e.find("["+this.panelConCrossYearComboxMarker+"]"),v=e.find("["+this.panelConMonthComboxMarker+"]"),m=p.find("["+this.orientationMarker+'="'+this.ptool.orientation.left+'"]'),g=d.find("["+this.orientationMarker+'="'+this.ptool.orientation.left+'"]'),y=v.find("["+this.orientationMarker+'="'+this.ptool.orientation.left+'"]'),b=p.find("["+this.orientationMarker+'="'+this.ptool.orientation.right+'"]'),w=d.find("["+this.orientationMarker+'="'+this.ptool.orientation.right+'"]'),E=v.find("["+this.orientationMarker+'="'+this.ptool.orientation.right+'"]');switch(t.timeType){case this.calendarDefaultTimeTypeArr[0]:case this.calendarDefaultTimeTypeArr[1]:f=y.psel().index+1,h=E.psel().index+1;case this.calendarDefaultTimeTypeArr[2]:u=s+m.psel().index,l=s+b.psel().index;break;case this.calendarDefaultTimeTypeArr[3]:var S=this.crossYearStep,x=g.psel().index*S,T=w.psel().index*S;u=s+x,a=Math.min(u+S-1,o),l=s+T,c=Math.min(l+S-1,o)}return{startYear1:u,endYear1:a,month1:f,startYear2:l,endYear2:c,month2:h}},ptime.prototype.getCurrTimeSpacing=function(e){var t=this.getStoreTime(e[0]).temp,n=t.timeType,r=t.startTimeMsec||t.endTimeMsec,i=t.startTimeMsec&&t.endTimeMsec?t.endTimeMsec:0,s=new Date(r),o=new Date(i);if(i){var u=new Date(i);u.setDate(u.getDate()+1);var a=u.getTime()}var f="",l="",c=0;switch(n){case this.calendarDefaultTimeTypeArr[0]:f=s.format("y.M.d"),i?(l=o.format("y.M.d"),c=(a-r)/this.oneDayMillSeconds):c=1;break;case this.calendarDefaultTimeTypeArr[1]:f=s.format("y.M");var h=s.getChineseWeekStartAndEnd().week;f+=this.weekUnit+h;if(i){var p=o.getChineseWeekStartAndEnd();l=(new Date(p.startTime)).format("y.M");var d=p.week;l+=this.weekUnit+d,c=(a-r)/this.oneDayMillSeconds/7}else c=1;break;case this.calendarDefaultTimeTypeArr[2]:f=s.format("y.M");if(i){l=o.format("y.M");var v=(o.getFullYear()-s.getFullYear()-1)*12;c=v+(12-s.getMonth())+(o.getMonth()+1)}else c=1;break;case this.calendarDefaultTimeTypeArr[3]:f=s.format("y"),i?(l=o.format("y"),c=o.getFullYear()-s.getFullYear()+1):c=1}var m=f+(l?this.showTextSperator+l:"");return{timeType:n,count:c,timeStr:m}},ptime.prototype.getStoreTime=function(e){var t=e[this.ptool.libraryToPro]||{},n=t[this.ptool.controlPrivateToProName]||{};return{temp:n.temp||{},real:n.real||{}}},ptime.prototype.setStoreTime=function(e){var t=e.ele,n=$(t),r=t[this.ptool.libraryToPro]||{},i=r[this.ptool.controlPrivateToProName]||{},s=i.temp||{},o=i.real||{};s.timeType=e.timeType||s.timeType;var u=n.find("["+this.footLockMarker+"]"),a=e.isReal||!1,f=this.isLock(t);if(f&&e.startTimeMsec&&e.endTimeMsec){var l=-1;e.startTimeMsec!=s.startTimeMsec?l=1:e.endTimeMsec!=s.endTimeMsec&&(l=0);var c=parseInt(n.find("["+this.stepValueMarker+"]").text())-1,h;switch(s.timeType){case this.calendarDefaultTimeTypeArr[0]:switch(l){case 1:h=new Date(e.startTimeMsec),h.setDate(h.getDate()+c),e.endTimeMsec=h.getTime();break;case 0:h=new Date(e.endTimeMsec),h.setDate(h.getDate()-c),e.startTimeMsec=h.getTime()}break;case this.calendarDefaultTimeTypeArr[1]:switch(l){case 1:h=new Date(e.startTimeMsec),h.setDate(h.getDate()+c*7+6),e.endTimeMsec=h.getTime();break;case 0:h=new Date(e.endTimeMsec),h.setDate(h.getDate()-c*7-6),e.startTimeMsec=h.getTime()}break;case this.calendarDefaultTimeTypeArr[2]:switch(l){case 1:h=new Date(e.startTimeMsec),h.setDate(1),h.setMonth(h.getMonth()+c),h.setDate(h.getMonthLength()),e.endTimeMsec=h.getTime();break;case 0:h=new Date(e.endTimeMsec),h.setDate(1),h.setMonth(h.getMonth()-c),e.startTimeMsec=h.getTime()}break;case this.calendarDefaultTimeTypeArr[3]:switch(l){case 1:h=new Date(e.startTimeMsec),h.setFullYear(h.getFullYear()+c),h.setMonth(11),h.setDate(31),e.endTimeMsec=h.getTime();break;case 0:h=new Date(e.endTimeMsec),h.setFullYear(h.getFullYear()-c),h.setMonth(0),h.setDate(1),e.startTimeMsec=h.getTime()}}}else if(e.startTimeMsec&&e.endTimeMsec&&!f)u.parent().show();else if(!e.startTimeMsec||!e.endTimeMsec)u.parent().hide(),u.next().hide(),u.text(this.lockObj.s);s.startTimeMsec=e.startTimeMsec,s.endTimeMsec=e.endTimeMsec;if(s.startTimeMsec){var p=new Date(s.startTimeMsec);p.setHours(0),p.setMinutes(0),p.setSeconds(0),p.setMilliseconds(0),s.startTimeMsec=p.getTime(),s.startWeekIndex=(new Date(s.startTimeMsec)).getChineseWeekStartAndEnd().week}else s.startWeekIndex="";if(s.endTimeMsec){var p=new Date(s.endTimeMsec);p.setHours(0),p.setMinutes(0),p.setSeconds(0),p.setMilliseconds(0),s.endTimeMsec=p.getTime(),s.endWeekIndex=(new Date(s.endTimeMsec)).getChineseWeekStartAndEnd().week}else s.endWeekIndex="";a&&(o.timeType=s.timeType,o.startTimeMsec=s.startTimeMsec,o.endTimeMsec=s.endTimeMsec,o.startWeekIndex=s.startWeekIndex,o.endWeekIndex=s.endWeekIndex),i.temp=s,i.real=o,r[this.ptool.controlPrivateToProName]=i,t[this.ptool.libraryToPro]=r,this.headerIsQuick(n,s.startTimeMsec,s.endTimeMsec);if(f&&e.startTimeMsec&&e.endTimeMsec){var d=l==1?this.ptool.orientation.right:this.ptool.orientation.left;this.setConHeadComboxVal(n),this.calendarItem(n,d),this.calendarItemColor(n,d)}},ptime.prototype.headerIsQuick=function(e,t,n){return;var r,i,s,o,u,a,f,l,c,h,p,d},ptime.prototype.findMaxJqTarget=function(e){var t=$(e),n=0;while(t.attr(this.ptool.maxDivMarker)==null){t=t.parent(),++n;if(n>50)break}return n>50?!1:t},ptime.prototype.formTimeDateListReset=function(e,t,n){var r=e&&t?(new Date(e,t-1,1)).getMonthLength():!e&&t?t==2?29:(new Date(2018,t-1,1)).getMonthLength():31;(new pcombobox).resetList(n,1,r,this.calendarTimeTypeShow.d)},ptime.prototype.okEdChange=function(e){var t=e[0],n=this.getStoreTime(t).temp;n.isReal=!0,n.ele=t,this.setStoreTime(n);var r=e.find("["+this.tempSelTimeMarker+"]").text(),i=e.find("["+this.selTimeShowRegionMarker+"]");i.text(r),e.find("["+this.contentMaxMarker+"]").is(":hidden")||i[0].click()},ptime.prototype.executeSel=function(e,t){t=t||{};var n=e.attr(this.ptool.libraryIdToHtml),r=ptime[n],i=r.event||{};if(i.sel){var s=e.psel();t[this.ptool.eventOthAttribute]=s,this.executeEventCall(null,t,i.sel)}},ptime.prototype.hideCalendar=function(){var e=this.ptool.libraryTypeToHtml+'="'+this.controlTypes.ptime.name+this.ptool.typeSeparator+this.controlTypes.ptime.types[1]+'"',t=$("["+e+"]");for(var n=0;n<t.length;n++){var r=t.eq(n).find("["+this.contentMaxMarker+"]");r.is(":hidden")||r.hide()}},ptime.prototype.yearMonthSelWithDay=function(e){var t=e.find("["+this.timeTypeMarker+'="'+this.calendarDefaultTimeTypeArr[0]+'"]');if(t.length==0)return;var n=e.find("["+this.timeTypeMarker+'="'+this.calendarDefaultTimeTypeArr[2]+'"]');if(n.length==0)return;var r=e.find("["+this.timeTypeMarker+'="'+this.calendarDefaultTimeTypeArr[3]+'"]');if(r.length==0)return;var i=e.attr(this.ptool.libraryIdToHtml),s=ptime[i],o=s.attr.panel,u=n.psel().index+1,a=o.startyear+r.psel().index,f=(new Date(a,u-1,1)).getMonthLength(),l=t.psel().index+1;this.formTimeDateListReset(a,u,t);var c=Math.min(f,l);t.psel(c-1,!1)},ptime.prototype.psel=function(e,t){var n=arguments[0];e=arguments[1]||{},t=arguments[2]===!1?!1:!0;var r=$(n),i=this.ptool.getTypeAndChildTypeFromEle(n).childType,s=r.attr(this.ptool.libraryIdToHtml),o=ptime[s],u=o.attr.panel;if(arguments.length==1){if(i===this.controlTypes.ptime.types[1]){var a=this.getStoreTime(n).real,f=a.startTimeMsec,l=a.endTimeMsec,c=a.timeType,h;if(!f)switch(c){case this.calendarDefaultTimeTypeArr[0]:f=l;break;case this.calendarDefaultTimeTypeArr[1]:f=(new Date(l)).getChineseWeekStartAndEnd().startTime;break;case this.calendarDefaultTimeTypeArr[2]:h=new Date(l),h.setDate(1),f=h.getTime();break;case this.calendarDefaultTimeTypeArr[3]:h=new Date(l),h.setMonth(0),h.setDate(31),f=h.getTime()}if(!l)switch(c){case this.calendarDefaultTimeTypeArr[0]:l=f;break;case this.calendarDefaultTimeTypeArr[1]:l=(new Date(f)).getChineseWeekStartAndEnd().endTime;break;case this.calendarDefaultTimeTypeArr[2]:h=new Date(f),h.setDate(h.getMonthLength()),l=h.getTime();break;case this.calendarDefaultTimeTypeArr[3]:h=new Date(f),h.setMonth(11),h.setDate(31),l=h.getTime()}h=new Date(l),h.setHours(23),h.setMinutes(59),h.setSeconds(59),h.setMilliseconds(0),l=h.getTime();var p=l+1e3;return{timeType:c,startTime:f,endTime:l,realEndTime:p}}var d="",v=r.find("["+this.timeTypeMarker+"]");for(var m=0;m<v.length;m++){var g=v.eq(m),y=g.attr(this.timeTypeMarker);switch(y){case this.calendarDefaultTimeTypeArr[3]:b(this.dateSperator,u.startyear+g.psel().index);break;case this.calendarDefaultTimeTypeArr[2]:b(this.dateSperator,g.psel().index+1);break;case this.calendarDefaultTimeTypeArr[0]:b(this.dateSperator,g.psel().index+1);break;case this.calendarDefaultTimeTypeArr[4]:b(this.hourSperator,g.psel().index);break;case this.calendarDefaultTimeTypeArr[5]:case this.calendarDefaultTimeTypeArr[6]:b(this.timeSperator,g.psel().index)}}return{startTime:d};function b(e,t){t=t<10?"0"+t:t,d+=(d.length==0?"":e)+t}}if(e.startYear||e.endYear){var w=e.startYear||u.startyear,E=e.endYear||u.endyear,S=new pcombobox;if(i==this.controlTypes.ptime.types[1]){var x=arguments.callee.call(this,n),T=(new Date(w,0,1,0,0,0,0)).getTime(),N=(new Date(E,0,1,0,0,0,0)).getTime(),C=x.startTime,k=x.endTime;x.startTime>=T&&x.startTime<=N||(C=T),x.endTime>=T&&x.endTime<=N||(k=N);var L=r.find("["+this.panelConYearComboxMarker+"]"),A=r.find("["+this.panelConCrossYearComboxMarker+"]");for(var m=0;m<L.length;m++)S.resetList(L.eq(m).children().eq(0),w,E,this.calendarTimeTypeShow.y);for(m=0;m<A.length;m++)S.resetList(A.eq(m).children().eq(0),w,E,"",this.crossYearStep);u.startyear=w,u.endyear=E,(C!=x.startTime||k!=x.endTime)&&arguments.callee.call(this,n,{startTime:C,endTime:k},!1)}else{var O=r.find("["+this.timeTypeMarker+'="'+this.calendarDefaultTimeTypeArr[3]+'"]');if(O[0]){var M=u.startyear+O.psel().index;S.resetList(O,w,E,this.calendarTimeTypeShow.y),u.startyear=w,u.endyear=E,M>=w&&M<E||arguments.callee.call(this,n,{y:w,M:1,d:1,h:0,m:0,s:0},!1)}}}if(i===this.controlTypes.ptime.types[1]){if(e.startTime||e.endTime){var _=e.startTime||e.endTime,D=e.startTime&&e.endTime?e.endTime:null,P=(new Date(_)).getTime(),H=D?(new Date(e.endTime)).getTime():null,B=this.getStoreTime(n),a=B.real,j=B.temp,F=j.timeType,I=e.timeType||a.timeType;this.setStoreTime({startTimeMsec:P,endTimeMsec:H,ele:n});if(I!=F){var q=r.find("["+this.contentNavigationLiMarker+'="'+e.timeType+'"]');q[0].click()}else this.setConHeadComboxVal(r),this.calendarItem(r,this.ptool.orientation.left),this.calendarItem(r,this.ptool.orientation.right),this.calendarItemColor(r,this.ptool.orientation.left),this.calendarItemColor(r,this.ptool.orientation.right),this.calendarCountVal(r);this.okEdChange(r);var R=r.find("["+this.panelToggleMarker+"]");R.length>0&&(!H&&!R.parent().hasClass(this.toggleArrowCss)||H&&R.parent().hasClass(this.toggleArrowCss))&&R[0].click()}}else{for(var m=0;m<this.calendarDefaultTimeTypeArr.length;m++){var y=this.calendarDefaultTimeTypeArr[m],U=r.find("["+this.timeTypeMarker+'="'+y+'"]');if(U.length==0)continue;var z=parseInt(e[y]),W;switch(y){case this.calendarDefaultTimeTypeArr[3]:W=z-u.startyear;break;case this.calendarDefaultTimeTypeArr[2]:W=z-1;break;case this.calendarDefaultTimeTypeArr[0]:W=z-1;break;case this.calendarDefaultTimeTypeArr[4]:case this.calendarDefaultTimeTypeArr[5]:W=z}U.psel(W,!1)}this.yearMonthSelWithDay(r)}t&&this.executeSel(r)},ptime.prototype.plock=function(e,t){var n=arguments[0],r=$(n);e=arguments[1]||{},t=arguments[2],r.psel(e,t);var i=r.find("["+this.footLockMarker+"]");i.length>0&&i[0].click()},ptime.prototype.pslideUp=function(){var e=arguments[0],t=$(e),n=this.ptool.getTypeAndChildTypeFromEle(e).childType;if(n===this.controlTypes.ptime.types[0]){var r=this.controlTypes.pcombobox.name+this.ptool.typeSeparator+this.controlTypes.pcombobox.types[6],i=t.find("["+this.ptool.libraryTypeToHtml+'="'+r+'"]');i.each(function(){this.pslideUp()})}else t.find("["+this.contentMaxMarker+"]").hide()},ptime.headerQuickSelEvent=function(e){var t=new ptime,n=$(e[t.ptool.eventCurrTargetName]),r=t.findMaxJqTarget(n.parent());if(!r)return;var i=r[0],s=n.attr(t.orientationMarker),o=t.getStoreTime(i).real,u=o.startTimeMsec||o.endTimeMsec,a=o.startTimeMsec&&o.endTimeMsec?o.endTimeMsec:null;if(u&&a)var f=t.getCurrTimeSpacing(r).count;switch(o.timeType){case t.calendarDefaultTimeTypeArr[0]:if(!a)u=s==t.ptool.orientation.left?u-t.oneDayMillSeconds:u+t.oneDayMillSeconds;else if(s==t.ptool.orientation.left){var l=new Date(u);l.setDate(l.getDate()-1),a=l.getTime(),l.setDate(l.getDate()-f+1),u=l.getTime()}else{var l=new Date(a);l.setDate(l.getDate()+1),u=l.getTime(),l.setDate(l.getDate()+f-1),a=l.getTime()}break;case t.calendarDefaultTimeTypeArr[1]:if(!a)u=s==t.ptool.orientation.left?u-t.oneDayMillSeconds*7:u+t.oneDayMillSeconds*7;else if(s==t.ptool.orientation.left){var l=new Date(u);l.setDate(l.getDate()-1),a=l.getTime(),l.setDate(l.getDate()-(f*7-1)),u=l.getTime()}else{var l=new Date(a);l.setDate(l.getDate()+1),u=l.getTime(),l.setDate(l.getDate()+(f*7-1)),a=l.getTime()}break;case t.calendarDefaultTimeTypeArr[2]:if(!a){var l=new Date(u);s==t.ptool.orientation.left?l.setMonth(l.getMonth()-1):l.setMonth(l.getMonth()+1),u=l.getTime()}else if(s==t.ptool.orientation.left){var l=new Date(u);l.setDate(1),l.setMonth(l.getMonth()-1),l.setDate(l.getMonthLength()),a=l.getTime(),l.setDate(1),l.setMonth(l.getMonth()-f+1),u=l.getTime()}else{var l=new Date(a);l.setDate(1),l.setMonth(l.getMonth()+1),u=l.getTime(),l.setMonth(l.getMonth()+f-1),l.setDate(l.getMonthLength()),a=l.getTime()}break;case t.calendarDefaultTimeTypeArr[3]:if(!a){var l=new Date(u);s==t.ptool.orientation.left?l.setFullYear(l.getFullYear()-1):l.setFullYear(l.getFullYear()+1),u=l.getTime()}else if(s==t.ptool.orientation.left){var l=new Date(u);l.setDate(1),l.setFullYear(l.getFullYear()-1),l.setMonth(11),l.setDate(31),a=l.getTime(),l.setFullYear(l.getFullYear()-f+1),l.setMonth(0),l.setDate(1),u=l.getTime()}else{var l=new Date(a);l.setDate(1),l.setFullYear(l.getFullYear()+1),l.setMonth(0),u=l.getTime(),l.setFullYear(l.getFullYear()+f-1),l.setMonth(11),l.setDate(31),a=l.getTime()}}var c=r.attr(t.ptool.libraryIdToHtml),h=ptime[c],p=h.attr.panel,d=p.startyear,v=p.endyear;if(u){var m=new Date(u),g=m.getFullYear();if(g<d||g>v)return}if(a){var m=new Date(a);g=m.getFullYear();if(g<d||g>v)return}o.startTimeMsec=u,o.endTimeMsec=a,o.isReal=!1,o.ele=i,t.setStoreTime(o),t.setConHeadComboxVal(r),t.calendarItem(r,t.ptool.orientation.left),t.calendarItem(r,t.ptool.orientation.right),t.calendarItemColor(r,t.ptool.orientation.left),t.calendarItemColor(r,t.ptool.orientation.right),t.calendarCountVal(r),r.find("["+t.okBtnMarker+"]")[0].click()},ptime.panelTimeComboxSelEvent=function(e){var t=new ptime,n=$(e[t.ptool.eventCurrTargetName]),r=n.attr(t.orientationMarker),i=t.findMaxJqTarget(n.parent());if(!i)return;t.calendarItem(i,r),t.calendarItemColor(i,r),t.calendarItemColor(i,r)},ptime.timeOkEvent=function(e){var t=new ptime,n=$(e[t.ptool.eventCurrTargetName]),r=t.findMaxJqTarget(n.parent());if(!r)return;t.okEdChange(r),t.executeSel(r,e)},ptime.formTimeSelEvent=function(e){var t=new ptime,n=$(e[t.ptool.eventCurrTargetName]),r=t.findMaxJqTarget(n.parent());if(!r)return;var i=n.attr(t.timeTypeMarker);(i==t.calendarDefaultTimeTypeArr[2]||i==t.calendarDefaultTimeTypeArr[3])&&t.yearMonthSelWithDay(r),t.executeSel(r,e)},ptime.formTimeDateHeaderEvent=function(e){};function plogin_template(){this.loginMarker="login",this.formMarker="formm",this.normal="<div "+this.ptool.maxDivMarker+"{{id}}"+' class="per-login-normal"><form '+this.formMarker+' method="post" action="/'+pconst.requestType.plogin+'"><div class="per-login-normal-con"><div class="per-login-normal_title ">',this.mian=' </div><div class="per-login-normal_main"><input type="text" placeholder="用户名" name="name"><input type="password" placeholder="密码" name="pass" class="marB20"></div><input type="button" value="登录" '+this.loginMarker+' class="per-login-normal_button"></div><div class="per-login-ICP" ></div></form></div>'}plogin_template.prototype=new persagyElement,plogin_template.prototype.getTemplateStr=function(e,t){var n=e.attr,r=n.panel;return this.mainTitle="<b>"+r.title+"</b><em>"+r.subtitle+" </em>",this[t]+this.mainTitle+this.mian};function plogin(){this.constructor=arguments.callee}plogin.prototype=new plogin_template,plogin.prototype.init=function(e,t,n){var r=t.attr,i=n.find("panel");r.panel={title:i.attr("title"),subtitle:i.attr("subtitle")};var s=this.getTemplateStr(t,e);this.renderView(s,this.controlTypes.plogin.name,e,t,n)},plogin.prototype.rendered=function(e,t,n){var r=t.attr,i=t.event,s=ptool.getJqElement(e),o=s.find("["+this.loginMarker+"]");this.createEvent(o,this.controlTypes.plogin.name,"click",window[this.ptool.pstaticEventFnName])},plogin.prototype.eventHandout=function(e,t){var n=this,r=$(t[this.ptool.eventCurrTargetName]),i=t.type;i=="click"&&r.find("["+this.formMarker+"]").submit()};function pframe_template(){this.constructor=arguments.callee,this.menuLiMarker="menuli",this.navigationMarker="nav",this.subTitleMarker="smt",this.userNameMarkder="unm",this.iframeMarker="ifm",this.navigationActiveMarker="nam",this.userMenuItemMarker="umim",this.systemManagerMarker="smm",this.navigationMenuUlMarker="nmum",this.navigationNoActiveCss="active"}pframe_template.prototype=new persagyElement,pframe_template.prototype.getTemplateStr=function(e,t){var n=e.attr,r=n.header,i=n.item,s="";i.manageText&&(s="<div "+this.systemManagerMarker+' class="per-frame-set"><i {{icon}}></i><em {{text}}>项目管理</em></div>',s=this.joinHtmlToBindByAttrCss(s,{text:i.manageText,icon:i.manageIcon}));var o="";if(i.comboxHtml){o='<div class="per-frame-combobox">'+i.comboxHtml+"</div>";var u=$(o);u.children().attr("isborder","false"),o=u[0].outerHTML}var a=this.createStyleBind("<em></em>",{"background-image":"'url('+model."+i.icon+"+')'"}),f="<li {{click}} "+this.menuLiMarker+"><b>></b>"+a+"<span {{text}}></span></li>";f=this.joinHtmlToBindByAttrCss(f,{text:i.text},null,null,!0),f=this.createForBind(f,i.child,!0),f="<ul "+this.navigationMenuUlMarker+">"+f+"</ul>";var l='<div class="per-frame-nav_temp"><div class="temp-title" {{text}}></div>'+f+"</div>";l=this.joinHtmlToBindByAttrCss(l,{text:i.text},null,null,!0),l=this.createForBind(l,i.datasource);var c='<div class="per-frame-nav-wrap">'+l+"</div>",h='<div class="per-frame-nav blckScroll" '+this.navigationMarker+">"+s+o+c+"</div>",p='<span class="pic"></span>';if(r.uhead){var d="'url('+"+r.uhead+"+')'";p=this.createStyleBind(p,{"background-image":d})}var v="<em {{text}}></em>";v=this.joinHtmlToBindByAttrCss(v,{text:r.uname});var m="<span {{text}}></span>";m=this.joinHtmlToBindByAttrCss(m,{text:r.title});var g="<li "+this.userMenuItemMarker+" {{click}}><em {{text}}></em></li>";g=this.joinHtmlToBindByAttrCss(g,{text:r.text||""},null,null,!0),g=this.createForBind(g,r.dataSource);var y='<div class="per-frame-header"><div '+this.navigationActiveMarker+' class="per-frame-nav_button active"></div>'+'<div class="per-frame-title">'+m+"><span "+this.subTitleMarker+"></span></div>"+'<div class="per-frame-nav_header_right"><div class="per-frame-user">'+"<div "+this.userNameMarkder+' class="user-title"><b>v</b>'+p+v+"</div>"+'<div class="user-con" style="display: none;"><ul>'+g+"</ul></div></div></div></div>",b='<div class="per-frame-main"><iframe '+this.iframeMarker+' src="" style="height: 100%; width: 100%;"></iframe></div>';return"<div "+this.ptool.maxDivMarker+' class="per-frame-normal"{{id}}>'+y+h+b+"</div>"},pframe_template.prototype.joinUrl=function(e){return e+=(e.indexOf("?")>-1?"&":"?")+pconst.pticket+"="+(window[pconst.pticket]||""),e};function pframe(){this.constructor=arguments.callee}pframe.prototype=new pframe_template,pframe.prototype.init=function(e,t,n){var r=t.attr;r.bind=!0;var i=n.find("user"),s=i.find("item");r.header={title:r.title,uhead:i.attr("head"),uname:i.attr("name"),dataSource:s.attr("datasource"),text:s.attr("text"),click:s.attr("click"),unameClick:i.attr("click")};var o=n.children().filter("item"),u=o.find("manage"),a=o.find("combobox");r.item={datasource:o.attr("datasource"),text:o.attr("text"),icon:o.attr("icon"),url:o.attr("url"),click:o.attr("click"),child:o.attr("child"),manageText:u.attr("text"),manageIcon:u.attr("icon")||"'u'",manageClick:u.attr("click"),manageUrl:u.attr("url"),comboxHtml:a.html()};var f=this.getTemplateStr(t,e);this.renderView(f,this.controlTypes.pframe.name,e,t,n)},pframe.prototype.rendered=function(e,t,n){var r=this,i=t.attr,s=t.event,o=ptool.getJqElement(e),u=o.find("["+this.navigationActiveMarker+"]");this.createEvent(u,this.controlTypes.pframe.name,"mouseenter",window[this.ptool.pstaticEventFnName]),this.createEvent(u,this.controlTypes.pframe.name,"mouseleave",window[this.ptool.pstaticEventFnName]);var a=o.find("["+this.navigationMarker+"]");this.createEvent(a,this.controlTypes.pframe.name,"mouseenter",window[this.ptool.pstaticEventFnName]),this.createEvent(a,this.controlTypes.pframe.name,"mouseleave",window[this.ptool.pstaticEventFnName]);var f=o.find("["+this.userNameMarkder+"]");this.createEvent(f,this.controlTypes.pframe.name,"click",window[this.ptool.pstaticEventFnName]);var l=o.find("["+this.systemManagerMarker+"]");this.createEvent(l,this.controlTypes.pframe.name,"click",window[this.ptool.pstaticEventFnName])},pframe.prototype.eventHandout=function(e,t){var n=this,r=$(t[this.ptool.eventCurrTargetName]),i=t.type,s=$(t.currentTarget),o=r.attr(this.ptool.libraryIdToHtml),u=pframe[o],a=u.attr,f=a.header,l=a.item,c=r.find("["+this.navigationActiveMarker+"]"),h=r.find("["+this.navigationMarker+"]");switch(i){case"mouseenter":h.css({left:"0px"}),c.removeClass(this.navigationNoActiveCss);break;case"mouseleave":h.css({left:"-222px"}),c.addClass(this.navigationNoActiveCss);break;case"click":if(s.attr(this.userNameMarkder)!=null)s.next().toggle(),this.executeEventCall(e,t,f.unameClick);else if(s.attr(this.userMenuItemMarker)!=null){var p=s.index();t=this.ptool.appendProToEvent(t,{index:p}),this.executeEventCall(e,t,f.click),r.find("["+this.userNameMarkder+"]")[0].click()}else if(s.attr(this.systemManagerMarker)!=null){var d=s.find("em").text();r.find("["+this.subTitleMarker+"]").text(d),r.find("["+this.iframeMarker+"]").attr("src",l.manageUrl),this.executeEventCall(e,t,l.manageClick)}else if(s.attr(this.menuLiMarker)!=null){var v=e[l.url],m=this.joinUrl(v),g=e[l.text];r.find("["+this.subTitleMarker+"]").text(g),r.find("["+this.menuLiMarker+"]").removeClass(this.navigationNoActiveCss),s.addClass(this.navigationNoActiveCss),this.validSession(e,t,l.click,r.find("["+this.iframeMarker+"]"),m)}}},pframe.prototype.psel=function(e){var t=arguments[0],n=$(t);e=arguments[1]||{};var r=e.groupIndex,i=e.itemIndex,s=e.subTitle||"",o=e.url||"";if(o)n.find("["+this.subTitleMarker+"]").text(s),n.find("["+this.iframeMarker+"]").attr("src",o);else{var u=n.find("["+this.navigationMenuUlMarker+"]").eq(r);u.find("["+this.menuLiMarker+"]")[i].click()}},pframe.prototype.validSession=function(e,t,n,r,i){var s=this;$.ajax({type:"get",url:"/"+pconst.requestType.pvalidse,success:function(){r.attr("src",i),s.executeEventCall(e,t,n)},error:function(e){if(e.status==302)return window.location.href=e.responseText;window.location.href="/"}})};function pgrid_template(){this.constructor=arguments.callee,this.normalFirstColumMarker="nfcm",this.normalFirstColumConUlMarker="nfccum",this.normalRightColumnUlMarker="nrcum",     this.normalRightConUlMarker="nrconum",this.normalRightConScrollRegionMarker="nrcsrm",this.normalLeftRegionConMarker="nlrcm",this.multiPagingMarker="mpm",this.multiGridLeftComboxMarker="mplcm",this.multiGridConCheckboxMarker="mgccbm",this.multiGridConUlMarker="mgcum",this.multiGridConMarker="mgcm",this.multiGridSortHeadMarker="mgshm",this.multiGridSortIconMarker="mgsim",this.multiGridOutHeaderMarker="mgohm",this.multiGridColumnHeaderMarker="mgchm",this.conColumnMarker="ccm",this.conLineMarker="clm",this.nodataMarker="ndm",this.multiGridOneColumnHeaderMarker="mgochm",this.quickSelLinePrefix="已选：",this.quickSelLineSperator="/",this.pageSize=20,this.normalFirstConLi="<li {{mouseover}}"+this.conLineMarker+" "+this.normalFirstColumMarker,this.normalFirstConLi2="><em {{text}}></em></li>",this.normalRightConColumn="<div {{mouseover}}"+this.conColumnMarker,this.normalRightConColumn3="><em {{text}}></em></div>",this.normalRightConColumn1="<li "+this.conLineMarker+" {{click}}>",this.normalRightConColumn2="</li>",this.sortIconCss="op",this.multiGridPadding={max:70,min:20}}pgrid_template.prototype=new persagyElement,pgrid_template.prototype.getTemplateStr=function(e,t){function z(){return"<div "+n.nodataMarker+' class="per-grid-nodata">'+i+"</div>"}function W(e,t){if(!e)return"";var r=$("<div "+n.multiPagingMarker+'="'+t+'" class="per-grid-paging">'+e+"</div>"),i=r.children().eq(0);return i.attr("number","0"),i.attr("sel","pgrid.pageSelEvent"),r[0].outerHTML}var n=this,r=t.attr,i=r.noticeHtml||'<pnotice-nodata text="暂无数据" subtitle="请检查网络是否通畅"></pnotice-nodata>',s=r.panel,o=r.columns;switch(e){case this.controlTypes.pgrid.types[0]:var u="",a=o[0];if(s.lock==1){var f=this.normalFirstConLi+(s.columnclick?" {{click}}":"")+this.normalFirstConLi2;f=this.joinHtmlToBindByAttrCss(f,{text:a.source},{},{},!0),f=this.createForBind(f,s.datasource),u='<div class="per-grid-normal_l"><div class="per-grid-normal_column">'+a.name+"</div><div "+this.normalLeftRegionConMarker+'  class="per-grid-normal_ul"><ul '+this.normalFirstColumConUlMarker+' style="top: 0px;">'+f+"</ul></div></div>"}var l="",c="",h="",p="",d=s.lock?1:0;for(d;d<o.length;d++){var v=o[d];c+="<li><em>"+v.name+"</em></li>";var m=this.normalRightConColumn+(s.columnclick?" {{click}}":"")+this.normalRightConColumn3;p+=this.joinHtmlToBindByAttrCss(m,{text:v.source},{},{},!0)}l='<div class="per-grid-normal_title"><ul '+this.normalRightColumnUlMarker+' style="left: 0px;">'+c+"</ul></div>",h=this.normalRightConColumn1+p+this.normalRightConColumn2,h=this.joinHtmlToBindByAttrCss(h,{},{},{},!0),h=this.createForBind(h,s.datasource);var g="<div "+this.normalRightConScrollRegionMarker+' class="per-grid-normal_con scroll_big"><div class="per-grid-nomal-wrap"><ul '+this.normalRightConUlMarker+">"+h+"</ul></div></div>",y='<div class="per-grid-normal_r">'+l+g+"</div>",b=z(),w=W(r.pageHtml,this.controlTypes.pgrid.types[0]);return"<div "+this.ptool.maxDivMarker+' class="per-grid-normal" {{id}}><div class="per-grid-normal_main">'+u+y+b+w+"</div></div>";default:var E="";if(s.checkbox||r.customButtonHtml){var S="";s.checkbox&&(S='<div class="per-grid-dynamic_header_left"><pcombobox-menuminor '+this.multiGridLeftComboxMarker+' sel="pgrid.quickSelLineEvent">'+'<header placeholder="'+this.quickSelLinePrefix+"0"+this.quickSelLineSperator+'0"></header>'+'<item datasource="pgrid.multiGridTopLeftComboxSource" text="name"></item></pcombobox-menuminor></div>');var x="";r.customButtonHtml&&(x='<div class="per-grid-dynamic_header_right">'+r.customButtonHtml+"</div>"),E="<div "+this.multiGridOutHeaderMarker+' class="per-grid-dynamic_header">'+S+x+"</div>"}var T="",N="",C=[];if(s.templateid){var k="<div>"+document.getElementById(s.templateid).innerHTML+"</div>";C=$(k).children()}var L=s.checkbox?'<div class="per-grid-dynamic_item _grid-cheackbox"></div>':"";for(var d=0;d<o.length;d++){var A=o[d],O=A.width?' style="width:'+A.width+';"':"",M="<b"+(A.bind=="true"?"{{text}}":"")+">"+(A.bind=="true"?"":A.name)+"</b>",_=M+'<span class="icon"><em '+this.multiGridSortIconMarker+' class="'+(A.defaultsort==this.ptool.sortType.asc?this.sortIconCss:"")+'">t</em><em '+this.multiGridSortIconMarker+' class="'+(A.defaultsort==this.ptool.sortType.desc?this.sortIconCss:"")+'">b</em></span>',D=M,P="<div"+(A.bind=="true"&&A.visible?"{{visible}}":"")+" "+this.multiGridOneColumnHeaderMarker+" "+O+' class="per-grid-dynamic_item'+(A.sort?" _grid-sort":"")+'"'+(A.sort?" "+this.multiGridSortHeadMarker+'="'+A.defaultsort+'"':"")+">"+(A.sort?_:D)+"</div>";A.bind=="true"&&(P=this.joinHtmlToBindByAttrCss(P,{text:A.name,visible:A.visible})),L+=P;var H=C[d]?C[d].outerHTML:"<em {{text}}></em>",B="<div{{visible}}></div>";B=this.joinHtmlToBindByAttrCss(B,{visible:A.visible}),B=B.substring(0,B.indexOf(">")),B+=" "+this.conColumnMarker+O+' class="per-grid-dynamic_item" {{mouseover}}'+(s.columnclick?" {{click}}":"")+">"+H+"</div>",B=this.joinHtmlToBindByAttrCss(B,{text:A.source,visible:A.visible},{},{},!0),N+=B}s.operation&&(L+='<div class="per-grid-dynamic_item  _grid-dynamic_item-icon"></div>',N+='<div class="per-grid-dynamic_item _grid-dynamic_item-icon">'+(C[C.length-1].outerHTML||"")+"</div>"),L="<div "+this.multiGridColumnHeaderMarker+' class="per-grid-dynamic_title"><ul><li class="per-grid-dynamic_li _dynamic-title-height">'+L+"</li></ul></div>",s.checkbox&&(N='<div class="per-grid-dynamic_item _grid-cheackbox"><pswitch-checkbox '+this.multiGridConCheckboxMarker+' change="pgrid.checkboxSelEvent" bind="true">'+"</pswitch-checkbox></div>"+N),N="<li "+this.conLineMarker+' class="per-grid-dynamic_li" {{click}}>'+N+"</li>",N=this.joinHtmlToBindByAttrCss(N,{},{},{},!0),N=this.createForBind(N,s.datasource);var j="<ul "+this.multiGridConUlMarker+">"+N+"</ul>",F=$(j);F.prender(),j=F[0].outerHTML;var I=this.ptool.createDynamicTemplate(j),q="<div "+this.multiGridConMarker+' class="per-grid-dynamic_con"><pscroll-small templateid="'+I+'"></pscroll-small></div>',R=W(r.pageHtml,this.controlTypes.pgrid.types[1]),b=z(),U=R?this.multiGridPadding.max:this.multiGridPadding.min;return T='<div class="per-grid-dynamic_wrap" style="padding-bottom:'+U+'px">'+L+q+R+b+"</div>","<div {{id}} "+this.ptool.maxDivMarker+'  class="per-grid-dynamic">'+E+T+"</div>"}};function pgrid(){this.constructor=arguments.callee}pgrid.prototype=new pgrid_template,pgrid.multiGridTopLeftComboxSource=[{name:"选择本页",code:"curr"},{name:"本页反选",code:"uncurr"},{name:"全不选",code:"unall"}],pgrid.prototype.init=function(e,t,n){var r=t.attr,i=t.event,s=t.css;r.bind=!0;var o=n.find("panel"),u=o.attr("checkbox")=="true"?!0:!1,a=o.attr("operation")=="true"?!0:!1,f=o.attr("lock")=="true"?!0:!1;r.panel={datasource:o.attr("datasource"),checkbox:u,templateid:o.attr("templateid"),lineclick:o.attr("lineclick"),columnclick:o.attr("columnclick"),sel:o.attr("sel"),change:o.attr("change"),lock:f,operation:a,pagesize:parseInt(o.attr("pagesize"))||this.pageSize,sortevent:o.attr("sortevent")};var l=n.find("header").find("column"),c=[];for(var h=0;h<l.length;h++){var p=l.eq(h),d=p.attr("sort")=="true"?!0:!1,v=p.attr("defaultsort")||"";c.push({name:p.attr("name"),source:p.attr("source"),sort:d,defaultsort:v,width:p.attr("width"),visible:p.attr("visible"),bind:p.attr("bind")})}r.columns=c,r.customButtonHtml=n.find("button").html(),r.pageHtml=n.find("page").html(),r.noticeHtml=n.find("notice").html();var m=this.getTemplateStr(e,t);this.renderView(m,this.controlTypes.pgrid.name,e,t,n)},pgrid.prototype.rendered=function(e,t){if(!t)return;var n=t.attr,r=t.event,i=ptool.getJqElement(e),s=this.ptool.getTypeAndChildTypeFromEle(i);switch(s.childType){case this.controlTypes.pgrid.types[0]:this.createEvent(i.find("["+this.normalRightConScrollRegionMarker+"]"),this.controlTypes.pgrid.name,"scroll",window[this.ptool.pstaticEventFnName]);var o=i.find("["+this.normalRightConUlMarker+"]");break;case this.controlTypes.pgrid.types[1]:var u=i.find("["+this.multiGridSortHeadMarker+"]");for(var a=0;a<u.length;a++)this.createEvent(u[a],this.controlTypes.pgrid.name,"click",window[this.ptool.pstaticEventFnName]);var f=i.find("["+this.multiGridConUlMarker+"]"),l=i.attr(this.ptool.libraryIdToHtml),c=this.controlTypes.pgrid.name+this.ptool.typeSeparator+this.controlTypes.pgrid.types[1];f.attr(this.ptool.libraryTypeToHtml,c),f.attr(this.ptool.libraryIdToHtml,l)}},pgrid.prototype.eventHandout=function(e,t){var n=t.currentTarget,r=$(n),i=$(t[this.ptool.eventCurrTargetName]);i.attr(this.ptool.maxDivMarker)==null&&(i=this.findMaxJqTarget(i.parent(),this.controlTypes.pgrid.types[1]),i||(i=this.findMaxJqTarget(i.parent(),this.controlTypes.pgrid.types[0])));var s=i.attr(this.ptool.libraryIdToHtml),o=pgrid[s],u=o.attr,a=this.ptool.getTypeAndChildTypeFromEle(i),f=u.panel,l=u.columns,c=t.type;switch(c){case"scroll":var h=n.scrollTop,p=n.scrollLeft;i.find("["+this.normalRightColumnUlMarker+"]").css({left:"-"+p+"px"}),i.find("["+this.normalFirstColumConUlMarker+"]").css({top:"-"+h+"px"});break;case"click":if(r.attr(this.multiGridSortHeadMarker)!=null){var d=r.index();f.checkbox&&--d;var v=r.attr(this.multiGridSortHeadMarker),m=v==this.ptool.sortType.asc?this.ptool.sortType.desc:this.ptool.sortType.asc;t=this.ptool.appendProToEvent(t,{columnIndex:d,sortType:m}),this.executeEventCall(null,t,f.sortevent);var g=r.find("["+this.multiGridSortIconMarker+"]");g.eq(m==this.ptool.sortType.desc?0:1).removeClass(this.sortIconCss),g.eq(m==this.ptool.sortType.desc?1:0).addClass(this.sortIconCss),r.attr(this.multiGridSortHeadMarker,m);return}if(r.attr(this.conColumnMarker)!=null){var d=r.index();f.checkbox&&--d,f.lock&&++d;var y=l[d],b=r.parent("["+this.conLineMarker+"]:first"),w=b.index();t=this.ptool.appendProToEvent(t,{columnIndex:d,lineIndex:w}),this.executeEventCall(e,t,f.columnclick);return}if(r.attr(this.conLineMarker)!=null){var w=r.index(),E=f.lineclick;t=this.ptool.appendProToEvent(t,{lineIndex:w}),this.executeEventCall(e,t,E);return}break;case"mouseover":(r.attr(this.conColumnMarker)!=null||r.attr(this.normalFirstColumMarker)!=null)&&r.children().eq(0).elementShowTitle(),a.childType===this.controlTypes.pgrid.types[1]&&i.find("["+this.multiGridConMarker+"]").children().eq(0).mouseenter();break;case"DOMSubtreeModified":this.isShowNoData(a.childType,i)}},pgrid.prototype.isShowNoData=function(e,t,n){var r=t.find("["+this.nodataMarker+"]"),i=t.find("["+this.multiPagingMarker+"]");switch(e){case this.controlTypes.pgrid.types[0]:var s=t.find("["+this.normalRightConUlMarker+"]"),o=t.find("["+this.normalLeftRegionConMarker+"]"),u=t.find("["+this.normalRightConScrollRegionMarker+"]");n>0?(r.hide(),o.show(),u.show(),i.show()):(o.hide(),u.hide(),i.hide(),r.show());break;case this.controlTypes.pgrid.types[1]:var a=t.find("["+this.multiGridConMarker+"]"),f=t.find("["+this.multiGridConUlMarker+"]");n>0?(r.hide(),a.show(),i.show()):(a.hide(),i.hide(),r.show())}},pgrid.prototype.pcount=function(e){var t=arguments[0],n=$(t),r=this.ptool.getTypeAndChildTypeFromEle(n),i=n.attr(this.ptool.libraryIdToHtml),s=pgrid[i];if(arguments.length==1)return s.pageCount||0;e=parseInt(arguments[1])||0;var o=s.attr,u=o.panel.pagesize,a=Math.ceil(Math.division(e,u));s.pageCount=a,s.count=e,s.selLeftIndex==0&&(s.selCount=e),pgrid[i]=s,n.find("["+this.multiPagingMarker+"]").pcount(a),this.setLeftComboxHeaderText(n),this.isShowNoData(r.childType,n,e);var f=n.find("["+this.multiGridConMarker+"]");f.psetScroll(0),f.psetScroll(0,this.ptool.arrangeType.horizontal)},pgrid.prototype.precover=function(e){var t=arguments[0],n=$(t),r=this.ptool.getTypeAndChildTypeFromEle(n),i=n.find("["+this.multiPagingMarker+"]");i.psel(1,!1);if(r.childType===this.controlTypes.pgrid.types[0])return;var s=n.attr(this.ptool.libraryIdToHtml),o=pgrid[s],u=o.attr;o.pageCount=0,o.count=0,o.selLeftIndex=-1,o.selCount=0,o.pageIndex=1,pgrid[s]=o,i.pcount(1),this.setLeftComboxHeaderText(n),e=arguments[1]===!1?!1:!0;if(!e)return;var a=n.find("["+this.multiGridOneColumnHeaderMarker+"]"),f=u.columns||[];for(var l=0;l<f.length;l++){var c=f[l];if(c.sort===!0){var h=a.eq(l),p=c.defaultsort,d=h.find("["+this.multiGridSortIconMarker+"]");d.removeClass(this.sortIconCss),h.attr(this.multiGridSortHeadMarker,p||"");if(!p)continue;d.eq(p==this.ptool.sortType.desc?1:0).addClass(this.sortIconCss)}}},pgrid.prototype.findMaxJqTarget=function(e,t){var n=$(e),r=this.controlTypes.pgrid.name+this.ptool.typeSeparator+t,i=0;while(n.attr(this.ptool.libraryTypeToHtml)!==r||n.attr(this.ptool.maxDivMarker)==null){n=n.parent(),++i;if(i>50)break}return i>50?!1:n},pgrid.prototype.setLeftComboxHeaderText=function(e){var t=e.attr(this.ptool.libraryIdToHtml),n=pgrid[t],r=this.quickSelLinePrefix+(n.selCount||0)+this.quickSelLineSperator+(n.count||0);(new pcombobox).setHeaderText(e.find("["+this.multiGridLeftComboxMarker+"]"),r)},pgrid.prototype.psel=function(e,t){var n=arguments[0],r=$(n),i=r.attr(this.ptool.libraryIdToHtml),s=pgrid[i];if(arguments.length==1)return s.pageIndex||1;e=parseInt(arguments[1]),t=arguments[2]===!1?!1:!0;if(!e)return s.pageIndex||1;r.find("["+this.multiPagingMarker+"]").psel(e,t),s.pageIndex=e,pgrid[i]=s},pgrid.prototype.psetHeaderSort=function(e){var t=arguments[0];e=arguments[1]||null;var n=$(t),r=this.ptool.getTypeAndChildTypeFromEle(n);if(r.childType===this.controlTypes.pgrid.types[0]||!e)return;e=e instanceof Array?e:[e];var i=n.find("["+this.multiGridOneColumnHeaderMarker+"]");for(var s=0;s<e.length;s++){var o=e[s],u=i.eq(o.index),a=o.sortType;u.attr(this.multiGridSortHeadMarker,a||"");var f=u.find("["+this.multiGridSortIconMarker+"]");f.removeClass(this.sortIconCss);if(!a)continue;f.eq(a==this.ptool.sortType.desc?1:0).addClass(this.sortIconCss)}},pgrid.quickSelLineEvent=function(e){var t=new pgrid,n=e[t.ptool.eventOthAttribute].index,r=pgrid.multiGridTopLeftComboxSource[n],i=$(e[t.ptool.eventCurrTargetName]),s=t.findMaxJqTarget(i.parent(),t.controlTypes.pgrid.types[1]),o=s.find("["+t.multiGridConCheckboxMarker+"]"),u=s.attr(t.ptool.libraryIdToHtml),a=pgrid[u],f=0,l=!0;switch(r.code){case"all":a.selLeftIndex=0,f=a.count,l=!0;break;case"curr":a.selLeftIndex=1,l=!0;break;case"uncurr":a.selLeftIndex=2,l=-1;break;case"unall":a.selLeftIndex=3,f=0,l=!1}if(r.code=="curr"||r.code=="uncurr"){var c=0;for(var h=0;h<o.length;h++)o.eq(h).psel()&&++c;f=c}a.selCount=f,a.nowSelLeft=!0,a.selLeftCode=r.code,pgrid[u]=a,(r.code=="all"||r.code=="unall")&&t.setLeftComboxHeaderText(s);for(var h=0;h<o.length;h++){var p=o.eq(h),d=p.psel(),v=r.code!="uncurr"?l:!d;if(v===d)continue;p.psel(v)}a.nowSelLeft=!1},pgrid.checkboxSelEvent=function(e,t){var n=new pgrid,r=t[n.ptool.eventOthAttribute].state,i=$(t[n.ptool.eventCurrTargetName]),s=n.findMaxJqTarget(i.parent(),n.controlTypes.pgrid.types[1]),o=s.attr(n.ptool.libraryIdToHtml),u=pgrid[o];u.nowSelLeft!==!0&&(u.selLeftCode=-1);if(u.selLeftCode!="all"&&u.selLeftCode!="unall"){var a=u.selCount||0;r?++a:--a,u.selCount=a,pgrid[o]=u,n.setLeftComboxHeaderText(s)}t=n.ptool.appendProToEvent(t,{state:r}),n.executeEventCall(e,t,u.attr.panel.change)},pgrid.pageSelEvent=function(e){var t=new pgrid,n=e[t.ptool.eventOthAttribute].pageIndex,r=$(e[t.ptool.eventCurrTargetName]),i=r.parent(),s=i.attr(t.multiPagingMarker),o=t.findMaxJqTarget(i,s),u=o.attr(t.ptool.libraryIdToHtml),a=pgrid[u];s===t.controlTypes.pgrid.types[1]&&(a.pageIndex=n,a.selLeftIndex!=0&&(a.selCount=0,pgrid[u]=a,t.setLeftComboxHeaderText(o))),e=t.ptool.appendProToEvent(e,{pageIndex:n}),t.executeEventCall(null,e,a.attr.panel.sel)} ;})();