/*需加一个ajax请求，用于获取commonlib地址
*基于highcharts 6.0，依赖的commonLib中的js有：Date.js、Math.js、ptool.js、pcolor.js
*范围面积均线图、气泡图需引用highcharts-more.js，热力图需引入highcharts-heatmap.js
*可点击的图形，请在plotOptions的point的events里加入click事件，或者在series的point的events里加入click事件
*x轴的格式化规则：
*   x轴的跨度最大为1天，x轴格式化为时分，时间类型为1
*   x轴的跨度最大为一周，x轴格式化为月日时分，时间类型为2
*   x轴的跨度最大为一个月，x轴格式化为月日，时间类型为3
*   x轴的最大跨度为12个月，x轴格式化为年月，时间类型为4
*   x轴的最小跨度为12个月以上，x轴格式化为年，时间类型为5
*   非时间类型时，为6
*当使用pchart的自带tips时，并且tips里显示单位时，请在series.data里的每一项中加入unit属性
*对于散点图和气泡图，使用pchart的自带tips时，请在series.data里的每项中，加入属性：xname(x轴的名称)、yname(y轴的名称)、xunit(x轴的单位)、yunit(y轴的单位)
*混合多图表，请自行在series里加入type属性
*x轴带分区时，请调用setXPlotBands方法
*legend boolean或object 可包括属性如下：{align:'left right center bottom(只对饼、环生效) 其中饼图、环形图的只有：right、bottom两种',padding:'10px 指图例之间的间距',color:'字体颜色',fontSize:'',fontFamily:''}；
*       当使用pchart默认的图例样式时，legend请赋值为true
*/
var pchart = (function () {
    var pchart_logicFn = function () {
        this.appendChartProName = '_pchartPros';    //实例化highcharts之后，会把highcharts的此属性置为空对象。其内可放任意的属性
        this.extendChartTypeName = 'chartType';     //在_pchartPros放置chartType属性，代表chart的扩展类型
        this.extendTimeTypeName = 'timeType';       //在_pchartPros放置timeType属性，代表chart的时间类型
        this.extendChartIdPro = 'id';               //在_pchartPros放置id属性，用以唯一标识一个chart
        this.extendChartRelv = 'relvCharts';        //在_pchartPros放置relvCharts属性，用以存放和当前chart相关的chart
        this.extendLegend = 'legend';               //在_pchartPros放置legend属性，用以存放图例对应的元素ID以及自定义属性

        //分区的默认样式
        this.xPlotBandDefaultStyle = {
            labelAlign: 'left',
            labelVerticalAlign: 'top',
            style: {
                fontFamily: '微软雅黑',
                color: '#333333',
                fontSize: '12px'
            },
            xPlotBandBackColor: '#EFF3F4'
        };
        //辅助线的默认样式
        this.yPlotLineDefaultStyle = {
            color: '#7A94AD',
            width: 1,
            dashStyle: 'Dash',
            label: {
                align: 'right',
                x: -2,
                y: -6,
                style: {
                    fontFamily: 'Arial-BoldMT',
                    color: '#7A94AD',
                    fontSize: '12px'
                }
            }
        };
        //chart的背景颜色
        this.chartBackColor = 'rgba(0,0,0,0)';
        //默认的title样式
        this.titleDefaultStyle = {
            text: '',
            align: 'center',
            style: {
                color: '#333333',
                fontSize: '16px',
                fontFamily: '微软雅黑'
            }
        };

        //X轴的轴线样式
        this.xAxisLineStyle = {
            lineColor: '#EEEEEE',
            lineDashStyle: 'Solid',
            lineWidth: 2,
            opposite: true
        };

        //X轴的默认样式
        this.xAxisDefaultStyle = {
            visible: true,
            startOnTick: false,
            endOnTick: false,/*结束于标线；是否强制轴线在标线处结束*/
            tickmarkPlacement: 'off',   //刻度线居中
            tickWidth: 0,       //刻度宽
            lineColor: this.xAxisLineStyle.lineColor,
            lineDashStyle: this.xAxisLineStyle.lineDashStyle,
            lineWidth: this.xAxisLineStyle.lineWidth,
            labels: {
                style: {
                    fontFamily: 'ArialMT',
                    color: '#6D6D6D',
                    fontSize: '12px'
                },
                formatter: ptool.formatChartX
            }
        };

        //柱图悬浮时的背景颜色
        this.normalColumnHoverColor = '#F8F8F8';

        //可点击的柱图悬浮时的背景颜色
        this.clickColumnHoverColor = '#3DA8D3';

        //十字准星竖线的颜色
        this.shiziCrosshairColor = '#7A94AD';

        //Y轴默认样式
        this.yAxisDefaultStyle = [{
            visible: true,
            title: {
                text: '',
                align: 'high',
                offset: 0,
                rotation: 0,
                y: -15,
                x: 25,
                style: {
                    fontFamily: '微软雅黑',
                    color: '#6D6D6D',
                    fontSize: '12px'
                }
            },
            labels: {
                style: {
                    fontFamily: 'ArialMT',
                    color: '#6D6D6D',
                    fontSize: '12px'
                }
            },
            gridLineColor: '#EEEEEE',/*x轴网格线的颜色*/
            gridLineDashStyle: 'Dash',/*x轴网格线的样式*/
            gridLineWidth: 1,/*x轴网格线*/
            endOnTick: true
        }, {
            title: {
                text: '',
                align: 'high',
                offset: 0,
                rotation: 0,
                y: -15,
                x: -10,
                style: {
                    fontFamily: '微软雅黑',
                    color: '#6D6D6D',
                    fontSize: '12px'
                }
            },
            gridLineWidth: 0,
            labels: {
                style: {
                    fontFamily: 'ArialMT',
                    color: '#6D6D6D',
                    fontSize: '12px'
                }
            },
            opposite: true
        }];

        //默认提示样式
        this.tipDefaultStyle = {
            enabled: true,
            animation: true,
            borderColor: null,
            borderWidth: 0,
            shadow: false,
            backgroundColor: null,
            useHTML: true,
            shared: true,

            formatter: ptool.formatChartTip(),
            style: {
                zIndex: 6
            }
        };

        //plotOptions.series的默认样式
        this.plotOptionsSeriesStyle = {
            series: {
                animation: false,
                events: {
                    //addSeries和初始化带有数据的chart时会执行此回调
                    //afterAnimate: function () { }
                },
                turboThreshold: 999999999
            }
        };

        //chart的默认marginTop
        this.chartMarginTop = 30;

        //线、面积图的点默认marker样式
        this.circleMarkerDefaultStyle = {
            radius: 4,
            symbol: 'circle',
            lineColor: 'white',
            lineWidth: 2
        };

        //线、面积图的点hover时的样式
        this.circleMarkerHoverDefaultStyle = {
            lineColor: 'white',
            lineWidth: 2,
            radius: 5
        };

        //线图上的点值默认样式样式
        this.pointValueDefaultStyle = {
            color: '#02A9D1', fontSize: '12px', fontFamily: 'ArialMT'
        };

        //默认的颜色轴样式，一般用于热力图
        this.colorAxisDefaultStyle = {
            minColor: '#fdd38c',
            maxColor: '#df4569'
        };

        //拓展的图表类型
        this.chartType = {
            //直方图，pchart内的直方图非highcharts内的直方图，仅仅只是一列的柱图
            histogram: 'histogram',
            //渐变面积图
            gradientArea: 'gradientArea',
            //环形图
            doughnutPie: 'doughnutPie',
            //环形填充图
            doughnutFillPie: 'doughnutFillPie',
            //年热力图
            yearheatmap: 'yearheatmap',
            //周热力图
            weekheatmap: 'weekheatmap'
        };

        //填充环图的灰色色值
        this.fillPieGrayVal = '#E4EDF0';
    };
    pchart_logicFn.prototype = {
        /*初始化chart-------------------------------------缺少，创建存放图例容器的代码
        *objParam:{chart:{},title:{},subtitle:{},xAxis:[],yAxis:[],tooltip:{},plotOptions:{}}
        */
        initChart: function (objParam) {
            var legendHtmlId;
            var chartEleId = objParam.chart.renderTo;
            var legendObj;
            if (objParam.chart.type == 'pie' || (objParam.legend && this.isCreateLegend(objParam.chart.type))) {
                var htmlObj = objParam.chart.type == 'pie' ? this.constructorEleForPie(objParam) : this.createLegendHtml(objParam);
                legendHtmlId = htmlObj.legendHtmlId;
                chartEleId = htmlObj.chartHtmlId;
                legendObj = objParam.legend === true ? {} : objParam.legend;
                if (objParam.legend)
                    legendObj.id = legendHtmlId;
            }

            objParam.chart.renderTo = chartEleId;

            var chart = new Highcharts.Chart({
                chart: objParam.chart,
                /*导出模块*/
                exporting: {
                    enabled: false
                },
                /*版权的一些事*/
                credits: {
                    enabled: false
                },
                legend: {
                    enabled: false
                },
                colorAxis: objParam.colorAxis,
                title: objParam.title,
                subtitle: objParam.subtitle,
                colors: pcolor.cd,
                xAxis: objParam.xAxis,
                yAxis: objParam.yAxis,
                tooltip: objParam.tooltip,
                /*绘图区选项*/
                plotOptions: objParam.plotOptions
            }, objParam.call);

            chart[this.appendChartProName] = objParam[pchart_logicFnInstance.appendChartProName];
            chart[this.appendChartProName][this.extendLegend] = legendObj;
            return chart;
        },

        /*y轴上添加辅助线，会覆盖id相同的辅助线
        *yPlotLines 同yAxis.plotLines   注：yPlotLines可以是object也可以是array，且具有yIndex属性 代表辅助线对应的y轴的索引，默认是零
        */
        addPlotLineToYAxis: function (chart, yPlotLines) {
            yPlotLines = yPlotLines || [];
            yPlotLines = yPlotLines instanceof Array ? yPlotLines : [yPlotLines];
            var arr = [];
            for (var i = 0; i < yPlotLines.length; i++) {
                var currPlotLine = yPlotLines[i];

                var yIndex = currPlotLine.yIndex || 0;
                currPlotLine.color = currPlotLine.color || this.yPlotLineDefaultStyle.color;
                currPlotLine.width = currPlotLine.width || this.yPlotLineDefaultStyle.width;
                currPlotLine.dashStyle = currPlotLine.dashStyle || this.yPlotLineDefaultStyle.dashStyle;
                currPlotLine.zIndex = 10;

                var label = currPlotLine.label || {};
                label.align = label.align || this.yPlotLineDefaultStyle.label.align;
                label.x = label.x || this.yPlotLineDefaultStyle.label.x;
                label.y = label.y || this.yPlotLineDefaultStyle.label.y;
                label.style = label.style || this.yPlotLineDefaultStyle.label.style;

                currPlotLine.id = currPlotLine.id || ptool.produceId();
                chart.yAxis[yIndex].removePlotLine(currPlotLine.id);
                chart.yAxis[yIndex].addPlotLine(currPlotLine);
                arr.push(currPlotLine);
            }
            chart[this.appendChartProName].yPlotLines = arr;
        },
        /*移除辅助线
        *objParam:{id:辅助线ID,yIndex:对应的y轴索引，默认零}
        */
        removePlotLineToYAxis: function (chart, objParam) {
            chart.yAxis[objParam.yIndex || 0].removePlotLine(objParam.id);
            var arr = chart[this.appendChartProName].yPlotLines;
            for (var i = 0; i < arr.length; i++) {
                if (arr[i].id == objParam.id) {
                    arr.splice(i, 1);
                    break;
                }
            }
            chart[this.appendChartProName].yPlotLines = arr;
        },

        /*
        *添加图标
        *chartPlotIcon(也可以是object):[{seriesIndex: 0,pointIndex: 1,icons: [{url: '',height: 1,width: 1,id:'图标ID，可不传'}]}]
        *   seriesIndex series的索引，默认0
        *   pointIndex 对应的数据列的索引
        *   icons 图标信息数组
        */
        addPlotIcon: function (chart, chartPlotIcon) {
            var chartOldPlotIcon = chart[this.appendChartProName].chartPlotIcon || [];
            chartPlotIcon = chartPlotIcon instanceof Array ? chartPlotIcon : [chartPlotIcon];
            for (var i = 0; i < chartPlotIcon.length; i++) {
                var currPlotIcon = chartPlotIcon[i];
                var isFind = false;
                for (var j = 0; j < chartOldPlotIcon.length; j++) {
                    var currOldPlotIcon = chartOldPlotIcon[j];
                    if (currOldPlotIcon.seriesIndex == currPlotIcon.seriesIndex && currOldPlotIcon.pointIndex == currPlotIcon.pointIndex) {
                        isFind = true;
                        currOldPlotIcon.icons = currOldPlotIcon.icons.concat(currPlotIcon.icons);
                        break;
                    }
                }
                if (!isFind) chartOldPlotIcon.push(currPlotIcon);
            }
            chart[this.appendChartProName].chartPlotIcon = chartOldPlotIcon;
            this.setPlotIcon(chart);
        },
        /*
        *更新图标
        *chartPlotIcon(也可以是object):[{seriesIndex: 0,pointIndex: 1,icons: [{url: '',height: 1,width: 1,iconIndex:0,id:'图标ID，可不传'}]}]
        *   seriesIndex series的索引，默认0
        *   pointIndex 对应的数据列的索引
        *   icons 图标信息数组  iconIndex 要更新的是点上的哪一个图标，默认是0
        */
        updatePlotIcon: function (chart, chartPlotIcon) {
            var chartOldPlotIcon = chart[this.appendChartProName].chartPlotIcon || [];
            chartPlotIcon = chartPlotIcon instanceof Array ? chartPlotIcon : [chartPlotIcon];
            for (var i = 0; i < chartPlotIcon.length; i++) {
                var currPlotIcon = chartPlotIcon[i];
                var currIcons = currPlotIcon.icons;

                var currOldPlotIcon = chartOldPlotIcon.filter(function (a) {
                    return a.seriesIndex == currPlotIcon.seriesIndex && a.pointIndex == currPlotIcon.pointIndex;
                })[0];
                if (!currOldPlotIcon) continue;

                var oldIcons = currOldPlotIcon.icons;
                for (var j = 0; j < currIcons.length; j++) {
                    var currIconObj = currIcons[j];
                    if (currIconObj.iconIndex || currIconObj.iconIndex === 0) {
                        if (currIconObj.iconIndex < oldIcons.length)
                            oldIcons[currIconObj.iconIndex] = currIconObj;
                    } else {
                        if (currIconObj.id) {
                            var oldIcon = oldIcons.filter(function (a) {
                                return a.id == currIconObj.id;
                            })[0];
                            if (oldIcon) oldIcon = currIconObj;
                        }
                    }
                }
            }
            chart[this.appendChartProName].chartPlotIcon = chartOldPlotIcon;
            this.setPlotIcon(chart);
        },
        /*
        *内部使用方法，设置图标
        */
        setPlotIcon: function (chart) {
            var chartPlotIcon = chart[this.appendChartProName].chartPlotIcon;
            if (!chartPlotIcon || chartPlotIcon.length == 0) return;

            var oldIconMarkObj = chart[this.appendChartProName].iconMark || {};
            for (var i = 0; i < chartPlotIcon.length; i++) {
                var currPlotIcon = chartPlotIcon[i];
                var seriesIndex = currPlotIcon.seriesIndex;
                if (chart.series.length < seriesIndex + 1) continue;

                var points = chart.series[seriesIndex].points;
                var currPoint = points[currPlotIcon.pointIndex] || {};
                if (!currPoint.y) continue;

                var plotY = Math.summation(currPoint.plotY, chart.plotTop);
                var plotX = Math.summation(currPoint.shapeArgs.x, chart.plotLeft);
                plotX = Math.subtraction(plotX, 4);
                var iconInfos = currPlotIcon.icons;
                for (var z = 0; z < iconInfos.length; z++) {
                    var currIcon = iconInfos[z];
                    var iconX = plotX;
                    var tempY = Math.multiplication(z, currIcon.height);
                    var iconY = Math.subtraction(plotY, currIcon.height);
                    iconY = Math.subtraction(iconY, tempY);
                    var markName = this.createIconName(chart, seriesIndex, currPlotIcon.pointIndex, z);
                    if (oldIconMarkObj[markName]) {
                        oldIconMarkObj[markName].destroy();
                        oldIconMarkObj[markName] = null;
                    }
                    oldIconMarkObj[markName] =
                        chart.renderer.image(currIcon.url, iconX, iconY, currIcon.width, currIcon.height).attr({ zIndex: 100 }).add();
                }
            }
            chart[this.appendChartProName].iconMark = oldIconMarkObj;
        },
        /*移除图标，使用时可只传入id，或者传入seriesIndex、pointIndex、iconIndex
        *chartPlotIcon(也可以是object):[{seriesIndex: 0,pointIndex: 1,iconIndex:0,id:''}]
        */
        removePlotIcon: function (chart, chartPlotIcon) {
            var oldIconMarkObj = chart[this.appendChartProName].iconMark || {};
            var chartOldPlotIcon = chart[this.appendChartProName].chartPlotIcon || [];
            chartPlotIcon = chartPlotIcon instanceof Array ? chartPlotIcon : [chartPlotIcon];
            for (var i = 0; i < chartPlotIcon.length; i++) {
                var currPlotIcon = chartPlotIcon[i];
                var iconId = currPlotIcon.id || this.createIconName(chart, currPlotIcon.seriesIndex, currPlotIcon.pointIndex, currPlotIcon.iconIndex);
                if (oldIconMarkObj[iconId]) {
                    oldIconMarkObj[iconId].destroy();
                    oldIconMarkObj[iconId] = null;
                    delete oldIconMarkObj[iconId];
                }
                if (currPlotIcon.id) {
                    for (var x = 0; x < chartOldPlotIcon.length; x++) {
                        var oldPlotIcon = chartOldPlotIcon[x];
                        var oldIcons = oldPlotIcon.icons;
                        var isFind = false;
                        for (var y = 0; y < oldIcons.length; y++) {
                            var oldIcon = oldIcons[y];
                            if (oldIcon.id == currPlotIcon.id) {
                                oldIcons.splice(y, 1);
                                isFind = true;
                                break;
                            }
                        }
                        if (isFind) break;
                    }
                }
                else {
                    var currIcons = currPlotIcon.icons;
                    var currOldPlotIcon = chartOldPlotIcon.filter(function (a) {
                        return a.seriesIndex == currPlotIcon.seriesIndex && a.pointIndex == currPlotIcon.pointIndex;
                    })[0];
                    if (!currOldPlotIcon) continue;

                    currOldPlotIcon.icons.splice(currPlotIcon.iconIndex, 1);
                }
            }
            chart[this.appendChartProName].chartPlotIcon = chartOldPlotIcon;
        },
        /*内部使用，构造标记icon的名称*/
        createIconName: function (chart, seriesIndex, pointIndex, iconIndex) {
            return chart.renderTo.id + '-' + seriesIndex + '-' + pointIndex + '-' + iconIndex;
        },

        /*设置x轴分区，加空点时会影响点的顺序，进而对图标造成影响，所以在设置完分区之后，要更新series的data、更新categories、重新设置图标
        *xPlotBands:[]  x轴的分区；同highcharts的xAxis.plotBands
        */
        setXPlotBands: function (chart, xPlotBands) {
            if (xPlotBands.length == 0) return;
            chart[this.appendChartProName].xPlotBands = xPlotBands;
            var oldTempIndexArr = chart[this.appendChartProName].oldTempIndexArr || [];
            var plotBands = [];
            var categories = chart.xAxis[0].categories;
            var series = chart.series;

            //先把旧的人为加的空点去掉
            for (var y = 0; y < oldTempIndexArr.length; y++) {
                var oldTempIndex = oldTempIndexArr[y];
                categories.splice(oldTempIndex, 1);
                for (var z = 0; z < series.length; z++) {
                    series[z].data.splice(oldTempIndex, 1);
                }
            }
            oldTempIndexArr = [];

            //每列的宽
            var columnWidth = Math.division(chart.plotWidth, chart.xAxis[0].paddedTicks.length);
            var halfColumnWith = Math.division(columnWidth, 2);
            for (var i = 0; i < xPlotBands.length; i++) {
                var oldPlotBand = xPlotBands[i];
                var label = oldPlotBand.label || {};
                var from = oldPlotBand.from + i;
                var to = oldPlotBand.to + i;

                var appendIndex = to + 1;
                if (appendIndex < categories.length) {
                    categories.splice(appendIndex, 0, '');
                    for (var x = 0; x < series.length; x++) {
                        series[x].data.splice(appendIndex, 0, '');
                    }
                    oldTempIndexArr.push(appendIndex);
                }

                var plotBandsWRap = {
                    id: i + 1,
                    color: oldPlotBand.color || this.xPlotBandDefaultStyle.xPlotBandBackColor,
                    from: from,
                    to: to,
                    label: {
                        text: label.text,
                        align: label.align || this.xPlotBandDefaultStyle.labelAlign,
                        x: label.x || Math.subtraction(10, halfColumnWith),
                        verticalAlign: label.verticalAlign || this.xPlotBandDefaultStyle.labelVerticalAlign,
                        y: label.y || 18,
                        style: label.style || this.xPlotBandDefaultStyle.style
                    }
                };
                plotBands.push(plotBandsWRap);
            };

            for (var x = 0; x < series.length; x++) {
                var currSeries = series[x];
                var currSeriesDataArr = currSeries.data;
                var newData = [];
                for (var j = 0; j < currSeriesDataArr.length; j++) {
                    var currData = currSeriesDataArr[j];
                    if (currData) {
                        var options = currData.options;
                        newData.push(JSON.parse(JSON.stringify(options)));
                    }
                    else newData.push(null);
                }
                currSeries.setData(newData, false);
            }
            chart.xAxis[0].update({
                categories: categories,
                plotBands: plotBands
            });

            //重新设置x轴区块的宽，因为柱状图时，每个区块从第一个柱的中心开始到最后一个柱的中心结束，要调成包含第一个、最后一个的整根柱子
            var plotLinesAndBands = chart.xAxis[0].plotLinesAndBands;
            for (var j = 0; j < plotLinesAndBands.length; j++) {
                var plotElement = plotLinesAndBands[j].svgElem.element;
                var oldD = plotElement.getAttribute('d');
                var dArr = oldD.split(' ');

                for (var i = 0; i < dArr.length; i++) {
                    if (i == 1 || i == 4)
                        dArr[i] = Math.subtraction(parseFloat(dArr[i]), halfColumnWith);
                    if (i == 6 || i == 8)
                        dArr[i] = Math.summation(parseFloat(dArr[i]), halfColumnWith);
                }
                plotElement.setAttribute('d', dArr.join(' '));
            }

            this.setPlotIcon(chart);
        },
        /*移除x轴分区
        */
        removeXPlotBands: function (chart) {
            var oldTempIndexArr = chart[this.appendChartProName].oldTempIndexArr || [];
            var plotBands = [];
            var firstXAxis = chart.xAxis[0];
            var categories = firstXAxis.categories;
            var series = chart.series;

            //先把旧的人为加的空点去掉
            for (var y = 0; y < oldTempIndexArr.length; y++) {
                var oldTempIndex = oldTempIndexArr[y];
                categories.splice(oldTempIndex, 1);
                for (var z = 0; z < series.length; z++) {
                    series[z].data.splice(oldTempIndex, 1);
                }
                firstXAxis.removePlotBand(y + 1);
                if (y == oldTempIndexArr.length - 1)
                    firstXAxis.removePlotBand(y + 2);
            };

            chart[this.appendChartProName].oldTempIndexArr = [];
            chart[this.appendChartProName].xPlotBands = null;

            this.setXCategories(chart, categories);
            for (var x = 0; x < series.length; x++) {
                series[x].setData(series[x].data);
            }

            this.setPlotIcon(chart);
        },

        //设置x轴的categories
        setXCategories: function (chart, categories) {
            var firstDate = new Date(categories[0]);
            var endDate = new Date(categories[categories.length - 1]);
            var firstTime = firstDate.getTime();
            var endTime = endDate.getTime();
            var middleTime = endTime - firstTime;
            var oneDayTime = 1000 * 60 * 60 * 24;
            var timeType = 6;
            if (firstDate != 'Invalid Date' && endDate != 'Invalid Date' && firstTime > 100000000000) {
                timeType = middleTime <= oneDayTime ? 1 : middleTime <= 7 * oneDayTime ? 2 : middleTime <= 31 * oneDayTime ? 3 : (function () {
                    var monthCount = 0;
                    var prevMonth = -1;
                    var prevYear = -1;
                    for (var i = 0; i < categories.length; i++) {
                        var currDate = new Date(categories[i]);
                        if (currDate.getMonth() == prevMonth && currDate.getYear() == prevYear) continue;
                        ++monthCount;
                        prevMonth = currDate.getMonth();
                        prevYear = currDate.getYear();
                    }
                    return monthCount <= 12 ? 4 : 5;
                })();
            } else timeType == 6;

            chart[pchart_logicFnInstance.appendChartProName][pchart_logicFnInstance.extendTimeTypeName] = timeType;
            chart.axes[0].setCategories(categories);
        },

        //内部使用方法，判断chart是否有分区
        xAxisIsRegion: function (chart) {
            return (!!chart[this.appendChartProName].oldTempIndexArr && chart[this.appendChartProName].oldTempIndexArr.length > 0);
        },

        /*
        *批量添加series
        *chart Highcharts对象
        *series 可以是数组，也可以是object，属性同highcharts的series
        */
        addSeriesBatch: function (chart, series) {
            series = series instanceof Array ? series : [series];
            var chartType = chart.options.chart.type;
            var xIndex = -1;
            var categories = [];
            for (var i = 0; i < series.length; i++) {
                var serieData = series[i].data;
                for (var x = 0; x < serieData.length; x++) {
                    var currDataObj = serieData[x];
                    if (chartType == 'heatmap') {
                        var _date = new Date(currDataObj.x);
                        if (_date.getDay() == 0 || xIndex == -1)++xIndex;
                    }
                    if (currDataObj.x && i == 0) {
                        var _x = chartType != 'heatmap' ? currDataObj.x : chart[pchart_logicFnInstance.appendChartProName][pchart_logicFnInstance.extendChartTypeName] == pchart_logicFnInstance.chartType.yearheatmap ? xIndex : _date.getHours();
                        categories.push(_x);
                    }
                    currDataObj.value = currDataObj.y;
                    currDataObj.ptime = !currDataObj.x ? null : Number(currDataObj.x) ? Number(currDataObj.x) : currDataObj.x.toString();
                    currDataObj.y = chartType == 'heatmap' ? (_date.getDay() == 0 ? 6 : _date.getDay() - 1) : currDataObj.y;
                    if (chartType == 'heatmap') {
                        currDataObj.x = chart[pchart_logicFnInstance.appendChartProName][pchart_logicFnInstance.extendChartTypeName] == pchart_logicFnInstance.chartType.yearheatmap ? xIndex : _date.getHours();
                    }
                    else
                        delete currDataObj.x;
                }
            }

            var isRegion = this.xAxisIsRegion(chart);
            /*存在分区时*/
            if (isRegion) {
                var oldTempIndexArr = chart[this.appendChartProName].oldTempIndexArr || [];
                for (var y = 0; y < oldTempIndexArr.length; y++) {
                    var oldTempIndex = oldTempIndexArr[y];
                    for (var z = 0; z < series.length; z++) {
                        series[z].data.splice(oldTempIndex, 0, null);
                    }
                    if (categories.length > 0)
                        categories.splice(oldTempIndex, 0, null);
                }
            }

            if (categories.length > 0)
                this.setXCategories(chart, categories);
            for (var i = 0; i < series.length; i++) {
                chart.addSeries(series[i]);
            }

            this.setPlotIcon(chart);
            this.addPlotLineToYAxis(chart, chart[this.appendChartProName].yPlotLines);
        },
        //移除某series
        removeSeries: function (chart, seriesIndex) {
            //先移除当前series上的图标
            var chartPlotIcon = chart[this.appendChartProName].chartPlotIcon || [];
            var oldIconMarkObj = chart[this.appendChartProName].iconMark || {};
            for (var i = 0; i < chartPlotIcon.length; i++) {
                var currPlotIcon = chartPlotIcon[i];
                if (currPlotIcon.seriesIndex == seriesIndex) {
                    var iconInfos = currPlotIcon.icons;
                    for (var z = 0; z < iconInfos.length; z++) {
                        var markName = this.createIconName(chart, seriesIndex, currPlotIcon.pointIndex, z);
                        if (oldIconMarkObj[markName]) {
                            oldIconMarkObj[markName].destroy();
                            oldIconMarkObj[markName] = null;
                            delete oldIconMarkObj[markName];
                        }
                    }
                    chartPlotIcon.splice(i, 1);
                    --i;
                }
            }
            chart[this.appendChartProName].chartPlotIcon = chartPlotIcon;
            chart[this.appendChartProName].iconMark = oldIconMarkObj;
            chart.series[seriesIndex].remove();

            //某一个y轴上没有series时移除对应的辅助线，否则重新渲染辅助线
            var yAxis = chart.yAxis || [];
            for (var i = 0; i < yAxis.length; i++) {
                var currYAxis = yAxis[i];
                if (currYAxis.series.length == 0) {
                    for (var x = 0; x < chart[this.appendChartProName].yPlotLines.length; x++) {
                        var currPlotLine = chart[this.appendChartProName].yPlotLines[x];
                        if (currPlotLine.yIndex == i) {
                            this.removePlotLineToYAxis(chart, { id: currPlotLine.id, yIndex: i });
                            --x;
                        }
                    }
                }
            }
            this.addPlotLineToYAxis(chart, chart[this.appendChartProName].yPlotLines);

            //当chart上没有series时，移除分区
            if (chart.series.length == 0)
                this.removeXPlotBands(chart);
        },
        /*更新某series的data
        *objParam:{seriesIndex:series的索引,data:[]}
        */
        updateDataToSeries: function (chart, objParam) {
            var serieData = objParam.data;
            var categories = [];
            for (var x = 0; x < serieData.length; x++) {
                var currDataObj = serieData[x];
                if (currDataObj.x) categories.push(currDataObj.x);
                delete currDataObj.x;
            }

            var isRegion = this.xAxisIsRegion(chart);
            if (isRegion) {
                var oldTempIndexArr = chart[this.appendChartProName].oldTempIndexArr || [];
                for (var y = 0; y < oldTempIndexArr.length; y++) {
                    var oldTempIndex = oldTempIndexArr[y];
                    serieData.splice(oldTempIndex, 0, null);
                    if (categories.length > 0)
                        categories.splice(oldTempIndex, 0, null);
                }
            }

            chart.series[objParam.seriesIndex].setData(serieData);
            if (categories.length > 0)
                this.setXCategories(chart, categories);

            this.setPlotIcon(chart);
            this.addPlotLineToYAxis(chart, chart[this.appendChartProName].yPlotLines);
        },







        /*
        *创建chart的各个结构
        *objParam:{container:'容器',type:'图形类型',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},legend:{}}
        */
        initChartInfo: function (objParam) {
            var realObj = { container: objParam.container, type: objParam.type };
            var extendObj = {};
            extendObj[pchart_logicFnInstance.extendChartTypeName] = objParam[pchart_logicFnInstance.extendChartTypeName];
            extendObj[pchart_logicFnInstance.extendChartIdPro] = ptool.produceId();
            realObj[pchart_logicFnInstance.appendChartProName] = extendObj;

            realObj.title = ptool.combineObj(pchart_logicFnInstance.titleDefaultStyle, objParam.title);
            realObj.subtitle = ptool.combineObj(objParam.subtitle);
            realObj.titleUnit = objParam.titleUnit;
            realObj._title = objParam._title;
            realObj._subtitle = objParam._subtitle;


            //之所以用两个x轴，是为了做出chart的上下边线和网格线条不一样的效果
            var xAxisObj = ptool.combineObj(pchart_logicFnInstance.xAxisDefaultStyle, objParam.xAxis);
            var xAxis = [xAxisObj];
            xAxis.push(this.xAxisLineStyle);
            realObj.xAxis = xAxis;

            var paramYAxis = objParam.yAxis || [];
            paramYAxis = paramYAxis instanceof Array == true ? paramYAxis : [paramYAxis];
            var realYAxis = [];
            //当y轴带有标题，且chart上既没有主标题也没有副标题时，chart一定要加上marginTop
            var yIsTitle = false;
            for (var i = 0; i < this.yAxisDefaultStyle.length; i++) {
                if (!paramYAxis[i] && i > 0) break;
                var yObj = paramYAxis[i] || {};
                realYAxis.push(ptool.combineObj(this.yAxisDefaultStyle[i], yObj));
                if ((yObj.title || {}).text) yIsTitle = true;
            }
            realObj.yAxis = realYAxis;

            realObj.tooltip = ptool.combineObj(pchart_logicFnInstance.tipDefaultStyle, objParam.tooltip);

            objParam.plotOptions = this.createDefaultPlotOptions(objParam);
            realObj.plotOptions = ptool.combineObj(pchart_logicFnInstance.plotOptionsSeriesStyle, objParam.plotOptions);

            var chartObj = {
                type: objParam.type,
                animation: false,
                renderTo: objParam.container,
                backgroundColor: this.chartBackColor,
                events: {
                    addSeries: function (event) {
                        pchart_logicFnInstance.addSeriesEvent(this, event.options);
                    }
                }
            };
            !(objParam.title || {}).text && !(objParam.subtitle || {}).text ? chartObj.marginTop = this.chartMarginTop : '';
            realObj.chart = chartObj;

            realObj.colorAxis = ptool.combineObj(pchart_logicFnInstance.colorAxisDefaultStyle, objParam.colorAxis);
            realObj.legend = objParam.legend;

            return realObj;
        },

        /*创建chart*/
        executeInitChart: function (objParam) {
            var realObj = this.initChartInfo(objParam);
            realObj.call = objParam.call;

            var chart = this.initChart(realObj);
            if (objParam.type == 'heatmap') chart.yAxis[0].setCategories(['一', '二', '三', '四', '五', '六', '日']);
            if (objParam.series) chart.addSeriesBatch(objParam.series);
            return chart;
        },

        /*每添加一个series前，进行的操作*/
        addSeriesEvent: function (chart, currSeries) {
            var currSeriesType = currSeries.type || chart.options.chart.type;
            currSeries.zIndex = currSeriesType == 'line' || currSeriesType == 'spline' ? 100 : 50;

            /*处理填充环图的灰色块*/
            if (chart[pchart_logicFnInstance.appendChartProName][pchart_logicFnInstance.extendChartTypeName] == pchart_logicFnInstance.chartType.doughnutFillPie) {
                var seData = currSeries.data || [];
                if (seData.length > 0) {
                    var readY = seData[0].y;
                    if (readY < 1) {
                        seData.push({
                            y: Math.subtraction(1, readY),
                            color: pchart_logicFnInstance.fillPieGrayVal
                        });
                    }
                    currSeries.data = seData;
                }
            }

            /*设置柱子的间距
            *单列对比柱图的柱与柱之间间距是0.25  堆积柱之间的间距是0.18   多列对比柱的列间距是0.1    直方图列于列之间的间距是-0.3
            */
            var columnCount = 0;
            var areaCount = 0;
            for (var i = 0; i < chart.series.length; i++) {
                switch (chart.series[i].type) {
                    case 'column':
                        ++columnCount;
                        break;
                    case 'area':
                        ++areaCount;
                        break;
                }
            }

            currSeriesType == 'column' ? ++columnCount : currSeriesType == 'area' ? ++areaCount : '';

            //设置柱图的间距。因为单列对比柱图和多列对比柱图间距不一样，所以只能动态设置，索性把堆积柱图间距、直方图间距，全在这里进行设置
            if (columnCount > 0) {
                var stacking = chart.options.plotOptions.column.stacking;
                var pointPadding = chart[pchart_logicFnInstance.appendChartProName][pchart_logicFnInstance.extendChartTypeName] == pchart_logicFnInstance.chartType.histogram ? -0.3 :
                stacking != null ? 0.18 : columnCount == 1 ? 0.25 : 0.1;
                chart.update({
                    plotOptions: {
                        column: { pointPadding: pointPadding }
                    }
                });
            }

            //设置渐变面积图的填充渐变
            var color = currSeries.color || chart.options.colors[chart.series.length];
            if (areaCount > 0 && chart[pchart_logicFnInstance.appendChartProName][pchart_logicFnInstance.extendChartTypeName] == pchart_logicFnInstance.chartType.gradientArea
                && !currSeries.fillColor) {
                var areaPlotoptions = chart.options.plotOptions.area;
                currSeries.fillColor = {
                    linearGradient: [0, 0, 0, 300],
                    stops: [
                        [0, color],
                        [1, Highcharts.Color(color).setOpacity(0).get('rgba')]
                    ]
                };
            }

            if (currSeriesType != 'boxplot') {
                //判断是否具有图形上的点的点击事件
                var isPointClick = ((currSeries.point || {}).events || {}).click ||
                                    (((chart.options.plotOptions[currSeriesType] || {}).point || {}).events || {}).click ||
                                    (((chart.options.plotOptions.series || {}).point || {}).events || {}).click ? true : false;

                /*关于十字准星   纯柱图时为每个柱子加阴影，颜色如下介绍；其他时候，只要带有线的，以及面积图，均为竖虚线，宽1，颜色7A94AD
                *F8F8F8  3DA8D3  可点击的柱时(即可下钻)用色彩3DA8D3 同时应用样式类pchart_backcolumntohover ，不可点击时用色彩F8F8F8即可
                *默认为十字准星竖线
                */
                var xAxisObj = chart.xAxis[0];
                var xcrosshair = xAxisObj.crosshair || {};

                if (isPointClick)
                    currSeries.cursor = 'pointer';
                else currSeries.cursor = 'default';

                if (columnCount == chart.series.length + 1) {
                    if (isPointClick === true) {
                        xcrosshair.className = 'pchart_backcolumntohover';
                        xcrosshair.color = this.clickColumnHoverColor;
                    }
                    else
                        xcrosshair.color = this.normalColumnHoverColor;
                    xcrosshair.dashStyle = null;
                    delete xcrosshair.dashStyle;
                    xcrosshair.width = null;
                    delete xcrosshair.width;
                } else {
                    xcrosshair.className = null;
                    delete xcrosshair.className;
                    xcrosshair.color = this.shiziCrosshairColor;
                    xcrosshair.dashStyle = 'Dash';
                    xcrosshair.width = 1;
                }

                xAxisObj.crosshair = xcrosshair;
            }

            /*添加图例。图例html及对应关系如下：
            <li><em class="pchart_legend_icon legend_icon_square" style="background: red;"></em><b>柱图--正方形</b></li>
            <li><em class="pchart_legend_icon legend_icon_line" style="background: red;"></em><b>曲线--长方形</b></li>
            <li><em class="pchart_legend_icon legend_icon_circle" style="background: red;"></em><b>散点图、气泡图--圆圈</b></li>
            <li><em class="pchart_legend_icon legend_icon_area" style="border-top:1px solid red;background:rgba(255,0,0,0.2);"></em><b>面积--区域</b></li>
            <li><em class="pchart_legend_icon legend_icon_circleline" style="background: red;">
                <i style="background: red;"></i></em><b>折线--线带圆圈</b></li>
            */

            var legendObj = chart[this.appendChartProName][this.extendLegend];
            if (legendObj && this.isCreateLegend(currSeriesType)) {
                var legendUlJquery = $('#' + legendObj.id);
                var legendHtml = '';
                switch (currSeriesType) {
                    case 'pie':
                        var serieData = currSeries.data || [];
                        for (var i = 0; i < serieData.length; i++) {
                            var currPoint = serieData[i];
                            legendHtml += '<li><span><em class="pchart_pielegend_circle" style="background:' + (currPoint.color || chart.options.colors[i]) +
                                ';"></em><b title="' + currPoint.name + '">' + currPoint.name +
                                '</b></span><span title="' + (currPoint.y || (currPoint.unit || '')) +
                                '"><i>' + currPoint.y + '</i><b>' + (currPoint.unit || '') + '</b></span></li>';
                        }
                        legendUlJquery.empty();
                        break;
                    default:
                        var htmlStart = '<li' + (legendObj.padding ? 'style="margin-left:' + legendObj.padding + ';"' : '') + '><em class="pchart_legend_icon ';
                        var htmlMiddle = '';
                        var htmlEnd = '</b></li>';
                        var startClass = currSeriesType == 'column' ? 'legend_icon_square' : currSeriesType == 'spline' ? 'legend_icon_line' :
                                        currSeriesType == 'line' ? 'legend_icon_circleline' :
                                        (currSeriesType == 'area' || currSeriesType == 'areaspline' || currSeriesType == 'arearange') ? 'legend_icon_area' :
                                        (currSeriesType == 'scatter' || currSeriesType == 'bubble') ? 'legend_icon_circle' : '';
                        var rgbColor = ptool.colorValToRgb(color);
                        htmlMiddle = startClass + '" style="' +
                            (currSeriesType == 'area' || currSeriesType == 'areaspline' || currSeriesType == 'arearange' ?
                                'border-top:1px solid ' + color + ';background:rgba(' + rgbColor.r + ',' + rgbColor.g + ',' + rgbColor.b + ',0.2)' :
                                'background: ' + color) + ';">' + (currSeriesType == 'line' ? '<i style="background: ' + color + ';"></i>' : '') +
                            '</em><b>' + (currSeries.name || '');
                        legendHtml = htmlStart + htmlMiddle + htmlEnd;
                        if (chart.title || chart.subtitle) {
                            var legendTop = (chart.subtitle ? chart.subtitle.alignAttr.y : chart.title.alignAttr.y) + 16;
                            legendUlJquery.parent().css('top', legendTop + 'px');
                        }
                        break;
                }
                legendUlJquery.append(legendHtml);
            }
        },

        /*构建默认的plotOptions*/
        createDefaultPlotOptions: function (objParam) {
            var plotOptions = objParam.plotOptions || {};

            plotOptions.column = plotOptions.column || {};
            plotOptions.column.stacking = plotOptions.column.stacking || objParam.isStack === true ? 'normal' : null;

            plotOptions.line = constructorTypePlotOptions(plotOptions.line, 'line');
            plotOptions.spline = constructorTypePlotOptions(plotOptions.spline, 'spline');

            plotOptions.area = constructorTypePlotOptions(plotOptions.area, 'area');
            plotOptions.areaspline = constructorTypePlotOptions(plotOptions.areaspline, 'areaspline');
            plotOptions.arearange = constructorTypePlotOptions(plotOptions.arearange, 'arearange');

            plotOptions.pie = constructorTypePlotOptions(plotOptions.pie, 'pie');

            plotOptions.scatter = constructorTypePlotOptions(plotOptions.scatter, 'scatter');

            plotOptions.bubble = constructorTypePlotOptions(plotOptions.bubble, 'bubble');
            plotOptions.boxplot = constructorTypePlotOptions(plotOptions.boxplot, 'boxplot');

            plotOptions.heatmap = constructorTypePlotOptions(plotOptions.boxplot, 'heatmap');

            return plotOptions;

            function constructorTypePlotOptions(typePlotOptionsObj, type) {
                if (type == 'bubble') return typePlotOptionsObj || {};
                typePlotOptionsObj = typePlotOptionsObj || {};
                var markerObj = typePlotOptionsObj.marker || {};
                markerObj.radius = markerObj.radius || pchart_logicFnInstance.circleMarkerDefaultStyle.radius;
                markerObj.symbol = markerObj.symbol || pchart_logicFnInstance.circleMarkerDefaultStyle.symbol;
                markerObj.lineColor = markerObj.lineColor || pchart_logicFnInstance.circleMarkerDefaultStyle.lineColor;
                markerObj.lineWidth = markerObj.lineWidth || pchart_logicFnInstance.circleMarkerDefaultStyle.lineWidth;
                switch (type) {
                    case 'area':
                    case 'areaspline':
                    case 'arearange':
                        typePlotOptionsObj.fillOpacity = typePlotOptionsObj.fillOpacity || 0.2;
                    case 'spline':
                        markerObj.enabled = false;
                        break;
                }

                var states = markerObj.states || {};
                var statesHover = states.hover || {};
                statesHover.lineColor = statesHover.lineColor || pchart_logicFnInstance.circleMarkerHoverDefaultStyle.lineColor;
                statesHover.lineWidth = statesHover.lineWidth || pchart_logicFnInstance.circleMarkerHoverDefaultStyle.lineWidth;
                statesHover.radius = statesHover.radius || pchart_logicFnInstance.circleMarkerHoverDefaultStyle.radius;
                states.hover = statesHover;
                markerObj.states = states;

                typePlotOptionsObj.marker = markerObj;

                var dataLabels = typePlotOptionsObj.dataLabels || {};
                if (objParam.isLabels == true) {
                    dataLabels.enabled = true;
                    dataLabels.style = {
                        color: pchart_logicFnInstance.pointValueDefaultStyle.color,
                        fontSize: pchart_logicFnInstance.pointValueDefaultStyle.fontSize,
                        fontFamily: pchart_logicFnInstance.pointValueDefaultStyle.fontFamily
                    };

                } else
                    dataLabels.enabled = false;
                typePlotOptionsObj.dataLabels = dataLabels;
                return typePlotOptionsObj;
            };
        },
        /*设置chart的关联性。设置完之后，在悬浮到设置的任一个chart的点的时候，其它相关chart的x轴上的同位置的点则也触发悬浮效果*/
        setChartRelv: function (chartArr) {
            for (var i = 0; i < chartArr.length; i++) {
                var currChart = chartArr[i];
                var oldExtendObj = currChart[this.appendChartProName];
                oldExtendObj[this.extendChartRelv] = chartArr;
                currChart[this.appendChartProName] = oldExtendObj;
            }
        },
        /*非饼图、环形图----有图例时创建图例以及存放图表的div*/
        createLegendHtml: function (objParam) {
            var legendObj = objParam.legend;
            legendObj = legendObj === true ? {} : legendObj;
            var styleStr = legendObj.align == 'left' ? 'float:left;' : legendObj.align == 'center' ? 'margin-left:45%;' : 'float:right;';
            styleStr += this.constructorFontStyle(legendObj);
            styleStr = ' style="' + styleStr + '"';

            var legendEleId = ptool.produceId();
            var chartEleId = ptool.produceId();
            var legendHtml = '<div class="pchart_legend"><ul id="' + legendEleId + '"' + styleStr + '></ul></div>';
            var chartHtml = '<div id="' + chartEleId + '"></div>';

            var jqueryEle = $(ptool.getDomElement(objParam.chart.renderTo));
            jqueryEle.append(legendHtml + chartHtml);

            return {
                legendHtmlId: legendEleId,
                chartHtmlId: chartEleId
            };
        },
        /*饼图、环形图----创建存放图例、环内文字的div*/
        constructorEleForPie: function (objParam) {
            var legendObj = objParam.legend;
            var legendHtml = '';
            var titleHtml = '';
            var orientionClass = '';
            var legendEleId = ptool.produceId();
            var chartEleId = ptool.produceId();
            if (legendObj) {
                legendObj = legendObj === true ? {} : legendObj;
                var legendStyleStr = this.constructorFontStyle(legendObj);
                orientionClass = legendObj.align === 'bottom' ? ' _pievertical' : '';
                legendHtml = '<div class="pchart_pielegend' + orientionClass + '"><ul id="' + legendEleId +
                            '" style="' + legendStyleStr + '"></ul></div>';
            }

            if (objParam._title || objParam._subtitle || objParam.titleUnit) {
                var tStr = '';
                if (objParam._title) {
                    tStr = '<b style="' + this.constructorFontStyle(objParam._title.style || {}) + '">' + (objParam._title.text || '') + '</b>';
                }

                var stStr = '';
                if (objParam._subtitle) {
                    stStr = '<em style="' + this.constructorFontStyle(objParam._subtitle.style || {}) + '">' + (objParam._subtitle.text || '') + '</em>';
                }

                var tuStr = '';
                if (objParam.titleUnit) {
                    tuStr = '<span style="' + this.constructorFontStyle(objParam.titleUnit.style || {}) + '">' + (objParam.titleUnit.text || '') + '</span>';
                }

                titleHtml = '<div class="pchart_pietitle">' + tStr + stStr + tuStr + '</div>';
            }
            var html = '';
            if (legendHtml || titleHtml) {
                html = '<div class="pchart_pie' + orientionClass + '"><div class="pchart_pack" id="' + chartEleId +
                       '"></div>' + titleHtml + '</div>' + legendHtml;
            }
            if (html) {
                var jqueryEle = $(ptool.getDomElement(objParam.chart.renderTo));
                jqueryEle.append(html);
            }
            return {
                legendHtmlId: legendHtml ? legendEleId : legendHtml,
                chartHtmlId: html ? chartEleId : objParam.chart.renderTo
            };
        },
        /*判断图表是否可以创建图例*/
        isCreateLegend: function (type) {
            return type == 'column' || type == 'spline' || type == 'line' || type == 'area' || type == 'areaspline' || type == 'arearange'
                            || type == 'scatter' || type == 'bubble' || type == 'pie' ? true : false;
        },
        /*构造文字相关样式*/
        constructorFontStyle: function (obj) {
            return (obj.color ? 'color:' + obj.color + ';' : '') +
                   (obj.fontSize ? 'font-size:' + obj.fontSize + ';' : '') +
                   (obj.fontFamily ? 'font-family:' + obj.fontFamily + ';' : '');
        }
    };
    var pchart_logicFnInstance = new pchart_logicFn();

    //扩展highcharts
    (function () {
        /*y轴上添加辅助线，会覆盖id相同的辅助线
        *yPlotLines 同yAxis.plotLines   注：yPlotLines可以是object也可以是array，且具有yIndex属性 代表辅助线对应的y轴的索引，默认是零
        */
        Highcharts.Chart.prototype.addPlotLineToYAxis = function (yPlotLines) {
            pchart_logicFnInstance.addPlotLineToYAxis(this, yPlotLines);
        };

        /*移除Y轴辅助线
        *objParam:{id:辅助线ID,yIndex:对应的y轴索引，默认零}
        */
        Highcharts.Chart.prototype.removePlotLineToYAxis = function (objParam) {
            pchart_logicFnInstance.removePlotLineToYAxis(this, objParam);
        };

        /*
        *添加图标
        *chartPlotIcon(也可以是object):[{seriesIndex: 0,pointIndex: 1,icons: [{url: '',height: 1,width: 1,id:'图标ID，可不传'}]}]
        *   seriesIndex series的索引，默认0
        *   pointIndex 对应的数据列的索引
        *   icons 图标信息数组
        */
        Highcharts.Chart.prototype.addPlotIcon = function (chartPlotIcon) {
            pchart_logicFnInstance.addPlotIcon(this, chartPlotIcon);
        };

        /*
        *更新图标
        *chartPlotIcon(也可以是object):[{seriesIndex: 0,pointIndex: 1,icons: [{url: '',height: 1,width: 1,iconIndex:0,id:'图标ID，可不传'}]}]
        *   seriesIndex series的索引，默认0
        *   pointIndex 对应的数据列的索引
        *   icons 图标信息数组  iconIndex 要更新的是点上的哪一个图标，默认是0
        */
        Highcharts.Chart.prototype.updatePlotIcon = function (chartPlotIcon) {
            pchart_logicFnInstance.updatePlotIcon(this, chartPlotIcon);
        };

        /*移除图标，使用时可只传入id，或者传入seriesIndex、pointIndex、iconIndex
        *chartPlotIcon(也可以是object):[{seriesIndex: 0,pointIndex: 1,iconIndex:0,id:''}]
        */
        Highcharts.Chart.prototype.removePlotIcon = function (chartPlotIcon) {
            pchart_logicFnInstance.removePlotIcon(this, chartPlotIcon);
        };

        /*设置x轴分区，加空点时会影响点的顺序，进而对图标造成影响，所以在设置完分区之后，要更新series的data、更新categories、重新设置图标
        *xPlotBands:[]  x轴的分区；同highcharts的xAxis.plotBands
        */
        Highcharts.Chart.prototype.setXPlotBands = function (xPlotBands) {
            pchart_logicFnInstance.setXPlotBands(this, xPlotBands);
        };

        /*移除x轴分区
        */
        Highcharts.Chart.prototype.removeXPlotBands = function () {
            pchart_logicFnInstance.removeXPlotBands(this);
        };

        //设置x轴的categories
        Highcharts.Chart.prototype.setXCategories = function (categories) {
            pchart_logicFnInstance.setXCategories(this, categories);
        };

        /*
        *批量添加series
        *chart Highcharts对象
        *series 可以是数组，也可以是object，属性同highcharts的series
        *series内的data只支持x、y的形式
        */
        Highcharts.Chart.prototype.addSeriesBatch = function (series) {
            pchart_logicFnInstance.addSeriesBatch(this, series);
        };

        //移除某series
        Highcharts.Chart.prototype.removeSeries = function (seriesIndex) {
            pchart_logicFnInstance.removeSeries(this, seriesIndex);
        };

        /*更新某series的data
        *objParam:{seriesIndex:series的索引,data:[]}
        *data只支持x、y的形式
        */
        Highcharts.Chart.prototype.updateDataToSeries = function (objParam) {
            pchart_logicFnInstance.updateDataToSeries(this, objParam);
        };
    })();

    /*柱图相关*/
    function columnChart() { };
    columnChart.prototype = {
        initColumn: function (objParam) {
            objParam.isStack = false;
            return this.init(objParam);
        },
        initColumnStacking: function (objParam) {
            objParam.isStack = true;
            return this.init(objParam);
        },
        initColumnHistogram: function (objParam) {
            objParam[pchart_logicFnInstance.extendChartTypeName] = pchart_logicFnInstance.chartType.histogram;
            return this.init(objParam);
        },
        init: function (objParam) {
            objParam.type = 'column';
            return pchart_logicFnInstance.executeInitChart(objParam);
        }
    };
    var columnChartInstance = new columnChart();


    /*线图相关*/
    function lineChart() { };
    lineChart.prototype = {
        initLine: function (objParam) {
            objParam.type = 'line';
            return pchart_logicFnInstance.executeInitChart(objParam);
        },
        initLinePointValue: function (objParam) {
            objParam.type = 'line';
            objParam.isLabels = true;
            return pchart_logicFnInstance.executeInitChart(objParam);
        },
        initSpline: function (objParam) {
            objParam.type = 'spline';
            return pchart_logicFnInstance.executeInitChart(objParam);
        }
    };
    var lineChartInstance = new lineChart();


    /*面积图相关*/
    function areaChart() { };
    areaChart.prototype = {
        initArea: function (objParam) {
            objParam.type = 'area';
            return pchart_logicFnInstance.executeInitChart(objParam);
        },
        initSplineArea: function (objParam) {
            objParam.type = 'areaspline';
            return pchart_logicFnInstance.executeInitChart(objParam);
        },
        initStackingArea: function (objParam) {
            objParam.type = 'area';
            var plotOptions = objParam.plotOptions || {};
            var areaPlot = plotOptions.area || {};
            areaPlot.stacking = areaPlot.stacking || 'normal';
            plotOptions.area = areaPlot;
            objParam.plotOptions = plotOptions;

            return pchart_logicFnInstance.executeInitChart(objParam);
        },
        initRangeArea: function (objParam) {
            objParam.type = 'arearange';
            var plotOptions = objParam.plotOptions || {};
            var areaPlot = plotOptions.arearange || {};
            areaPlot.lineWidth = areaPlot.lineWidth || 0;
            plotOptions.arearange = areaPlot;
            objParam.plotOptions = plotOptions;

            return pchart_logicFnInstance.executeInitChart(objParam);
        },
        initGradientArea: function (objParam) {
            objParam.type = 'area';
            objParam[pchart_logicFnInstance.extendChartTypeName] = pchart_logicFnInstance.chartType.gradientArea;
            return pchart_logicFnInstance.executeInitChart(objParam);
        }
    };
    var areaChartInstance = new areaChart();


    /*饼图、环形图相关*/
    function pieChart() { };
    pieChart.prototype = {
        initPie: function (objParam) {
            return this.init(objParam);
        },
        initDoughnutPie: function (objParam) {
            objParam[pchart_logicFnInstance.extendChartTypeName] = pchart_logicFnInstance.chartType.doughnutPie;
            return this.init(objParam);
        },
        initDoughnutFillPie: function (objParam) {
            objParam[pchart_logicFnInstance.extendChartTypeName] = pchart_logicFnInstance.chartType.doughnutFillPie;
            return this.init(objParam);
        },
        init: function (objParam) {
            objParam.type = 'pie';
            var plotOptions = objParam.plotOptions || {};
            var piePlotOptions = plotOptions.pie || {};
            var piePoint = piePlotOptions.point || {};
            var piePointEvents = piePoint.events || {};

            piePointEvents.mouseOver = pointMouseOver(piePointEvents.mouseOver);
            piePointEvents.mouseOut = pointMouseOut(piePointEvents.mouseOut);

            switch (objParam[pchart_logicFnInstance.extendChartTypeName]) {
                case pchart_logicFnInstance.chartType.doughnutFillPie:
                case pchart_logicFnInstance.chartType.doughnutPie:
                    piePlotOptions.innerSize = '80%';

                    objParam._title = objParam.title ? JSON.parse(JSON.stringify(objParam.title)) : null;
                    objParam._subtitle = objParam.subtitle ? JSON.parse(JSON.stringify(objParam.subtitle)) : null;

                    objParam.title = null;
                    objParam.subtitle = null;
                    delete objParam.title;
                    delete objParam.subtitle;
                    break;
            }

            piePoint.events = piePointEvents;
            piePlotOptions.point = piePoint;
            plotOptions.pie = piePlotOptions;
            objParam.plotOptions = plotOptions;

            return pchart_logicFnInstance.executeInitChart(objParam);

            function pointMouseOver(customMouseOver) {
                return (function (_customMouseOver) {
                    return function (event) {
                        var extendChartType = this.series.chart[pchart_logicFnInstance.appendChartProName][pchart_logicFnInstance.extendChartTypeName];
                        if (extendChartType != pchart_logicFnInstance.chartType.doughnutPie
                            && extendChartType != pchart_logicFnInstance.chartType.diagramPie) {
                            var pointIndex = this.index;
                            this.series.chart.series[0].points[pointIndex].update({ sliced: true });
                        }
                        if (typeof _customMouseOver == 'function')
                            _customMouseOver.call(this, event);
                    };
                })(customMouseOver);
            };

            function pointMouseOut(customMouseOut) {
                return (function (_customMouseOut) {
                    return function (event) {
                        var pointIndex = this.index;
                        this.series.chart.series[0].points[pointIndex].update({ sliced: false });
                        if (typeof _customMouseOut == 'function')
                            _customMouseOut.call(this, event);
                    };
                })(customMouseOut);
            };
        }
    };
    var pieChartInstance = new pieChart();

    /*包括：散点图、气泡图、箱线图、热力图、组合图*/
    function moreChart() { };
    moreChart.prototype = {
        initScatter: function (objParam) {
            objParam.type = 'scatter';
            return this.init(objParam);
        },
        initBubble: function (objParam) {
            objParam.type = 'bubble';
            var plotOptions = objParam.plotOptions || {};
            var bubblePlotOptions = plotOptions.bubble || {};
            bubblePlotOptions.minSize = bubblePlotOptions.minSize || 24;
            bubblePlotOptions.maxSize = bubblePlotOptions.maxSize || 120;

            plotOptions.bubble = bubblePlotOptions;
            objParam.plotOptions = plotOptions;

            return this.init(objParam);
        },
        initBoxplot: function (objParam) {
            objParam.type = 'boxplot';
            return this.init(objParam);
        },
        initYearHeatmap: function (objParam) {
            objParam.type = 'heatmap';
            objParam[pchart_logicFnInstance.extendChartTypeName] = pchart_logicFnInstance.chartType.yearheatmap;
            return this.init(objParam);
        },
        initWeekHeatmap: function (objParam) {
            objParam.type = 'heatmap';
            objParam[pchart_logicFnInstance.extendChartTypeName] = pchart_logicFnInstance.chartType.weekheatmap;
            return this.init(objParam);
        },
        init: function (objParam) {
            switch (objParam.type) {
                case 'scatter':
                case 'bubble':
                    var yAxisArr = objParam.yAxis instanceof Array == true ? objParam.yAxis : typeof objParam.yAxis == 'object' ? [objParam.yAxis] : [{}];
                    for (var i = 0; i < yAxisArr.length; i++) {
                        var yAxisObj = yAxisArr[i];
                        var ycrosshair = yAxisObj.crosshair || {};

                        ycrosshair.color = this.shiziCrosshairColor;
                        ycrosshair.dashStyle = 'Dash';
                        ycrosshair.width = 1;
                        ycrosshair.zIndex = 0;

                        yAxisObj.crosshair = ycrosshair;
                    }
                    break;
            }
            var chart = pchart_logicFnInstance.executeInitChart(objParam);
            return chart;
        }
    };
    var moreChartInstance = new moreChart();

    return {
        chartType: pchart_logicFnInstance.chartType,
        appendChartProName: pchart_logicFnInstance.appendChartProName,
        extendChartTypeName: pchart_logicFnInstance.extendChartTypeName,
        extendTimeTypeName: pchart_logicFnInstance.extendTimeTypeName,
        extendChartIdPro: pchart_logicFnInstance.extendChartIdPro,
        extendChartRelv: pchart_logicFnInstance.extendChartRelv,
        /*设置chart的关联性。设置完之后，在悬浮到设置的任一个chart的点的时候，其它相关chart的x轴上的同位置的点则也触发悬浮效果
        *chartArr chart对象数组，如：[chart1,chart2]
        */
        setChartRelv: function (chartArr) {
            pchart_logicFnInstance.setChartRelv(chartArr);
        },
        /*初始化基本柱图，多柱子时为非堆叠
        *注：用 plotOptions.column.stacking: 'normal'控制堆叠
        *    plotOptions.column.pointPadding 控制柱之间的间距  plotOptions.column.groupPadding 控制同列柱之间的间距
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initColumn: function (objParam) {
            return columnChartInstance.initColumn(objParam);
        },
        /*初始化堆积柱图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initColumnStacking: function (objParam) {
            return columnChartInstance.initColumnStacking(objParam);
        },
        /*初始化直方图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initColumnHistogram: function (objParam) {
            return columnChartInstance.initColumnHistogram(objParam);
        },



        /*初始化折线图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initLine: function (objParam) {
            return lineChartInstance.initLine(objParam);
        },
        /*初始化带点值的折线图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initLinePointValue: function (objParam) {
            return lineChartInstance.initLinePointValue(objParam);
        },
        /*初始化曲线图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initSpline: function (objParam) {
            return lineChartInstance.initSpline(objParam);
        },



        /*初始化基本面积图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initArea: function (objParam) {
            return areaChartInstance.initArea(objParam);
        },
        /*初始化曲线面积图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initSplineArea: function (objParam) {
            return areaChartInstance.initSplineArea(objParam);
        },
        /*初始化堆积面积图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initStackingArea: function (objParam) {
            return areaChartInstance.initStackingArea(objParam);
        },
        /*初始范围面积均线图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initRangeArea: function (objParam) {
            return areaChartInstance.initRangeArea(objParam);
        },
        /*初始渐变面积图
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initGradientArea: function (objParam) {
            return areaChartInstance.initGradientArea(objParam);
        },



        /*初始普通饼形图
        *objParam:{container:'容器',xAxis:{},yAxis(可以是数组，也可以是object):[],
        *   tooltip:{},plotOptions:{},series:[],legend:{}}
        */
        initPie: function (objParam) {
            return pieChartInstance.initPie(objParam);
        },
        /*初始环形图。注：title、subtitle、titleUnit可包括属性有{text:'',style:{支持字体相关属性}}
        *objParam:{container:'容器',title:{},subtitle:{},titleUnit:{},xAxis:{},yAxis(可以是数组，也可以是object):[],
        *   tooltip:{},plotOptions:{},series:[],legend:{}}
        */
        initDoughnutPie: function (objParam) {
            return pieChartInstance.initDoughnutPie(objParam);
        },
        /*初始环形填充图。注：title、subtitle、titleUnit可包括属性有{text:'',style:{支持字体相关属性}}。series.data为长度为一的数组，且项内的y必须为<=1的数
        *objParam:{container:'容器',title:{},subtitle:{},titleUnit:{},xAxis:{},yAxis(可以是数组，也可以是object):[],
        *   tooltip:{},plotOptions:{},series:[],legend:{}}
        */
        initDoughnutFillPie: function (objParam) {
            return pieChartInstance.initDoughnutFillPie(objParam);
        },



        /*初始散点图。注：x必须是数字；向量散点图的本质是x轴、y轴各加一条辅助线，可由使用者调用添加辅助线的方法来实现
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initScatter: function (objParam) {
            return moreChartInstance.initScatter(objParam);
        },
        /*初始气泡图。注：series.data由x、y、z三个值组成，z决定了气泡的大小
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initBubble: function (objParam) {
            return moreChartInstance.initBubble(objParam);
        },
        /*初始箱线图。注：series.data由x、high(最大值)、q3(上位值)、median(中位值)、q1(下位值)、low(最小值)组成
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *      legend:{}}
        */
        initBoxplot: function (objParam) {
            return moreChartInstance.initBoxplot(objParam);
        },
        /*初始化年热力图。注：series.data里的x必须是时间，y是值；原生highcharts的热力图的x、y均是指索引，pchart做了处理，让使用者依然可以把x设置为时间，y设置为值
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *colorAxis(同highcharts的colorAxis):{},legend:{}}
        */
        initYearHeatmap: function (objParam) {
            return moreChartInstance.initYearHeatmap(objParam);
        },
        /*初始化周热力图。注：series.data里的x必须是时间，y是值；原生highcharts的热力图的x、y均是指索引，pchart做了处理，让使用者依然可以把x设置为时间，y设置为值
        *objParam:{container:'容器',title:{},subtitle:{},xAxis:{},yAxis(可以是数组，也可以是object):[],tooltip:{},plotOptions:{},series:[],
        *colorAxis(同highcharts的colorAxis):{},legend:{}}
        */
        initWeekHeatmap: function (objParam) {
            return moreChartInstance.initWeekHeatmap(objParam);
        }
    };
})();