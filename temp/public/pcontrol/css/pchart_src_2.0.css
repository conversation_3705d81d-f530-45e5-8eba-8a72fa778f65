
/*可点击的柱子，hover时的样式*/


.pchart_backcolumntohover {
    opacity: 0.16;
}

/*chart tips*/
.highcharts-tooltip {
    z-index: 11;
}

    .highcharts-tooltip * {
        font-family: <PERSON><PERSON>,"Microsoft Yahei",sans-serif;
    }

    .highcharts-tooltip > span {
        border: 1px solid #cdcdcd;
        border-radius: 3px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.2);
        background: rgba(255,255,255,0.8);
        padding-top: 2px;
        box-sizing: border-box;
    }

        .highcharts-tooltip > span > span:first-child {
            color: #666;
            line-height: 29px;
            border-bottom: 1px solid #d2d2d2;
            font-size: 14px;
        }

        .highcharts-tooltip > span > span > i {
            font-size: 12px;
            padding: 0 2px;
        }

        .highcharts-tooltip > span > span > b {
            color: #000;
        }

        .highcharts-tooltip > span > span {
            display: block;
            height: 29px;
            line-height: 29px;
            padding: 0 8px;
            font-size: 14px;
            color: #333;
        }



        .highcharts-tooltip > span > div {
            color: #666666;
            line-height: 29px;
            font-size: 12px;
            padding-left: 8px;
            min-width: 156px;
            height: 24px;
        }

    .highcharts-tooltip ul li {
        height: 29px;
        line-height: 29px;
        padding: 0 8px;
        font-size: 14px;
        color: #6d6d6d;
    }

        .highcharts-tooltip ul li i {
            display: inline-block;
            height: 10px;
            width: 10px;
            margin-right: 5px;
            vertical-align: middle;
        }

        .highcharts-tooltip ul li b {
            color: #000;
        }

        .highcharts-tooltip ul li span {
            font-size: 14px;
        }

    .highcharts-tooltip ul em {
        font-size: 12px;
        margin-left: 4px;
    }


/*chart 图例*/
.pchart_legend {
    height: 20px;
    overflow: hidden;
    position: relative;
    top: 20px;
    right: 10px;
}

    .pchart_legend ul {
        float: right;
    }

    .pchart_legend li {
        float: left;
        margin-left: 20px;
    }

.pchart_legend_icon {
    display: block;
    float: left;
}

.legend_icon_square {
    width: 10px;
    height: 10px;
    margin-top: 5px;
    margin-right: 8px;
}

.legend_icon_circle {
    width: 10px;
    height: 10px;
    margin-top: 5px;
    margin-right: 8px;
    border-radius: 50%;
}

.legend_icon_line {
    width: 14px;
    height: 2px;
    margin-top: 9px;
    margin-right: 8px;
}

.legend_icon_area {
    width: 14px;
    height: 8px;
    margin-top: 6px;
    margin-right: 8px;
}

.legend_icon_circleline {
    width: 16px;
    height: 2px;
    margin-top: 9px;
    margin-right: 8px;
    position: relative;
}

    .legend_icon_circleline i {
        content: '';
        position: absolute;
        width: 6px;
        height: 6px;
        border: 1px solid #fff;
        border-radius: 50%;
        left: 50%;
        top: -3px;
        margin-left: -4px;
    }


/*饼图图例*/


.pchart_pie {
    width: 45%;
    height: 100%;
    background: #ccc;
    float: left;
    position: relative;
}

.pchart_pielegend {
    font-size: 14px;
    overflow: auto;
    height: 100%;
    width: 55%;
    float: left;
    padding: 20px;
    box-sizing: border-box;
}

._pievertical.pchart_pielegend, ._pievertical.pchart_pie {
    height: 50%;
    width: 100%;
}

.pchart_pielegend_circle {
    width: 10px;
    height: 10px;
    margin-top: 5px;
    margin-right: 8px;
    border-radius: 50%;
    display: block;
    float: left;
}

.pchart_pielegend b {
    color: #808080;
}

.pchart_pielegend i {
    margin-right: 5px;
}

.pchart_pielegend li {
    height: 32px;
    line-height: 32px;
    cursor: pointer;
    padding: 0 10px;
    box-sizing: border-box;
}

    .pchart_pielegend li > span {
        width: calc(50% - 4px);
        float: left;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

        .pchart_pielegend li > span:last-child {
            text-align: right;
            padding-left: 10px;
            box-sizing: border-box;
        }

.pchart_pielegend em {
    margin-top: 10px;
}

.pchart_pielegend li:hover {
    background: #f8f8f8;
}

.pchart_pietitle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    text-align: center;
    width: 116px;
}


.pchart_pack {
    height: 100%;
}



.pchart_pietitle > * {
      display: block;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
}

.pchart_pietitle > em {
    font-size: 22px;
    margin: 5px 0;
    line-height: 30px;
    height: 30px;
}

.pchart_pietitle > span {
    color: #6d6d6d;
}
