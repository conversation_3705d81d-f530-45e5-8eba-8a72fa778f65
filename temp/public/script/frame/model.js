/*数据model*/
function frameModel() {
    this.fGroups = ko.observableArray();
    this.currUser = ko.observable(new user());
    this.alarms = ko.observableArray();
    this.currWeather = ko.observable(new weather());
    this.selBuild = ko.observable(new build());
    this.selWork = ko.observable(new workCalendar());
    this.unreadAlarmNum = ko.observable(0);                             /*未读报警数量*/
    this.alarmUrl = ko.observable();                                    /*全局报警url*/
    this.structs = ko.observableArray();                                /*组织结构树*/
    this.currStruct = ko.observable(new companyStruct());               /*当前组织结构*/
};

frameModel.instance = function () {
    return frameModel._inatance || (frameModel._inatance = new frameModel()) || frameModel._inatance;
};

/*建筑信息*/
function build() {
    this.constructor = arguments.callee;
    this.icon = null;
    /*暂定为国家+省+市+分区*/
    this.description = ko.computed({
        owner: this,
        read: function () {
            return cloudGlobal.parsVal(this.country(), this.province().name, this.city().name, this.region().name);
        }
    });
};
build.prototype = new gbuild();

/*天气信息*/
function weather() {
    this.temperature = null;            /*当日温度*/
    this.nowWeaName = null;            /*当日天气名称*/
    this.nowWeaIcon = null;//ko.observable();            /*当日天气图标*/
    this.humidity = null;               /*湿度*/
    this.pm2_5 = null;                  /*PM2.5*/
    this.windDirection = ko.observable();          /*风向*/
    this.windScale = ko.observable();              /*风力*/
    this.wind = ko.computed({                       /*此属性绑定到前台*/
        owner: this,
        read: function () {
            return cloudGlobal.parsVal(this.windDirection(), this.windScale());
        }
    });

    this.sunrise = null;                      /*日出时间*/
    this.sunset = null;                       /*日落时间*/
    this.nowDate = null;                      /*当日的日期，只显示月和日 格式为：03.01*/
    this.nowMaxTemper = ko.observable();                 /*当日最大温度*/
    this.nowMinTemper = ko.observable();                /*当日最小温度*/
    this.nowTemper = ko.computed({                       /*此属性绑定到前台*/
        owner: this,
        read: function () {
            return cloudGlobal.parsVal({vals:[this.nowMinTemper(),this.nowMaxTemper()],separator:'~',unit:' ℃'});
        }
    });
    this.tomWeaName = null;                   /*明日天气名称*/
    this.tomWeaIcon = null;                   /*明日天气图标*/
    this.tomDate = null;                      /*明日日期，格式同当日日期*/
    this.tomMaxTemper =  ko.observable();                  /*明日最大温度*/
    this.tomMinTemper =  ko.observable();                  /*明日最小温度*/
    this.tomTemper = ko.computed({                       /*此属性绑定到前台*/
        owner: this,
        read: function () {
            return cloudGlobal.parsVal({vals:[this.tomMinTemper(),this.tomMaxTemper()],separator:'~',unit:' ℃'});
        }
    });

    this.houWeaName = null;                   /*后日天气名称*/
    this.houWeaIcon = null;                   /*后日天气图标*/
    this.houDate = null;                      /*后日日期，格式同当日日期*/
    this.houMaxTemper =  ko.observable();                 /*后日最大温度*/
    this.houMinTemper =  ko.observable();                 /*后日最小温度*/
    this.houTemper = ko.computed({                       /*此属性绑定到前台*/
        owner: this,
        read: function () {
            return cloudGlobal.parsVal({vals:[this.houMinTemper(),this.houMaxTemper()],separator:'~',unit:' ℃'});
        }
    });
}

/*工作历信息*/
function workCalendar() {
    this.constructor = arguments.callee;
    this.nowDate = new Date().format('y.M.d');                                /*当日日期 格式示例：2016.03.03*/
    this.nowWeek = new Date().getChineseWeek();                                /*当日是周几*/
    this.day = null;                                    /*几号，小于10需补零*/
    /*营业时间 此属性绑定到前台*/
    this.business = ko.computed({
        owner: this,
        read: function () {
            return this.isWork() == true ? this.businessTime() : (this.isWork() == false ? ['今日不营业'] : '--');
        }
    });
    this.unreadNum = ko.observable(0);                                 /*工作历未读消息数量*/
    this.url = null;                                    /*工作历url*/
}
workCalendar.prototype = new gworkCalendar();

/*报警信息*/
function alarm() {
    this.constructor = arguments.callee;
    this.url=null;              /*报警跳转地址*/
    this.clickJumpAdd= function (alarm,ev) {
        window.open(alarm.url);
    };
}
alarm.prototype = new galarm();