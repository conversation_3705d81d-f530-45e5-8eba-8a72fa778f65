/*逻辑控制 html事件会调用此js中的方法*/
function frameController() { }

frameController.init = function () {
    var instance = frameModel.instance();
    ko.applyBindings(instance, document.getElementById('v6s-wrap'));
     frameController.getF();
    frameController.getUserInfo();
    frameController.getBuildInfo();
    frameController.getfgmnfn();
};

/*获取框架用组织结构*/
frameController.initStructs = function () {
    globalController.getCompanyStruct(frameModel.instance());
};

/*刷新时间*/
frameController.updateTime = 600000;
/*获取F及分组*/
frameController.getF = function () {
    $.ajax({
        type: 'get',
        url: '/f',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            var koGroups = [];
            for (var i = 0; i < data.length; i++) {
                var currGroup = data[i];
                var currKoGroup = new fGroup();
                currKoGroup.code = currGroup.code;
                currKoGroup.name = currGroup.name;
                var currFList = currGroup.list || [];
                var currKoFlist = [];
                for (var j = 0; j < currFList.length; j++) {
                    var currF = currFList[j];
                    var currKoF = new f();
                    currKoF.name = currF.name;
                    currKoF.icon = '/img/frame/f/'+ currF.rawCode+'.ico';
                    currKoF.url = currF.code;
                    currKoFlist.push(currKoF);
                }
                currKoGroup.fList(currKoFlist);
                koGroups.push(currKoGroup);
            }
            frameModel.instance().fGroups(koGroups);
        },
        error: function () {
            console.log("获取F及分组错误信息");
        },
        complete: function () {

        }
    });
};




frameController.alarmTimeOut;
/*报警信息*/


/*天气信息*/

/*获取用户信息*/
frameController.getUserInfo = function (call) {
    $.ajax({
        type: 'get',
        url: '/gsu',
        dataType: 'json',
        success: function (data) {
            // console.log(data);
            data = (data || {}).content || {};
            var currKoUser = new user();
            currKoUser.id = data.id;
            currKoUser.userId = data.userId;
            currKoUser.name = data.name == '--' ? currKoUser.userId : data.name;

            var headIcon = data.icon.indexOf('?') > -1 ? data.icon + '&' + new Date().getTime() :
                data.icon + '?' + new Date().getTime();
            currKoUser.icon = headIcon;
            currKoUser.email = cloudGlobal.parsVal(data.email);
            currKoUser.phone = cloudGlobal.parsVal(data.phone);
            currKoUser.isSuper = data.isSuper;
            currKoUser.isAdmin = data.isAdmin;
            var currStruct = new companyStruct();
            currStruct.name = cloudGlobal.parsVal(data.structName);
            currStruct.id = data.structId;
            currKoUser.struct(currStruct);
            currKoUser.roles(data.roles || []);
            frameModel.instance().currUser(currKoUser);
            frameModel.instance().currStruct(currStruct);
        },
        error: function () {
            console.log("获取用户信息错误信息");
        },
        complete: function () {
            if (typeof call == 'function') call();
        }
    });
};

/*编辑用户信息*/
frameController.editeUserInfo = function () {
    var currUser = frameModel.instance().currUser();
    $('#txtName input').val(currUser.name == '' ? currUser.userId : currUser.name);
    $('#txtEmail input').val(currUser.email == '--' ? '' : currUser.email);
    $('#txtPhone input').val(currUser.phone == '--' ? '' : currUser.phone);
    $('#txtStructName ').text(currUser.struct().name == '--' ? '请选择组织结构' : currUser.struct().name);
    if (!currUser.isAdmin) {
        $('.structure-tree')[0].setAttribute("disabled", "true");
    }
};

/*修改用户密码*/
frameController.updatePass = function (oldPass, newPass) {
    $('#v6s-loading').pshow();
    $.ajax({
        type: 'post',
        url: '/ups',
        dataType: 'json',
        data: {
            id: frameModel.instance().currUser().id,
            oldPass: oldPass, newPass: newPass
        },
        success: function (result) {
            result = result || {};
            switch (result.result) {
                case '-1':
                    $("#newPassword").pshowTextTip("新旧密码不可相同！");
                    break;
                case '0':
                    $("#originPassword").pshowTextTip("原密码不正确！");
                    break;
                case '1':
                    $(".prompt-success").pshow("failure", "保存失败");
                    break;
                case '2':
                    userDataHide();
                    $(".prompt-success").pshow("success", "保存成功");
                    break;
            }
        },
        error: function () {
            $('#v6s-loading').phide();
            console.log("修改密码失败信息");
        },
        complete: function () {
            $('#v6s-loading').phide();
        }
    });
};

/*个人信息修改*/
frameController.userInfoModify = function () {
    $('#v6s-loading').pshow();
    var instance = frameModel.instance();
    var name = $('#txtName').pval().ptrimHeadTail();
    var email = $('#txtEmail').pval().ptrimHeadTail();
    var phone = $('#txtPhone').pval().ptrimHeadTail();
    var structId = instance.currStruct().id;
    var structName = instance.currStruct().name;
    save();
    function save() {
        $.ajax({
            type: 'post',
            url: '/esu',
            dataType: 'json',
            data: {
                name: name, email: email, phone: phone, structId: structId,
                structName: structName, id: instance.currUser().id,
                userId: instance.currUser().userId
            },
            success: function (result) {
                result = result || {};
                if (result.result == 'success') {
                    frameController.getUserInfo(function () {
                        userDataHide();
                        $(".prompt-success").pshow('success', '个人信息修改成功');
                    });
                } else {
                    $('#v6s-loading').phide();
                    $(".prompt-success").pshow('failure', '个人信息修改失败');
                }
            },
            error: function () {
                $('#v6s-loading').phide();
                $(".prompt-success").pshow('failure', '个人信息修改失败');
            },
            complete: function () {
                $('#v6s-loading').phide();
            }
        });
    }
};

/*主建筑信息*/
frameController.getBuildInfo = function () {
    $.ajax({
        type: 'get',
        url: '/gmb',
        dataType: 'json',
        success: function (data) {
            data = data || {};
            var currKoBuild = new build();
            currKoBuild.id = data.id;
            currKoBuild.name = cloudGlobal.parsVal(data.name);

            var buildIcon = data.icon && data.icon.indexOf('?') > -1 ? data.icon + '&' + new Date().getTime()
                            : data.icon + '?' + new Date().getTime();
            currKoBuild.icon = buildIcon;
            currKoBuild.country(data.country);
            currKoBuild.province(cloudGlobal.createGtype(data.province));
            currKoBuild.city(cloudGlobal.createGtype(data.city));
            currKoBuild.region(cloudGlobal.createGtype(data.region));
            frameModel.instance().selBuild(currKoBuild);
        },
        error: function () {
            console.log("获取建筑信息失败");
        },
        complete: function () { }
    });
};

/*工作历*/

/*获取工作历的未读消息数量*/

/*获取主建筑名称及当前F名称*/
frameController.getfgmnfn = function () {
    $.ajax({
        type: 'get',
        url: '/fgmnfn',
        dataType: 'json',
        success: function (data) {
            data = data || {};
            document.title = data.fname || '';
            $('#spanTiMbname').text(data.mainName || '');
            $('#spanTiFName').text(data.fname || '');
        },
        error: function () {
            console.log("getfgmnfn错误");
            location.href =location.origin +'/PersagyCloud/login';
        },
        complete: function () {
        }
    });
};