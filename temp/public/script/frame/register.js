/*页面事件注册*/

$(function () {

    frameController.init();
    //用户    
    $(".user-title").click(function (e) {
        e.stopPropagation();
        $(".user-con").toggle();
        $(this).parent().siblings().children("[class*='-con']").hide();
        userDataHide();
    });
    //用户编辑，保存
    $(".userLi-1").click(function (e) {
        e.stopPropagation();
        $(document).click();
        $(".user-data").show();
    });
    $(".user-data-x").click(function () {
        userDataHide();
    });
    $(".user-data").click(function (e) {
        e.stopPropagation();
    });


    //用户编辑结构树

    $(".user-data .tree-title").click(function (e) {
        e.stopPropagation();
        $(this).toggleClass('show');
        var hasShow = $(this).hasClass("show");
        if (hasShow) {
            $(this).next(".tree-con").slideDown();
        } else {
            $(this).next(".tree-con").slideUp();
            $(this).next().find('.show').removeClass('show').find('.temp-con').slideUp();
            $(this).next().find('span').text('r')
        }
    });
    $(".user-data .temp-tit").click(function () {
        $(".user-data .tree-title").click();
    });
    $(".user-data-edit").click(function (e) {
        e.stopPropagation();
        $(".user-data .tree-title").removeClass('show');
        $(".user-data .tree-con").slideUp();
    });


    //修改密码
    $(".userLi-2").click(function (e) {
        e.stopPropagation();
        $(document).click();
        $(".user-password-edit").show();
    });
    $(".user-password-edit").click(function (e) {
        e.stopPropagation();
    });

    $(".password-edit-x").click(function () {
        $(".user-password-edit").hide();
    });

    //报警
    $(".alarm-title").click(function (e) {
        e.stopPropagation();
        $(".alarm-con").toggle();
        $(this).parent().siblings().children("[class*='-con']").hide();
        userDataHide();
        frameController.getAlarms();
    });
    //工作日历
    $(".jobcalendar-title").click(function (e) {
        e.stopPropagation();
        $(".jobcalendar-con").toggle();
        $(this).parent().siblings().children("[class*='-con']").hide();
        userDataHide();
        frameController.getWorkCalendar();
        frameController.getUnReadNum();
    });

    //左侧导航
    $(".v6s-h-navButton").mouseover(function () {
        $(".v6s-nav").css('left', 0);
        $(".v6s-h-navButton").removeClass("active");
    });
    $(".v6s-h-navButton").mouseout(function () {
        $(".v6s-nav").css('left', '-222px');
        $(".v6s-h-navButton").addClass("active");
    });
    $(".v6s-nav").mouseenter(function () {
        $(".v6s-nav").css('left', 0);
        $(".v6s-h-navButton").removeClass("active");
    });
    $(".v6s-nav").mouseleave(function () {
        $(".v6s-nav").css('left', '-222px');
        $(".v6s-h-navButton").addClass("active");
    });
    $(".alarm-button").click(function (e) {
        e.stopPropagation();
    });
    $(".weather-con").click(function (e) {
        e.stopPropagation();
    });
    $(".jobcalendar-con").click(function (e) {
        e.stopPropagation();
    });

    $(document).click(function () {
        dcHideTip();
    });

    /*点击密码保存成功*/
    $('.user-password-edit .edit-button').click(function (e) {
        e.stopPropagation();
        if (!$('#user-password-edit').pverifi()) {
            return;
        }

        frameController.updatePass($('#originPassword input').val(), $('#newPassword input').val());
    });
});

//隐藏提示框
function dcHideTip() {
    $(".user-con").hide();
    $(".alarm-con").hide();
    $(".jobcalendar-con").hide(0);
    $(".weather-con").hide();
    $(".user-data").hide();
    $(".user-password-edit").hide();
    $("#user-password-edit").pctlsRecover();
    $(".user-data").attr("dataSign", "select");
    $(".user-data  .tree-title").removeClass('show');
    $(".user-data  .tree-con").hide();
};

function userDataHide() {
    $(".user-data").hide();
    $(".user-password-edit").hide();
    $('#divSelfInfopp').pctlsRecover();
}
/*验证密码*/
function verifyPassword() {
    var reg = /^[0-9a-zA-Z_]{6,10}$/;
    var newPass = $('#newPassword').pval();
    var againPass = $('#againPassword').pval();
    if(newPass.length == 0) {
        return $('#newPassword').pshowTextTip('请输入新密码！');
    }else if(!reg.test(newPass)){
        return $('#newPassword').pshowTextTip('仅支持6到10位数字、字母、下划线');
    }
    if (newPass != againPass) {
        return $('#againPassword').pshowTextTip('再次输入内容需与新密码一致！');
    }
}

/*新密码框获取焦点时隐藏密码不一致的提示*/
function hidePassTip() {
    document.getElementById('againPassword').phideTextTip();
};

//头部工作历跳转
function headerWorkHref() {
    dcHideTip();
    setTimeout(function () {
        window.open(frameModel.instance().selWork().url, '_blank');
    }, 0);
};

//头部全局报警跳转
function headerAlarmHref() {
    dcHideTip();
    setTimeout(function () {
        window.open(frameModel.instance().alarmUrl(), '_blank');
    }, 0);
};

function windowClose(event) {
    if (event.clientX <= 0 && event.clientY < 0) {
        $.ajax({
            type: 'get',
            url: '/pexit'
        });
    }
};