/*工作历信息*/
function gworkCalendar() {
    this.specialTags = ko.observableArray(); /*特殊日标签名称 字符串数组*/
    this.isWork = ko.observable(true); /*是否是工作日*/
    this.season = null; /*所属季节*/
    this.businessTime = ko.observableArray(); /*营业时间数组*/
}

/*报警信息*/
function galarm() {
    this.reason = null; /*报警原因*/
    this.position = null; /*报警位置*/
    this.time = null; /*报警时间*/
}

/*F分组即产品线*/
function fGroup() {
    this.code = null;
    this.name = null;
    this.sort = null; //顺序号
    this.isDisabled = false; //true 禁用     false 启用
    this.isSwitch = true; //是否可开启或关闭  true 可以   false 不可以
    this.fList = ko.observableArray();
}

/*F*/
function f() {
    this.name = null;
    this.icon = null;
    this.url = null;
    this.code = null;
    this.isEditUrl = true; //true 可编辑url   false 不可编辑url

}

/*文档*/
function file() {
    this.id = null;
    this.name = null;
    this.url = null;
};

/*建筑功能类型、省份、城市、城市区域、空调类型、外保温、采暖类型、建筑结构类型、经济指标、朝向*/
function gtype() {
    this.code = null;
    this.name = null;
    this.parentCode = null;
    this.child = [];
    /*一级功能类型的选择事件*/
    this.functionTypeSel = function (obj, event) {
        var instance = setModel.instance();
        var child = ko.toJS(obj.child || []);

        var disabled = child.length == 0 ? true : false;
        $('#childBuildTypeName').pdisable(disabled);

        var gt = new gtype();
        gt.name = '请选择二级建筑功能类型';
        child.splice(0, 0, gt);

        instance.childBuildTypes(child);
        $('#childBuildTypeName').psel(0);
    };

    /*二级功能类型的选择事件*/
    this.functionChildTypeSel = function (obj, event) {};

    /*省选择事件*/
    this.provinceSel = function (obj, event) {
        var instance = setModel.instance();
        var child = ko.toJS(obj.child || []);

        var disabled = child.length == 0 ? true : false;
        $('#buildCity').pdisable(disabled);

        var gt = new gtype();
        gt.name = '请选择城市';
        child.splice(0, 0, gt);

        instance.citys(child);
        $('#buildCity').psel(0);
    };

    /*市选择事件*/
    this.citySel = function (obj, event) {
        var instance = setModel.instance();
        var child = ko.toJS(obj.child || []);

        var disabled = child.length == 0 ? true : false;
        $('#buildRegion').pdisable(disabled);

        var gt = new gtype();
        gt.name = '请选择城市区域';
        child.splice(0, 0, gt);

        instance.region(child);
        $('#buildRegion').psel(0);
    };

    /*城市区域选择事件*/
    this.regionSel = function (obj, event) {
        if (setModel.instance().selBuild().auto == false) return;
        var longitude = obj.longitude;
        var longitude2 = longitude < 0 ? 1 : 0; //小于零代表西经，对应第二项。否则，代表东经，对应第一项
        longitude = Math.abs(longitude) || '';
        $('#buildLongitude input').val(longitude);
        $('#divCmLongitude').psel(longitude2);
        $('#buildLongitude').phideTextTip();

        var latitude = obj.latitude;
        var latitude2 = latitude < 0 ? 1 : 0; //小于零代表南纬，对应第二项。否则代表北纬，对应第一项
        latitude = Math.abs(latitude) || '';
        $('#buildLatitude input').val(latitude);
        $('#divCmLatitude').psel(latitude2);
        $('#buildLatitude').phideTextTip();

        $('#buildAltitude input').val(obj.altitude || obj.altitude == 0 ? obj.altitude : '');
        if (obj.altitude || obj.altitude == 0) $('#buildAltitude').phideTextTip();
    };
};

/*建筑信息*/
function gbuild() {
    this.id = null;
    this.name = null;
    this.isMain = false; //是否是主建筑 true 是    false 否
    this.pFunctionTypeName = '请选择建筑功能类型';
    this.functionTypeName = '请选择二级建筑功能类型';
    this.blueprint1Arr = [];
    this.blueprint2Arr = [];
    this.blueprint3Arr = [];
    this.blueprint4Arr = [];
    this.blueprint5Arr = [];
    this.blueprint6Arr = [];
    this.blueprint7Arr = [];
    this.blueprint8Arr = [];
    this.country = ko.observable(); /*国家*/
    this.buildType = new gtype(); //建筑功能类型
    this.childBuildType = new gtype(); //二级建筑功能类型
    this.useTime = null; //使用时间
    this.province = ko.observable(new gtype()); /*省*/
    this.city = ko.observable(new gtype()); /*市*/
    this.region = ko.observable(new gtype()); /*城市内的分区*/
    this.climateRegion = ko.observable(new gtype()); //气候区
    this.longitude = null; //经度
    this.latitude = null; //纬度
    this.altitude = null; //海拔
    this.direction = new gtype(); //朝向
    this.smallImg = null; //小图
    this.bigImg = null; //大图
    this.remark = null; //简介
    this.totalArea = null; //总面积
    this.airArea = null; //空调面积
    this.parkArea = null; //停车场面积
    this.equipmentArea = null; //设备机房面积
    this.floorUpArea = null; //地上面积
    this.floorDownArea = null; //地下面积
    this.floorNum = null; //建筑总层数
    this.floorUpNum = null; //地上层数
    this.floorDownNum = null; //地下层数
    this.height = null; //建筑总高度
    this.floorArea = null; //建筑占地面积
    this.airType = new gtype(); //空调类型
    this.heatingType = new gtype(); //采暖类型
    this.unitAreaCold = null; //单位面积设计冷量
    this.unitAreaHot = null; //单位面积设计热量
    this.unitAreaPower = null; //单位面积配电设计容量
    this.structType = new gtype(); //建筑结构类型
    this.antiScale = null; //抗震设防烈度
    this.figureRatio = null; //建筑体系系数
    this.hotIndicator = null; //外围护结构热指标
    this.warm = new gtype(); //外保温
    this.buildPlanFiles = ko.observableArray(); //建筑规划文档与图纸
    this.structFiles = ko.observableArray(); //结构文档与图纸
    this.hvFiles = ko.observableArray(); //暖通文档与图纸
    this.powerFiles = ko.observableArray(); //强电文档与图纸
    this.lowFiles = ko.observableArray(); //弱电文档与图纸
    this.lightFiles = ko.observableArray(); //照明文档与图纸
    this.fireFiles = ko.observableArray(); //消防文档与图纸
    this.autoFiles = ko.observableArray(); //自动化系统文档与图纸
    this.priceCase = []; //建筑价格方案,每项均是energyPrice对象
    this.buildInfo = ko.computed({
        owner: this,
        read: function () {
            return cloudGlobal.parsVal(this.city(), this.region());
        }
    })
};

/*建筑能源类型价格方案*/
function energyPrice() {
    //0电  1燃气   2燃油   3自来水  4供冷   5供热   6生活热水   7蒸汽
    this.type = null;
    this.prices = []; //每项均是price对象
};
/*建筑价格方案*/
function price() {
    this.id = null;
    this.startTime = null; //开始使用时间
    this.type = null; //0 平均    1 分时     2 阶梯
    this.cycle = null; //阶梯周期，阶梯电价专用
    this.prices = []; //每项均是pricePartial对象
};

function pricePartial() {
    this.id = null;
    this.start = null; //开始时间或开始用量
    this.end = null; //结束时间或结束用量
    this.price = null; //此段内的价格
};

/*用户信息*/
function user() {
    this.id = null;
    this.userId = null;
    this.name = null;
    this.icon = null;
    this.email = null;
    this.phone = null;
    this.isSuper = false;
    this.isAdmin = false;
    this.struct = ko.observable(new companyStruct());
    this.roles = ko.observableArray(); /*用户角色数组*/
    /*当前用户的产品角色数组  设置用*/
    this.cpRoles = ko.observableArray();
    /*用户列表选择事件  设置用*/
    this.sel = function (obj, event) {
        var instance = setModel.instance();
        instance.selUser(new user());
        instance.selUser(obj);
        frameModel.instance().currStruct(obj.struct());
        userSetGrilFloatShow(event);
    };
};

/*组织结构*/
function companyStruct() {
    this.id = null;
    this.parentId = null;
    this.name = null;
    this.childs = [];
    /*框架用组织结构展开*/
    this.clickSpan = function (o, e) {
        e.stopPropagation();
        var $treeTemp = $(e.target).parent().parent(), //('.tree-temp'),
            $tit = $(e.target).parent('.temp-tit'),
            $icon = $tit.next('.temp-con'),
            isShow = $treeTemp.hasClass('show');
        $treeTemp.toggleClass('show', !$treeTemp.hasClass('show'));
        if (isShow) {
            $treeTemp.find('.show').removeClass('show').find('.temp-con').slideUp();
            $icon.slideUp().children().find('span').text('r');
            $(e.target).text('r');
        } else {
            $icon.slideDown();
            $(e.target).text('b');
        }
    };
    /*框架用选择*/
    this.lineSel = function (obj, event) {
        frameModel.instance().currStruct(obj);
    };

    /*组织结构设置 添加*/
    this.add = function (obj, event) {
        var struct = new companyStruct();
        struct.id = new Date().getTime();
        struct.parentId = obj.id;
        var oldChilds = obj.childs;
        oldChilds.push(struct);
        obj.childs = oldChilds;
        var oldStruct = setModel.instance().structsManEdit();
        setModel.instance().structsManEdit(oldStruct);
        settingController.setStructWidth();
    };

    /*组织结构设置 移除*/
    this.remove = function (obj, event) {
        if (obj.childs.length > 0) {
            $('#v6s-setwrap-warning').pshow([del], '您确定要删除此组织吗？', '删除后，其下级组织将被一起删除');
        } else del();

        function del() {
            var oldStruct = setModel.instance().structsManEdit();
            oldStruct.childs = delChild(oldStruct.childs);

            setModel.instance().structsManEdit(oldStruct);
            settingController.setStructWidth();
        }

        function delChild(childs) {
            for (var i = 0; i < childs.length; i++) {
                var currChild = childs[i];
                if (currChild.id == obj.id) {
                    childs.splice(i, 1);
                    return childs;
                }
                var nextChilds = arguments.callee(currChild.childs);
                if (nextChilds) {
                    currChild.childs = nextChilds;
                    return childs;
                }
            }
        };
    };

    /*组织结构输入框获取焦点时隐藏错误提示*/
    this.focusH = function (obj, event) {
        $(event.currentTarget).next().hide();
        $(event.currentTarget).removeClass("error");

    };
};