/*公共的js*/
function cloudGlobal() { }

/*
*objPar:{vals:['',''],separator:'默认无分隔符',unit:''}
*/
cloudGlobal.parsVal = function (objPar) {
    if (!objPar) return '--';
    var separator = typeof arguments[0] != 'object' ? '' : (objPar.separator || '');
    var vals = typeof arguments[0] != 'object' ? arguments : (objPar.vals || []);
    var unit = typeof arguments[0] != 'object' ? '' : (objPar.unit || '');
    var returnStr = '';
    for (var i = 0; i < vals.length; i++) {
        var currVal = vals[i] || '';
        returnStr += currVal.toString() + separator;
    }
    returnStr = separator ? returnStr.substr(0, returnStr.length - 1) : returnStr.substr(0);
    returnStr = returnStr ? returnStr + unit : returnStr;
    return returnStr || '--';
};

/*objPar:{id:'',name:''}*/
cloudGlobal.createGtype = function (objPar) {
    var gt = new gtype();
    if (!objPar) return gt;
    if (typeof objPar == 'string') objPar = { name: objPar };
    gt.id = objPar.id;
    gt.name = cloudGlobal.parsVal(objPar.name);
    return gt;
};