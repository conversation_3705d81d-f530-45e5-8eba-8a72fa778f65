/*逻辑控制 html事件会调用此js中的方法*/
function globalController() { }

/*获取组织结构*/
globalController.getCompanyStruct = function (instance, call) {
    function createStruct(structs) {
        var koCompanyStruct = [];
        for (var i = 0; i < structs.length; i++) {
            var currCompanyStruct = structs[i];
            var currKoCompanyStruct = new companyStruct();
            currKoCompanyStruct.id = currCompanyStruct.id;
            currKoCompanyStruct.name = currCompanyStruct.name;
            var currKoChilds = [];
            if ((currCompanyStruct.childs || []).length > 0)
                currKoChilds = arguments.callee(currCompanyStruct.childs);
            currKoCompanyStruct.childs = currKoChilds;
            koCompanyStruct.push(currKoCompanyStruct);
        }
        return koCompanyStruct;
    }
    $.ajax({
        type: 'get',
        url: '/fstructs',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            var createCompanyStruct = createStruct(data);

            var currKoCompanyStruct = new companyStruct();
            currKoCompanyStruct.name = '请选择组织结构';
            createCompanyStruct.splice(0, 0, currKoCompanyStruct);

            instance.structs(createCompanyStruct);
            if (typeof call == 'function') call();
        }
    });
};

/*上传文件*/
globalController.uploadFile = function (objParam) {
    var formData = new FormData();
    formData.append('file', objParam.file);
    formData.append('type', objParam.type);
    formData.append('uploadServer', objParam.uploadServer == false ? false : true);
    $.ajax({
        type: 'post',
        url: '/upload',
        processData: false,
        contentType: false,
        data: formData,
        success: function (result) {
            if (result.result != 'success') return typeof objParam.errCall == 'function' ? objParam.errCall() : '';
            typeof objParam.successCall == 'function' ? objParam.successCall(result) : '';
        },
        error: function (err) {
            typeof objParam.errCall == 'function' ? objParam.errCall() : '';
        }, complete: function () {
            typeof objParam.completeCall == 'function' ? objParam.completeCall() : '';
        }
    });
};