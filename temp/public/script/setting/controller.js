/*逻辑控制 html事件会调用此js中的方法*/
function settingController() { }
settingController.createGtype = function (arr) {
    var returnArr = [];
    for (var i = 0; i < arr.length; i++) {
        var curr = arr[i];
        var gt = new gtype();
        for (var cuo in curr) {
            if (curr.hasOwnProperty(cuo) == false) continue;
            gt[cuo] = curr[cuo];
        }
        gt.child = arguments.callee(curr.child || []);
        returnArr.push(gt);
    }
    return returnArr;
};
settingController.init = function () {
    var instance = setModel.instance();
    ko.applyBindings(instance, document.getElementById('v6s-setwrap'));
    settingController.getPsftps();
    settingController.getPspris();
    settingController.getPseils();
    settingController.getPsotls();
    settingController.getPsairs();
    settingController.getPshtls();
    settingController.getPsstls();
    settingController.getPsetis();
    settingController.initBuild();
};

settingController.initBuild = function () {
    settingController.getBuilds();
};

settingController.initF = function () {
    settingController.getAllF();
};

settingController.initUser = function () {
    settingController.getAllUsers();
    settingController.getCpRoles();
    settingController.getCompanyStruct();
};

/*计算组织结构某一级的最多个数，以便给组织结构管理内的元素赋宽*/
settingController.setStructWidth = function () {
    var struct = setModel.instance().structsManEdit();
    var maxNum = struct.childs.length;
    compute(struct.childs);
    function compute(structs) {
        for (var i = 0; i < structs.length; i++) {
            var currStruct = structs[i];
            maxNum = currStruct.childs.length > maxNum ? currStruct.childs.length : maxNum;
            arguments.callee(currStruct.childs);
        }
    };
};

/*初始化组织结构  用于组织结构管理编辑*/
settingController.initStructs = function () {
    var struct = setModel.instance().structsMan();
    var newStruct = ko.toJS(struct);
    setModel.instance().structsManEdit(newStruct);
    settingController.setStructWidth();
};

/*获取组织结构 用于组织结构管理*/
settingController.getCompanyStruct = function (call) {
    function createStruct(structs) {
        var koCompanyStruct = [];
        for (var i = 0; i < structs.length; i++) {
            var currCompanyStruct = structs[i];
            var currKoCompanyStruct = new companyStruct();
            currKoCompanyStruct.id = currCompanyStruct.id;
            currKoCompanyStruct.name = currCompanyStruct.name;
            currKoCompanyStruct.parentId = currCompanyStruct.parentId;
            var currKoChilds = [];
            if ((currCompanyStruct.childs || []).length > 0)
                currKoChilds = arguments.callee(currCompanyStruct.childs);
            currKoCompanyStruct.childs = currKoChilds;
            koCompanyStruct.push(currKoCompanyStruct);
        }
        return koCompanyStruct;
    }
    $.ajax({
        type: 'get',
        url: '/fstructs',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            var createCompanyStruct = createStruct(data);
            var newStruct = createCompanyStruct[0];
            if (!newStruct) {
                newStruct = new companyStruct();
                newStruct.name = '一级结构';
            }
            setModel.instance().structsMan(newStruct);
        }, complete: function () {
            if (typeof call == 'function') call();
        }
    });
};

/*保存组织结构*/
settingController.structSave = function () {
    $('#v6s-loading').pshow();
    var departs = [];
    var struct = setModel.instance().structsManEdit();
    souji(struct);

    function souji(currStruct) {
        departs.push({
            id: currStruct.id,
            name: currStruct.name,
            parentId: currStruct.parentId
        });
        for (var i = 0; i < currStruct.childs.length; i++) {
            arguments.callee(currStruct.childs[i]);
        }
    };

    $.ajax({
        type: 'post',
        url: '/pupstructs',
        data: { departs: departs },
        dataType: 'json',
        success: function (data) {
            data = data || {};
            if (data.result != 'success') return $('#v6s-loading').phide(), $('#pcTip').pshow('failure', '保存失败');
            frameController.initStructs();
            frameController.getUserInfo();
            settingController.getCompanyStruct(function () {
                settingController.getAllUsers(function () {
                    setModel.instance().isEditStruct(false);
                    $('#v6s-loading').phide();
                    $('#divTreeEditCancel').click();
                    $('#pcTip').pshow('success', '保存成功');
                });
            });
        },
        error: function (err) {
            console.log("structSave err：" + err);
            $('#v6s-loading').phide();
            $('#pcTip').pshow('failure', '保存失败');
        },
        complete: function () { }
    });
};

/*获取所有的功能类型，内含子级*/
settingController.getPsftps = function () {
    $.ajax({
        type: 'get',
        url: '/psftps',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            var instance = setModel.instance();
            var types = settingController.createGtype(data);

            var gt = new gtype();
            gt.name = '请选择建筑功能类型';
            types.splice(0, 0, gt);

            instance.buildTypes(types);
        },
        error: function (err) {
            console.log("getPsftps err：" + err);
        },
        complete: function () { }
    });
};

/*获取所有的省份，内含子级*/
settingController.getPspris = function () {
    $.ajax({
        type: 'get',
        url: '/pspris',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            var instance = setModel.instance();
            var provinces = settingController.createGtype(data);

            var gt = new gtype();
            gt.name = '请选择省份';
            provinces.splice(0, 0, gt);

            instance.provinces(provinces);
        },
        error: function (err) {
            console.log("getPspris err：" + err);
        },
        complete: function () { }
    });
};

/*获取所有的经济指标，内含价格类型*/
settingController.getPseils = function () {
    $.ajax({
        type: 'get',
        url: '/pseils',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            var instance = setModel.instance();
            for (var i = 0; i < data.length; i++) {
                data[i].tempPrices = ko.observableArray();
                data[i].prices = ko.observableArray();
                data[i].unit = ko.observable(data[i].unit);
                data[i].index = i;
            }
            instance.economicIndicator(data);
        },
        error: function (err) {
            console.log("getPseils err：" + err);
        },
        complete: function () { }
    });
};

/*获取所有的朝向*/
settingController.getPsotls = function () {
    $.ajax({
        type: 'get',
        url: '/psotls',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            var instance = setModel.instance();
            instance.directions(data);
        },
        error: function (err) {
            console.log("getPsotls err：" + err);
        },
        complete: function () { }
    });
};

/*获取所有的空调类型*/
settingController.getPsairs = function () {
    $.ajax({
        type: 'get',
        url: '/psairs',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            data.splice(0, 0, {
                name: '请选择空调类型'
            });
            var instance = setModel.instance();
            instance.airTypes(data);
        },
        error: function (err) {
            console.log("getPsairs err：" + err);
        },
        complete: function () { }
    });
};

/*获取所有的采暖类型*/
settingController.getPshtls = function () {
    $.ajax({
        type: 'get',
        url: '/pshtls',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            data.splice(0, 0, {
                name: '请选择采暖类型'
            });
            var instance = setModel.instance();
            instance.heatingTypes(data);
        },
        error: function (err) {
            console.log("getPshtls err：" + err);
        },
        complete: function () { }
    });
};

/*获取所有的建筑结构类型*/
settingController.getPsstls = function () {
    $.ajax({
        type: 'get',
        url: '/psstls',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            data.splice(0, 0, {
                name: '请选择建筑结构类型'
            });
            var instance = setModel.instance();
            instance.structTypes(data);
        },
        error: function (err) {
            console.log("getPsstls err：" + err);
        },
        complete: function () { }
    });
};

/*获取所有的外保温类型*/
settingController.getPsetis = function () {
    $.ajax({
        type: 'get',
        url: '/psetis',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            data.splice(0, 0, {
                name: '请选择有无外保温'
            });
            var instance = setModel.instance();
            instance.externalTypes(data);
        },
        error: function (err) {
            console.log("getPsetis err：" + err);
        },
        complete: function () { }
    });
};

/*判断建筑ID是否重复*/
settingController.buildIdValid = function (id, call) {
    var oldId = setModel.instance().selBuild().oldId;
    if (oldId && oldId == id) return fn();
    $.ajax({
        type: 'get',
        url: '/buildidvalid',
        data: { id: id },
        dataType: 'json',
        success: function (data) {
            data = data || {};
            if (data.result == false) $('#buildId').pshowTextTip('建筑ID已存在！');
        },
        error: function (err) {
            console.log("buildidvalid err：" + err);
        },
        complete: function () {
            fn();
        }
    });
    function fn() {
        if (typeof call == 'function') call();
    }
};

/*判断建筑名称是否重复*/
settingController.buildNameValid = function (name, call) {
    var oldName = setModel.instance().selBuild().oldName;
    if (oldName && oldName == name) return fn();
    $.ajax({
        type: 'get',
        url: '/buildnamevalid',
        dataType: 'json',
        data: { name: name },
        success: function (data) {
            data = data || {};
            if (data.result == false) $('#buildName').pshowTextTip('建筑名称已存在！');
        },
        error: function (err) {
            console.log("buildidvalid err：" + err);
        },
        complete: function () {
            fn();
        }
    });
    function fn() {
        if (typeof call == 'function') call();
    }
};

/*获取所有的建筑*/
settingController.getBuilds = function (call) {
    $('#v6s-loading').pshow();
    $.ajax({
        type: 'get',
        url: '/sbuilds',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            var koBuilds = [];
            for (var i = 0; i < data.length; i++) {
                var currBuilds = data[i];
                currBuilds.showProvince =
                    cloudGlobal.parsVal(currBuilds.provinceName, currBuilds.cityName, currBuilds.partitionName);
                currBuilds.buildingShowTime = !currBuilds.buildingTime ? '--' :
                    new Date(currBuilds.buildingTime.replace(/\-/g, '/')).format('y年M月d日');
            }
            var instance = setModel.instance();
            instance.builds(data);
        },
        error: function () {
            console.log("获取所有建筑信息失败");
        },
        complete: function () {
            $('#v6s-loading').phide();
            typeof call == 'function' ? call() : '';
        }
    });
};

/*删除建筑*/
settingController.deleteBuilds = function () {
    $('#v6s-loading').pshow();
    var id = setModel.instance().selBuild().id;
    $.ajax({
        type: 'post',
        url: '/sdelbuild',
        data: { id: id },
        dataType: 'json',
        success: function (result) {
            result = result || {};
            if (result.result == 'success') {
                settingController.getBuilds(function () {
                    setSelectBulidHide();
                    $('#pcTip').pshow('failure', '建筑删除成功');
                });
            } else $('#v6s-loading').phide(), $('#pcTip').pshow('failure', '建筑删除失败');
        },
        error: function () {
            $('#v6s-loading').phide();
            $('#pcTip').pshow('failure', '建筑删除失败');
        },
        complete: function () { }
    });
};
/*新增建筑或修改建筑*/
settingController.saveBuild = function (build) {
    $('#v6s-loading').pshow();
    var selBuild = setModel.instance().selBuild();
    var url = selBuild.oldId == null ? '/saddbuild' : '/seditbuild';
    $.ajax({
        type: 'post',
        url: url,
        data: { build: JSON.stringify(build) },
        dataType: 'json',
        success: function (result) {
            result = result || {};
            if (result.result == 'success') {
                settingController.getBuilds(function () {
                    $('#divBuildBack').attr('sign', true);
                    $('#divBuildBack').click();
                    $('#pcTip').pshow('success', '建筑保存成功');
                });
                if (build.isMain == true) {
                    frameController.getBuildInfo();
                    frameController.getfgmnfn();
                }
            } else $('#v6s-loading').phide(), $('#pcTip').pshow('failure', '建筑保存失败');
        },
        error: function () {
            $('#v6s-loading').phide();
            $('#pcTip').pshow('failure', '建筑保存失败');
        },
        complete: function () { }
    });
};


/*上传建筑图片*/
settingController.uploadBuildImg = function (file, imgTarget) {
    globalController.uploadFile({
        file: file, type: 'build', uploadServer: false,
        successCall: function (result) {
            result = result || {};
            if (result.result != 'success') return $('#pcTip').pshow('failure', '建筑图片上传失败');
            imgTarget = $(imgTarget);
            // imgTarget.attr('src', result.url);
            imgTarget.css('background-image', 'url(' + result.url + ')');
            imgTarget.attr('newFile', true);
            imgTarget.attr('fpath', result.path);
            //imgTarget.next().show();
        }, errCall: function () {
            $('#pcTip').pshow('failure', '建筑图片上传失败');
        }
    });
};

/*上传建筑文档*/
settingController.uploadBuildDoc = function (ulTarget, fileTarget) {
    var file = fileTarget.files[0];
    fileTarget.value = '';
    globalController.uploadFile({
        file: file, type: 'build', uploadServer: false,
        successCall: function (result) {
            result = result || {};
            if (result.result != 'success') return $('#pcTip').pshow('failure', '建筑文档上传失败');
            var liStr = '<li newFile="true" fpath="' + result.path + '" fn="' +
                result.name + '"><b>x</b><em title="' + result.name + '">' + result.name + '</em></li>';
            var jqUl = $(ulTarget);
            var liLength = jqUl.find('li').filter(':visible').length + 1;
            jqUl.append(liStr);
            $(fileTarget).next().text(liLength + '/50');
            if (liLength >= 50) {
                var label = jqUl.parent().prev().find('label')[0];
                label.setAttribute("disabled", "true");
            }
        }, errCall: function () {
            $('#pcTip').pshow('failure', '建筑文档上传失败');
        }
    });
};

/*获取所有F*/
settingController.getAllF = function (call) {
    $('#v6s-loading').pshow();
    $.ajax({
        type: 'get',
        url: '/sallf',
        dataType: 'json',
        success: function (data) {
            data = data || [];
            var koGroups = [];
            for (var i = 0; i < data.length; i++) {
                var currGroup = data[i];
                var currKoGroup = new fGroup();
                currKoGroup.name = currGroup.name;
                currKoGroup.isDisabled = currGroup.isDisabled;
                currKoGroup.code = currGroup.code;
                currKoGroup.sort = currGroup.sort;
                currKoGroup.isSwitch = currGroup.isSwitch;
                var currFList = currGroup.list || [];
                var currKoFlist = [];
                for (var j = 0; j < currFList.length; j++) {
                    var currF = currFList[j];
                    var currKoF = new f();
                    currKoF.name = currF.name;
                    var ficon = currF.icon.indexOf('?') > -1 ? currF.icon + '&' + new Date().getTime()
                                : currF.icon + '?' + new Date().getTime();
                    currKoF.icon = ficon;
                    currKoF.url = currF.url;
                    currKoF.code = currF.rawCode;
                    currKoF.isEditUrl = currF.isEditUrl;
                    currKoFlist.push(currKoF);
                }
                currKoGroup.fList(currKoFlist);
                koGroups.push(currKoGroup);
            }
            setModel.instance().fGroups(koGroups);
        },
        error: function () {
            console.log("获取所有F信息失败");
        },
        complete: function () {
            $('#v6s-loading').phide();
            if (typeof call == 'function') call();
        }
    });
};
/*更新产品线状态*/
settingController.updateCpstate = function (code, state, call) {
    $('#v6s-loading').pshow();
    $.ajax({
        type: 'post',
        url: '/updatecpstate',
        dataType: 'json',
        data: { code: code, state: state },
        success: function (result) {
            result = result || {};
            if (result.result == "success") {
                frameController.getF();
                codeChangeState(code, state);
            }
        },
        error: function (e) {

        },
        complete: function () {
            $('#v6s-loading').phide();
            if (typeof call == 'function') call();
        }
    });
};

/*更新产品线顺序*/
settingController.updateCpsort = function (cparr, call) {
    $('#v6s-loading').pshow();
    var resultState = 'failure';
    var mess = '产品线顺序更新失败';
    $.ajax({
        type: 'post',
        url: '/pupdatecpsort',
        dataType: 'json',
        data: { cparr: cparr },
        success: function (result) {
            result = result || {};
            if (result.result == "success") {
                frameController.getF();
                resultState = 'success';
                mess = '产品线顺序更新成功';
            }
        },
        error: function (e) {
            $('#v6s-loading').phide();
        },
        complete: function () {
            settingController.getAllF(function () {
                $("#pcTip").pshow(resultState, mess);
                if (typeof call == 'function') call();
            });
        }
    });
};

/*批量修改F的url*/
settingController.updataFUrl = function (farr, successCall) {
    $('#v6s-loading').pshow();
    var resultState = 'failure';
    var mess = '保存失败';
    $.ajax({
        type: 'post',
        url: '/pupdatafurl',
        dataType: 'json',
        data: { farr: farr },
        success: function (result) {
            if (result.result != 'success') {
                return $('#v6s-loading').phide(), $("#pcTip").pshow('failure', '保存失败');
            }
            frameController.getF();
            settingController.getAllF(function () {
                $("#pcTip").pshow('success', '保存成功');
                if (typeof successCall == 'function') successCall();
            });
        },
        error: function (e) {
            $('#v6s-loading').phide();
            $("#pcTip").pshow('failure', '保存失败');
        },
        complete: function () {
        }
    });
};
/*批量修改F名称*/
settingController.updataFName = function (farr, successCall) {
    $('#v6s-loading').pshow();
    var resultState = 'failure';
    var mess = '保存失败';
    $.ajax({
        type: 'post',
        url: '/pupdatafname',
        dataType: 'json',
        data: { farr: farr },
        success: function (result) {
            if (result.result != 'success') {
                return $('#v6s-loading').phide(), $("#pcTip").pshow('failure', '保存失败');
            }

            frameController.getF();
            settingController.getAllF(function () {
                $("#pcTip").pshow('success', '保存成功');
                if (typeof successCall == 'function') successCall();
            });
        },
        error: function (e) {
            $('#v6s-loading').phide();
            $("#pcTip").pshow('failure', '保存失败');
            console.log("pupdatafname err" + e);
        },
        complete: function () {
        }
    });
};
/*新增F*/
settingController.newAddF = function (code, cpcode, name, icon, url) {
    $('#v6s-loading').pshow();
    var resultState = 'failure';
    var mess = '保存失败';
    $.ajax({
        type: 'post',
        url: '/paddf',
        dataType: 'json',
        data: { code: code, cpcode: cpcode, name: name, icon: icon, url: url },
        success: function (result) {
            if (result.result == 'success') {
                resultState = 'success';
                mess = '保存成功';
                frameController.getF();
                $(".functionSet-float").css("right", "-531px");
                $('#functionSet').pctlsRecover();
                functionSetFloatHide();//成功后收起 wyy
            }
        },
        error: function (e) {
            console.log("paddf err" + e);
        },
        complete: function () {
            $('#v6s-loading').phide();
            settingController.getAllF(function () {
                $("#pcTip").pshow(resultState, mess);
            });
        }
    });
};
/*获取所有启用的产品线及其下属角色*/
settingController.getCpRoles = function () {
    $.ajax({
        type: 'get',
        url: '/pcproles',
        success: function (result) {
            result = result || [];
            setModel.instance().allCproles(result);
        }, error: function (e) {
            console.log('getCpRoles err：' + e);
        }
    });
};

/*获取所有的用户*/
settingController.getAllUsers = function (call) {
    $('#v6s-loading').pshow();
    $.ajax({
        type: 'get',
        url: '/pusers',
        success: function (result) {
            result = result || [];
            var koUserArr = [];
            for (var i = 0; i < result.length; i++) {
                var currUser = result[i];
                var koUser = new user();
                koUser.id = currUser.id;
                koUser.userId = currUser.userId;
                koUser.name = currUser.name;
                koUser.email = cloudGlobal.parsVal(currUser.email);
                koUser.phone = cloudGlobal.parsVal(currUser.phone);
                var koStruct = new companyStruct();
                koStruct.id = currUser.structId;
                koStruct.name = cloudGlobal.parsVal(currUser.structName);
                koUser.struct(koStruct);
                koUser.cpRoles(currUser.roles);
                koUserArr.push(koUser);
            }
            setModel.instance().users(koUserArr);
        },
        error: function (e) {
            console.log('getAllUsers err:' + e);
        }, complete: function () {
            $('#v6s-loading').phide();
            if (typeof call == 'function') call();
        }
    });
};

/*删除用户*/
settingController.delUser = function (id) {
    $('#v6s-loading').pshow();
    var instance = setModel.instance();
    $.ajax({
        type: 'get',
        url: '/pdeluser',
        data: { id: instance.selUser().id },
        success: function (result) {
            result = result || {};
            if (result.result != 'success')
                return $('#v6s-loading').phide(), $('#pcTip').pshow('failure', '删除失败');
            settingController.getAllUsers(function () {
                userSetGrilFloatHide();
                instance.selUser(new user());
                $('#pcTip').pshow('success', '删除成功');
            });
            if (frameModel.instance().currUser().id == instance.selUser().id) {
                window.location.href = "/pexit";
            }

        }, error: function () {
            $('#v6s-loading').phide();
            $('#pcTip').pshow('failure', '删除失败');
        }
    });
};
/*判断用户名是否重复*/
settingController.userNameValid = function (call) {
    $.ajax({
        type: 'post',
        url: '/pvaliduid',
        dataType: 'json',
        data: { userId: $('#txtUserId').pval().ptrimHeadTail() },
        success: function (result) {
            result = result || {};
            if (result.result == '0')
                return $('#v6s-loading').phide(), $('#txtUserId').pshowTextTip('用户名已存在！');
            if (typeof call == 'function') call();
        },
        error: function (err) {
            console.log("userNameValid err：" + err);
            $('#v6s-loading').phide();
        },
        complete: function () { }
    });
};

/*判断用户姓名是否重复*/
settingController.userRelyNameValid = function (call) {
    var name = $('#txtUserName').pval().ptrimHeadTail();
    if (!name) {
        if (typeof call == 'function') call();
        return;
    }
    $.ajax({
        type: 'post',
        url: '/pvalidrelyname',
        dataType: 'json',
        data: {
            name: name, id: setModel.instance().selUser().id
        },
        success: function (result) {
            result = result || {};
            if (result.result != 1)
                return $('#v6s-loading').phide(), $('#txtUserName').pshowTextTip('用户姓名已存在！');
            if (typeof call == 'function') call();
        },
        error: function (err) {
            console.log("userRelyNameValid err：" + err);
            $('#txtUserName').pshowTextTip('用户姓名已存在！');
            $('#v6s-loading').phide();
        },
        complete: function () { }
    });
};
/*新增或修改用户*/
settingController.updateUser = function () {
    $('#v6s-loading').pshow();

    settingController.userNameValid(function () {
        settingController.userRelyNameValid(function () {
            var instance = setModel.instance();
            var id = instance.selUser().id;
            var isEdit = id == null ? false : true;

            var userId = isEdit ? instance.selUser().userId : $('#txtUserId input').val().ptrimHeadTail();
            var showName = $('#txtUserName input').val().ptrimHeadTail();
            var email = $('#txtUserEmail input').val().ptrimHeadTail();
            var mobile = $('#txtUserMobile input').val().ptrimHeadTail();
            var departmentId = instance.currStruct().id;
            var roleList = [];
            var roleDivId = isEdit ? 'setCurrUserRoles' : 'divAddUserInfoRole';
            var selRoleTags = $('#' + roleDivId).find('[state="on"]');
            selRoleTags.each(function (i) {
                roleList.push(selRoleTags.eq(i).parent().attr('code'));
            });
            var param = {
                showName: showName,
                email: email,
                mobile: mobile,
                departmentId: departmentId,
                roleList: roleList,
                id: id,
                userId: userId
            };
            var url = isEdit ? '/pedituser' : '/padduser';
            $.ajax({
                type: 'post',
                url: url,
                data: param,
                success: function (result) {
                    result = result || {};
                    if (result.result != 'success') return $('#v6s-loading').phide(), $('#pcTip').pshow('failure', '操作失败');
                    settingController.getAllUsers(function () {
                        userSetGrilFloatHide();
                        instance.selUser(new user());
                        $('#pcTip').pshow('success', '保存成功');
                        if (frameModel.instance().currUser().id == id) frameController.getUserInfo();
                    });
                }, error: function () {
                    $('#v6s-loading').phide();
                    $('#pcTip').pshow('failure', '操作失败');
                }
            });
        });
    });
};

/*重置密码*/
settingController.initPass = function () {
    $('#v6s-loading').pshow();
    $.ajax({
        type: 'post',
        url: '/pinitpass',
        data: { id: setModel.instance().selUser().id },
        dataType: 'json',
        success: function (data) {
            data = data || {};
            if (data.result != 'success') return $('#v6s-loading').phide(), $("#pcTip").pshow('failure', '重置失败');
            $("#pcTip").pshow('success', '重置成功');
            $('#v6s-loading').phide();
            userSetGrilFloatHide();
        },
        error: function (e) {
            $('#v6s-loading').phide();
            $("#pcTip").pshow('failure', '重置失败');
            console.log("pinitpass err" + e);
        },
        complete: function () {

        }
    });
};

/*上传功能标识*/
settingController.uploadFIcon = function (file) {
    globalController.uploadFile({
        file: file, type: 'build', uploadServer: false,
        successCall: function (result) {
            result = result || {};
            if (result.result != 'success') return $('#pcTip').pshow('failure', '图标上传失败');
            var fIconTarget = $('#fIcon');
            fIconTarget.attr('path', result.path);
            fIconTarget.css('background', 'url(' + result.url + ') no-repeat');
            $('#lblFIcon').text('');
            $('#delFIcon').show();
        }, errCall: function () {
            $('#pcTip').pshow('failure', '图标上传失败');
        }
    });
};

/*用户编辑事件*/
settingController.userEditEvent = function () {
    var instance = setModel.instance();
    var currUser = instance.selUser();
    instance.selUser(new user());
    instance.selUser(currUser);
};