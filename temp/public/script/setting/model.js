/*数据model*/
function setModel() {
    this.builds = ko.observableArray();                         //建筑数组
    this.selBuild = ko.observable(new gbuild());          //当前选择的建筑
    this.buildTypes = ko.observableArray();                     //所有的建筑功能类型
    this.childBuildTypes = ko.observableArray();                //所有的二级建筑功能类型
    this.provinces = ko.observableArray();                      //省份
    this.citys = ko.observableArray();                          //城市
    this.region = ko.observableArray();                         //城市内的分区
    this.economicIndicator = ko.observableArray();              //所有的经济指标
    this.currEconomicIndicator = ko.observable();               //当前经济指标
    this.directions = ko.observableArray();                     //朝向
    this.airTypes = ko.observableArray();                       //所有的空调类型
    this.heatingTypes = ko.observableArray();                   //所有的采暖类型
    this.structTypes = ko.observableArray();                    //所有的建筑结构类型
    this.externalTypes = ko.observableArray();                    //所有的外保温类型
    this.fGroups = ko.observableArray();                        //F分组列表
    this.isSuper = ko.computed({
        owner: this,
        read: function () {
            return frameModel.instance().currUser().isSuper;
        }
    });
    this.allCproles = ko.observableArray();                     //所有的产品线及其下属角色
    this.users = ko.observableArray();                          //所有的用户
    this.selUser = ko.observable(new user());            //当前选择的用户
    /*当前是否在编辑组织结构*/
    this.isEditStruct = ko.observable(false);
    /*组织结构树，用于组织结构管理展示*/
    this.structsMan = ko.observable(new companyStruct());
    /*组织结构树，用于组织结构管理编辑*/
    this.structsManEdit = ko.observable(new companyStruct());
    /*组织结构树 用于用户管理*/
    this.structs = ko.computed({
        owner: this,
        read: function () {
            return frameModel.instance().structs();
        }
    });
    this.currStruct = ko.computed({
        owner: this,
        read: function () {
            return frameModel.instance().currStruct();
        }
    });
};

setModel.instance = function () {
    return setModel._inatance || (setModel._inatance = new setModel()) || setModel._inatance;
};

function tempPrice() {
    this.name = null;
    this.unit = ko.observable();
    this.yunit = ko.observable();
    this.jietis = ko.observableArray();
    this.fenshis = ko.observableArray();
    /*添加阶梯*/
    this.addPriceJt = function (obj, event) {
        var arr = obj.jietis();
        arr.push({
            unit: obj.unit,
            name: obj.name,
            yunit: obj.yunit
        });
        obj.jietis(arr);
    };

    /*删除阶梯*/
    this.delPriceJt = function (obj, event, index) {
        var targetParent = $(event.currentTarget).parent();
        var prevInputs = targetParent.prev().find('.ladder-temp').eq(0).find('input');
        var prevInput = prevInputs.eq(prevInputs.length - 1);

        var nextInput = targetParent.next().find('.ladder-temp').eq(0).find('input').eq(0);

        var arr = obj.jietis();
        arr.splice(index, 1);
        obj.jietis(arr);

        nextInput.val(prevInput.val());
    };

    /*添加分时*/
    this.addPriceFenshi = function (obj, event) {
        var arr = obj.fenshis();
        arr.push({
            unit: obj.unit,
            name: obj.name,
            yunit: obj.yunit
        });
        obj.fenshis(arr);

        var maxTarget = $(event.currentTarget).parent().parent();
        var newTarget = maxTarget.find('.p-t-ladder-temp').eq(arr.length);
        var newTimeDivs = newTarget.find('.ladder-temp').eq(0).find('[time]');
        var firstTimeDiv = newTimeDivs.eq(0);
        var lastTimeDiv = newTimeDivs.eq(1);

        var prevTimeDivs = newTarget.prev().find('.ladder-temp').eq(0).find('[time]');
        var prevTimeDiv = prevTimeDivs.eq(prevTimeDivs.length - 1);

        var nextTimeDiv = newTarget.next().find('.ladder-temp').eq(0).find('[time]').eq(0);

        var dateStr = new Date().format();
        var firstStr = dateStr + ' ' + prevTimeDiv.pgetTime().startStr + ':00';
        var lastStr = dateStr + ' ' + nextTimeDiv.pgetTime().startStr + ':00';

        firstTimeDiv.psetTime(firstStr);
        lastTimeDiv.psetTime(lastStr);
    };

    /*删除分时*/
    this.delPriceFenshi = function (obj, event, index) {
        var targetParent = $(event.currentTarget).parent();
        var prevTimes = targetParent.prev().find('[time]');
        var prevTime = prevTimes.eq(prevTimes.length - 1);

        var nextTime = targetParent.next().find('[time]').eq(0);

        var arr = obj.fenshis();
        arr.splice(index, 1);
        obj.fenshis(arr);

        var currTimeStr = new Date().format() + ' ';
        var prevTimeStr = currTimeStr + prevTime.pgetTime().startStr + ':00';
        nextTime.psetTime(prevTimeStr);
    };
};
