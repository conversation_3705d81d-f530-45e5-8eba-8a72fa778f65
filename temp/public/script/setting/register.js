/*页面事件注册*/
var v6sSetNavMenus = [{
        text: '建筑设置',
        icon: 'g'
    },
    {
        text: '功能设置',
        icon: 'u'
    },
    {
        text: '用户设置',
        icon: '#'
    }
];

var userMinYear = new Date().getFullYear() - 10;
var userMaxYear = new Date().getFullYear() + 10;
var longitudeMenus = ["东经", "西经"];
var latitudeMenus = ["北纬", "南纬"];

//12个月
var twelveMonths = ['1个月', '2个月', '3个月', '4个月', '5个月', '6个月', '7个月', '8个月',
    '9个月', '10个月', '11个月', '12个月'
];
$(function () {
    settingController.init();
    //用户编辑结构树
    $(".select-user-float .tree-title").click(function (e) {
        e.stopPropagation();
        $(this).toggleClass('show');
        var hasShow = $(this).hasClass("show");
        if (hasShow) {
            $(this).next(".tree-con").slideDown();
        } else {
            $(this).next(".tree-con").slideUp();
        }
    });

    $(".select-user-float .temp-tit span").click(function (e) {
        e.stopPropagation();
        $(this).parent().toggleClass('show');
        var hasShow = $(this).parent().hasClass("show");
        if (hasShow) {
            $(this).parent().next(".temp-con").slideDown();
            $(this).text("b");
        } else {
            $(this).parent().next(".temp-con").slideUp();
            $(this).text("r");
        }
    });
    $(".select-user-float .temp-tit").click(function () {
        $(".user-data .tree-title").click();
    });
    $(document).on("click", function () {
        $(".select-user-float .tree-title").next(".tree-con").slideUp();
        $(".select-user-float .tree-title").removeClass('show');

    });
    /*功能设置name*/
    window.nameEditSign = true;
    window.urlEditSign = true;
    window.nameFlag = false;
    window.urlFlag = false;
    window.isEdit = false;
    $(".functionSet-gril-title .name-edit").on("click", function () {
        var _this = $(this);
        if (window.isEdit == false) {
            $(".functionSet-gril-content .nameNoIsEdit").css("display", "none");
            $(".functionSet-gril-content .nameIsEdit").css("display", "block");
            _this.text("Z");
            _this.attr("title", "点击保存");
            window.isEdit = true;
        } else if (window.isEdit == true && _this.text() == 'Z') {
            //保存编辑内容
            var farr = [];
            var groups = setModel.instance().fGroups();
            for (var i = 0; i < groups.length; i++) {
                var currGroup = groups[i];
                var currFlist = currGroup.fList();
                for (var j = 0; j < currFlist.length; j++) {
                    var currF = currFlist[j];
                    if (currF.name == '')
                        return $('#pcTip').pshow('failure', '内容不可为空！');
                    for (var k = j + 1; k < currFlist.length; k++) {
                        if ((currF.name || '').ptrimHeadTail() == (currFlist[k].name || '').ptrimHeadTail())
                            return $('#pcTip').pshow('failure', '内容不可重复！');
                    }
                    farr.push({
                        code: currF.code,
                        name: (currF.name || '').ptrimHeadTail()
                    });
                }
            }
            settingController.updataFName(farr, function () {
                _this.text("e");
                _this.attr("title", "点击编辑名称");
                window.isEdit = false;
            });
        } else
            $(".prompt-success").pshow("failure", '请先保存当前修改的内容！');
    });
    //功能设置URL
    $(".functionSet-gril-title .name-url").on("click", function () {
        var _this = $(this);
        var urlInputs = _this.parent().parent().parent().next().find('[t="up"]').filter('[ed="true"]');
        if (window.isEdit == false) {
            for (var i = 0; i < urlInputs.length; i++) {
                var currInput = urlInputs.eq(i);
                currInput.parent().prev().hide();
                currInput.parent().show();
            }
            _this.text("Z");
            _this.attr("title", "点击保存");
            window.isEdit = true;
        } else if (window.isEdit == true && _this.text() == 'Z') {
            //保存编辑
            var farr = [];
            for (var i = 0; i < urlInputs.length; i++) {
                var currInput = urlInputs.eq(i);
                if (!currInput.val()) return $(".prompt-success").pshow("failure", '功能URL不可为空');
                if (!(/http:\/\/.+/.test(currInput.val())))
                    return $(".prompt-success").pshow("failure", '功能URL格式不正确');
                farr.push({
                    code: currInput.attr('code'),
                    url: currInput.val()
                });
            }

            settingController.updataFUrl(farr, function () {
                window.isEdit = false;
                _this.text("e");
                _this.attr("title", "点击编辑url");
            });
        } else
            $(".prompt-success").pshow("failure", '请先保存当前修改的内容！');
    });
    /*产品线拖拽排序*/
    $(".functionSet-gril-content").sortable({
        axis: 'y',
        connectWith: false,
        containment: 'parent',
        cursor: "-webkit-grabbing",
        distance: 1,
        dropOnEmpty: false,
        delay: 0,
        helper: 'original',
        handle: ".functionSet-gril-item",
        items: " ul:gt(0)", //除了第一个ul其他都可以拖拽
        revert: false, //释放时，增加动画
        scroll: true,
        scrollSensitivity: 10,
        stop: function () { //拖拽结束后执行的方法
            var cparr = [];
            $('.functionSet-gril-content .functionSet-gril-item').each(function () {
                cparr.push({
                    code: $(this).attr('code'),
                    sort: $(this).parent().index()
                });
            });
            settingController.updateCpsort(cparr, function () {
                var spanNameEdit = $('#spanfnameedit');
                if (spanNameEdit.text() == 'Z') {
                    spanNameEdit.text("e");
                    spanNameEdit.attr("title", "点击编辑url");
                }

                var spanUrlEdit = $('#spanfnameurl');
                if (spanUrlEdit.text() == 'Z') {
                    spanUrlEdit.text("e");
                    spanUrlEdit.attr("title", "点击编辑名称");
                }
                window.isEdit = false;
            });
        }
    }).disableSelection(); //设置文字不能被选中

});

function setwrapMindinOpen() {
    window.open('/PersagyCloud/mindinset');
}

/*产品线名称及产品线url是否可处在编辑状态*/
function validEdit() {
    return;
    if (window.nameFlag == true) {
        $(".functionSet-gril-content").attr('signName', true);
        $(".functionSet-gril-title .name-edit").text("e");
        $(".functionSet-gril-title .name-edit").attr("title", "点击编辑名称");
        window.nameEditSign = true;
    }
    if (window.urlFlag == true) {
        $(".functionSet-gril-content").attr('signUrl', true);
        $(".functionSet-gril-title .name-url").attr("title", "点击编辑url");
        $(".functionSet-gril-title .name-url").text("e");
        $(".functionSet-gril-title .name-url").click();
        window.urlEditSign = true;
    }
}

/*产品线名称及产品线url是否可处在编辑状态 new*/
function productEdit() {
    if (window.isEdit == true) {
        $(".functionSet-gril-content").attr('signName', true);
        $(".functionSet-gril-title .name-edit").text("e");
        $(".functionSet-gril-title .name-edit").attr("title", "点击编辑名称");
        $(".functionSet-gril-content").attr('signUrl', true);
        $(".functionSet-gril-title .name-url").text("e");
        $(".functionSet-gril-title .name-url").attr("title", "点击编辑url");
        window.isEdit = false;
    }
}


/*产品线状态改变*/
function changeState(obj, event) {
    var jqTarget = $(event.currentTarget);
    var state = jqTarget.psel();
    var code = jqTarget.parents('.functionSet-gril-item').attr("code");
    if (state == "off") {
        $('#v6s-setwrap-warning').pshow([function () {
            settingController.updateCpstate(code, state, validEdit);
        }, function () {
            switch (state) {
                case 'on':
                    jqTarget.poffState();
                    break;
                case 'off':
                    jqTarget.ponState();
                    break;
            }
        }], '您确定要关闭此产品线吗？', '关闭后该产品线功能将不再纳入使用');
    } else {
        settingController.updateCpstate(code, state, validEdit);
        $("#pcTip").pshow('success', '系统已开启使用');
        jqTarget.parent().attr("title", "点击关闭系统");
    }
}

function codeChangeState(code, state) {
    var _this = '[code="' + code + '"]';
    var has = $(_this).hasClass("cpSwich");
    switch (state) {
        case 'on':
            if (has) $(_this).attr("title", "点击关闭系统");
            break;
        case 'off':
            if (has) $(_this).attr("title", "点击开启系统");
            break;
    }


}

function v6sSetNavClick(setnav) {
    var index = setnav.pEventAttr.index;
    $(".setwrap-con-temp>div").eq(index).show().siblings().hide();

    switch (index) {
        case 0:
            settingController.initBuild();
            validEdit();
            productEdit();
            break;
        case 1:
            settingController.initF();
            productEdit();
            break;
        case 2:
            settingController.initUser();
            validEdit();
            productEdit();
            break;
    }
    //float面板关闭
    setSelectBulidHide(); //建筑详情查看float
    userSetGrilFloatHide(); //用户详情float
    functionSetFloatHide(); //添加功能float
}


//功能设置添加功能float show
function functionSetFloatShow() {
    $(".functionSet-float").css("right", "-30px");
};

//功能设置添加功能保存
function functionSetFloatSaveHide() {
    var isValided = $('#functionSet').pverifi();
    var index = $('#cpcode').psel();
    if (index == -1) return $('#cpcode').next().show();
    $('#cpcode').next().hide();
    if (!isValided) return;
    var url = $('#featureUrl input').val();
    if (!(/http:\/\/.+/.test(url))) return $('#featureUrl').pshowTextTip('功能URL格式不正确');

    var cpcode = (setModel.instance().fGroups()[index] || {}).code;
    var name = $('#featureName input').val().ptrimHeadTail();
    var code = $('#featureCode input').val();

    var cplist = setModel.instance().fGroups();
    for (var i = 0; i < cplist.length; i++) {
        var currcp = cplist[i];
        var currfList = currcp.fList();
        for (var j = 0; j < currfList.length; j++) {
            var currf = currfList[j];
            if ((currf.code || '').ptrimHeadTail() == code.ptrimHeadTail())
                return $('#featureCode').pshowTextTip('功能编码已存在');
            if (i == index && (currf.name || '').ptrimHeadTail() == name)
                return $('#featureName').pshowTextTip('功能名称已存在');
        }
    }

    settingController.newAddF(code, cpcode, name, '', url);
};
//功能设置float hide
function functionSetFloatHide() {
    $('#functionSet').pctlsRecover();
    $('.f-f-w-input #fIcon').attr('path', '').css("background", '');
    $('.f-f-w-input #delFIcon').hide();
    $('.f-f-w-input label').text('点击上传标志');
    $(".functionSet-float").css("right", "-600px");
}
//功能设置添加功能icon  remove
function buildIconRemove(_this) {
    $(_this).parent().find('.img').css('background', '');
    $(_this).parent().find('label').text('点击上传标志');
    $(_this).hide();
    $('#fIcon').attr('path', '');
};
//添加功能时上传标志
function fIconUpload(target) {
    var file = target.files[0];
    target.value = '';
    settingController.uploadFIcon(file);
};
//无建筑时，添加建筑
function setDataAddBulidShow() {
    $('#childBuildAddbt').click();
}

//建筑列表点击事件 建筑详情show
var buildIndex = '';

function setSelectBulidShow(obj, event) {
    var instance = setModel.instance();
    var build = new gbuild();
    for (var pro in obj) {
        if (obj.hasOwnProperty(pro) == false) continue;
        build[pro] = obj[pro];
    }
    instance.selBuild(build);
    buildIndex = $(event.target).parents('li').index();
    $(".select-build-float").css("right", "-30px");
    $(".select-build-float").ptitle("建筑详情");
    $(".select-build-float").picon("S");
    initEconPrice();
};
//建筑详情hide
function setSelectBulidHide() {
    $(".select-build-float").css("right", "-600px");
    $("#selsetBuildGrid").poff(buildIndex);
};

//初始化指标价格页面
function initEconPrice() {
    buildImgDeld = {};
    //获取每个指标对应的当前建筑的价格
    var instance = setModel.instance();
    var currBuild = instance.selBuild();
    var economicIndicators = instance.economicIndicator();
    var currBuildPrices = currBuild.priceList || [];
    for (var j = 0; j < economicIndicators.length; j++) {
        var currEconomic = economicIndicators[j];
        var currEconomicPrices = [];
        var currPriceTypes = currEconomic.priceTypes || [];
        for (var i = 0; i < currBuildPrices.length; i++) {
            var currPrice = currBuildPrices[i];
            var startDate = new Date(currPrice.startTime);
            if (startDate.getTime() <= new Date().getTime()) currPrice.isOld = true;
            else currPrice.isOld = false;
            currPrice.startShowTime = startDate.format('y.M.d');
            if (currPrice.eneryCode != currEconomic.code) continue;
            currPrice.eneryName = currEconomic.name;
            for (var k = 0; k < currPriceTypes.length; k++) {
                var currpt = currPriceTypes[k];
                if (currpt.code == currPrice.code) {
                    currPrice.typeName = currpt.name;
                    currPrice.unit = currpt.unit;
                    currPrice.yunit = currpt.unit.split('/')[1];
                    currPrice.content = typeof currPrice.content == 'string' ?
                        JSON.parse(currPrice.content) : currPrice.content;
                    break;
                }
            }
            currEconomicPrices.push(currPrice);
        }
        if (currEconomicPrices.length > 0) {
            currEconomic.lastTime = currEconomicPrices[0].startTime;
        } else {
            var ddd = new Date();
            ddd.setDate(ddd.getDate() - 1);
            currEconomic.lastTime = ddd.format();
        }
        currEconomic.prices(currEconomicPrices);
    }
};

//添加分建筑点击事件
function addChildBuildEvent() {
    var instance = setModel.instance();
    instance.selBuild(new gbuild());
    setAddBulidShow();
};

var buildImgDeld = {};
//新建建筑初始化信息或编辑建筑按钮点击事件
function setAddBulidShow() {
    var currBuild = setModel.instance().selBuild();
    var isDisabled = currBuild.oldId ? false : true;
    currBuild.auto = false;
    buildProvince.psel(currBuild.provinceName || '0');
    buildCity.psel(currBuild.cityName || '0');
    buildRegion.psel(currBuild.partitionName || '0');
    currBuild.auto = true;
    buildDirection.psel(currBuild.orientationName || '0');
    buildTypeName.psel(currBuild.pFunctionTypeName || '0');
    childBuildTypeName.psel(currBuild.functionTypeName || '0');

    buildAirType.psel(currBuild.airConditioningTypeName || '0');
    buildHeatingType.psel(currBuild.heatingTypeName || '0');
    buildStructType.psel(currBuild.structureTypeName || '0');
    buildHotIndicator.psel(currBuild.externalThermalInsulationName || '0');
    var dddate = new Date((currBuild.buildingTime || '').replace(/\-/g, '/'));
    dddate = dddate == 'Invalid Date' ? new Date() : dddate;
    buildUserTimer.psetTime({
        y: dddate.getFullYear(),
        M: dddate.getMonth() + 1,
        d: dddate.getDate()
    });
    $('#childBuildTypeName').pdisable(isDisabled);
    $('#buildCity').pdisable(isDisabled);
    $('#buildRegion').pdisable(isDisabled);
    $(".v6s-setwrap .add-build").show();
    $(".v6s-setwrap .add-step1-wrap").scrollTop("0");
    $(".v6s-setwrap .build-datawrap").hide();
    if (currBuild.picture1) {
        $(".add-build-pic #buildSmallX").show()
    }
    if (currBuild.picture2) {
        $(".add-build-pic #buildBigX").show()
    }

    var description = currBuild.introduction || '';
    $('#buildRemark').ptipCount(description.length);

    //小于零代表西经，对应第二项。否则，代表东经，对应第一项
    var longitudeIndex = currBuild.longitude2 < 0 ? 1 : 0;
    //小于零代表南纬，对应第二项。否则代表北纬，对应第一项
    var latitudeIndex = currBuild.latitude2 < 0 ? 1 : 0;
    $('#divCmLongitude').psel(longitudeIndex);
    $('#divCmLatitude').psel(latitudeIndex);
    setSelectBulidHide();
    if (!currBuild.oldId)
        initEconPrice();
};
//新建建筑返回
function setAddBulidHide() {
    var sign = $('#divBuildBack').attr('sign');
    if (sign != 'true') {
        $('#v6s-setwrap-warning').pshow([back], '您确定要离开此页面吗？', '离开后，编辑的内容将不会保存');
    } else {
        back();
    }
    $('#divBuildBack').attr('sign', false);

    function back() {
        $(".v6s-setwrap .add-build").hide();
        $(".v6s-setwrap .build-datawrap").show();
        //清空新建建筑第一页内的填写内容和下来框
        $('#divUserNewBuild').pctlsRecover();
        //禁用二级建筑功能类型、城市、区域
        $('#childBuildTypeName').pdisable(true);
        $('#buildCity').pdisable(true);
        $('#buildRegion').pdisable(true);
        //去除建筑功能类型、二级建筑功能类型、省市区的错误提示
        var comboboxIds = ['buildTypeName', 'childBuildTypeName', 'buildProvince', 'buildCity', 'buildRegion'];
        for (var i = 0; i < comboboxIds.length; i++) {
            var curr = '#' + comboboxIds[i];
            $(curr).parent().removeClass('error');
            $(curr).next().hide();
        };

        //新加的价格给去除 并恢复每个指标的价格为空 
        var instance = setModel.instance();
        instance.selBuild(new gbuild());
        for (var xyz = 1; xyz < 9; xyz++) {
            $('#ul' + xyz).empty();
        }
        var economicIndicators = instance.economicIndicator();
        for (var j = 0; j < economicIndicators.length; j++) {
            var currEconomic = economicIndicators[j];
            currEconomic.prices([]);
            currEconomic.tempPrices([]);
        }
        //清除下拉框选项、时间控件的错误提示、文本框的填写内容
        $('.price-type-con').pctlsRecover();
        addBuildPrev();
        /*上传的建筑图片隐藏*/
        $("#buildFileDiv li").hide();
        var smallTarget = $('#buildSmallImg');
        smallTarget.attr('newfile', 'false');

        var bigTarget = $('#buildBigImg');
        bigTarget.attr('newfile', 'false');
    }
};
//建筑建筑图片remove
function buildImgRemove(_this) {
    var imgTarget = $(_this).parent().find('.img');
    imgTarget.css('background-image', '');
    $(_this).hide();
    var newFile = imgTarget.attr('newFile');
    newFile == undefined ? (imgTarget.attr('id') == 'buildSmallImg' ?
        buildImgDeld.p1 = imgTarget.attr('fpath') : buildImgDeld.p2 = imgTarget.attr('fpath')) : '';
    imgTarget.attr('newFile', 'false');
    imgTarget.attr('old', 'false');
}
//新建建筑第一步转下一步
function addBuildNext() {
    if (!$('#divBuildAddFirst').pverifi()) return;
    var comboboxIds = ['buildTypeName', 'childBuildTypeName', 'buildProvince', 'buildCity', 'buildRegion'];
    for (var i = 0; i < comboboxIds.length; i++) {
        if (!mustComboxIsSel(comboboxIds[i])) {
            var curr = '#' + comboboxIds[i];
            location.hash = curr;
            location.href = location.href;
            return;
        }
    }
    $('#v6s-loading').pshow();
    settingController.buildIdValid($('#buildId').pval(), function () {
        settingController.buildNameValid($('#buildName').pval(), function () {
            $('#v6s-loading').phide();
            if (!$('#buildId').pverifi() || !$('#buildName').pverifi()) {
                location.hash = '#buildId';
                location.href = location.href;
                return;
            }
            $(".v6s-setwrap .add-step2").show();
            $(".v6s-setwrap .add-step1").hide();
            //经济指标选项卡恢复默认第一项
            $('#divEcEnEn').psel(0);
        });
    });
};

/*建筑保存*/
function buildSave() {
    $('#divPriceCasex .error-tip').hide();
    $('#divPriceCasex .error').removeClass('error');
    $('#divPriceCasex').phideTextTip();
    if (!$('#divPriceCasex').pverifi()) return;
    var crstr = new Date().format('y/M/d');
    var currTime = new Date().getTime();
    var priceList = [];
    var instance = setModel.instance();
    var economicIndicators = instance.economicIndicator();
    for (var j = 0; j < economicIndicators.length; j++) {
        var currEconomic = economicIndicators[j];
        var oldPrices = currEconomic.prices();
        var existsTimeStr = ',';
        for (var ab = 0; ab < oldPrices.length; ab++) {
            var currOldPrice = oldPrices[ab];
            priceList.push({
                type: currOldPrice.code,
                prices: [{
                    type: currOldPrice.type,
                    startTime: currOldPrice.startTime,
                    period: currOldPrice.period,
                    prices: currOldPrice.content || []
                }]
            });
            existsTimeStr = existsTimeStr + currOldPrice.startTime + ',';
        }
        var priceTypeDivs = $('[divec="' + currEconomic.code + '"]').find('.price-type');
        for (var k = 0; k < priceTypeDivs.length; k++) {
            var currPriceTypeDiv = priceTypeDivs.eq(k);
            var currChildDiv = currPriceTypeDiv.children().filter('[se="true"]');
            if (!currChildDiv.pverifi()) {
                $('#divEcEnEn').psel(j);
                return;
            }
            var selPriceTypeIndex = currPriceTypeDiv.prev().find('[divt]').psel();
            var selPriceType = currEconomic.priceTypes[selPriceTypeIndex];
            var currTimeControl = currPriceTypeDiv.prev().prev().find('[ec]');
            var startTime = currTimeControl.pgetTime().startStr.replace(/\//g, '-') + ' 00:00:00';
            if (new Date(startTime).getTime() <= currTime) {
                $('#divEcEnEn').psel(j);
                currTimeControl.next().find('em').text('开始时间不可早于明日');
                currTimeControl.parent().addClass('error');
                currTimeControl.next().show();
                return;
            }
            if (existsTimeStr.indexOf(',' + startTime + ',') > -1) {
                $('#divEcEnEn').psel(j);
                currTimeControl.next().find('em').text('时间不可重复');
                currTimeControl.parent().addClass('error');
                currTimeControl.next().show();
                return;
            }
            existsTimeStr += startTime + ',';
            currTimeControl.next().hide();
            var currPrice = {
                type: selPriceType.code,
                prices: [{
                    type: selPriceType.type,
                    startTime: startTime
                }]
            };
            switch (selPriceType.type) {
                case '1': //平均
                    var val = currChildDiv.find('input').val();
                    if (!val.pisNumber()) return currChildDiv.pshowTextTip('请填写正确的价格'),
                        $('#divEcEnEn').psel(j);
                    currPrice.prices[0].prices = [{
                        start: '00:00',
                        end: '23:59',
                        price: parseFloat(val)
                    }];
                    break;
                case '2': //阶梯
                    var period = currChildDiv.find('.p-t-t-temp').psel() + 1;
                    var ladderTemps = currChildDiv.find('.p-t-ladder-temp');
                    var jietiPrices = [];
                    for (var x = 0; x < ladderTemps.length; x++) {
                        var currLadderTemp = ladderTemps.eq(x);
                        var lps = currLadderTemp.find('.ladder-temp');
                        var startInput = x == 0 ? null : lps.eq(0).find('input').eq(0);
                        var start = x == 0 ? '0' : startInput.val();
                        if (!start) return startInput.pshowTextTip('请填写用量'),
                            $('#divEcEnEn').psel(j);
                        var endInput = x == ladderTemps.length - 1 ? null :
                            x == 0 ? lps.eq(0).find('input').eq(0) :
                            lps.eq(0).find('input').eq(1);
                        var end = x == ladderTemps.length - 1 ? null : endInput.val();
                        if (end == '') return endInput.pshowTextTip('请填写用量'),
                            $('#divEcEnEn').psel(j);
                        var price = lps.eq(1).find('input').val();
                        if (!price.pisNumber()) return lps.eq(1).find('input').pshowTextTip('请填写正确的价格'),
                            $('#divEcEnEn').psel(j);
                        if (end != '' && parseFloat(end) <= parseFloat(start)) {
                            return endInput.pshowTextTip('阶梯位必须大于' + start),
                                $('#divEcEnEn').psel(j);
                        }
                        jietiPrices.push({
                            start: parseFloat(start),
                            end: parseFloat(end),
                            price: parseFloat(price)
                        });
                        currPrice.prices[0].prices = jietiPrices;
                        currPrice.prices[0].period = period;
                    }
                    break;
                case '3': //分时
                    var ladderTemps = currChildDiv.find('.p-t-ladder-temp');
                    var fenshiPrices = [];
                    var fenshiTimes = [];
                    for (var x = 0; x < ladderTemps.length; x++) {
                        var currLadderTemp = ladderTemps.eq(x);
                        var lps = currLadderTemp.find('.ladder-temp');
                        var timesDiv = lps.eq(0).find('[time]');
                        var start = x == 0 ? '00:00' : timesDiv.eq(0).pgetTime().startStr;
                        var end = x == ladderTemps.length - 1 ? '23:59' :
                            timesDiv.eq(timesDiv.length - 1).pgetTime().startStr;
                        var price = lps.eq(1).find('input').val();
                        if (!price.pisNumber()) return lps.eq(1).find('input').pshowTextTip('请填写正确的价格'),
                            $('#divEcEnEn').psel(j);
                        var stime = new Date(crstr + ' ' + start + ':00').getTime();
                        var etime = new Date(crstr + ' ' + end + ':00').getTime();
                        var errDiv = timesDiv.eq(timesDiv.length - 1).next();
                        if (etime <= stime) {
                            var errStr = x == ladderTemps.length - 1 ? '时间必须早于' + end : '时间必须晚于' + start;
                            errDiv.find('em').text(errStr);
                            errDiv.show();
                            divEcEnEn.psel(j);
                            return;
                        }
                        errDiv.hide();
                        fenshiPrices.push({
                            start: start,
                            end: end,
                            price: parseFloat(price)
                        });
                        currPrice.prices[0].prices = fenshiPrices;
                    }
                    break;
            }
            priceList.push(currPrice);
        }
    }
    var smallTarget = $('#buildSmallImg');
    var picture1 = {
        newFile: smallTarget.attr('newFile') == 'true' ? smallTarget.attr('fpath') : '',
        delFile: buildImgDeld.p1,
        oldFile: smallTarget.attr('old') == 'true' ? smallTarget.attr('fpath') : ''
    };
    var bigTarget = $('#buildBigImg');
    var picture2 = {
        newFile: bigTarget.attr('newFile') == 'true' ? bigTarget.attr('fpath') : '',
        delFile: buildImgDeld.p2,
        oldFile: bigTarget.attr('old') == 'true' ? bigTarget.attr('fpath') : ''
    };
    var currBuild = instance.selBuild();
    var isMain = !currBuild.oldId ? (instance.builds().length == 0 ? true : false) : currBuild.isMain;
    var longitude = $('#buildLongitude input').val();
    var latitude = $('#buildLatitude input').val();
    longitude = $('#divCmLongitude').psel() == 1 ? '-' + longitude : longitude;
    latitude = $('#divCmLatitude').psel() == 1 ? '-' + latitude : latitude;
    var build = {
        oldId: currBuild.oldId,
        isMain: isMain,
        priceList: priceList,
        building: {
            id: $('#buildId input').val(),
            name: $('#buildName input').val(),
            longitude: parseFloat(longitude),
            latitude: parseFloat(latitude),
            altitude: parseFloat($('#buildAltitude input').val()),
            buildingTime: buildUserTimer.pgetTime().startStr.replace(/\//g, '-') + ' 00:00:00',
            introduction: $('#buildRemark textarea').val(),
            coldQuantity: $('#buildUnitAreaCold input').val() == '' ? null : parseFloat($('#buildUnitAreaCold input').val()),
            heatQuantity: $('#buildUnitAreaHot input').val() == '' ? null : parseFloat($('#buildUnitAreaHot input').val()),
            powerQuantity: $('#buildUnitAreaPower input').val() == '' ? null : parseFloat($('#buildUnitAreaPower input').val()),
            seismic: $('#buildAntiScale input').val() == '' ? null : parseFloat($('#buildAntiScale input').val()),
            somatotype: $('#antiScale input').val() == '' ? null : parseFloat($('#antiScale input').val()),
            thermalIndicator: $('#buildFiguerRatio input').val() == '' ? null : parseFloat($('#buildFiguerRatio input').val()),
            areaTree0: parseFloat($('#buildTotalArea input').val()),
            areaTree1: parseFloat($('#buildAirArea input').val()),
            areaTree2: parseFloat($('#buildParkArea input').val()),
            areaTree3: parseFloat($('#buildEquipmentArea input').val()),
            onArea: $('#buildFloorUpArea input').val() == '' ? null : parseFloat($('#buildFloorUpArea input').val()),
            underArea: $('#buildFloorDownArea input').val() == '' ? null : parseFloat($('#buildFloorDownArea input').val()),

            floorCount: $('#buildFloorNum input').val() == '' ? null : parseFloat($('#buildFloorNum input').val()),
            onFloorCount: $('#buildFloorUpNmu input').val() == '' ? null : parseFloat($('#buildFloorUpNmu input').val()),
            underFloorCount: $('#buildFloorDownNum input').val() == '' ? null : parseFloat($('#buildFloorDownNum input').val()),
            height: $('#buildHeight input').val() == '' ? null : parseFloat($('#buildHeight input').val()),
            area: $('#buildFloorArea input').val() == '' ? null : parseFloat($('#buildFloorArea input').val()),
            province: (instance.provinces()[buildProvince.psel()] || {}).code,
            city: (instance.citys()[buildCity.psel()] || {}).code,
            partition: (instance.region()[buildRegion.psel()] || {}).code,
            climaticRegion: (instance.region()[buildRegion.psel()] || {}).climaticRegion,
            orientation: (instance.directions()[buildDirection.psel()] || {}).code,
            functionType: (instance.childBuildTypes()[childBuildTypeName.psel()] || {}).code,
            airConditioningType: (instance.airTypes()[buildAirType.psel()] || {}).code,
            heatingType: (instance.heatingTypes()[buildHeatingType.psel()] || {}).code,
            structureType: (instance.structTypes()[buildStructType.psel()] || {}).code,
            externalThermalInsulation: (instance.externalTypes()[buildHotIndicator.psel()] || {}).code,
            picture1: picture1,
            picture2: picture2
        }
    };
    var buildFilesCon = $('#buildFileDiv .add-build-temp');
    for (var xa = 0; xa < buildFilesCon.length; xa++) {
        var currFileCon = buildFilesCon.eq(xa);
        var lis = currFileCon.find('li');
        var delFiles = [],
            newFiles = [],
            oldFiles = [];
        lis.each(function (currIndex) {
            var currLi = lis.eq(currIndex);
            currLi.attr('del') == 'true' ? delFiles.push(currLi.attr('fid')) : '';
            currLi.attr('newFile') == 'true' ? newFiles.push({
                path: currLi.attr('fpath'),
                name: currLi.attr('fn')
            }) : '';
            currLi.attr('old') == 'true' ? oldFiles.push({
                id: currLi.attr('fid'),
                name: currLi.attr('fn'),
                suffix: currLi.attr('su')
            }) : '';
        });
        build.building['blueprint' + (xa + 1)] = {
            delFiles: delFiles,
            newFiles: newFiles,
            oldFiles: oldFiles
        };
    }
    settingController.saveBuild(build);
};

//判断必选项是否选择了
function mustComboxIsSel(id) {
    var curr = '#' + id;
    if ($(curr).psel() == 0 || $(curr).psel() == -1) {
        $(curr).parent().addClass('error');
        $(curr).next().show();
        return false;
    }
    $(curr).parent().removeClass('error');
    $(curr).next().hide();
    return true;
};

//必选项选择时判断选项是否有效
function mustSelingIsSel(obj, event) {
    var oo = $(event.currentTarget).parent().parent().parent().parent();
    if (obj.code == null) {
        oo.parent().addClass('error');
        oo.next().show();
        return false;
    }
    oo.parent().removeClass('error');
    oo.next().hide();
    return true;
};

/*价格方案开始时间验证*/
function priceStartSel(event) {
    var target = $(event.pEventAttr.target).parent();
    var currTime = target.pgetTime().startStr;

    var prevTime;
    var prevTimeDiv = target.parent().parent().parent().next();
    if (prevTimeDiv[0]) prevTime = prevTimeDiv.find('.p-t-t-temp').eq(0).find('[ec]').pgetTime().startStr;
    else {
        var encode = target.attr('ec');
        var economicIndicators = setModel.instance().economicIndicator();
        for (var i = 0; i < economicIndicators.length; i++) {
            var currEconomic = economicIndicators[i];
            if (currEconomic.code == encode) {
                prevTime = currEconomic.lastTime;
                break;
            }
        }
    }

    var currDate = new Date(currTime);
    var prevDate = new Date(prevTime);
    if (currDate.getTime() <= prevDate.getTime()) {
        target.parent().addClass('error');
        target.next().find('em').text('时间必须晚于' + prevDate.format('y.M.d'));
    } else {
        target.parent().removeClass('error');
        target.next().hide();
    }
}

/*价格方案阶梯价位验证*/
function jietiStEnValid(obj, event) {
    var target = $(event.target);
    var currValue = parseFloat(target.val());

    var inputDiv = target.parent().parent().parent();
    if (!inputDiv.pverifi()) return;

    var oreign = 'next';
    var itindex = 0;
    var ot = inputDiv.attr('po');
    ot == 'f' ? (oreign = 'prev', itindex = -1) : '';

    var stepInputDiv = inputDiv.parent().parent()[oreign]().find('.ladder-temp').eq(0).find('.ladder-input');
    stepInputDiv = itindex == 0 ? stepInputDiv.eq(0) : stepInputDiv.eq(stepInputDiv.length - 1);
    stepInputDiv.find('input').val(currValue);
    stepInputDiv.phideTextTip();
};

/*价格方案分时时间验证*/
function fenshiStEnValid(event) {
    var currTimeStr = new Date().format();

    var target = $(event.pEventAttr.target).parent();
    var currValue = currTimeStr + ' ' + target.pgetTime().startStr + ':00';
    var inputDiv = target.parent();

    var oreign = 'next';
    var itindex = 0;
    var ot = target.attr('po');
    ot == 'f' ? (oreign = 'prev', itindex = -1) : '';

    var stepInputDiv = inputDiv.parent().parent()[oreign]().find('.ladder-temp').eq(0).find('.ladder-input');
    stepInputDiv = itindex == 0 ? stepInputDiv.eq(0) : stepInputDiv.eq(stepInputDiv.length - 1);
    stepInputDiv.find('[time]').psetTime(currValue);
};


//新建建筑上一步
function addBuildPrev() {
    $(".v6s-setwrap .add-step2").hide();
    $(".v6s-setwrap .add-step1").show();
    $(".v6s-setwrap  .add-step1-wrap").scrollTop("0");
};
//经济指标点击事件
function priceTypeClick(obj, event) {
    var index = $(event.currentTarget).index();
    $(".price-type-Wcon>div").eq(index).show().siblings().hide();
    var instance = setModel.instance();
    instance.currEconomicIndicator(instance.economicIndicator()[index]);
};

//第二步内的添加价格方案事件
function setAddPriceTypeTemp(obj, event) {
    var code = $(event.currentTarget).attr('ptc');
    var instance = setModel.instance();
    var economicIndicators = instance.economicIndicator();
    var index = -1;
    for (var j = 0; j < economicIndicators.length; j++) {
        var currEconomic = economicIndicators[j];
        if (currEconomic.code != code) continue;
        index = j;
        var arr = currEconomic.tempPrices();
        var tp = new tempPrice();
        tp.name = currEconomic.name;
        tp.unit(currEconomic.unit);
        tp.yunit(currEconomic.unit().split('/')[1]);
        arr.push(tp);
        currEconomic.tempPrices(arr);
        break;
    }

    var typeTemps = $('#divPriceCasex .price-type-wrap').eq(index).find('.price-type-temp');
    var currTypeTemp = typeTemps.eq(typeTemps.length - 1);

    /*给新加的方案开始时间置为当前时间*/
    var date = new Date();
    date.setDate(date.getDate() + 1);
    currTypeTemp.find('.p-t-t-temp').eq(0).find('[ec]').psetTime({
        y: date.getFullYear(),
        M: date.getMonth() + 1,
        d: date.getDate()
    });

    /*新加的方案默认选择平均价*/
    var currPriceTypes = currEconomic.priceTypes || [];
    var currPtDiv = currTypeTemp.find('.p-t-t-temp').eq(1).find('[divt]');
    for (var k = 0; k < currPriceTypes.length; k++) {
        var currpt = currPriceTypes[k];
        if (currpt.type == '1') {
            currPtDiv.find('li')[k].click();
            break;
        }
    }
};
//第二步内的删除价格方案事件
function setRemovePriceTypeTemp(obj, event) {
    var target = $(event.currentTarget);
    var code = target.attr('cn');
    var index = target.attr('tin');
    var instance = setModel.instance();
    var economicIndicators = instance.economicIndicator();
    for (var j = 0; j < economicIndicators.length; j++) {
        var currEconomic = economicIndicators[j];
        if (currEconomic.code != code) continue;
        var arr = currEconomic.tempPrices();
        arr.splice(index, 1);
        currEconomic.tempPrices(arr);
        break;
    }
};
//第二步删除已有的价格方案
function setSelectRemovePriceTypeTemp(obj, event, index) {
    var arr = obj.prices();
    arr.splice(index, 1);
    obj.prices(arr);
};
//建筑设置  新建建筑  第二步  计费类型选择
function addBulidTwoCostType(d, event) {
    var type = parseInt(d.type);
    var index = type - 1;
    var priceTypeDiv = $(event.currentTarget).parents(".price-type-temp").find(".price-type>div");
    var showDiv = priceTypeDiv.eq(index);
    showDiv.show().siblings().hide();
    showDiv.attr('se', 'true');
    showDiv.siblings().attr('se', 'false');
    priceTypeDiv.pctlsRecover();

    var tempPriceIndex = $(event.currentTarget).parent().parent().parent().parent().attr('tmpi');
    var code = d.ecode;
    var instance = setModel.instance();
    var economicIndicators = instance.economicIndicator();
    for (var j = 0; j < economicIndicators.length; j++) {
        var currEconomic = economicIndicators[j];
        if (currEconomic.code != code) continue;
        //currEconomic.unit(d.unit);
        var tempPrices = currEconomic.tempPrices();
        tempPrices[tempPriceIndex].yunit(d.unit.split('/')[1]);
        tempPrices[tempPriceIndex].unit(d.unit);
        break;
    }
};

//无价格信息时的添加价格类型事件
function addPriceType(target) {
    var index = $(target).attr('ix');
    $('#divEcEnEn').psel(index);
    $('[ddadda]').eq(index).click();
};

//用户设置  用户详情float show
function userSetGrilFloatShow(event) {
    $(".select-user-float").css("right", "-30px");
    $(".select-user-float").ptitle("用户详情");
    $(".select-user-float").picon("S");


    selectUserFloat(event);
};
var addUser = true;


//用户设置  编辑 show
function userEditSetGrilFloatShow() {
    setModel.instance().selUser(new user());
    frameModel.instance().currStruct(new companyStruct());
    $(".select-user-float").css("right", "-30px");
    editUserFloat();
    $(".select-user-float").ptitle("添加用户");
    $(".select-user-float").picon("J");
    if (addUser) {
        $("#cancelEditUser").hide();
        addUser = false;
    }
};


//编辑或添加用户的保存事件
function userInfoSave() {
    var isValided = $('#divsetUserInfoEdit').pverifi();
    if (!isValided) return;
    settingController.updateUser();
};

//用户查看，设置
var userIndex = '';

function selectUserFloat(event) {
    $('#divsetUserInfoEdit').phideTextTip();
    $(".selectUserDetail").show();
    $(".editUserDetail").hide();
    userIndex = $(event.target).parents("li").index();
    $(".select-user-float").ptitle("用户详情");
    $(".select-user-float").picon("S");

};
//用户设置  用户详情float hide
function userSetGrilFloatHide() {
    $('#divsetUserInfoEdit').pctlsRecover();
    $('#divAddUserInfoRole .edit-u-r-ck').children().each(function () {
        var target = $(this);
        if (target.attr('code') != 'PC-Normal')
            $(this).poffState();
    });
    $("#selsetUserGrid").poff(userIndex);
    $(".select-user-float").css("right", "-600px");


};

function editUserFloat() {
    $('#divsetUserInfoEdit').phideTextTip();
    $(".selectUserDetail").hide();
    $(".editUserDetail").show();
    var currUser = setModel.instance().selUser();
    $('#txtUserName').pval(currUser.name);
    $('#txtUserEmail').pval(currUser.email == '--' ? '' : currUser.email);
    $('#txtUserMobile').pval(currUser.phone == '--' ? '' : currUser.phone);

    $(".select-user-float").ptitle("编辑用户");
    $(".select-user-float").picon("e");

    $("#cancelEditUser").show(); //编辑状态下的取消显示
    addUser = true;
    settingController.userEditEvent();

};
//用户设置，添加组织结构树
function userStructureTreeShow() {
    $(".userSet-detail-con").hide();
    $(".userSet-Structure-tree").show();
    $('#divTreeEditCancel').click();
}

function userStructureTreeHide() {
    userStructureTreeCancel(back);

    function back() {
        $(".userSet-detail-con").show();
        $(".userSet-Structure-tree").hide();
    }
}

function userStructureTreeEdit() {
    setModel.instance().isEditStruct(true);
    $(".userSet .u-s-tree-con").addClass("edit").find("input").attr("readonly", false);
    $(".userSet .save-but").show();
    $(".userSet .edit-but").hide();
}

function userStructureTreeSave() {
    var inputs = $('#divStTreeFirst input').filter(':visible');
    var oldNames = [];
    for (var i = 0; i < inputs.length; i++) {
        var currInput = inputs.eq(i);
        var currVal = currInput.val();
        if (!currVal) {
            currInput.next().text('组织名称不可为空');
            currInput.addClass("error");
            return currInput.next().show();
        }
        if (currVal.length > 20) {
            currInput.next().text('组织名称不可超过20个字符');
            currInput.addClass("error");
            return currInput.next().show();
        }
        var isReply = oldNames.some(function (curr) {
            return curr == currVal;
        });
        if (isReply == true) {
            currInput.next().text('组织名称不可重复');
            currInput.addClass("error");
            return currInput.next().show();
        }
        oldNames.push(currVal);
        currInput.removeClass("error");
        currInput.next().hide();
    }
    settingController.structSave();
};

function userStructureTreeCancel(call) {
    if (setModel.instance().isEditStruct() == true) {
        $('#v6s-setwrap-warning').pshow([back], '您确定要离开此页面吗？', '离开后，编辑的内容将不会保存');
    } else back();

    function back() {
        setModel.instance().isEditStruct(false);
        $(".userSet .u-s-tree-con").removeClass("edit").find("input").attr("readonly", true);
        $(".userSet .save-but").hide();
        $(".userSet .edit-but").show();
        settingController.initStructs();
        if (typeof call == 'function') call();
    }
}

/*重置密码事件*/
function resetPassEvent() {
    $('#v6s-setwrap-warning').pshow([settingController.initPass], '您确定要为该用户重置密码吗？', '重置后的密码为123456');
};

/*删除用户事件*/
function delUserEv() {
    $('#v6s-setwrap-warning').pshow([settingController.delUser], '您确定删除该用户吗？', '被删除的用户无法恢复');
};
/*用户名重复性及长度检查检查*/
function userIdValid(call) {
    var value = $('#txtUserId').pval();
    if (!value) return;
    if (value.length > 20) return $('#txtUserId').pshowTextTip('用户名不可超过20个字符！');
    var oldUserId = setModel.instance().selUser().userId;
    if (oldUserId && oldUserId == value) return;
    settingController.userNameValid();
};
/*姓名长度检查*/
function nameValid(obj, event) {
    var value = event.currentTarget.value;
    if (value.length > 20) return $('#txtUserName').pshowTextTip('姓名不可超过20个字符！');
}
/*建筑ID重复性及长度检查*/
function buildIdValid(obj, event) {
    var value = event.currentTarget.value;
    if (!value) return;
    for (var x in value) {
        if (/[0-9a-zA-Z]/g.test(value[x]) == false) return $('#buildId').pshowTextTip('建筑ID只可输入字母和数字！');
    }
    if (value.length > 20) return $('#buildId').pshowTextTip('建筑ID不可超过20个字符！');
    settingController.buildIdValid(value);
};

/*建筑名称重复性及长度检查*/
function buildNameValid(obj, event) {
    var value = event.currentTarget.value;
    if (!value) return;
    if (value.length > 20) return $('#buildName').pshowTextTip('建筑名称不可超过20个字符！');
    settingController.buildNameValid(value);
};

/*经度长度检查*/
function buildLongitudeValid(obj, event) {
    var value = event.currentTarget.value;
    if (value.length > 20) return $('#buildLongitude').pshowTextTip('长度不可超过20个字符！');
};

/*纬度长度检查*/
function buildLatitudeValid(obj, event) {
    var value = event.currentTarget.value;
    if (value.length > 20) return $('#buildLatitude').pshowTextTip('长度不可超过20个字符！');
};

/*海拔长度检查*/
function buildAltitudeValid(obj, event) {
    var value = event.currentTarget.value;
    if (value.length > 20) return $('#buildAltitude').pshowTextTip('长度不可超过20个字符！');
};

/*建筑图片选择事件*/
function buildImgSelChange(target) {
    var file = target.files[0];
    if (file.type != 'image/jpeg' && file.type != 'image/png')
        return $('#pcTip').pshow('failure', '错误的图片格式');
    target.value = '';
    $(target).next().next().show();
    settingController.uploadBuildImg(file, document.getElementById(target.getAttribute('for')));
};

/*建筑文档选择事件*/
function buildDocSelChange(target) {
    settingController.uploadBuildDoc(document.getElementById(target.getAttribute('u')), target);
};

/*建筑文档删除*/
function buildDocDelEvent(event) {
    if (event.srcElement.tagName != 'B') return;
    var li = $(event.srcElement).parent();
    var newFile = li.attr('newFile');
    newFile == 'true' ? li.attr('newFile', 'false') : li.attr('del', 'true');
    li.attr('old', 'false');
    var count = li.siblings().filter('li:visible').length;
    var countDiv = $('#' + li.parent().attr('cv'));
    var label = $(event.srcElement).parent().parent().parent().prev().find('label')[0].setAttribute("disabled", "false");
    li.hide();
    countDiv.text(count + '/50');
};

/*建筑删除*/
function buildDel() {
    $('#v6s-setwrap-warning').pshow([settingController.deleteBuilds], '您确定删除该建筑吗？', '被删除的建筑无法恢复');
}