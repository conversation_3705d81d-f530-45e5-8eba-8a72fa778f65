Vue.component("component-downcombobox", {
    template: "#template-downCombox",
    props: {
        downArr: {
            type: Array
        }
    },
    data: function () {
        return {
            isShow: false
        }
    },
    methods: {
        handle: function (index) {
            this.isShow = !this.isShow;
            if ($('.downcombobox-con').is(':visible')) {
                $('.downcombobox-con').hide();
            } else {
                $('.downcombobox-con').show();
            }
            restoreSearch();
            $('.chooseType .combobox-level-menu ').hide();
            $('#dataCompataBulid ._combobox_bottom  ').hide();
            $('#dataComparaTimer >._combobox_bottom').hide();
        },
        downSomething: function (event, index) {
            this.isShow = !this.isShow;
            $('.downcombobox-con').hide();
            switch (index) {
                case 0: // 下载图表
                    var hearStr = '<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><style>.highcharts-tooltip{display:none;}</style></head><body>',
                        footStr = '</body></html>';
                    var _htmlStr = hearStr + $('#comparative-chart').html() + footStr;
                    var model = monitorModel.instance();
                    var imgName = "租户" + model.selecteddataCpmpara.name + "数据图表";
                    controller.downFileByHtmlContent(_htmlStr, imgName, "img");
                    break;
                case 1: //下载报表
                    controller.downReport();
                    break;
            }
        },
    }
});
Vue.component("component-recursion-tree", {
    template: "#template-recursion-tree",
    props: ["recursionArr", "selected", "formType"],
    name: "component-recursion-tree",
    methods: {
        choose: function (o) {
            $('#dataComparaTimer > ._combobox_bottom').hide();
            $('#dataCompataBulid ._combobox_bottom ').slideUp();
            $('#dataAnalysispage .combobox-level-menu').slideUp();
            restoreSearch();
            var model = monitorModel.instance();
            if (!o.isChecked && model.chooseTimeArr.length > 1 && model.tenementReportArr.length > 0) {
                //当前操作为选择租户
                //时间为多选，则，租户为单选，把已经选择的租户干掉
                //找出选择的租户，干掉
                //肯定是这个租户，因为租户为单选
                var tenentId = model.tenementReportArr[0]._tenentId;
                controller.removeTenentChart(tenentId);
                var ok = false;
                for (var i = 0; i < model.world.length; i++) {
                    var orgTenantList = model.world[i].tenantList;
                    for (var j = 0; j < orgTenantList.length; j++) {
                        if (orgTenantList[j].id == tenentId) {
                            orgTenantList[j].isChecked = false;
                            ok = true;
                        }
                    }
                    if (ok) {
                        break;
                    }
                }
            }
            o.isChecked = !o.isChecked;
            // 检查选中个数
            var countNum = 0;
            if (model.isshowMoney) {
                for (var i = 0; i < model.world.length; i++) {
                    var orgTenantList = model.world[i].tenantList;
                    for (var j = 0; j < orgTenantList.length; j++) {
                        var oneItem = orgTenantList[j];
                        if (oneItem.isChecked) {
                            countNum++;
                        }
                    }
                }
                if (countNum > 10) {
                    o.isChecked = !o.isChecked;
                    $("#notice-con").pshow({
                        text: "不能大于10条",
                        state: "failure"
                    })
                    return;
                }
            }

            var chartSeries = dataComparaChart.series;
            if (!o.isChecked) {
                //取消线
                controller.removeTenentChart(o.id);
            } else {
                controller.compareIndex++;
                //添加线
                for (var i = 0; i < model.chooseTimeArr.length; i++) {
                    var timeObj = model.chooseTimeArr[i];
                    var startDate = timeObj.dataStartTime;
                    var endDate = timeObj.dataEndTime;
                    controller.getFNMEnergyDataListService({
                        density: ptool.formatGranularityToJava($("#dataComparaTimer")),
                        doType: 1,
                        endDate: endDate,
                        startDate: startDate,
                        meterId: null,
                        tenantId: o.id,
                        tenantName: o.name,
                        typeId: model.selecteddataCpmpara.id
                    }, o, controller.replaceTimer);
                }
            }
        },
        viewToggle: function (event) {
            $('#dataComparaTimer > ._combobox_bottom').hide();
            $('#dataCompataBulid ._combobox_bottom ').slideUp();
            $('#dataAnalysispage .combobox-level-menu').slideUp();
            restoreSearch();
            var $target = $(event.target);
            $target.text() === "r" ? $target.text("b") : $target.text("r");
            $target.closest(".treeTitle").siblings("ul").slideToggle();
        },
        viewToggleTit: function (event) {
            $('#dataComparaTimer > ._combobox_bottom').hide();
            $('#dataCompataBulid ._combobox_bottom ').slideUp();
            $('#dataAnalysispage .combobox-level-menu').slideUp();
            restoreSearch();
            var $target = $(event.target);
            if (!$target.hasClass('isFloor')) return;
            $target.find('span.arrow').text() === "r" ? $target.find('span.arrow').text("b") : $target.find('span.arrow').text("r");
            $target.closest(".treeTitle").siblings("ul").slideToggle();
        }
    }
});
Vue.component("component-choosetype", {
    template: "#template-chooseType",
    props: {
        datachoosetypes: {
            type: Array
        },
        selecteddatacpmparatype: {
            type: Object
        },
        isshow: {
            type: Boolean,
            default: true
        }
    },
    data: function () {
        return {
            isShow: false
        }
    },
    methods: {
        toggle: function (event) {
            $('#dataComparaTimer >._combobox_bottom').hide();
            $('#dataCompataBulid ._combobox_bottom ').slideUp();
            $('#float-diyAlarmset').hide();
            $('.downcombobox-con').hide();
            $('#detailTimerAll ._combobox_bottom').hide();
            $('#detailTimerexceptY ._combobox_bottom').hide();
            $('#detailTimerOnlyD ._combobox_bottom').hide();
            restoreSearch();
            $('.timeChartBox').hide();
            var tag = $(event.currentTarget);
            if (tag.siblings('.combobox-level-menu').is(':visible')) {
                tag.siblings('.combobox-level-menu').slideUp();
            } else {
                tag.siblings('.combobox-level-menu').slideDown();
            }
        },
        _selType: function (_type, defaultType) {
            var unit;
            var model = monitorModel.instance();
            if (_type.name.indexOf('对比') > -1) {
                model.isDuiBiCha = true;
            } else {
                model.isDuiBiCha = false;
            }
            model.sumShow = true;
            switch (_type.name) {
                case '耗电量':
                    unit = 'kWh';
                    break;
                case '电功率':
                    unit = 'kW';
                    model.sumShow = false;
                    break;
                case '费用':
                case '剩余金额':
                    unit = '元';
                    break;
                case '耗水量':
                case '耗热水量':
                case '耗燃气量':
                    unit = 'm³';
                    break;
                default:
                    unit = 'kWh';
            }
            switch (_type.id) {
                case 'Dian_ShengYuLiang':
                    unit = 'kWh';
                    break;
                case 'Dian_DianGongLv':
                    unit = 'kW';
                    break;
                case 'Shui_ShengYuLiang':
                case 'ReShui_ShengYuLiang':
                case 'RanQi_ShengYuLiang':
                    unit = 'm³';
                    break;
            }
            $('#chooseType .combobox-level-menu').hide();
            if (!$('#detailFloat').is(':visible')) {
                if (_type.id == model.selecteddataCpmpara.id) {
                    return;
                }
                model.selecteddataCpmpara.name = _type.name;
                model.selecteddataCpmpara.id = _type.id;
                model.selecteddataCpmpara.unit = unit;
                //数据对比
                model.chooseTimeArr = [];
                model.tenementReportArr = [];
                if (dataComparaChart.series) {
                    var chartSeries = dataComparaChart.series;
                    var needRemove = [];
                    for (var j = 0; j < chartSeries.length; j++) {
                        var seriesItem = chartSeries[j];
                        needRemove.push(seriesItem);
                    }
                    for (var j = 0; j < needRemove.length; j++) {
                        needRemove[j].remove(true);
                    }
                }
                $('#searchTement').pval('');
                var _index = $('#dataCompataBulid').psel().index;
                var witchTab = $('#buttonTab').psel(); //0:租户 1:房间
                if (witchTab) {
                    controller.getroomlist({
                        buildingId: model.copybuilds[_index].id,
                        typeId: model.selecteddataCpmpara.id
                    })
                } else {
                    controller.getFNMFloorTreeService({
                        buildingId: model.copybuilds[_index].id,
                        typeId: model.selecteddataCpmpara.id
                    })
                }
            } else {
                //详情
                if (_type.id == model.selecteddataCpmparaType.id) {
                    return;
                }
                model.selecteddataCpmparaType.name = _type.name;
                model.selecteddataCpmparaType.id = _type.id;
                model.selecteddataCpmparaType.unit = unit;
                loadChart();
                model.prevDetailTime = {
                    timeType: 'd',
                    startTime: new Date().getTime()
                };
                if (_type.name === '剩余量' || _type.name === '电功率') {
                    if (_type.name === '剩余量') {
                        var detailTimer = '#detailTimerexceptY';
                        var timeObj = $(detailTimer).psel();
                    } else {
                        var detailTimer = '#detailTimerOnlyD';
                        var timeObj = $(detailTimer).psel();
                    }
                    controller.getFNMMeterListService({
                        tenantId: monitorModel._instance.selectedchunk.tenantId,
                        typeId: _type.id,
                    });
                } else {
                    var detailTimer = '#detailTimerAll';
                    var timeObj = $(detailTimer).psel();
                    controller.getFNMEnergyInfoService({
                        density: ptool.formatGranularityToJava($(detailTimer)),
                        endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
                        startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
                        tenantId: model.selectedchunk.tenantId,
                        typeId: model.selecteddataCpmparaType.id,
                        meterId: null
                    });
                    controller.getFNMEnergyDataListService({
                        density: ptool.formatGranularityToJava($(detailTimer)),
                        doType: 0,
                        endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
                        startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
                        meterId: null,
                        tenantId: model.selectedchunk.tenantId,
                        typeId: model.selecteddataCpmparaType.id
                    });
                }
            }
        },
        _isshow: function (item) {
            if (!this.isshow) {
                return item.id.split('_')[1] !== 'FeiYong';
            } else {
                return item.id.split('_')[1] !== 'DuiBiCha';
            }
        }
    }
});
Vue.component("component-recursion-tree2", {
    template: "#template-recursion-tree2",
    props: ["recursionArr", "isfold", "selected", "formType"],
    name: "component-recursion-tree2",
    methods: {
        choose: function (o) {
            $('#dataComparaTimer > ._combobox_bottom').hide();
            $('#dataCompataBulid ._combobox_bottom ').slideUp();
            $('#dataAnalysispage .combobox-level-menu').slideUp();
            restoreSearch();
            var model = monitorModel.instance();
            if (!o.isChecked && model.chooseTimeArr.length > 1 && model.tenementReportArr.length > 0) {
                //当前操作为选择租户
                //时间为多选，则，租户为单选，把已经选择的租户干掉
                //找出选择的租户，干掉
                //肯定是这个租户，因为租户为单选
                var tenentId = model.tenementReportArr[0]._tenentId;
                controller.removeTenentChart(tenentId);
                var ok = false;
                for (var i = 0; i < model.roomwatch.length; i++) {
                    var _world = model.roomwatch[i];
                    for (var j = 0; j < _world.dataList.length; j++) {
                        var watch = _world.dataList[j].dataList;
                        for (var l = 0; l < watch.length; l++) {
                            if (watch[l].id == tenentId) {
                                watch[l].isChecked = false;
                                ok = true;
                            }
                        }
                        if (ok) {
                            break;
                        }
                    }
                }
            }
            o.isChecked = !o.isChecked;
            // 检查选中个数
            var countNum = 0;
            for (var i = 0; i < model.roomwatch.length; i++) {
                var _world = model.roomwatch[i];
                for (var j = 0; j < _world.dataList.length; j++) {
                    var watch = _world.dataList[j].dataList;
                    for (var l = 0; l < watch.length; l++) {
                        if (watch[l].isChecked) {
                            countNum++;
                        }
                    }
                }
            }
            if (countNum > 10) {
                o.isChecked = !o.isChecked;
                $("#notice-con").pshow({
                    text: "不能大于10条",
                    state: "failure"
                })
                return;
            }
            if (countNum > 1 && model.isDuiBiCha) {
                o.isChecked = !o.isChecked;
                $("#notice-con").pshow({
                    text: "对比差只能单选",
                    state: "failure"
                })
                return;
            }

            var chartSeries = dataComparaChart.series;
            if (!o.isChecked) {
                //取消线
                controller.removeTenentChart(o.id);
            } else {
                controller.compareIndex++;
                //添加线
                for (var i = 0; i < model.chooseTimeArr.length; i++) {
                    var timeObj = model.chooseTimeArr[i];
                    var startDate = timeObj.dataStartTime;
                    var endDate = timeObj.dataEndTime;
                    controller.getFNMRoomEnergyDataListService({
                        density: ptool.formatGranularityToJava($("#dataComparaTimer")),
                        doType: 0,
                        endDate: endDate,
                        startDate: startDate,
                        meterId: o.id,
                        buildingId: model.selBuild.id,
                        typeId: model.selecteddataCpmpara.id
                    }, o, controller.replaceTimer);
                }
            }
        },
        viewToggle: function (event) {
            $('#dataComparaTimer > ._combobox_bottom').hide();
            $('#dataCompataBulid ._combobox_bottom ').slideUp();
            $('#dataAnalysispage .combobox-level-menu').slideUp();
            restoreSearch();
            var $target = $(event.target);
            $target.text() === "r" ? $target.text("b") : $target.text("r");
            $target.closest(".treeTitle").siblings("ul").slideToggle();
            $target.closest(".treeTitle").siblings("ul").find('li >div').show();
        },
        viewToggleTit: function (event) {
            $('#dataComparaTimer > ._combobox_bottom').hide();
            $('#dataCompataBulid ._combobox_bottom ').slideUp();
            $('#dataAnalysispage .combobox-level-menu').slideUp();
            restoreSearch();
            var $target = $(event.currentTarget);
            $target.find('span.arrow').text() === "r" ? $target.find('span.arrow').text("b") : $target.find('span.arrow').text("r");
            $target.closest(".treeTitle").siblings("ul").slideToggle();
            $target.closest(".treeTitle").siblings("ul").find('li >div').show();

        }
    }
});