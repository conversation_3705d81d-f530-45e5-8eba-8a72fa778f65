var tenementEvent = {
    dataArr: [{
        name: "总览查看",
        icon: 'M'
    }, {
        name: "巡检查看",
        icon: 'E'
    }, {
        name: "数据分析",
        icon: '*'
    }, {
        name: "报警管理",
        icon: "Q"
    }],
    pollingbuttonArr: [{
        name: '查看楼层'
    }, {
        name: '查看业态'
    }]
};
var buttonArr = [{
    name: '1号',
    id: '1'
}, {
    name: '2号',
    id: '2'
}, {
    name: '3号',
    id: '3'
}, {
    name: '4号',
    id: '1'
}, {
    name: '5号',
    id: '2'
}, {
    name: '6号',
    id: '3'
}, {
    name: '7号',
    id: '1'
}, {
    name: '8号',
    id: '2'
}, {
    name: '9号',
    id: '3'
}];
var floatRight = -($('#tenementMonitor').width() - 100);
/*事件注册*/
$(function () {
    $(".dialog-wrap").removeClass("animationBottom");
    $("#floorTable .pieceItem").on('mousemove', function () {
        $(this).find('.alarmContent').show();
        var overHeight = $('#overviewPage').offset().top;
        var thisHeight = $(this).offset().top;

        var acHeight = $(this).find('.alarmContent').height();
        if (acHeight > thisHeight - overHeight) {
            $(this).find('.alarmContent').addClass('edge');
        }
    }).on('mouseout', function () {
        $(this).find('.alarmContent').hide();
    });
    $("#roundsPage .rpLeftp_foot .rpLeftp_body").on('mousemove', 'li .alarmCellContent', function () {
        $(this).parent().find('.alarmCellDetail').show();
    }).on('mouseout', 'li .alarmCellContent', function () {
        $(this).parent().find('.alarmCellDetail').hide();
    });
    $('.buildChangebtn').on('click', function () {
        monitorModel.instance().curBuild = true;
        $(" #modal-bulid .buildList_wrap .buildLIst li b").removeClass("leftCross");
        $('#currentBuild').addClass('leftCross');
        $('#modal-bulid').pshow();
    })
    $("#detailPage .timeContent").on('click', function (e) {
        e.stopPropagation();
        $('.combobox-level-menu').slideUp();
        $('#float-diyAlarmset').hide();
        $(".float_w").hide();
        if ($(this).parent().find('.timeChartBox').is(":hidden")) {
            $(this).parent().find('.timeChartBox').show();
            var model = monitorModel.instance();
            //将时间重置为当前时间
            var timechart = $('.timeChartBox [p-type="time-chart"]:visible');
            if (model.selecteddataCpmparaType.id === 'Dian_DianGongLv') {
                timechart.psetTime();
                timechart.psetType('d');
                model.newshowTime = new Date().format('yyyy.MM.dd');
            } else {
                timechart.psetType(_timeObj.timeType);
                timechart.psetTime({
                    y: new Date(_timeObj.startStr).getFullYear(),
                    M: new Date(_timeObj.startStr).getMonth() + 1,
                    d: new Date(_timeObj.startStr).getDate()
                });
            }

            if (model.currentPage !== 'dataAnalysispage' && model.selecteddataCpmparaType.name === '剩余量') {
                $('#detailPage_tc2 .year-box .cur').css('pointer-events', 'none');
                model.newshowTime = new Date().format('yyyy.MM.dd');
                model.density = 1;
                $('#detailPage_tc2').psetType('d');
                $('#detailPage_tc2').psetTime();
                _timeObj = {
                    timeType: 'd',
                    startStr: new Date().getTime()
                };
            } else {
                if ($('#detailPage_tc2')) {
                    $('#detailPage_tc2 .year-box .cur').css('pointer-events', 'auto');
                }
            }
        } else {
            $(this).parent().find('.timeChartBox').hide();
        }

    });
    $(".inDetail").on('click', function (e) {
        e.stopPropagation();
        console.log('l');
    })
    $("#chooseType .combobox-level").on('click', function (e) {
        e.stopPropagation();
        $('.timeChartBox').hide();
        $(".float_w").hide();
        if ($(this).parent().find('.combobox-level-menu').is(":hidden")) {
            $(this).parent().find('.combobox-level-menu').slideDown();
        } else {
            $(this).parent().find('.combobox-level-menu').slideUp();
        }
    });
    $("#chooseType .combobox-level2 >li>span").on('click', function (e) {
        e.stopPropagation();
        $("#chooseType .combobox-level >em").html($(this).html());
        $("#chooseType .combobox-level-menu").slideUp();
    })
    $(".checkMoth").on('click', function (e) {
        e.stopPropagation();
        $("#float-checkMoth").show();
        controller.getMonthlyEnergys();
    });
    $('#floorTable').on('mouseover', function () {
        $('#globalBuild .combobox-menu-bottom').slideUp();
    });
    $('#rPtent').on('mouseover', function () {
        $('#globalBuild .combobox-menu-bottom').slideUp();
    });
    Highcharts.setOptions({
        global: {
            timezoneOffset: -8 * 60
        }
    });

    $(document).on("click", function (event) {
        var tg = event.target;
        $('#globalBuild .combobox-menu-bottom').slideUp();
        $(".downcombobox-con").slideUp();
        !$(tg).hasClass('postilSwitch0') && !$(tg).parents('#float-postil').length && $('#float-postil').hide();
        $(".timeChartBox").hide();
        $(".combobox-level-menu").slideUp();
        $(".muteTimeCombobox2").slideUp();
        $(".muteTimeCombobox").slideUp();
        if (!$(tg).parents('#float-diyAlarmset')[0]) {
            $('#float-diyAlarmset').hide();
        }
        $('.topsearchWrap .searchHint').hide();
        if ($(tg).parents('#detailTimerWrap')[0]) {
            return;
        }
        $('#detailTimerWrap ._combobox_bottom ').hide();
        if ($(tg).parents('#dataComparaTimer')[0]) {
            return;
        }
        $('#dataComparaTimer ._combobox_bottom ').hide();
    });
    $('div[p-type="tab-navigation"] ul').on('click', function (e) {
        e.stopPropagation();
        $('#float-postil').hide();
    });
    $(".timeChartBox").on("click", function (e) {
        e.stopPropagation();
    });
    $('#searchTement').on('focus', 'input', function () {
        $('#dataComparaTimer >._combobox_bottom').hide();
    });
    $('.topSearch').on('focus', 'input', function () {
        $('#dataComparaTimer >._combobox_bottom').hide();
    });

    function ellipsis(e) { //鼠标滑过显示全称
        var target = e.target;
        var containerLength = $(target).width();
        var textLength = target.scrollWidth;
        var paddingL = parseInt($(target).css('padding-left'));
        var paddingR = parseInt($(target).css('padding-right'));
        var text = $(target).text().replace(/\s/g, ''); //去除所有空格
        if (textLength > (containerLength + paddingL + paddingR)) {
            $(target).attr('title', text);
        }
    }

    $("body").on("mouseenter", ".slh", ellipsis);
    controller.init();
    $('#mainTab').psel(0, false);
});
/*鼠标移入*/
//定位tips
function positionTips(obj) {
    /*定位*/
    var overHeight = $('#overviewPage').offset().top;
    var overx = $('#overviewPage').offset().left;
    var over_height = document.getElementById('overviewPage').clientHeight;
    var overWidth = $('#overviewPage').width();
    var tippart = $(obj).find('.alarmContent');
    var tippartoffsetX;
    tippartoffsetX = tippart.offset().left;
    if (tippartoffsetX <= overx) {
        tippart.css({
            'left': '0px'
        }).removeClass('basicsposition');
    }
    // 右边的边界
    if (tippart.width() + tippartoffsetX >= overWidth) {
        tippart.css({
            'right': '0px'
        }).removeClass('basicsposition');
    }
    //下边的边界
    if (over_height < tippart.offset().top - overHeight + tippart.find('.alarmContentWrap').height() + 30) {
        tippart.css({
            'bottom': '100%',
            'top': 'auto'
        });
    }
}
/*鼠标移出*/
function hideTentTip(obj) {
    $(obj.currentTarget).find('.alarmContent').hide();
}

function enterMonthDetail(v) {
    var timeStr = $(v).prev().text(); //"2016.011"
    var model = monitorModel.instance();
    model.showTime = timeStr;
    $('#detailPage_tc').psetTime({
        y: timeStr.slice(0, 4),
        M: timeStr.slice(5, 7)
    })
    model.selectedTime.time = timeStr.replace(/\./g, '-') + '-01 00:00:00';
    controller.getdailyEnergy();
}

function powerBubbleClick(v) {
    console.log(v);
    $("#float-postil").show();
}

function closeVoicemodel() {
    $("#modal-voice").phide();
}

function gg(e, data) {
    e.stopPropagation();
    var model = monitorModel.instance();
    var ms = event.point.category;
    var timeType = '';
    model.showTime3 = new Date(ms).format('yyyy.MM.dd');
    var detailTimer = getdetailTimerId();
    switch (model.density) {
        case 1:
            return false;
            break;
        case 4: //年--->月
            model.density = 2;
            var date = new Date(ms);
            var y = date.getFullYear();
            var m = date.getMonth();
            var startTime = new Date(y, m, 1, 0, 0, 0).getTime();
            var endTime = new Date(y, m, date.getMonthLength(), 0, 0, 0).getTime();
            $(detailTimer).psel({
                timeType: 'M',
                startTime: startTime
            }, false);
            model.newshowTime = new Date(ms).format('yyyy.MM');
            model.detailTitleTime = new Date(ms).format('yyyy.MM');
            timeType = 'M';
            break;
        case 2: //月--->日
            model.density = 1;
            var date = new Date(ms);
            var y = date.getFullYear();
            var m = date.getMonth();
            var d = date.getDate();
            var startTime = new Date(y, m, d, 0, 0, 0).getTime();
            var endTime = new Date(y, m, d, 0, 0, 0).getTime();
            $(detailTimer).psel({
                timeType: 'd',
                startTime: startTime
            }, false);
            model.newshowTime = new Date(ms).format('yyyy.MM.dd');
            model.detailTitleTime = new Date(ms).format('yyyy.MM.dd');
            timeType = 'd';
            break;
        case 5: //年段-->年
            model.density = 4;
            var date = new Date(ms);
            var y = date.getFullYear();
            var startTime = new Date(y, 1, 1, 0, 0, 0).getTime();
            var endTime = new Date(y + 1, 1, 1, 0, 0, 0).getTime();
            $(detailTimer).psel({
                timeType: 'y',
                startTime: startTime
            }, false);
            model.newshowTime = new Date(ms).format('yyyy');
            model.detailTitleTime = new Date(ms).format('yyyy');
            timeType = 'y';
            break;
    }
    _timeObj = {
        timeType: timeType,
        startStr: ms
    };

    controller.getFNMEnergyDataListService({
        density: model.density,
        doType: 0,
        endDate: new Date($(detailTimer).psel().realEndTime).format('yyyy-MM-dd hh:mm:ss'),
        startDate: new Date($(detailTimer).psel().startTime).format('yyyy-MM-dd hh:mm:ss'),
        meterId: null,
        tenantId: model.selectedchunk.tenantId,
        typeId: model.selecteddataCpmparaType.id
    });
    controller.getFNMEnergyInfoService({
        density: model.density,
        endDate: new Date($(detailTimer).psel().realEndTime).format('yyyy-MM-dd hh:mm:ss'),
        startDate: new Date($(detailTimer).psel().startTime).format('yyyy-MM-dd hh:mm:ss'),
        tenantId: model.selectedchunk.tenantId,
        typeId: model.selecteddataCpmparaType.id,
        meterId: $('#meterWraperCombo').is(':visible') ? (model.meterArr[$('#meterWraperCombo').psel().index].meterId) : null
    });
}

function savePostil() {
    if (monitorModel.instance().content.length > 100) {
        $("#notice-con").pshow('failure', '批注内容不可超过100字');
    } else {
        controller.saveAlarmComment();
    }
}

function getDiyAlarmSet(e) {
    monitorModel.instance().isdiyAlarm = true;
    monitorModel.instance().isdiy = false;
    e.stopPropagation();
    $('#detailTimerWrap ._combobox_bottom ').hide();
    $('#chooseType .combobox-level-menu').slideUp();
    if ($('#float-diyAlarmset').is(':visible')) {
        $('#diyAlarm').psel('off');
        $('#followGalarm').psel('off');
        $('#float-diyAlarmset').hide();
    } else {
        $('#float-diyAlarmset').show();
        controller.getcustomAlarmSetArr();
    }

    //controller.getAlarmSet(true, false);       //此处也可不传,这里面会显示这个弹窗，先隐藏掉
}

function resetToGlobalAlarm() {
    var model = monitorModel.instance();
    controller.saveAlarmSet(model.alarmSets, true, false);
}

function toSaveAlarmSet() {
    controller.setFNMAlarmLimitSetUpService();
}

function saveDiyAlarmSet() {
    var model = monitorModel.instance();
    var isModifyThreshold = false;

    var alarmSets = model.customAlarmSets;
    for (var i = 0; i < alarmSets.length; i++) {
        if (alarmSets[i].status && !alarmSets[i].isFollowGlobal && !alarmSets[i].customThreshold && parseFloat(alarmSets[i].customThreshold) !== 0) {
            $('#notice-con').pshow('failure', '报警开关开启时，输入框不能为空');
            return;
        }
        alarmSets[i].customThreshold = /*alarmSets[i].status && */ !alarmSets[i].isFollowGlobal ? parseFloat(alarmSets[i].customThreshold) : 0;
    }

    for (var i = 0; i < model.customAlarmSets.length; i++) {
        var item = model.customAlarmSets[i];
        var itemCopy = model.customAlarmSetsCopy[i];
        var overproof = item.name.slice(item.name.length - 2, item.name.length) == '超标';
        if (overproof && item.status /* != item.status ||*/ && ((itemCopy.isFollowGlobal && !item.isFollowGlobal && itemCopy.threshold != item.customThreshold) || (!itemCopy.isFollowGlobal && item.isFollowGlobal && itemCopy.customThreshold != item.threshold) || (!itemCopy.isFollowGlobal && !item.isFollowGlobal && itemCopy.customThreshold != item.customThreshold))) { //此处判断需与中间层确认属性threshold、customThreshold数据
            isModifyThreshold = true;
            break;
        }
    }
    model.isGlobal = false;
    if (isModifyThreshold) {
        $('#modal-editAlarm').pshow();
    } else {
        toSaveAlarmSet();
    }
}

function setTentCellHeight() {
    var liLen = $('#rPtent li').length;
    var _html = '';
    for (var i = 0; i < liLen; i++) {
        var len = $($("#rPtent li")[i]).find(".tentCellwrap").length;
        for (var j = 0; j < len; j++) {
            var n = $($($("#rPtent li")[i]).find(".tentCellwrap")[j]).attr("across");
            var n1 = $($($("#rPtent li")[i + 1]).find(".tentCellwrap")[j]).attr("across");
            var n2 = $($($("#rPtent li")[i - 1]).find(".tentCellwrap")[j]).attr("across");
            var key = $($($("#rPtent li")[i]).find(".tentCell")[j]).find(".rptentName").html();
            var key1 = $($($("#rPtent li")[i + 1]).find(".tentCell")[j]).find(".rptentName").html();
            var key2 = $($($("#rPtent li")[i - 1]).find(".tentCell")[j]).find(".rptentName").html();
            if (n == n1 && n > 1 && key == key1 && n != n2 && key != key2) {
                var str = $($($("#rPtent li")[i]).find(".tentCell")[j]).html();
                _html = "<div class='isAcolumns tentAcross' style='height:" + ((n - 1) * 2 * 4 + 90 * n) + "px;'>" + str + "</div>";
                $($($("#rPtent li")[i]).find('.tentCellwrap')[j]).append(_html);
            }
        }
    }
}

function hideNoDataPrompt(id) {
    var promptId = id ? id : '#tenementNoEnergy';
    $(promptId).hide();
    $(promptId).siblings().show();
}

function tenantHasEnergy(tenantEnergys, energyId) {
    var hasEnergy = false;
    for (var i = 0; i < tenantEnergys.length; i++) {
        if (tenantEnergys[i].id === energyId) hasEnergy = true;
    }
    return hasEnergy;
}

function showNoDataPrompt(id, class1, class2, hideAllTips) {
    $("#detailPage  .timeBox > .timeChartBox").hide();

    $(id).find(class1).hide();
    hideAllTips ? $(id).find(class2).hide() : $(id).find(class2).show();
    $(id).siblings().hide();
    $(id).show();
}

function isTimeValid(timeObj, flag) {
    var model = monitorModel.instance();
    var isValid = false;
    var timeType = timeObj.timeType;
    if (timeType === 'd' && new Date(timeObj.startStr).getTime() < new Date(model.selectedTenement.activatedTime).getTime() || timeType === 'M' && new Date(timeObj.startStr).getTime() < new Date(model.selectedTenement.activatedTime.slice(0, 8) + '01').getTime()) {
        var id = flag === 'daily' ? '#tenementNoEnergy' : '#tenementNoEnergy1';
        showNoDataPrompt(id, '.tip1', '.tip2');
    } else if (new Date(timeObj.startStr).getTime() > new Date().getTime()) {
        $('#notice-con').pshow('failure', '不可选择未来时间');
    } else {
        isValid = true;
    }
    return isValid;
}

function detailPage_tcBtn(event) {
    event.stopPropagation();
    var model = monitorModel.instance();
    var detailTimer = getdetailTimerId();
    var timeObj = $(detailTimer).psel();
    var str = '';
    var beginDate = new Date(timeObj.startTime);
    var endDate = new Date(timeObj.endTime);
    var activeDate = new Date(model.selectedchunk.activeTime.replace(/-/g, '/'));
    if (endDate < activeDate) {
        $("#notice-con").pshow({
            state: 'failure',
            text: '无法选择租户激活之前的时间'
        });
        $(detailTimer).psel(model.prevDetailTime, false);
        return;
    } else {
        model.prevDetailTime = {
            timeType: timeObj.timeType,
            startTime: timeObj.startTime
        };
    }
    var timeType = timeObj.timeType;
    var subLen = timeType.indexOf('y') > -1 ? 4 : timeType.indexOf('M') > -1 ? 7 : 10;
    var beginTimeStr = new Date(timeObj.startTime).format('y.M.d').substring(0, subLen);
    var timeStr = beginTimeStr;
    model.newshowTime = timeStr;
    model.density = ptool.formatGranularityToJava($(detailTimer));
    model.realdensity = timeType;
    // todo
    _timeObj = {
        timeType: timeType,
        startStr: timeObj.startStr
    };
    controller.getFNMEnergyDataListService({
        density: model.density,
        doType: 0,
        endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
        startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
        meterId: $('#meterWraperCombo').is(':visible') ? (model.meterArr[$('#meterWraperCombo').psel().index].meterId) : null,
        tenantId: model.selectedchunk.tenantId,
        typeId: model.selecteddataCpmparaType.id
    });
    controller.getFNMEnergyInfoService({
        density: model.density,
        endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
        startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
        tenantId: model.selectedchunk.tenantId,
        typeId: model.selecteddataCpmparaType.id,
        meterId: $('#meterWraperCombo').is(':visible') ? (model.meterArr[$('#meterWraperCombo').psel().index].meterId) : null
    });
    $('#detailTimerWrap ._combobox_bottom ').hide();
    loadChart();
    $('#float-diyAlarmset').hide();
}

function detailPage_tcDayBtn() {
    var model = monitorModel.instance();
    var timeObj = $('#detailPage_tcDay').pgetTime();
    var timeStr = timeObj.startStr.replace(/\//g, '.').substring(0, 10);
    model.showTime3 = timeStr;
    $("#detailPage  .timeBox > .timeChartBox").hide();
    model.selectedTimeValue2 = timeObj.startStr.replace(/\//g, '-').substring(0, 10) + ' 00:00:00';
    if (isTimeValid(timeObj, 'hourly')) {
        getChartData();
    }
}

function closegAlarmset() {
    var typeList = monitorModel.instance().globalAlarmSetArr.typeList;
    for (var i = 0; i < typeList.length; i++) {
        var item = typeList[i].alarmList;
        item.forEach(function (val) {
            if (val.valid && !$('#modal-gAlarmset' + ' ' + '#' + val.typeId).pverifi()) {
                return $('#modal-gAlarmset' + ' ' + '#' + val.typeId).precover();
            }
        });
    }
    $("#modal-gAlarmset").phide();
    globalWarningIsvisible = false;
}

function closeModal() {
    $('#modal-bulid').phide();
}

function setPieceWidth(n) {
    var maxWidth = 100;
    var minWidth = 60;
    var liLen = $("#floorTable > li").length;
    var _html = '';
    for (var j = 0; j < liLen; j++) {
        var len = $($("#floorTable > li")[j]).find('.pieceItem').length;


        for (var i = 0; i < len; i++) {
            if (j < 4) {
                $($($("#floorTable > li")[j]).find('.pieceItem')[i]).find('.alarmContent').addClass('edge');
            }
            var n = $($($("#floorTable > li")[j]).find('.pieceItem')[i]).attr("across");
            var n1 = $($($("#floorTable > li")[j + 1]).find('.pieceItem')[i]).attr("across");
            var n2 = $($($("#floorTable > li")[j - 1]).find('.pieceItem')[i]).attr("across");
            var key = $($($("#floorTable > li")[j]).find('.pieceItem')[i]).find('.tentName').html();
            var key1 = $($($("#floorTable > li")[j + 1]).find('.pieceItem')[i]).find('.tentName').html();
            if (n == n1 && n > 1 && key == key1) {
                var str = $($($("#floorTable > li")[j]).find('.pieceItem')[i]).find('.tentName').html();
                _html = " <div class='across' style='height:" + ((n - 1) * 2 * 4 + 30 * n) + "px;line-height:" + ((n - 1) * 2 * 4 + 30 * n) + "px;'>" + str + "</div>";
                $($($("#floorTable > li")[j]).find('.pieceItem')[i]).append(_html);
            }
        }
    }
}
/*tab页的假数据*/
var buttonMenus = [{
    name: '总览查看',
    icon: 'M'
}, {
    name: '巡检查看',
    icon: 'E'
}, {
    name: '数据分析',
    icon: 'K'
}];
var buttonMenu = ['7天', '6天', '5天', '4天', '3天', '2天', '1天'];
var muteTimeLimits = [1, 2, 3, 4, 5, 6, 7];
var checkDetail = ['查看楼层', '查看业态'];
var surplusOrCostTab = ['剩余量', '每日费用'];
var surplusOrCostTab2 = ['剩余金额', '每日费用'];
var typeTab = ['电', '水', '热水', '燃气', '冷热量'];
var students = [{
    name: 'Jack1111',
    age: '15',
    grade: '三年一班'
}, {
    name: 'Tom11',
    age: '16',
    grade: '三年二班'
}, {
    name: 'Marry11',
    age: '17',
    grade: '三年三班'
}];
var world = [{
    name: '太阳系',
    level: 0,
    child: [{
        name: '地球',
        level: 1,
        child: [{
            name: '中国',
            child: [],
            level: 2
        }]
    }, {
        name: '火星',
        child: [],
        level: 1
    }]
}, {
    name: '太阳系',
    level: 0,
    child: [{
        name: '地球',
        level: 1,
        child: [{
            name: '中国',
            child: [],
            level: 2
        }]
    }, {
        name: '火星',
        child: [],
        level: 1
    }]
}];
var showType = ['图表', '报表', '图表、报表'];
var downType = ['图表', '报表', '图表、报表'];
var timerChoose = ['年', '月', '日'];
var isfollowGlobalAlarm = false; //是否跟随全局报警 ，默认false
var dataComparaChart = null;
var tabPages = ['overviewPage', 'roundsPage', 'dataAnalysispage', "alarmLists"];
var page = {
    overviewPage: 0,
    roundsPage: 1,
    detailPage: 2,
    dataAnalysispage: 3,
    alarmLists: 4 //报警管理页面

};
var globalWarningIsvisible = false; //全局报警设置是否显示，默认false
var _timeObj = {
    timeType: 'd',
    startStr: new Date().getTime()
}; //详情的时间存储 默认今天
var isrunInhole = true; //修改chart之后点击时间
var meterArrcopy;
var dataArr = [{
    name: "租户"
}, {
    name: "房间"
}];

function showBuildList() {
    monitorModel.instance().curBuild = true;
    $(" #modal-bulid .buildList_wrap .buildLIst li b").removeClass("leftCross");
    $('#currentBuild').addClass('leftCross');
    $('#modal-bulid').pshow();
}

function selTab(event, tabIndex) { //tabIndex为2且tenement不为undefined表示从首页点击'进入租户详情'
    $('#globalBuild .combobox-menu-bottom').slideUp();
    var model = monitorModel.instance();
    model.density = 1;
    var index = event.pEventAttr.index;
    if (model.currentPage === tabPages[index] && !model.switchBuild) return;
    $('#globalBuilds ._combobox_bottom ').hide();
    $('#floorandcommercial ._combobox_bottom ').hide();
    $(".combobox-level-menu").slideUp();
    $(".dialog-wrap").removeClass("animationBottom");
    $("#float-diyAlarmset").hide();
    $('#dataComparaTimer ._combobox_bottom ').hide();
    model.currentPage = tabPages[index];
    model.pollingExamineData = {};
    $('#globalSearch').pval('');
    $('#globalSearch .per-searchbox-input_x').hide();
    $('.topsearchWrap .searchHint').hide();
    switch (index) {
        case 0: //总览
            controller.getFNMFloorAlarmService();
            initDataAnalyisPage();
            break;
        case 1: //巡检
            controller.getFNMEnergyTypeListService();
            model.selectenergysType = model.energysType[0];
            model.floorBusinessIndex = 0;
            $('.toggleFood-format').psel(0, false);
            initDataAnalyisPage();
            $('#typeTab .tab-tit ul>li').eq(0).addClass('cur').siblings().removeClass('cur');
            break;
        case 2: //详情
            monitorModel.instance().chooseTimeArr = [];
            monitorModel.instance().tenementReportArr = [];
            controller.getFNMFloorTreeService();
            controller.getFNMEnergyTypeTreeService({
                buildingId: model.selBuild.id,
                doType: 1,
                tenantId: null
            });
            $('#dataCompataBulid').psel(0, false);
            $('#buttonTab').psel(0, true);
            $('#chartCheck').psel(true, true);
            $('#reportCheck').psel(true, true);
            setTimeout(function () {
                dataComparaChart = tentchart.compare_linechart('comparative-chart', []);
            }, 0);

            break;
        case 3: //报警管理，获取报警类型、报警数据
            initParameters(model);
            initTime("tenantCalendar");
            $("#alarmNewsPage").psel(1);
            controller.getAlarmTypes(controller.getAlarms);
            monitorModel.instance().currAlarmType = null;
            initClass1(); //初始化样式

    }
    model.switchBuild = false;
}

function initPage(page) {
    var model = monitorModel.instance();
    switch (page) {
        case tabPages[0]:
            model.selectedFloorId = '';
            break;
        case tabPages[1]:
            $('.toggleFood-format').psel(0, false);
            break;
        case tabPages[2]:
            $('#chartCheck').psel('on', false);
            $('#reportCheck').psel('on', false);
            break;
        default:
            break;
    }
}

function setTabCheckedStyle(tabIndex) {
    var tabLis = $($('#teMonTab').find("ul").get(0)).find('li');
    $(tabLis[tabIndex]).addClass('cur').siblings().removeClass('cur');
}

function confirmChooseBuild() {
    $('#modal-bulid').phide();
    var model = monitorModel.instance();
    model.selBuild = model.selBuild2;
    model.switchBuild = true;
    model.gettedEnergyAndAlarm = false;
    model.gettedBusinesss = false;
    $('#teMonTab').psel(page[model.currentPage]);
    switch (model.currentPage) {
        case 'overviewPage':
            controller.getFNMFloorAlarmService();
            break;
        case 'roundsPage':
            controller.getFNMEnergyTypeListService();
            break;
        case 'dataAnalysispage':
            controller.getFNMFloorTreeService();
            controller.getFNMEnergyTypeTreeService({
                buildingId: model.selBuild.id,
                doType: 1,
                tenantId: null
            });
            break;
    }
}

function getGlobalAlarmSet() {
    var model = monitorModel.instance();
    model.isdiyAlarm = false;
    $('#globalBuilds ._combobox_bottom ').hide();
    $('#floorandcommercial ._combobox_bottom ').hide();
    $('.chooseType .combobox-level-menu ').hide();
    $('#dataCompataBulid ._combobox_bottom  ').hide();
    $('.downcombobox-con').hide();
    restoreSearch();
    controller.getFNMAlarmLimitListService();
    globalWarningIsvisible = true;
}

function saveGlobalAlarmSet() {
    var typeList = monitorModel.instance().globalAlarmSetArr.typeList;
    for (var i = 0; i < typeList.length; i++) {
        var item = typeList[i].alarmList;
        var verifiFlag = item.every(function (val) {
            if (val.valid) {
                return $('#modal-gAlarmset' + ' ' + '#' + val.typeId).pverifi();
            } else {
                return true;
            }
        });
        if (!verifiFlag) {
            return;
        }
    }
    toSaveAlarmSet();
    globalWarningIsvisible = false;
}

function selFloorOrFormat(event) {
    var model = monitorModel.instance();
    var index = event.pEventAttr.index;
    model.floorBusinessIndex = index;
    $(".combobox-level-menu").slideUp();
    $(".float_w").hide();
    if (model.currentPage == 'roundsPage') { //To Delete
        if (index == 0) {
            model.selectedFloorId = '';
            controller.getFloorsAndTenements(model.selEnergyXunjian.energyId, true, true);
        } else {
            model.selectedBusinessId = '';
            controller.getBusinessAndTenements(model.selEnergyXunjian.energyId, true, true);
        }
    } else {
        if (index == 0) {
            $('#divxxx input').attr('placeholder', '租户名称、房间号或楼层');
            controller.getFloorsAndTenements(model.selectedEnergyType.id, true, true, null, null, true);
        } else {
            $('#divxxx input').attr('placeholder', '租户名称、房间号或业态');
            controller.getBusinessAndTenements(model.selectedEnergyType.id, true, true, true);
        }
    }
}

function selSurplusOrCost(event) {
    var model = monitorModel.instance();
    var index = event.pEventAttr.index;
    model.surplusCostType = index;
    index == 0 ? controller.getPrepaidSurplus() : controller.getEnergyCost();
}

//取得n天之前或之后的日期，n为正整数时取得n天之后的日期，n为负整数时取得-n天之前的日期，返回日期的格式为"2016.01.01"形式
function getBeforeOrAfterDate(n) {
    n = -n;
    var d = new Date();
    var year = d.getFullYear();
    var mon = d.getMonth() + 1;
    var day = d.getDate();
    if (day <= n) {
        if (mon > 1) {
            mon = mon - 1;
        } else {
            year = year - 1;
            mon = 12;
        }
    }
    d.setDate(d.getDate() - n);
    year = d.getFullYear();
    mon = d.getMonth() + 1;
    day = d.getDate();
    var s = year + "." + (mon < 10 ? ('0' + mon) : mon) + "." + (day < 10 ? ('0' + day) : day);
    return s;
}

function goBack() {
    var model = monitorModel.instance();
    model.currEnergysPage = 1;
    controller.getdailyEnergy(null, null, true);
}

function saveMuteSet() {
    controller.saveMuteSet();
}

function removeMuteSet() {
    controller.cancelMuteSet();
}

function getAlarmTypeAndId() {
    var model = monitorModel.instance();
    var alarmType, alarmId, alarmLimitId;
    if (model.currEnergysPage == 2) {
        alarmType = 1;
        alarmId = model.hourlyEnergy.alarmId;
        alarmLimitId = model.hourlyEnergy.alarmLimitId;
    } else if (model.selectedEnergyType.id == 'electric' && model.selectedType.typeId === 2) {
        alarmType = 2;
        alarmId = model.electricPower.alarmId;
        alarmLimitId = model.electricPower.alarmLimitId;
    } else if (model.selectedType.typeId == 3 && model.selectedEnergyType.id != 'fcu' && model.selectedTenement.payType == 0 && model.surplusCostType == 0) {
        alarmType = 3;
        alarmId = model.prepaidSurplus.alarmId;
        alarmLimitId = model.prepaidSurplus.alarmLimitId;
    }
    return {
        alarmType: alarmType,
        alarmId: alarmId,
        alarmLimitId: alarmLimitId
    }
}

function updateChartAlarmData(alarmType) {
    if (!alarmType) {
        var obj = getAlarmTypeAndId();
        alarmType = obj.alarmType;
    }
    if (alarmType == 1) {
        controller.getHourlyEnergy();
    } else if (alarmType == 2) {
        controller.getElectricPower();
    } else if (alarmType == 3) {
        controller.getPrepaidSurplus();
    }
}

//wyy

function rpR_foolNo() {
    $(".rpR_foolNo").hide();
}

function recoverFloor() {

}


function selectFloor(Iid) {
    var offsetTop = $(".detailFoot .rightP").find('[floorid=' + Iid + ']')[0].offsetTop
    $(".detailFoot .rightP").scrollTop(offsetTop);
    var offsetTop1 = $(".detailFoot .leftP").find('[floorid=' + Iid + ']')[0].offsetTop
    $(".detailFoot .leftP").scrollTop(offsetTop1);
}

function getChartData(locationFloor, toPage) {
    var model = monitorModel.instance();
    if (model.currEnergysPage == 2) {
        controller.getHourlyEnergy();
    } else if (model.selectedEnergyType.id == 'electric' && model.selectedType.typeId === 2) {
        controller.getElectricPower();
    } else if (model.selectedEnergyType.id == 'coldHeat' && model.selectedType.typeId === 2) {
        controller.getColdHeatPower();
    } else if (model.selectedType.typeId === 5) {
        controller.getFcuStatus();
    } else if (model.selectedType.typeId == 3 && model.selectedEnergyType.id != 'fcu' && model.selectedTenement.payType == 0 && model.surplusCostType == 0) {
        controller.getPrepaidSurplus(locationFloor);
    } else if (model.selectedType.typeId == 3 && (model.selectedTenement.payType == 0 && model.surplusCostType == 1 || model.selectedTenement.payType !== 0 || model.selectedEnergyType.id == 'fcu')) {
        controller.getEnergyCost();
    } else {
        controller.getdailyEnergy(locationFloor, toPage);
    }
}

function getMonths(date1, date2) {
    date1 = date1.split("-");
    date2 = date2.split("-");
    var year1 = parseInt(date1[0]),
        month1 = parseInt(date1[1]),
        year2 = parseInt(date2[0]),
        month2 = parseInt(date2[1]),
        months = (year2 - year1) * 12 + (month2 - month1);
    return months;
}

function updateSelectedFloor(updatePayType) {
    var model = monitorModel.instance();
    var attrName = model.floorBusinessIndex === 0 ? 'floors' : 'businesss';
    var obj;
    if (!updatePayType) {
        obj = model.selectedFloor;
    } else {
        for (var i = 0; i < model[attrName].length; i++) {
            if (model.selectedFloor.id === model[attrName][i].id) {
                obj = model[attrName][i];
                //更新选择的租户
                for (var j = 0; j < obj.tenements.length; j++) {
                    if (model.selectedTenement.id === obj.tenements[j].id) {
                        model.selectedTenement = JSON.parse(JSON.stringify(obj.tenements[j]));
                        break;
                    }
                }
                break;
            }
        }
    }
    return obj;
}

function updateTenement(tenement, arr) {
    var model = monitorModel.instance();
    var obj;
    for (var i = 0; i < arr.length; i++) {
        if (model.selectedFloor.id === arr[i].id) { //依赖所选的楼层
            for (var j = 0; j < arr[i].tenements.length; j++) {
                var item = arr[i].tenements[j];
                if (tenement.id == item.id) {
                    obj = JSON.parse(JSON.stringify(item));
                    break;
                }
            }
            break;
        }
    }
    return obj;
}

function toLocationFloor(locationFloor) {
    setTimeout(function () {
        selectFloor(monitorModel.instance().selectedFloor.id);
    }, 0);
}

function getTopTenement(tenements) {
    for (var i = 0; i < tenements.length; i++) {
        if (tenements[i].isBottom) return tenements[i];
    }
    return null;
}

function setBeforeActivatedData(arr, attrName) {
    var model = monitorModel.instance();
    var activatedMs = new Date(model.selectedTenement.activatedTime).getTime();
    for (var i = 0; i < arr.length; i++) {
        if (arr[i].time < activatedMs && arr[i][attrName]) {
            arr[i][attrName] = null;
        }
    }
    return arr;
}

function changeTopsearch(event) {
    var read = JSON.parse(localStorage.getItem('key'));
    $('#globalBuilds ._combobox_bottom ').hide();
    $('#floorandcommercial ._combobox_bottom ').hide();
    $('.chooseType .combobox-level-menu ').hide();
    $('#dataCompataBulid ._combobox_bottom  ').hide();
    $('.downcombobox-con').hide();
    var putVal = event.pEventAttr.key;
    var model = monitorModel.instance();
    if (putVal != "" && putVal) {
        if (controller.searchTimer) {
            clearTimeout(controller.searchTimer);
        }
        controller.searchTimer = setTimeout(function () {
            controller.searchTimer = null;
            model.topSearchArr = getSearchResArry(alltement, 'tenantName', putVal);
            $('.searchHint').show();
        }, 20);
    } else {
        setTimeout(function () {
            $('.searchHint').hide();
            model.topSearchArr = [];
        }, 20)

    }

}

function toggleshow(event) {
    $('#dataComparaTimer >._combobox_bottom').hide();
    var index = event.pEventAttr.state,
        chart = $('#comparative-chart'),
        report = $('#comparative-table'),
        chartState = $('#chartCheck').psel(),
        reportState = $('#reportCheck').psel();
    if (chartState && reportState) {
        report.show().removeClass('showReport');
        chart.css({
            'position': 'relative',
            'z-index': '0'
        }).removeClass('showChart');
        dataComparaChart.reflow();
    } else if (!chartState && !reportState) {
        report.hide().removeClass('showReport');
        chart.css({
            'position': 'absolute',
            'z-index': '-2'
        }).removeClass('showChart');
    } else {
        if (chartState) {
            chart.css({
                'position': 'relative',
                'z-index': '0'
            }).addClass('showChart');
            report.hide().removeClass('showReport');
        } else {
            chart.css({
                'position': 'absolute',
                'z-index': '-2'
            }).removeClass('showChart');
            report.show().addClass('showReport');
        }
        dataComparaChart.reflow();
    }
}

function dataCompara_searchTement(isItem) {
    $('#dataComparaTimer >._combobox_bottom').hide();
    var model = monitorModel.instance();
    if (isItem) {
        model.searchText = $('#searchTement').find('input').val();
        var allArr = [];
        if (model.isshowMoney) {
            allArr = []
            for (var i = 0; i < model.world.length; i++) {
                allArr = allArr.concat(model.world[i].tenantList);
            }
        } else {
            allArr = [];
            for (var i = 0; i < model.roomwatch.length; i++) {
                var _world = model.roomwatch[i];
                for (var j = 0; j < _world.dataList.length; j++) {
                    // var watch = _world.dataList[j].dataList;
                    // for (var l = 0; l < watch.length; l++) {
                    //     allArr = allArr.concat(watch)
                    // }
                    allArr = allArr.concat(_world.dataList[j])
                }
            }
        }
        if (model.searchText.trim().length >= 0) {
            /*根据关键字要数组  todo*/
            controller.searchArr = getSearchResArry(allArr, 'name', model.searchText);
            if (controller.searchArr.length == 0) {
                $('#popTree-wrap').show();
                $('#dataCompara_tementTree').hide();
            } else {
                $('#popTree-wrap').hide();
                $('#dataCompara_tementTree').show();
            }

            if (model.isshowMoney) {
                for (var i = 0; i < allArr.length; i++) {
                    var t = allArr[i];
                    var had = false;
                    for (var j = 0; j < controller.searchArr.length; j++) {
                        var st = controller.searchArr[j];
                        if (t.id == st.id) {
                            had = true;
                            break;
                        }
                    }
                    t.isShow = had;
                }
                if(timer) {
                    clearTimeout(timer);
                }
                var timer = setTimeout(function(){searchPart()},0);
            } else {
                for (var i = 0; i < allArr.length; i++) {
                    var t = allArr[i];
                    t.isShow = false;
                    //每个房间下的租户
                    for(var k = 0; k < t.dataList.length; k++) {
                        t.dataList[k].isShow = false;
                    }
                    for (var j = 0; j < controller.searchArr.length; j++) {
                        var st = controller.searchArr[j];
                        if (t.id == st.id) {
                            //搜索的房间
                            t.isShow = true;
                            for(var k = 0; k < t.dataList.length; k++) {
                                t.dataList[k].isShow = true;
                            }
                        }
                    }
                }
                for (var j = 0; j < allArr.length; j++) {
                    for (var q = 0; q < model.roomwatch.length; q++) {
                        var _world = model.roomwatch[q];
                        for (var p = 0; p < _world.dataList.length; p++) {
                            var watch = _world.dataList[p].dataList;
                            for (var l = 0; l < watch.length; l++) {
                                if (watch[l].id == allArr[j].id) {
                                    watch[l].isShow = allArr[j].isShow;
                                }
                            }
                        }
                    }
                }
                model.isfold = true;
                if(timer1) {
                    clearTimeout(timer1);
                }
                var timer1 = setTimeout(function(){searchPartRoom()},0);
            }

        } else {
            $('#popTree-wrap').hide();
            $('#dataCompara_tementTree').show();
            model.searchText = '';
            model.dataCompara_searchArr = [];
            for (var i = 0; i < allArr.length; i++) {
                if (model.isshowMoney) {
                    allArr[i].isShow = true;
                } else {
                    for (var q = 0; q < model.roomwatch.length; q++) {
                        var _world = model.roomwatch[q];
                        for (var p = 0; p < _world.dataList.length; p++) {
                            var watch = _world.dataList[p].dataList;
                            for (var l = 0; l < watch.length; l++) {
                                watch[l].isShow = true
                            }
                        }
                    }
                }
            }
        }
        return;
    }
}

// 选择时间后 点击确定按钮 事件
function _addTimer() {
    $('#dataComparaTimer ._combobox_bottom ').hide();
    var model = monitorModel._instance,
        obj = $('#dataComparaTimer').psel();
    _start = obj.startTime,
        _end = obj.endTime,
        _realEndStr = obj.realEndTime,
        step = new Date(_end).getTime() - new Date(_start).getTime(),
        timerStr = '';
    // model.density = '';//1时  2日   4月   5年
    model.comparedensity = ptool.formatGranularityToJava($('#dataComparaTimer'));
    switch (model.comparedensity) {
        /**/
        case 1:
            _format = 'y.M.d';
            break;
        case 2:
            _format = 'y.M.d';
            break;
        case 4:
            _format = 'y.M';
            break;
        case 5:
            _format = 'y.M';
            break;
    }
    timerStr = new Date(_start).format(_format) + '~' + new Date(_end).format(_format);
    var tenantList = [];
    if (model.isshowMoney) {
        for (var i = 0; i < model.world.length; i++) {
            var orgTenantList = model.world[i].tenantList;
            var tempArr = [];
            for (var j = 0; j < orgTenantList.length; j++) {
                var oneItem = orgTenantList[j];
                if (oneItem.isChecked) {
                    tempArr.push(oneItem);
                }
            }
            tenantList = tenantList.concat(tempArr);
        }
    } else {
        for (var i = 0; i < model.roomwatch.length; i++) {
            var _world = model.roomwatch[i];
            var tempArr = [];
            for (var j = 0; j < _world.dataList.length; j++) {
                var watch = _world.dataList[j].dataList;
                for (var l = 0; l < watch.length; l++) {
                    if (watch[l].isChecked) {
                        tempArr.push(watch[l]);
                    }
                }
            }
            tenantList = tenantList.concat(tempArr);
        }
    }


    if (tenantList.length > 1 && model.chooseTimeArr.length > 0 && model.isAddTimer) {
        $("#notice-con").pshow({
            state: 'failure',
            text: '多租户单时间模式,只能选择一个时间'
        });
        return;
    }
    var startDate = new Date(_start).format('yyyy-MM-dd hh:mm:ss');
    var endDate = new Date(_realEndStr).format('yyyy-MM-dd hh:mm:ss');
    if (model.isAddTimer) {
        if (model.chooseTimeArr.length > 0) {
            var prevTimer = model.chooseTimeArr[model.chooseTimeArr.length - 1];
            var lastStep = prevTimer.step;
            if (prevTimer.timeType != obj.timeType) {
                $("#notice-con").pshow({
                    state: 'failure',
                    text: '需要保证所选时间步长一致'
                });
                return;
            } else if (prevTimer.timeType == 'd' && obj.timeType == 'd') {
                if (lastStep !== step) {
                    $("#notice-con").pshow({
                        state: 'failure',
                        text: '需要保证所选时间步长一致'
                    });
                    return;
                }
            } else {}
            for (var i = 0; i < model.chooseTimeArr.length; i++) {
                var startTime = model.chooseTimeArr[i].dataStartTime;
                var endTime = model.chooseTimeArr[i].dataEndTime;
                if (startTime == startDate && endTime == endDate) {
                    return;
                }
            }
        }
    }
    model.dataStartTime = startDate;
    model.dataEndTime = endDate;
    if (model.isAddTimer) { //添加时间
        model.chooseTimeArr.push({
            timeType: obj.timeType,
            timer: timerStr,
            step: step,
            dataStartTime: model.dataStartTime,
            dataEndTime: model.dataEndTime,
            timeId: startDate + "_" + endDate
        });
        for (var i = 0; i < tenantList.length; i++) {
            var o = tenantList[i];
            if (model.isshowMoney) {
                //zuhu
                controller.getFNMEnergyDataListService({
                    density: model.comparedensity,
                    doType: 1,
                    endDate: endDate,
                    startDate: startDate,
                    meterId: null,
                    tenantId: o.id,
                    typeId: model.selecteddataCpmpara.id
                }, o);
            } else {
                //房间
                controller.getFNMRoomEnergyDataListService({
                    density: ptool.formatGranularityToJava($("#dataComparaTimer")),
                    doType: 0,
                    endDate: endDate,
                    startDate: startDate,
                    meterId: o.id,
                    buildingId: model.selBuild.id,
                    typeId: model.selecteddataCpmpara.id
                }, o, controller.replaceTimer)
            }

        }
    } else {
        //编辑时间时
        var _timeObj_ = model.chooseTimeArr[0];
        _timeObj_.timer = timerStr;
        _timeObj_.dataStartTime = startDate;
        _timeObj_.dataEndTime = endDate;
        _timeObj_.timeId = startDate + "_" + endDate;
        _timeObj_.timeType = obj.timeType;
        _timeObj_.step = step;

        for (var i = 0; i < model.tenementReportArr.length; i++) {
            var t = model.tenementReportArr[i];
            var parames;
            if (model.isshowMoney) {
                parames = {
                    density: model.comparedensity,
                    doType: 1,
                    endDate: endDate,
                    startDate: startDate,
                    meterId: null,
                    tenantId: t._tenentId,
                    tenantName: t.name,
                    typeId: model.selecteddataCpmpara.id
                }
            } else {
                parames = {
                    density: ptool.formatGranularityToJava($("#dataComparaTimer")),
                    doType: 0,
                    endDate: endDate,
                    startDate: startDate,
                    meterId: t._tenentId,
                    buildingId: model.selBuild.id,
                    typeId: model.selecteddataCpmpara.id
                }
            }
            controller.updateChartData(parames, "date");
            if (model.isDuiBiCha) {
                break;
            }
        }
    }
}

function popTimer(event) {
    event.stopPropagation();
    $('.chooseType-wrapper .combobox-level-menu').slideUp();
    $('.downCombox .downcombobox-con').slideUp();
    $('#dataCompataBulid .combobox-con').slideUp();
    var model = monitorModel.instance();
    model.isAddTimer = true;
    if (model.chooseTimeArr.length == 1 && model.tenementReportArr.length > 1 || model.isDuiBiCha && model.chooseTimeArr.length === 1) {
        return;
    }
    if ($('#dataComparaTimer > ._combobox_bottom').is(':visible')) {
        $('#dataComparaTimer > ._combobox_bottom').hide();
    } else {
        if (model.chooseTimeArr.length >= 1) {
            var lastTimer = model.chooseTimeArr[model.chooseTimeArr.length - 1];
            setTimeout(function () {
                $("#dataComparaTimer").psel({
                    timeType: lastTimer.timeType,
                    startTime: new Date(lastTimer.dataStartTime.replace(/-/g, '/')).getTime(),
                    endTime: Number(new Date(lastTimer.dataEndTime.replace(/-/g, '/')).getTime()) - 1000
                }, false);
                $('#dataComparaTimer > ._combobox_bottom').show();
                if ($("#dataComparaTimer .per-calendar-lock .icon").html() == 'c') {
                    $("#dataComparaTimer").plock(false);
                    if ($("#dataComparaTimer").psel().timeType == 'd') {
                        $("#dataComparaTimer .per-calendar_details_nav li[navili='M']").pdisable(true);
                    }
                    if ($("#dataComparaTimer").psel().timeType == 'M') {
                        $("#dataComparaTimer .per-calendar_details_nav li[navili='d']").pdisable(true);
                    }
                }
            }, 0);
        } else {
            var date = new Date();
            var y = date.getFullYear();
            var m = date.getMonth();
            var d = date.getDate();
            var startTime = new Date(y, m, d, 0, 0, 0).getTime();
            var endTime = new Date(y, m, d, 23, 59, 59).getTime();
            setTimeout(function () {
                $("#dataComparaTimer").psel({
                    timeType: 'd',
                    startTime: startTime,
                    endTime: endTime
                }, false);
                $('#dataComparaTimer > ._combobox_bottom').show();
            }, 0);

            $("#dataComparaTimer .per-calendar_details_nav li[navili='M']").pdisable(false);
            $("#dataComparaTimer .per-calendar_details_nav li[navili='d']").pdisable(false);
            if ($("#dataComparaTimer .per-calendar-lock .icon").html() == 's') {
                $("#dataComparaTimer").plock(false);
            }
        }
    }

}
//计算每个租户块的高宽
function calcSize(row, list) {
    /*
     * row 横向租户的最大个数
     * list 纵向楼层的个数
     * wrapperWidth overviewPage的宽度
     * wrapperHeight overviewPage的高度
     * */
    var widthlimit = [60, 160],
        heightlimit = [30, 120],
        wrapperWidth = $('#overviewPage')[0].clientWidth,
        wrapperHeight = $('#overviewPage')[0].clientHeight,
        tenementWidth = Math.floor(wrapperWidth / row) < widthlimit[0] ? widthlimit[0] : Math.floor(wrapperWidth / row) > widthlimit[1] ? widthlimit[1] : Math.floor(wrapperWidth / row);
    //
    tenementHeight = Math.floor(wrapperHeight / list) < heightlimit[0] ? heightlimit[0] : Math.floor(wrapperHeight / list) > heightlimit[1] ? heightlimit[1] : Math.floor(wrapperHeight / list);
    setTimeout(function () {
        $('#overviewPage .piece').css({
            'min-width': tenementWidth + 'px',
            'height': tenementHeight + 'px'
        });
    }, 0)
    return {
        tenementWidth: tenementWidth,
        tenementHeight: tenementHeight
    };
}

function _saveDiyAlarmSet() {
    var vm = monitorModel._instance;

    var typeList = monitorModel.instance().customAlarmSetArr.typeList;
    for (var i = 0; i < typeList.length; i++) {
        var item = typeList[i].alarmList;
        var verifiFlag = item.every(function (val) {
            if (val.valid) {
                $('#float-diyAlarmset' + ' ' + '#' + val.typeId).pverifi();
                return $('#' + val.typeId).pverifi();
            } else {
                return true;
            }
        });
        if (!verifiFlag) {
            return;
        }
    }

    vm.isdiyAlarm = false;
    if ($('#followGalarm').psel()) {
        controller.setcustomAlarmSetArr(0);
        return;
    }
    var errorList = $('.gaBody .gaBPart .errorTips');
    for (var i = 0; i < errorList.length; i++) {
        if (errorList.eq(i).html()) {
            return;
        }
    }
    var alarmArr = vm.customAlarmSetArr.typeList;
    for (var i = 0; i < alarmArr.length; i++) {
        if (alarmArr[i].valid && !alarmArr[i].limit) {
            return $('#notice-con').pshow({
                state: 'failure',
                text: '报警开关开启时，输入框不能为空'
            });
        }
    }
    // 将 vm.customAlarmSetArr 传回 todo
    controller.setcustomAlarmSetArr();
}

function _resetToGlobalAlarm() {
    var model = monitorModel.instance();
    model.isdiyAlarm = false;
    model.customAlarmSetArr = model.copycustomAlarmSetArr;
    $('#float-diyAlarmset').hide();
}
// 巡检处理楼层合并的方法
function mergeLayer(json) {
    // 深复制json，返回一个新的数据
    var newJson = JSON.parse(JSON.stringify(json));
    if (newJson.length == 1) {
        return newJson
    }
    for (var i = 0; i < newJson.length; i++) {
        var itemArr = newJson[i].tenantList;

        // 添加标题新字段，用于 Tip 提示
        newJson[i].floorTip = newJson[i].floorName;
        // 如果该楼层内的租户只有一个
        if (itemArr.length == 1) {
            var layer = newJson[i].floorName,
                tenantName = itemArr[0].tenantName,
                len = i + 1 - newJson.length,
                lowLayerArr = newJson.slice(len);

            // 循环下面的楼层是否和该层的租户名称相同且只有一个
            for (var j = 0; j < lowLayerArr.length; j++) {
                var lowLayerItemArr = lowLayerArr[j].tenantList;

                if (lowLayerItemArr.length == 1 && lowLayerItemArr[0].tenantName == tenantName) {
                    var idx = j - lowLayerArr.length;
                    // 合并这些楼层的信息为 XX层 ~ XX层
                    var newFloorName = layer + ' ~ ' + lowLayerArr[j].floorName;
                    newJson[i].floorName = newFloorName;

                    //合并楼层，将 Tip 提示内容翻转
                    newJson[i].floorTip = newFloorName.split(' ~ ').reverse().join(' ~ ');

                    // 将租户每层的房间号合并为一个数组
                    itemArr[0].roomList = lowLayerItemArr[0].roomList.concat(itemArr[0].roomList);

                    // 将多余的重复楼层删除
                    newJson.splice(idx, 1);
                } else {
                    // 如果不符合条件直接跳出内层循环，去检测其他的楼层租户
                    break;
                }
            }
        }
    }
    return newJson;
}
// 处理也太模式类型标签换行
function typeBusinessName(json) {
    // 深复制json，返回一个新的数据
    var newJson = JSON.parse(JSON.stringify(json));
    for (var i = 0; i < newJson.length; i++) {
        var itemTypeName = newJson[i].typeName,
            len = itemTypeName.length,
            strArr = [];

        // 添加标题新字段，用于 Tip 提示
        newJson[i].floorTip = newJson[i].typeName;

        // 只针对中文，如果超过两个字
        if (len > 2) {
            // 将字符串转化为数组，并在第 2、4、6、8.....个子后面追加空格
            itemTypeName.split('').forEach(function (val, idx) {
                if (idx % 2 == 1) {
                    val = val + ' ';
                }
                strArr.push(val);
            });
            // 将追加空格后的数组重新合并为字符串并替换原属性值
            newJson[i].typeName = strArr.join('');
        }
    }
    return newJson;
}

// 租户列表模式的假数据
var lesseeListData = [{
        "floorId": "",
        "floorName": "15层",
        "isAlarm": true,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                        "roomId": "1501"
                    },
                    {
                        "roomId": "1502"
                    },
                    {
                        "roomId": "1503"
                    }
                ],
                "tenantId": "",
                "tenantName": "HM",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "ZARA",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "德克士",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "麦当劳",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "肯德基",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "口水鸡",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "周黑鸭",
                "type": 0
            }
        ]
    },
    {
        "floorId": "",
        "floorName": "14层",
        "isAlarm": true,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1401"
                }],
                "tenantId": "",
                "tenantName": "HM",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "Nest",
                "type": 1
            }
        ]
    },
    {
        "floorId": "",
        "floorName": "13层",
        "isAlarm": true,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1301"
                }],
                "tenantId": "",
                "tenantName": "Bodyshop",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "美宝莲",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "美肤宝",
                "type": 1
            }
        ]
    },
    {
        "floorId": "",
        "floorName": "12层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "1201"
            }],
            "tenantId": "",
            "tenantName": "Nest",
            "type": 0
        }]
    },
    {
        "floorId": "",
        "floorName": "11层",
        "isAlarm": false,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1101"
                }],
                "tenantId": "",
                "tenantName": "Nest",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "Bodyshop",
                "type": 0
            }
        ]
    },
    {
        "floorId": "",
        "floorName": "10层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "1001"
            }],
            "tenantId": "",
            "tenantName": "Nest",
            "type": 1
        }]
    },
    {
        "floorId": "",
        "floorName": "9层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "901"
            }],
            "tenantId": "",
            "tenantName": "Nest",
            "type": 1
        }]
    },
    {
        "floorId": "",
        "floorName": "8层",
        "isAlarm": true,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": true,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "801"
            }],
            "tenantId": "",
            "tenantName": "Nest",
            "type": 1
        }]
    },
    {
        "floorId": "",
        "floorName": "7层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "701"
            }],
            "tenantId": "",
            "tenantName": "Nest",
            "type": 1
        }]
    },
    {
        "floorId": "",
        "floorName": "6层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "601"
            }],
            "tenantId": "",
            "tenantName": "Nesta",
            "type": 1
        }]
    },
    {
        "floorId": "",
        "floorName": "5层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "501"
            }],
            "tenantId": "",
            "tenantName": "Nest",
            "type": 1
        }]
    },
    {
        "floorId": "",
        "floorName": "4层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "401"
            }],
            "tenantId": "",
            "tenantName": "nihao",
            "type": 1
        }]
    },
    {
        "floorId": "",
        "floorName": "3层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "301"
            }],
            "tenantId": "",
            "tenantName": "nihao",
            "type": 1
        }]
    },
    {
        "floorId": "",
        "floorName": "2层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "201"
            }],
            "tenantId": "",
            "tenantName": "nihao",
            "type": 1
        }]
    },
    {
        "floorId": "",
        "floorName": "1层",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "101"
            }],
            "tenantId": "",
            "tenantName": "nihaohaha",
            "type": 1
        }]
    }
];
// 业态模式的假数据
var typeBusinessData = [{
        "typeId": "",
        "typeName": "餐饮",
        "isAlarm": true,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                        "roomId": "1501"
                    },
                    {
                        "roomId": "1502"
                    },
                    {
                        "roomId": "1503"
                    }
                ],
                "tenantId": "",
                "tenantName": "HM",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "ZARA",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "德克士",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "麦当劳",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "肯德基",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "口水鸡",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "周黑鸭",
                "type": 0
            }
        ]
    },
    {
        "typeId": "",
        "typeName": "服装类",
        "isAlarm": true,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1401"
                }],
                "tenantId": "",
                "tenantName": "HM",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "Nest",
                "type": 1
            }
        ]
    },
    {
        "typeId": "",
        "typeName": "箱包皮具",
        "isAlarm": true,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1301"
                }],
                "tenantId": "",
                "tenantName": "Bodyshop",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "美宝莲",
                "type": 0
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": true,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "美肤宝",
                "type": 1
            }
        ]
    },
    {
        "typeId": "",
        "typeName": "手表饰品类",
        "isAlarm": false,
        "tenantList": [{
            "cost": "4180元",
            "data": "183Kwh",
            "energyTypeName": "耗电",
            "isAlarm": false,
            "remainDays": "2~3天",
            "roomList": [{
                "roomId": "1201"
            }],
            "tenantId": "",
            "tenantName": "Nest",
            "type": 0
        }]
    },
    {
        "typeId": "",
        "typeName": "生活综合用品",
        "isAlarm": false,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1101"
                }],
                "tenantId": "",
                "tenantName": "Nest",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "Bodyshop",
                "type": 0
            }
        ]
    },
    {
        "typeId": "",
        "typeName": "生活综合的用品",
        "isAlarm": false,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1101"
                }],
                "tenantId": "",
                "tenantName": "Nest",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "Bodyshop",
                "type": 0
            }
        ]
    },
    {
        "typeId": "",
        "typeName": "化妆及个护保健品",
        "isAlarm": false,
        "tenantList": [{
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1101"
                }],
                "tenantId": "",
                "tenantName": "Nest",
                "type": 1
            },
            {
                "cost": "4180元",
                "data": "183Kwh",
                "energyTypeName": "耗电",
                "isAlarm": false,
                "remainDays": "2~3天",
                "roomList": [{
                    "roomId": "1501"
                }],
                "tenantId": "",
                "tenantName": "Bodyshop",
                "type": 0
            }
        ]
    }
];
// 切换租户列表和业态分类列表
function tabLesseePattern(event) {
    var index = event.pEventAttr.index;
    var model = monitorModel.instance();

    if (index) {
        model.floorBusinessIndex = 1;
        controller.getFNMIndustryEnergyInfoService({
            buildingId: model.selBuild.id,
            energyTypeId: model.selEnergyXunjian.typeId
        });
    } else {
        model.floorBusinessIndex = 0;
        controller.getFNMFloorEnergyInfoService({
            buildingId: model.selBuild.id,
            energyTypeId: model.selEnergyXunjian.typeId
        });
    }
}
//将时间变成毫秒数
function getMilliseconds(arr, _type) {
    var model = monitorModel.instance();
    var temp = null;
    if (_type) {
        arr.dataList = arr.dataList.map(function (item) {
            return item = {
                x: new Date((item.x).replace(/-/g, '/')).getTime(),
                y: item.y,
                a: item.a,
                z: item.z
            };
        });
        temp = {
            dataList: arr.dataList
        };
        model.dataList = temp.dataList;
    } else {
        temp = arr.map(function (item) {
            return item = {
                x: new Date((item.x).replace(/-/g, '/')).getTime(),
                y: item.y,
                realTime: item.realTime
            };
        });
        return temp;
    }


}


//数据分析下拉框头部点击事件
function datacomboxHead() {
    $('#dataComparaTimer >._combobox_bottom').hide();
    $('.chooseType-wrapper .combobox-level-menu').slideUp();
    $('.downCombox .downcombobox-con').slideUp();
}

//初始化数据分析页
function initDataAnalyisPage() {
    $('#dataCompataBulid ._combobox_bottom ').hide();
    $('#searchTement').pval('');
    var model = monitorModel.instance();
    while (model.tenementReportArr.length > 0) {
        controller.removeTenentChart(model.tenementReportArr[0]._tenentId);
    }
    controller.compareIndex = -1;
    model.isShowIndex = false;
    $('.downcombobox-con').hide();
}
//数据分析左下角配合搜索
function searchPart() {
    var tempArr = $('#dataCompara_tementTree .isFirst .treeCont >li .treeTitle');
    var showArr = [];
    $('#dataCompara_tementTree').find('.isFirst').hide();
    for (var i = 0, len = tempArr.length; i < len; i++) {
        if (tempArr.eq(i).css('display') == 'block') {
            tempArr.eq(i).parent().parent().show();
            tempArr.eq(i).parent().parent().siblings('.isFloor').find('.arrow').html('b');
            showArr.push(i);
        }else{
            continue;
        }
    }   
    var showLen = showArr.length;
    for(var j = 0; j < showLen; j ++) {
        $(tempArr[showArr[j]]).parent().parent().parent().show();
    }
}
function searchPartRoom() {
    var tempArr = $('#dataCompara_tementTree1 .isFirst .treeCont >li .treeCont>li .treeTitle');
    var showArr = [];
    $('#dataCompara_tementTree1').find('.isFirst').hide();
    for (var i = 0, len = tempArr.length; i < len; i++) {
        showArr.push(i);
    }
    var showLen = showArr.length;
    for(var j = 0; j < showLen; j ++) {
        $(tempArr[showArr[j]]).parent().parent().parent().parent().parent().show();
    }
}

// function searchPartRoom() {
//     var tarr = $('#dataCompara_tementTree1 .isFirst .isFloor > .arrow')
//     for (var l = 0, len = tarr.length; l < len; l++) {
//         if (tarr.eq(l).html() == 'r') { 
//             tarr.eq(l).click();
//             (function (l) {
//                 setTimeout(function () {
//                     tarr.eq(l).parents('.treeTitle').siblings('.treeCont').find('li > .treeTitle > .arrow').click()
//                 }, l * 500)
//             })(l)


//         }
//     }
// }

function isIE() { //ie?
    if (!!window.ActiveXObject || "ActiveXObject" in window)
        return true;
    else
        return false;
}

function hideinit() {
    $('.topSearch').pctlRecover(true);
    $('.topSearch .topdelete').hide();
    $('.topSearch  input').blur();
}

function getFloorByTenent(obj) {
    var model = monitorModel.instance();
    for (var i = 0; i < model.world.length; i++) {
        var _world = model.world[i];
        for (var j = 0; j < _world.tenantList.length; j++) {
            if (_world.tenantList[j].id == obj.id) {
                return _world.name
            }

        }
    }
}

function getFloorByWatch(obj) {
    var model = monitorModel.instance();
    for (var i = 0; i < model.roomwatch.length; i++) {
        var _world = model.roomwatch[i];
        for (var j = 0; j < _world.dataList.length; j++) {
            var watch = _world.dataList[j].dataList;
            for (var l = 0; l < watch.length; l++) {
                if (watch[l].id == obj.id) {
                    return _world.name + '-' + _world.dataList[j].name
                }
            }
        }
    }
}

function hideFloatevent(event) {
    $('#detailFloat').phide();
    $('.maskdetailFloat').hide();
}

function hidebeforeevent() {
    var model = monitorModel.instance();
    $('.maskdetailFloat').hide();
    $('#float-diyAlarmset').hide();
    $('#chooseType .combobox-level-menu').slideUp();
    if (controller.chart_expend && !controller.chart_expend.series) {
        controller.chart_expend.destroy();
    }
    $('#meterWraperCombo').precover();
    model.density = 1;
    //重置单选框
    this.isdiyAlarm = false;
    this.isdiy = false;
    model.prevDetailTime = {
        timeType: 'd',
        startTime: new Date().getTime()
    };
    $('#detailTimerWrap ._combobox_bottom ').hide();
    return true;
}

function getdetailTimerId() {
    return '#' + $('#detailTimerWrap').find('>div:visible>div').attr('id')
}

function followGlobalAlarm(event) {
    var model = monitorModel.instance();
    model.isdiy = false;
    controller.getFNMAlarmLimitListService();
    $('#diyAlarm').psel(false, false);
}

function customizeAlarm(event) {
    var model = monitorModel.instance();
    model.isdiy = true;
    controller.getcustomAlarmSetArr();
    $('#followGalarm').psel(false, false);
}

function restoreSearch(event) {
    $('.topsearchWrap .searchHint').hide();
}

function loadChart() {
    $('#updatachartLoading').pshow();
    if (controller.chart_expend && !controller.chart_expend.series) {
        controller.chart_expend.destroy();
    }
}

function topTapEventCall(obj) {
    var model = monitorModel.instance();
    var index = obj.pEventAttr.index;
    var selectName = dataArr[index].name;
    $("#selecteddataArrItem").text('选择' + selectName);
    $("#searchInput").find("input").attr('placeholder', '搜索' + selectName);
    $('#buttonTab').psel(index, false);
    $("#loading").pshow();
    model.isshowMoney = !index;
    var _defaultType = {
        id: "Dian_HaoDianLiang",
        name: "耗电量"
    };
    model.selecteddataCpmpara.name = _defaultType.name;
    model.selecteddataCpmpara.id = _defaultType.id;
    model.isfold = false;
    if (index == 1) {
        pajax.post({
            url: 'FNMRoomEnergyTypeTreeService',
            data: {},
            success: function (res) {
                if (res && res.length) {
                    controller.getroomlist();
                    model.dataChooseTypesCompare = res;
                }
            },
            error: function (err) {},
            complete: function () {
                $("#loading").phide();
            }
        });
    };
    if (index == 0) {
        pajax.post({
            url: 'FNMEnergyTypeTreeService',
            data: {
                doType: 1
            },
            success: function (res) {
                if (res && res.length) {
                    controller.getFNMFloorTreeService();
                    model.dataChooseTypesCompare = res;
                }
            },
            error: function (err) {},
            complete: function () {
                $("#loading").phide();
            }
        });
    }
    $('.fun-wrapper span').click();
    $('#searchTement input').val('');
    $('.per-searchbox-input_x').hide();
}

function checkInput(item, e) {
    $(e.target).parents('.police_con').find('.per-input-basic').precover()
}

// 跳转到租户详情
function goToDetail() {
    var model = monitorModel.instance();
    var dustry = model.floorBusinessIndex;
    var page = model.currentPage == 'roundsPage' ? 1 : 0;
    var typeId = model.selEnergyXunjian.typeId || '';
    var index = model.index;
    var index1 = model.index1;
    var tenantId = model.selectedchunk.tenantId;
    var buildingId = model.selBuild.id;
    var href = window.location.href.split('&index')[0];
    window.location.href = href.replace(/PropertyMonitor/, "Tenant") + '&dustry=' + dustry + '&page=' + page + '&typeId=' + typeId + '&index=' + index + '&index1=' + index1 + '&tid=' + tenantId + '&bid=' + buildingId;
}

//当前租户报警记录展示
function getTenantAlarmRecord() {
    var model = monitorModel.instance();
    model.showChart = false; //控制报警记录页面显示
    initTime("detailTenantCalendar");
    controller.getAlarmTypes(controller.getAlarms);
    initClass1();
    model.currStatus = null;
    model.currAlarmType = null;
}
//当前租户报警记录隐藏
function getTenantAlarmRecordBack() {
    var model = monitorModel.instance();
    model.showChart = true; //控制报警记录页面显示
    initTime("detailTenantCalendar");
    controller.getAlarmTypes(controller.getAlarms);
    initClass1();
}

//报警消息左侧导航tree显示与收起
function treeTempArrows(_this, event) {
    event.stopPropagation();
    $(_this).parent().toggleClass("show");
    var hasShow = $(_this).parent().hasClass("show");
    if (hasShow) {
        $(_this).parent().next().slideDown();
        $(_this).find("div").text("b");
    } else {
        $(_this).parent().next().slideUp();
        $(_this).find("div").text("r");
    }
}

//page切换
function alarmPageSel(flag) {
    var model = monitorModel.instance();
    var index = $("#alarmNewsPage").psel();
    model.pageIndex = --index;
    if (flag) {
        controller.getAlarms(true); //获取最新数据
    }

}
//时间选择
function timeSelect(flag, container) {
    var model = monitorModel.instance();
    var timeValue = $("#" + container).psel();
    model.startTime = new Date(timeValue.startTime).format("y-M-d 00:00:00");
    model.endTime = new Date(timeValue.endTime).format("y-M-d 23:23:59");
    if (flag) {
        controller.getAlarms();
    }
}
//初始化时间
function initTime(container) {
    var model = monitorModel.instance();
    var startTime, endTime = null;
    var nowDate = new Date();
    if (window.ActiveXObject || "ActiveXObject" in window) {
        startTime = nowDate.format("yyyy/MM/01") + " 00:00:00";
        endTime = nowDate.format("yyyy/MM/dd") + " 23:23:59";
        startTime = nowDate.format("y-M-01") + " 00:00:00";
        endTime = nowDate.format("y-M-d") + " 23:23:59";
        model.startTime = startTime;
        model.endTime = endTime;
        model.startTimeIE = startTime;
        model.endTimeIE = endTime;
        $("#" + container).psel({
            startTimeIE: startTime,
            endTimeIE: endTime
        });
    } else {

        startTime = nowDate.format("y-M-01") + " 00:00:00";
        endTime = nowDate.format("y-M-d") + " 23:23:59";
        $("#" + container).psel({
            startTime: startTime,
            endTime: endTime
        });
        model.startTime = startTime;
        model.endTime = endTime;

    }
}

//初始化参数
function initParameters(model) {
    model.currStatus = null;
    model.currAlarmType = null;
    model.startTime = null;
    model.endTime = null;
    model.tenantId = null;
    model.pageIndex = 0;
}
//初始化样式
function initClass() {
    $(".alarm-nav-ul .allMessage").addClass("pitch").siblings().removeClass("pitch");
    $("#divAlarmType").find("li").removeClass("pitch");
    $("#divAlarmType").find("b").removeClass("pitch");
    $("#divAlarmType .arrows").parent().next().slideUp();
    $("#divAlarmType .arrows").children("div").text("r");
}

function initClass1() {
    $(".alarm-nav-ul .allMessage").addClass("pitch").siblings().removeClass("pitch");
    $(".alarm-nav-temp .temp-title").addClass("pitch");
    $(".alarm-nav-temp .tree-temp>ul>li").removeClass("pitch");
    $(".divAlarmType").find("li").removeClass("pitch");
    $(".divAlarmType").find("b").removeClass("pitch");
    $(".divAlarmType .arrows").parent().next().slideUp();
    $(".divAlarmType .arrows").children("div").text("r");
}