/*数据交互及逻辑*/
var alltement = [];
var controller = {
    init: function () {
        controller.getFNMBuildingListService();
        controller.getPermissionId('GobalAlarmSet');
        controller.getPermissionId('TenantDetail');
    },
    //获取建筑
    getFNMBuildingListService: function () {
        $('#loading').pshow();
        var model = monitorModel.instance(); //First Instance
        pajax.get({
            url: 'FNMBuildingListService',
            success: function (result) {
                if (Array.isArray(result) && result.length === 0) {
                    console.log("返回数据为空！")
                } else {
                    model.builds = result;
                    /* 20181022 wp+ */
                    var ifJump = window.location.href.indexOf('&bid=');
                    if (ifJump > 1) {
                        var bid = window.location.href.split('&bid=')[1];
                        for (var i = 0; i < model.builds.length; i++) {
                            var cur = model.builds[i];
                            if (cur.id == bid) {
                                var bIndex = i;
                                model.selBuild = cur;
                            }
                        }
                    } else {
                        model.selBuild = result.length ? result[0] : {};
                    }
                    /* end */
                    model.copybuilds = JSON.parse(JSON.stringify(result));
                    model.copyselBuild = JSON.parse(JSON.stringify(result[0]));
                    Vue.nextTick(function () {
                        if (typeof (bIndex) == 'number') {
                            $('#globalBuilds').psel(bIndex, false)
                        } else {
                            $('#globalBuilds').psel(0, false);
                        }
                    });
                }
            },
            error: function () {},
            complete: function () {
                var model = monitorModel.instance();
                var ifJump = window.location.href.indexOf('&tid');
                if (ifJump > 1) {
                    var arr = window.location.href.split('&');
                    var dustry = arr[arr.length - 7].split('=')[1];
                    var page = arr[arr.length - 6].split('=')[1];
                    var typeId = arr[arr.length - 5].split('=')[1];
                    var index = arr[arr.length - 4].split('=')[1];
                    var index1 = arr[arr.length - 3].split('=')[1];
                    var tenantId = arr[arr.length - 2].split('=')[1];
                    var buildingId = arr[arr.length - 1].split('=')[1];
                    model.selBuild.id = buildingId;
                    if (dustry) {
                        model.floorBusinessIndex = Number(dustry);
                        $('#floorandcommercial').psel(model.floorBusinessIndex, false);
                    }

                }
                if (page == '1') {
                    model.currentPage = 'roundsPage';
                    $('#mainTab').psel(1, true);
                    controller.ReGetFNMFloorAlarmService();
                    // 1:巡检页
                    pajax.get({
                        url: 'FNMEnergyTypeListService',
                        data: {
                            buildingId: model.selBuild.id
                        },
                        success: function (result) {
                            model.energysType = result || [];
                            var typeIndex;
                            for (var i = 0; i < model.energysType.length; i++) {
                                var cur = model.energysType[i];
                                if (cur.typeId == typeId) {
                                    model.selEnergyXunjian = cur;
                                    typeIndex = i
                                }
                            }
                            setTimeout(function () {
                                $('#typeTab li').eq(typeIndex).addClass('cur').siblings().removeClass('cur');
                            }, 0);
                        },
                        error: function () {},
                        complete: function () {
                            if (model.floorBusinessIndex == 1) { //业态
                                controller.getFNMIndustryEnergyInfoService({
                                    buildingId: model.selBuild.id,
                                    energyTypeId: typeId
                                }, true);
                            } else {
                                controller.getFNMFloorEnergyInfoService({
                                    buildingId: model.selBuild.id,
                                    energyTypeId: typeId
                                }, true);
                            }
                        }
                    });
                } else {
                    controller.getFNMFloorAlarmService(true); // 0：总览页
                }
            }
        });
    },

    //20181101 wp+
    // 进入巡检页重新获取alltement
    ReGetFNMFloorAlarmService: function () {
        $('#loading').pshow();
        var model = monitorModel.instance();
        alltement = [];
        pajax.get({
            url: 'FNMFloorAlarmService',
            data: {
                buildingId: model.selBuild.id
            },
            success: function (result) {
                var resultObj = result[0] || {};
                var tenantMap = {};
                var searchtenentData = {};
                var size = calcSize(resultObj.row, resultObj.cell); //计算租户块儿的宽高
                var bashHeight = size.tenementHeight;
                var nextCrossArr = [];
                for (var i = 0, i_length = resultObj.floorList.length; i < i_length; i++) {
                    var floor = resultObj.floorList[i];
                    var crossArr = [];
                    var singleArr = [];
                    for (var j = 0, j_length = floor.tenantList.length; j < j_length; j++) {
                        var t = floor.tenantList[j];
                        t["isAlarm"] = false;
                        var had = false;
                        for (var k = 0, k_length = nextCrossArr.length; k < k_length; k++) {
                            var next_t = nextCrossArr[k];
                            if (next_t && next_t.tenantId == t.tenantId) {
                                had = true;
                                break;
                            }
                        }
                        if (had) {
                            continue;
                        }
                        t["isCross"] = t.crossFloor > 1;
                        if (t.isCross) {
                            crossArr.push(t);
                        } else {
                            singleArr.push(t);
                        }
                    }
                    crossArr.sort(function (x, y) {
                        return x.crossFloor - y.crossFloor;
                    });
                    for (var j = 0, j_length = crossArr.length; j < j_length; j++) {
                        var c_t = crossArr[j];
                        c_t["height"] = c_t.crossFloor * bashHeight + (c_t.crossFloor - 1) * 2;
                    }
                    for (var j = 0, j_length = nextCrossArr.length; j < j_length && crossArr.length > 0; j++) {
                        if (!nextCrossArr[j]) {
                            var n_c_t = crossArr.shift();
                            n_c_t["crossCount"] = n_c_t.crossFloor;
                            nextCrossArr[j] = n_c_t;
                        }
                    }
                    if (crossArr.length == 0) {
                        for (var j = 0, j_length = nextCrossArr.length; j < j_length && singleArr.length > 0; j++) {
                            if (!nextCrossArr[j]) {
                                var n_c_t = singleArr.shift();
                                n_c_t["crossCount"] = n_c_t.crossFloor;
                                nextCrossArr[j] = n_c_t;
                            }
                        }
                    }
                    for (var j = 0, j_length = crossArr.length; j < j_length; j++) {
                        var n_c_t = crossArr[j];
                        n_c_t["crossCount"] = n_c_t.crossFloor;
                        nextCrossArr.push(n_c_t);
                    }
                    while (nextCrossArr.length > 0 && !nextCrossArr[nextCrossArr.length - 1]) {
                        nextCrossArr.pop();
                    }
                    floor.tenantList = nextCrossArr.concat(singleArr);
                    for (var j = 0, j_length = floor.tenantList.length; j < j_length; j++) {
                        var j_item = floor.tenantList[j];
                        if (j_item) {
                            var key = j_item.tenantId;
                            searchtenentData[key] = j_item;
                            searchtenentData[key].nameFloor = j_item.tenantName + '　' + floor.floorName;
                            if (key in tenantMap) {
                                continue;
                            }
                            tenantMap[key] = j_item;
                        }
                    }
                    nextCrossArr = JSON.parse(JSON.stringify(nextCrossArr));
                    for (var j = 0, j_length = nextCrossArr.length; j < j_length; j++) {
                        var n_c_t = nextCrossArr[j];
                        if (!n_c_t) {
                            nextCrossArr[j] = undefined;
                            continue;
                        }
                        n_c_t.crossCount--;
                        if (n_c_t.crossCount == 0) {
                            nextCrossArr[j] = undefined;
                            continue;
                        }
                        n_c_t.isCross = false;
                    }
                }
                model.floorTable = resultObj;
                model.tenantMap = tenantMap;
                for (s_item in searchtenentData) {
                    alltement.push(searchtenentData[s_item]);
                }
            },
        });
    },

    //总览查看--获取租户列表
    getFNMFloorAlarmService: function (auto) {
        $('#loading').pshow();
        var model = monitorModel.instance();
        alltement = [];
        pajax.get({
            url: 'FNMFloorAlarmService',
            data: {
                buildingId: model.selBuild.id
            },
            success: function (result) {
                var resultObj = result[0] || {};
                var tenantMap = {};
                var searchtenentData = {};
                var size = calcSize(resultObj.row, resultObj.cell); //计算租户块儿的宽高
                var bashHeight = size.tenementHeight;
                var nextCrossArr = [];
                for (var i = 0, i_length = resultObj.floorList.length; i < i_length; i++) {
                    var floor = resultObj.floorList[i];
                    var crossArr = [];
                    var singleArr = [];
                    for (var j = 0, j_length = floor.tenantList.length; j < j_length; j++) {
                        var t = floor.tenantList[j];
                        t["isAlarm"] = false;
                        var had = false;
                        for (var k = 0, k_length = nextCrossArr.length; k < k_length; k++) {
                            var next_t = nextCrossArr[k];
                            if (next_t && next_t.tenantId == t.tenantId) {
                                had = true;
                                break;
                            }
                        }
                        if (had) {
                            continue;
                        }
                        t["isCross"] = t.crossFloor > 1;
                        if (t.isCross) {
                            crossArr.push(t);
                        } else {
                            singleArr.push(t);
                        }
                    }
                    crossArr.sort(function (x, y) {
                        return x.crossFloor - y.crossFloor;
                    });
                    for (var j = 0, j_length = crossArr.length; j < j_length; j++) {
                        var c_t = crossArr[j];
                        c_t["height"] = c_t.crossFloor * bashHeight + (c_t.crossFloor - 1) * 2;
                    }

                    for (var j = 0, j_length = nextCrossArr.length; j < j_length && crossArr.length > 0; j++) {
                        if (!nextCrossArr[j]) {
                            var n_c_t = crossArr.shift();
                            n_c_t["crossCount"] = n_c_t.crossFloor;
                            nextCrossArr[j] = n_c_t;
                        }
                    }
                    if (crossArr.length == 0) {
                        for (var j = 0, j_length = nextCrossArr.length; j < j_length && singleArr.length > 0; j++) {
                            if (!nextCrossArr[j]) {
                                var n_c_t = singleArr.shift();
                                n_c_t["crossCount"] = n_c_t.crossFloor;
                                nextCrossArr[j] = n_c_t;
                            }
                        }
                    }
                    for (var j = 0, j_length = crossArr.length; j < j_length; j++) {
                        var n_c_t = crossArr[j];
                        n_c_t["crossCount"] = n_c_t.crossFloor;
                        nextCrossArr.push(n_c_t);
                    }

                    while (nextCrossArr.length > 0 && !nextCrossArr[nextCrossArr.length - 1]) {
                        nextCrossArr.pop();
                    }
                    floor.tenantList = nextCrossArr.concat(singleArr);
                    for (var j = 0, j_length = floor.tenantList.length; j < j_length; j++) {
                        var j_item = floor.tenantList[j];
                        if (j_item) {
                            var key = j_item.tenantId;
                            searchtenentData[key] = j_item;
                            searchtenentData[key].nameFloor = j_item.tenantName + '　' + floor.floorName;
                            if (key in tenantMap) {
                                continue;
                            }
                            tenantMap[key] = j_item;
                        }
                    }
                    nextCrossArr = JSON.parse(JSON.stringify(nextCrossArr));
                    for (var j = 0, j_length = nextCrossArr.length; j < j_length; j++) {
                        var n_c_t = nextCrossArr[j];
                        if (!n_c_t) {
                            nextCrossArr[j] = undefined;
                            continue;
                        }
                        n_c_t.crossCount--;
                        if (n_c_t.crossCount == 0) {
                            nextCrossArr[j] = undefined;
                            continue;
                        }
                        n_c_t.isCross = false;
                    }
                }
                model.floorTable = resultObj;
                model.tenantMap = tenantMap;
                for (s_item in searchtenentData) {
                    alltement.push(searchtenentData[s_item]);
                }
            },
            error: function () {},
            complete: function () {
                /* 20181022 wp+ */
                var model = monitorModel.instance();
                var ifJump = window.location.href.indexOf('&tid')
                if (ifJump > 1 && auto) {
                    var arr = window.location.href.split('&');
                    var typeId = arr[arr.length - 5].split('=')[1];
                    var index = arr[arr.length - 4].split('=')[1];
                    var index1 = arr[arr.length - 3].split('=')[1];
                    var buildingId = arr[arr.length - 1].split('=')[1];
                    $('#mainTab').psel(0, true);
                    var parentItem = model.floorTable.floorList[index].tenantList[index1];
                    model.goToAlarmDetail(parentItem, index, index1);
                }
                /* end */
                if (!globalWarningIsvisible) { //全局报警设置打开状态下
                    setTimeout(function () {
                        $('#loading').phide();
                    }, 20);
                }
                controller.getFNMTenantAlarmService();
            }
        });
    },
    //总览查看--租户报警状态
    getFNMTenantAlarmService: function () {
        clearTimeout(controller.tementTimer);
        var model = monitorModel.instance();
        var data = {
            buildingId: model.selBuild.id
        };
        if (!model.copySelBuildId) {
            return
        }
        pajax.get({
            url: 'FNMTenantAlarmService',
            data: data,
            success: function (result) {
                var alarmMap = {};
                for (var i = 0, a_len = result.length; i < a_len; i++) {
                    var a_item = result[i];
                    var key = a_item;
                    alarmMap[key] = a_item;
                }
                for (var j_item in model.tenantMap) {
                    model.tenantMap[j_item].isAlarm = j_item in alarmMap
                }
            },
            error: function () {},
            complete: function () {
                clearTimeout(controller.tementTimer);
                if (model.copySelBuildId) {
                    controller.tementTimer = setTimeout(function () {
                        controller.getFNMTenantAlarmService();
                    }, 3 * 1000);
                }
            }
        });
    },
    //获取全局报警
    getFNMAlarmLimitListService: function () {
        var model = monitorModel.instance();
        pajax.get({
            url: 'FNMAlarmLimitListService',
            data: {
                buildingId: model.selBuild.id,
                tenantId: null
            },
            success: function (result) {
                model.globalAlarmSetArr = result[0] || {};
                model.customAlarmSetArr = JSON.parse(JSON.stringify(model.globalAlarmSetArr)) //跟随自定义报警的情况
                if (!model.isdiy) {
                    setTimeout(function () {
                        $('#diyAlarm').psel(false, false);
                    }, 0);
                }
            },
            error: function () {},
            complete: function () {
                if (model.isdiyAlarm) {
                    return;
                } else {
                    $("#modal-gAlarmset").pshow();
                }
            }
        });
    },
    //保存全局报警
    setFNMAlarmLimitSetUpService: function () {
        var model = monitorModel.instance();
        var typeList = model.globalAlarmSetArr.typeList;
        var temTypeList = [];
        typeList.forEach(function (value) {
            temTypeList = temTypeList.concat(value.alarmList)
        })
        pajax.get({
            url: 'FNMAlarmLimitSetUpService',
            data: {
                buildingId: model.selBuild.id,
                setUp: 0,
                tenantId: null,
                typeList: temTypeList
            },
            success: function (result) {
                if (result) {
                    $('#notice-con').pshow({
                        state: 'success',
                        text: '保存成功'
                    });
                    $('#modal-gAlarmset').phide();
                }
            },
            error: function () {
                $('#notice-con').pshow({
                    state: 'failure',
                    text: '保存失败'
                });
                $('#loading').phide();
            },
            complete: function () {}
        });
    },
    //获取自定义报警
    getcustomAlarmSetArr: function () {
        var model = monitorModel.instance();
        model.customAlarmSetArr = {};
        pajax.get({
            url: 'FNMAlarmLimitListService',
            data: {
                buildingId: model.selBuild.id,
                tenantId: model.selectedchunk.tenantId
            },
            success: function (result) {
                model.customAlarmSetArr = result[0] || {};
                if (result[0].setUp == 1) {
                    $('#diyAlarm').psel(true, false);
                    $('#followGalarm').psel(false, false);
                    if (model.isdiy) {
                        $('#followGalarm').psel(false, false);
                    } else {
                        $('#diyAlarm').psel(true, false);
                        model.isdiy = true
                    }
                } else {
                    $('#followGalarm').psel(true, false);
                    $('#diyAlarm').psel(false, false);
                    if (model.isdiy) {
                        $('#followGalarm').psel(false, false);
                        $('#diyAlarm').psel(true, false);

                    }
                }


            },
            error: function () {},
            complete: function () {
                $('#loading').phide();
            }
        });
    },
    //保存自定义报警
    setcustomAlarmSetArr: function (arg) {
        if (arg == 0) {
            var setup = 0;
        } else {
            var setup = 1;
        }
        var model = monitorModel.instance(),
            arr = [];
        model.customAlarmSetArr.typeList.forEach(function (value) {
            arr = arr.concat(value.alarmList)
        })

        pajax.get({
            url: 'FNMAlarmLimitSetUpService',
            data: {
                buildingId: model.selBuild.id,
                setUp: setup,
                tenantId: model.selectedchunk.tenantId,
                typeList: arr
            },
            success: function (result) {
                if (result) {
                    $('#notice-con').pshow({
                        state: 'success',
                        text: '保存成功'
                    });
                    $('#float-diyAlarmset').hide();
                }
            },
            error: function () {
                monitorModel.instance().isdiyAlarm = true;
                $('#notice-con').pshow({
                    state: 'failure',
                    text: '保存失败'
                });
            },
            complete: function () {}
        });
    },
    //总览-租户hover
    getFNMTenantHoverInfoService: function (obj, data) {
        var model = monitorModel.instance();
        controller.xhr = pajax.get({
            url: 'FNMTenantHoverInfoService',
            data: data,
            success: function (result) {
                var m_obj = result[0] || {
                    "alarmList": [],
                    "roomList": []
                };
                for (var i = 0; i < m_obj.alarmList.length; i++) {
                    var item = m_obj.alarmList[i];
                    item.alarmValue = controller.numberFormat(item.alarmValue, item.unit === '%' ? controller.fixType_percent : controller.fixType_day, true);
                    item.historyMaxValue = controller.numberFormat(item.historyMaxValue, controller.fixType_dynamic, true);
                    item.historyAvgValue = controller.numberFormat(item.historyAvgValue, controller.fixType_dynamic, true);
                    item.powerValue = controller.numberFormat(item.powerValue, controller.fixType_dynamic, true);
                    item.baseLoad = controller.numberFormat(item.baseLoad, controller.fixType_dynamic, true);
                    item.limitValue = controller.numberFormat(item.limitValue, controller.fixType_day, true);
                }
                model.alarmTement = m_obj;
                $(obj).find('.alarmContent').show();
                positionTips(obj);
            },
            error: function () {},
            complete: function () {

            }
        });
    },
    //获取能耗类型参数 (巡检页的tab)
    getFNMEnergyTypeListService: function () {
        var model = monitorModel.instance();
        pajax.get({
            url: 'FNMEnergyTypeListService',
            data: {
                buildingId: model.selBuild.id
            },
            success: function (result) {
                model.energysType = result || [];
                model.selEnergyXunjian = result[0] || {};
            },
            error: function () {},
            complete: function () {
                if (model.floorBusinessIndex == 1) { //业态
                    controller.getFNMIndustryEnergyInfoService({
                        buildingId: model.selBuild.id,
                        energyTypeId: model.energysType[0].typeId
                    });
                } else {
                    controller.getFNMFloorEnergyInfoService({
                        buildingId: model.selBuild.id,
                        energyTypeId: model.energysType[0].typeId
                    });
                }

            }
        });
    },
    //巡检-租户列表
    getFNMFloorEnergyInfoService: function (data, auto) {
        $('#loading').pshow();
        var model = monitorModel.instance();
        pajax.get({
            url: 'FNMFloorEnergyInfoService',
            data: data,
            success: function (result) {
                result = result || [];
                for (var i = 0; i < result.length; i++) {
                    var tenantList = result[i].tenantList || [];
                    for (var j = 0; j < tenantList.length; j++) {
                        var tenant = tenantList[j];
                        tenant.cost = controller.numberFormat(tenant.cost, controller.fixType_money, true);
                        tenant.data = controller.numberFormat(tenant.data, controller.fixType_dynamic, true);
                        tenant.unit = tenant.energyTypeName === '耗电' ? 'kWh' : 'm³'
                    }
                }
                model.lesseeListData = result;
                model.pollingExamineData = mergeLayer(model.lesseeListData);
            },
            error: function () {},
            complete: function () {
                $('#loading').phide();
                var ifJump = window.location.href.indexOf('&tid')
                if (ifJump > 1 && auto) {
                    var arr = window.location.href.split('&');
                    var index = arr[arr.length - 4].split('=')[1];
                    var index1 = arr[arr.length - 3].split('=')[1];
                    parentItem = model.pollingExamineData[index].tenantList[index1];
                    model.goToAlarmDetail(parentItem, index, index1);
                }
            }
        });
    },
    //巡检-业态列表
    getFNMIndustryEnergyInfoService: function (data, auto) {
        $('#loading').pshow();
        var model = monitorModel.instance();
        model.typeBusinessData = {};
        model.pollingExamineData = {};
        pajax.get({
            url: 'FNMIndustryEnergyInfoService',
            data: data,
            success: function (result) {
                result = result || [];
                for (var i = 0; i < result.length; i++) {
                    var tenantList = result[i].tenantList || [];
                    for (var j = 0; j < tenantList.length; j++) {
                        var tenant = tenantList[j];
                        tenant.cost = controller.numberFormat(tenant.cost, controller.fixType_money, true);
                        tenant.data = controller.numberFormat(tenant.data, controller.fixType_dynamic, true);
                        tenant.unit = tenant.energyTypeName === '耗电' ? 'kWh' : 'm³'
                    }
                }
                model.typeBusinessData = result;
                model.pollingExamineData = typeBusinessName(model.typeBusinessData);
            },
            error: function () {},
            complete: function () {
                $('#loading').phide();
                var ifJump = window.location.href.indexOf('&tid')
                if (ifJump > 1 && auto) {
                    var arr = window.location.href.split('&');
                    var index = arr[arr.length - 4].split('=')[1];
                    var index1 = arr[arr.length - 3].split('=')[1];
                    parentItem = model.pollingExamineData[index].tenantList[index1];
                    model.goToAlarmDetail(parentItem, index, index1);
                }
            }
        });
    },

    //获取楼层租户列表
    getFNMFloorTreeService: function (data) {
        $('#loading').pshow();
        var model = monitorModel.instance();
        model.world = [];
        pajax.get({
            url: 'FNMFloorTreeService',
            data: data || {
                buildingId: model.copyselBuild.id,
                typeId: "Dian_HaoDianLiang"
            },
            success: function (result) {
                if (model.tenementReportArr.length > 0) {
                    var tempArr = model.tenementReportArr;
                    for (var l = 0; l < tempArr.length; l++) {
                        var _id = tempArr[l]._tenentId;
                        for (var i = 0; i < result.length; i++) {
                            var tenantList = result[i].tenantList || [];
                            for (var j = 0; j < tenantList.length; j++) {
                                tenantList[j]["isShow"] = true;
                                if (tenantList[j]["isChecked"] == true) {
                                    continue
                                }
                                tenantList[j]["isChecked"] = tenantList[j].id === _id ? true : false;
                            }
                        }
                    }
                } else {
                    for (var i = 0; i < result.length; i++) {
                        var tenantList = result[i].tenantList || [];
                        for (var j = 0; j < tenantList.length; j++) {
                            tenantList[j]["isChecked"] = false;
                            tenantList[j]["isShow"] = true;
                        }
                    }
                }
                model.world = result;
            },
            error: function () {},
            complete: function () {
                $('#loading').phide();
            }
        });
    },
    //能耗Chart图
    getFNMEnergyDataListService: function (data, obj, isreplace) {
        var data = data;
        var doType = data.doType;
        var typeId = data.typeId;
        var chartType = 'column';
        var model = monitorModel.instance();
        model.density = data.density;
        model.newshowTime = data.startDate;
        if (doType === 0) {
            switch (data.density) {
                case 1:
                    model.detailTitleTime = (data.startDate).substring(0, 10).replace(/-/g, '.');
                    break;
                case 2:
                    model.detailTitleTime = (data.startDate).substring(0, 7).replace(/-/g, '.');
                    break;
                case 4:
                    model.detailTitleTime = (data.startDate).substring(0, 4).replace(/-/g, '.');
                    break;
            }
        }
        if (!$('#dataComparaTimer')[0]) {
            var timeType = 'd';
        } else {
            var timeType = $('#dataComparaTimer').psel().timeType;
        }

        pajax.get({
            url: 'FNMEnergyDataListService',
            data: data,
            success: function (result) {
                var color = model.colorAttr[controller.compareIndex % model.colorAttr.length];
                var fixType = controller.fixType_dynamic;
                switch (typeId) {
                    case "Dian_FeiYong":
                    case "RanQi_FeiYong":
                    case "ReShui_FeiYong":
                    case "Shui_FeiYong":
                        fixType = controller.fixType_money
                        break;
                    case "Dian_DianGongLv":
                    case "Dian_HaoDianLiang":
                    case "Dian_ShengYuLiang":
                    case "RanQi_HaoRanQiLiang":
                    case "RanQi_ShengYuLiang":
                    case "ReShui_HaoReShuiLiang":
                    case "ReShui_ShengYuLiang":
                    case "Shui_HaoShuiLiang":
                    case "Shui_ShengYuLiang":
                        fixType = controller.fixType_dynamic;
                        break;
                }
                var m_obj = result[0];
                model._unit = m_obj.unit;
                for (var p in m_obj) {
                    var p_item = m_obj[p];
                    if (p_item instanceof Array) {
                        for (var i = 0; i < p_item.length; i++) {
                            var dataItem = p_item[i];
                            dataItem.y = controller.numberFormat(dataItem.y, fixType);
                        }
                    }
                }
                if (doType) { //1数据对比
                    //需要把数据的x统一化
                    var templateDataArr = [];
                    if (model.tenementReportArr.length > 0) {
                        templateDataArr = model.tenementReportArr[0].dataArr;
                    } else {
                        templateDataArr = m_obj.dataList;
                    }
                    for (var l = 0; l < m_obj.dataList.length; l++) {
                        var oneData = m_obj.dataList[l];
                        oneData["realTime"] = oneData.x;
                        switch (data.density) {
                            case 1:
                                if (new Date(data.endDate.replace(/-/g, '/')) - new Date(data.startDate.replace(/-/g, '/')) <= 7 * 24 * 60 * 60 * 1000) {
                                    //按小时算
                                    oneData.x = templateDataArr[l].x;
                                } else {
                                    oneData.x = '2016-01-01' + (oneData.x).substring(10, 19);
                                }
                                model.isShowIndex = false;
                                break;
                            case 2:
                                oneData.x = l + 1;
                                model.isShowIndex = true;
                                break;
                            case 4:
                                oneData.x = l + 1;
                                model.isShowIndex = true;
                                break;
                        }
                    }
                    if (data.density == 2 && $('#dataComparaTimer').psel().timeType == 'M') {
                        for (var q = 0; q < 31; q++) {
                            if (m_obj.dataList.length == 31) {
                                break
                            }
                            m_obj.dataList.push({
                                realTime: "2017-11-01 00:00:00",
                                x: 31,
                                y: null
                            })
                        }
                    }
                    model.chartData = m_obj.dataList;
                    var sumData = null;
                    for (var i = 0; i < m_obj.dataList.length; i++) {
                        if (m_obj.dataList[i].y == null) {
                            continue;
                        }
                        sumData += m_obj.dataList[i].y;
                    }
                    sumData = controller.numberFormat(sumData, fixType);
                    if (data.density == 4) {
                        var shortDate = (data.startDate).substring(0, 7).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 7);
                    } else {
                        var shortDate = (data.startDate).substring(0, 10).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 10);
                    }
                    model.tenementReportArr.push({
                        _tenentId: obj.id,
                        _timeId: data.startDate + '_' + data.endDate,
                        name: obj.name,
                        showName: getFloorByTenent(obj) + '-' + obj.name + '-' + shortDate + " (" + obj.unit + ") ",
                        dataArr: m_obj.dataList,
                        color: color,
                        shortDate: shortDate,
                        sumData: sumData,
                        unit: m_obj.unit
                    });
                } else {
                    switch (typeId) {
                        case 'Dian_DianGongLv':
                            model.chartData = m_obj;
                            chartType = 'line';
                            break;
                        default:
                            model.chartData = m_obj.dataList;
                    }
                    if (typeId.split('_')[1] === 'ShengYuLiang') {
                        chartType = 'line';
                    }
                }
                if (doType) { //1数据对比
                    controller.compareIndex++;
                    if (dataComparaChart) {
                        dataComparaChart.setTitle({
                            text: "单位: " + obj.unit
                        });
                    }
                    dataComparaChart.addSeries({
                        _tenentId: obj.id,
                        _timeId: data.startDate + '_' + data.endDate,
                        name: obj.name,
                        data: model.isShowIndex ? model.chartData : getMilliseconds(model.chartData, 0),
                        color: color,
                        _unit: obj.unit,
                        _density: data.density
                    });
                } else { //0 0查看详情
                    if (controller.chart_expend) {
                        controller.chart_expend = tentchart.columnChart_Month(data.density, chartType, [{
                            data: []
                        }]);
                    }
                    if (typeId === 'Dian_DianGongLv') {
                        getMilliseconds(model.chartData, 1);
                        for (var i = 0; i < model.dataList.length; i++) {
                            var orgData = model.dataList[i];
                            model.dataList[i] = orgData === null ? 0 : orgData;
                            model.dataList[i].fillColor = model.dataList[i].a === 1 ? '#f77c7c' : '#3399CC';
                        }
                        for (var i = 0; i < model.aList.length; i++) {
                            var orgData = model.aList[i];
                            model.aList[i] = orgData === null ? 0 : orgData;
                            model.aList[i].fillColor = model.aList[i].a === 1 ? '#f77c7c' : '#FFCC00';
                        }
                        for (var i = 0; i < model.bList.length; i++) {
                            var orgData = model.bList[i];
                            model.bList[i] = orgData === null ? 0 : orgData;
                            model.bList[i].fillColor = model.bList[i].a === 1 ? '#f77c7c' : '#68C5B3';
                        }
                        for (var i = 0; i < model.cList.length; i++) {
                            var orgData = model.cList[i];
                            model.cList[i] = orgData === null ? 0 : orgData;
                            model.cList[i].fillColor = model.cList[i].a === 1 ? '#f77c7c' : '#FEA366';
                        }
                        controller.isFiveMin = true;
                        controller.chart_expend = tentchart.columnChart_Month(data.density, chartType, [{
                                color: '#3399CC',
                                name: ' ',
                                data: model.dataList
                            },
                            {
                                color: '#FFCC00',
                                name: 'A相',
                                data: model.aList
                            },
                            {
                                color: '#68C5B3',
                                name: 'B相',
                                data: model.bList
                            },
                            {
                                color: '#FEA366',
                                name: 'C相',
                                data: model.cList
                            }
                        ], tentchart.powerTooltip);
                        controller.chart_expend.xAxis[0].setCategories([]);
                    } else {
                        controller.isFiveMin = false;
                        for (var i = 0; i < model.chartData.length; i++) {
                            var orgData = model.chartData[i];
                            model.chartData[i] = orgData === null ? 0 : orgData;
                        }
                        controller.chart_expend = tentchart.columnChart_Month(data.density, chartType, [{
                            unit: m_obj.unit,
                            data: getMilliseconds(model.chartData, 0)
                        }]);
                        if (typeId.split('_')[1].indexOf('Hao') > -1 || typeId.split('_')[1] === 'FeiYong') {
                            chartExtremeValue(controller.chart_expend); //加最大最小值的标
                        }
                    }
                }
            },
            error: function () {},
            complete: function () {
                $('#loading').phide();
                if ($('#detailFloat').is(':visible')) {
                    $('#updatachartLoading').phide();
                }
            }
        });
    },
    //能耗统计
    getFNMEnergyInfoService: function (data) {
        var model = monitorModel.instance();
        pajax.get({
            url: 'FNMEnergyInfoService',
            data: data,
            success: function (result) {
                var m_obj = result[0];
                var isFeiYong = data.typeId == "Dian_FeiYong" || data.typeId == "RanQi_FeiYong" || data.typeId == "ReShui_FeiYong" || data.typeId == "Shui_FeiYong";
                if (m_obj) {
                    //电功率
                    m_obj.load = controller.toThousands(controller.numberFormat(m_obj.load, controller.fixType_percent, true));
                    m_obj.historyMaxLoad = controller.toThousands(controller.numberFormat(m_obj.historyMaxLoad, controller.fixType_dynamic, true));
                    m_obj.emptyValue = controller.toThousands(controller.numberFormat(m_obj.emptyValue, controller.fixType_dynamic, true));
                    //耗 * 量（例如：耗电量）
                    m_obj.data = controller.toThousands(controller.numberFormat(m_obj.data, isFeiYong ? controller.fixType_money : controller.fixType_dynamic, true));
                    m_obj.tongbiRatio = controller.toThousands(controller.numberFormat(m_obj.tongbiRatio, controller.fixType_percent, true));
                    m_obj.historyAvg = controller.toThousands(controller.numberFormat(m_obj.historyAvg, isFeiYong ? controller.fixType_money : controller.fixType_dynamic, true));
                    m_obj.areaAvg = controller.toThousands(controller.numberFormat(m_obj.areaAvg, isFeiYong ? controller.fixType_money : controller.fixType_dynamic, true));
                    m_obj.avgData = controller.toThousands(controller.numberFormat(m_obj.avgData, m_obj.type == 0 ? controller.fixType_dynamic : controller.fixType_money, true));
                    m_obj.maxData = controller.toThousands(controller.numberFormat(m_obj.maxData, m_obj.type == 0 ? controller.fixType_dynamic : controller.fixType_money, true));
                    m_obj.remainData = controller.toThousands(controller.numberFormat(m_obj.remainData, m_obj.type == 0 ? controller.fixType_dynamic : controller.fixType_money, true));
                    m_obj.remainDays = controller.numberFormat(m_obj.remainDays, controller.fixType_day);
                } else {
                    m_obj = {};
                }
                model.energyInfo = m_obj;
            },
            error: function () {},
            complete: function () {
                $('#loading').phide();
            }
        });
    },
    //能耗类型树形图
    getFNMEnergyTypeTreeService: function (data, defaultType) {
        var model = monitorModel.instance();
        pajax.get({
            url: 'FNMEnergyTypeTreeService',
            data: data,
            success: function (result) {
                if (data.tenantId == null) { //数据分析
                    model.dataChooseTypesCompare = result;
                    var _defaultType = {
                        id: "Dian_HaoDianLiang",
                        name: "耗电量"
                    };
                    model.selecteddataCpmpara = _defaultType;
                } else { //详情
                    model.dataChooseTypes = result;

                    if (!result[0]) {
                        return
                    }
                    if (!defaultType) {
                        for (var l = 0; l < result[0].subTypeList.length; l++) {
                            if (result[0].subTypeList[l].name.indexOf('耗') >= 0) {
                                _defaultType = result[0].subTypeList[l];
                                break;
                            }
                        }
                    } else {
                        var _defaultType = defaultType
                    }
                    model.selecteddataCpmparaType = JSON.parse(JSON.stringify(_defaultType));
                    var unit;
                    switch (_defaultType.name) {
                        case '耗电量':
                        case '剩余量':
                            unit = 'kWh';
                            break;
                        case '电功率':
                            unit = 'kW';
                            break;
                        case '费用':
                        case '剩余金额':
                            unit = '元';
                            break;
                        default:
                            unit = 'kWh';
                    }
                    model.selecteddataCpmparaType.unit = unit;
                }

            },
            error: function () {},
            complete: function () {
                if ($('#detailFloat').is(':visible')) {
                    var detailTimer = getdetailTimerId();
                    var timeObj = $(detailTimer).psel();
                    if (model.selecteddataCpmparaType.name === '电功率' || model.selecteddataCpmparaType.name === '剩余量') {
                        controller.getFNMMeterListService({
                            tenantId: model.selectedchunk.tenantId,
                            typeId: model.selecteddataCpmparaType.id
                        })
                    } else {
                        controller.getFNMEnergyDataListService({
                            density: 1,
                            doType: 0,
                            endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
                            startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
                            meterId: null,
                            tenantId: model.selectedchunk.tenantId,
                            typeId: model.selecteddataCpmparaType.id
                        });
                    }
                    controller.getFNMEnergyInfoService({
                        density: 1,
                        endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
                        startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
                        tenantId: model.selectedchunk.tenantId,
                        typeId: model.selecteddataCpmparaType.id,
                        meterId: null
                    });
                }
            }
        });
    },
    //获取仪表列表
    getFNMMeterListService: function (data) {
            var data = data;
            var model = monitorModel.instance();
            pajax.get({
                url: 'FNMMeterListService',
                data: data,
                success: function (result) {
                    result = result || [];
                    if (result.length > 0) {
                        var isShow = result[0].isShow;
                        if (isShow == 0) {
                            $('.meterWraper').hide();
                        } else {
                            $('.meterWraper').show();
                        }
                    } else {
                        return;
                    }
                    model.meterArr = result[0].meterList;
                    model.selectedmeterArr = result.length > 0 ? result[0].meterList[0].meterId : "";
                    Vue.nextTick(function () {
                        $('#meterWraperCombo').psel(0, false);
                    });
                },
                error: function () {},
                complete: function () {
                    switch (model.selecteddataCpmparaType.name) {
                        case '电功率':
                            var detailTimer = '#detailTimerOnlyD';
                            break;
                        case '剩余量':
                            var detailTimer = '#detailTimerexceptY';
                            break;
                        default:
                            var detailTimer = 'detailTimerAll';
                    }
                    var timeObj = $(detailTimer).psel();
                    controller.getFNMEnergyDataListService({
                        density: ptool.formatGranularityToJava($(detailTimer)),
                        doType: 0,
                        endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
                        startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
                        meterId: model.selectedmeterArr,
                        tenantId: model.selectedchunk.tenantId,
                        typeId: model.selecteddataCpmparaType.id
                    });
                    controller.getFNMEnergyInfoService({
                        density: ptool.formatGranularityToJava($(detailTimer)),
                        endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
                        startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
                        tenantId: model.selectedchunk.tenantId,
                        typeId: model.selecteddataCpmparaType.id,
                        meterId: model.selectedmeterArr
                    });
                }
            });
        }

        //格式化
        //isStrict 严格执行精度，即会出现模式为0.00的数据,非严格模式，即0.00显示为0
        ,
    numberFormat: function (number, fixType, isStrict) {
        if (isNaN(number) || number === null) {
            return number;
        }
        var data = new Number(number);
        var fixCount = 0;
        switch (fixType) {
            case "day":
                fixCount = 0;
                break;
            case "money":
                fixCount = 2;
                break;
            case "percent":
                fixCount = 1;
                break;
            case "dynamic":
                if (data >= 1) {
                    fixCount = 1;
                } else {
                    fixCount = 3;
                }
                break;
            default:
                return number;
                break;
        }
        var data = data.toFixed(fixCount);
        if (!isStrict) {
            data = parseFloat(data)
        }
        return data;
    },
    fixType_day: "day",
    fixType_money: "money",
    fixType_percent: "percent",
    fixType_dynamic: "dynamic",
    downFileByHtmlContent: function (htmlContent, fileName, fileType) {
        var type = fileType == "img" ? 1 : 0;
        pajax.downloadByParam("FNSPdfAndImgBuilderService", {
            htmlContent: htmlContent,
            type: type,
            fileName: fileName
        });
    },
    removeTenentChart: function (tenetId, timeId) {
        var chartSeries = dataComparaChart.series;
        if (tenetId || timeId) {
            //取消线
            var needRemove = [];
            for (var j = 0; j < chartSeries.length; j++) {
                var seriesItem = chartSeries[j];
                if ((!tenetId || seriesItem.options._tenentId == tenetId) && (!timeId || seriesItem.options._timeId == timeId)) {
                    needRemove.push(seriesItem);
                }
            }
            for (var j = 0; j < needRemove.length; j++) {
                needRemove[j].remove(true);
            }
            var model = monitorModel.instance();
            var newArr = [];
            for (var i = 0; i < model.tenementReportArr.length; i++) {
                var oneItem = model.tenementReportArr[i];
                if (!((!tenetId || oneItem._tenentId == tenetId) && (!timeId || oneItem._timeId == timeId))) {
                    newArr.push(oneItem);
                }
            }
            model.tenementReportArr = newArr;
        }
    },
    downReport: function () {
        var model = monitorModel.instance();
        pajax.downloadByParam("FNSGridDownloadService", {
            fileName: "租户" + model.selecteddataCpmpara.name + "数据报表",
            gridContent: model.downReportDataArr
        });
    },
    updateChartData: function (data, updateType) {
        var typeId = data.typeId;
        var model = monitorModel.instance();
        if (!$('#dataComparaTimer')[0]) {
            var timeType = 'd';
        } else {
            var timeType = $('#dataComparaTimer').psel().timeType;
        }
        pajax.get({
            url: model.isshowMoney ? 'FNMEnergyDataListService' : 'FNMRoomEnergyDataListService',
            data: data,
            success: function (result) {
                var color = model.colorAttr[model.tenementReportArr.length % model.colorAttr.length];
                var fixType = controller.fixType_dynamic;
                switch (typeId) {
                    case "Dian_FeiYong":
                    case "RanQi_FeiYong":
                    case "ReShui_FeiYong":
                    case "Shui_FeiYong":
                        fixType = controller.fixType_money
                        break;
                    case "Dian_DianGongLv":
                    case "Dian_DuiBiCha":
                    case "Dian_HaoDianLiang":
                    case "Dian_ShengYuLiang":
                    case "RanQi_HaoRanQiLiang":
                    case "RanQi_ShengYuLiang":
                    case "ReShui_HaoReShuiLiang":
                    case "ReShui_ShengYuLiang":
                    case "Shui_HaoShuiLiang":
                    case "Shui_ShengYuLiang":
                        fixType = controller.fixType_dynamic;
                        break;
                }
                var m_obj = result[0];
                model._unit = m_obj.unit;
                for (var p in m_obj) {
                    var p_item = m_obj[p];
                    if (p_item instanceof Array) {
                        for (var i = 0; i < p_item.length; i++) {
                            var dataItem = p_item[i];
                            dataItem.y = controller.numberFormat(dataItem.y, fixType);
                        }
                    }
                }
                if (m_obj.dataList) {
                    //需要把数据的x统一化
                    var templateDataArr = [];
                    if (model.tenementReportArr.length > 0) {
                        templateDataArr = model.tenementReportArr[0].dataArr;
                    } else {
                        templateDataArr = m_obj.dataList;
                    }
                    for (var l = 0; l < m_obj.dataList.length; l++) {
                        var oneData = m_obj.dataList[l];
                        oneData["realTime"] = oneData.x;
                        switch (data.density) {
                            case 1:
                                if (new Date(data.endDate.replace(/-/g, '/')) - new Date(data.startDate.replace(/-/g, '/')) <= 7 * 24 * 60 * 60 * 1000) {
                                    //按小时算
                                } else {
                                    oneData.x = '2016-01-01' + (oneData.x).substring(10, 19);
                                }
                                model.isShowIndex = false;
                                break;
                            case 2:
                            case 4:
                                oneData.x = l + 1;
                                model.isShowIndex = true;
                                break;
                        }
                    }
                    if (data.density == 2 && $('#dataComparaTimer').psel().timeType == 'M') {
                        for (var q = 0; q < 31; q++) {
                            if (m_obj.dataList.length == 31) {
                                break
                            }
                            m_obj.dataList.push({
                                realTime: "2017-11-01 00:00:00",
                                x: 31,
                                y: null
                            })
                        }
                    }
                    model.chartData = m_obj.dataList;
                    var sumData = null;
                    for (var i = 0; i < m_obj.dataList.length; i++) {
                        if (m_obj.dataList[i].y == null) {
                            continue;
                        }
                        sumData += m_obj.dataList[i].y;
                    }
                    sumData = controller.numberFormat(sumData, fixType);
                    if (data.density == 4) {
                        var shortDate = (data.startDate).substring(0, 7).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 7);
                    } else {
                        var shortDate = (data.startDate).substring(0, 10).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 10);
                    }
                    //更新chart
                    var seriesArr = dataComparaChart.series;
                    var timeId = data.startDate + '_' + data.endDate;
                    if (model.isshowMoney) {
                        for (var i = 0; i < seriesArr.length; i++) {
                            var seriesItem = seriesArr[i];
                            if ((updateType == "date" && seriesItem.options._tenentId == data.tenantId) || (seriesItem.options._tenentId == data.tenantId && seriesItem.options._timeId == timeId)) {
                                seriesItem.options._timeId = timeId;
                                seriesItem.options._unit = model._unit;
                                seriesItem.setData(model.isShowIndex ? model.chartData : getMilliseconds(model.chartData, 0));
                                break;
                            }
                        }
                    } else {
                        for (var i = 0; i < seriesArr.length; i++) {
                            var seriesItem = seriesArr[i];
                            if (updateType == "date") {
                                seriesItem.options._timeId = timeId;
                                seriesItem.options._unit = model._unit;
                                seriesItem.setData(model.isShowIndex ? model.chartData : getMilliseconds(model.chartData, 0));
                                break;
                            }
                        }
                    }

                    //更新报表
                    if (model.isshowMoney) {
                        for (var i = 0; i < model.tenementReportArr.length; i++) {
                            var _t = model.tenementReportArr[i];
                            if ((updateType == "date" && _t._tenentId == data.tenantId) || (_t._tenentId == data.tenantId && _t._timeId == timeId)) {
                                _t._timeId = timeId;
                                _t.showName = _t.name + '-' + shortDate + " (" + m_obj.unit + ") ";
                                _t.shortDate = shortDate;
                                _t.dataArr = m_obj.dataList;
                                _t.sumData = sumData;
                                _t.unit = m_obj.unit;
                                break;
                            }
                        }
                    } else {
                        for (var i = 0; i < model.tenementReportArr.length; i++) {
                            var _t = model.tenementReportArr[i];
                            if (updateType == "date") {
                                _t._timeId = timeId;
                                _t.showName = _t.name + '-' + shortDate + " (" + m_obj.unit + ") ";
                                _t.shortDate = shortDate;
                                _t.dataArr = m_obj.dataList;
                                _t.sumData = sumData;
                                _t.unit = m_obj.unit;
                                break;
                            }
                        }
                    }
                } else {
                    var mathArr = [];
                    model.isDuiBiCha = true;
                    if (m_obj.yiBiaoList.length > 0) mathArr.push(m_obj.yiBiaoList);
                    if (m_obj.xiaoHaoList.length > 0) mathArr.push(m_obj.xiaoHaoList);
                    if (m_obj.duiBiChaList.length > 0) mathArr.push(m_obj.duiBiChaList);
                    for (var w = 0; w < mathArr.length; w++) {
                        color = model.colorAttr[controller.compareIndex % model.colorAttr.length];
                        var templateDataArr = mathArr[w];
                        for (var l = 0; l < templateDataArr.length; l++) {
                            var oneData = templateDataArr[l];
                            oneData["realTime"] = oneData.x;
                            switch (data.density) {
                                case 1:
                                    if (new Date(data.endDate.replace(/-/g, '/')) - new Date(data.startDate.replace(/-/g, '/')) <= 7 * 24 * 60 * 60 * 1000) {} else {
                                        oneData.x = '2016-01-01' + (oneData.x).substring(10, 19);
                                    }
                                    model.isShowIndex = false;
                                    break;
                                case 2:
                                case 4:
                                    oneData.x = l + 1;
                                    model.isShowIndex = true;
                                    break;
                            }
                        }
                        if (data.density == 2 && $('#dataComparaTimer').psel().timeType == 'M') {
                            for (var q = 0; q < 31; q++) {
                                if (templateDataArr.length == 31) {
                                    break
                                }
                                templateDataArr.push({
                                    realTime: "2017-11-01 00:00:00",
                                    x: 31,
                                    y: null
                                })
                            }
                        }
                        model.chartData = templateDataArr;
                        var sumData = null;
                        for (var i = 0; i < templateDataArr.length; i++) {
                            if (templateDataArr[i].y == null) {
                                continue;
                            }
                            sumData += templateDataArr[i].y;
                        }
                        sumData = controller.numberFormat(sumData, fixType);
                        if (data.density == 4) {
                            var shortDate = (data.startDate).substring(0, 7).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 7);
                        } else {
                            var shortDate = (data.startDate).substring(0, 10).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 10);
                        }
                        //更新chart
                        var seriesArr = dataComparaChart.series;
                        var timeId = data.startDate + '_' + data.endDate;
                        var seriesItem = seriesArr[w];
                        if (updateType == "date") {
                            seriesItem.options._timeId = timeId;
                            seriesItem.options._unit = model._unit;
                            seriesItem.setData(model.isShowIndex ? model.chartData : getMilliseconds(model.chartData, 0));
                        }
                        //更新报表
                        var _t = model.tenementReportArr[w];
                        if (updateType == "date") {
                            _t._timeId = timeId;
                            _t.showName = _t.name + '-' + shortDate + " (" + m_obj.unit + ") ";
                            _t.shortDate = shortDate;
                            _t.dataArr = templateDataArr;
                            _t.sumData = sumData;
                            _t.unit = m_obj.unit;
                        }
                    }
                }
            },
            error: function () {},
            complete: function () {
                $('#loading').phide();
            }
        });
    },
    tementTimer: null,
    xhr: null,
    chart_expend: null,
    toThousands: function (num) {
        if (num == null) {
            return num
        }
        if (num == undefined) {
            return num
        }
        return (num).toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
    },
    chartIndex: 0,
    searchArr: [], //数据分析搜索的结果     
    yAxisTemp: null,
    searchTimer: null,
    compareIndex: -1,
    timerstep: 0,
    replaceTimer: true,
    isFiveMin: false,
    //新增
    getroomlist: function (data) {
        $('#loading').pshow();
        var model = monitorModel.instance(); //First Instance
        model.roomwatch =[];
        pajax.get({
            url: 'FNMFloorRoomTreeService',
            data:data||{
                buildingId: model.copyselBuild.id,
                typeId: model.selecteddataCpmpara.id
            },
            success: function (result) {
                result = result || [];
                for (var i = 0; i < result.length; i++) {
                    var _world = result[i];
                    _world.isShow = true;
                    for (var j = 0; j < _world.dataList.length; j++) {
                        _world.dataList[j].isShow = true;
                        var watch = _world.dataList[j].dataList;
                        for (var l = 0; l < watch.length; l++) {
                            watch[l].isChecked = false;
                            watch[l].isShow = true;
                        }
                    }
                }
                model.roomwatch = result;
            },
            error: function () {},
            complete: function () {
                $('#loading').phide();
            }
        });
    },
    getFNMRoomEnergyDataListService: function (data, obj, isreplace) {
        var data = data;
        var doType = data.doType;
        var typeId = data.typeId;
        var chartType = 'column';
        var model = monitorModel.instance();
        model.density = data.density;
        model.newshowTime = data.startDate;
        if (doType === 0) {
            switch (data.density) {
                case 1:
                    model.detailTitleTime = (data.startDate).substring(0, 10).replace(/-/g, '.');
                    break;
                case 2:
                    model.detailTitleTime = (data.startDate).substring(0, 7).replace(/-/g, '.');
                    break;
                case 4:
                    model.detailTitleTime = (data.startDate).substring(0, 4).replace(/-/g, '.');
                    break;
            }
        }
        pajax.get({
            url: 'FNMRoomEnergyDataListService',
            data: data,
            success: function (result) {
                var color = model.colorAttr[controller.compareIndex % model.colorAttr.length];
                var fixType = controller.fixType_dynamic;
                switch (typeId) {
                    case "Dian_FeiYong":
                    case "RanQi_FeiYong":
                    case "ReShui_FeiYong":
                    case "Shui_FeiYong":
                    case "Dian_DuiBiZhi":
                        fixType = controller.fixType_money
                        break;
                    case "Dian_DianGongLv":
                    case "Dian_HaoDianLiang":
                    case "Dian_ShengYuLiang":
                    case "RanQi_HaoRanQiLiang":
                    case "RanQi_ShengYuLiang":
                    case "ReShui_HaoReShuiLiang":
                    case "ReShui_ShengYuLiang":
                    case "Shui_HaoShuiLiang":
                    case "Shui_ShengYuLiang":
                        fixType = controller.fixType_dynamic;
                        break;
                }
                var m_obj = result[0];
                model._unit = m_obj.unit;
                for (var p in m_obj) {
                    var p_item = m_obj[p];
                    if (p_item instanceof Array) {
                        for (var i = 0; i < p_item.length; i++) {
                            var dataItem = p_item[i];
                            dataItem.y = controller.numberFormat(dataItem.y, fixType);
                        }
                    }
                }
                if (m_obj.dataList) {
                    var templateDataArr = [];
                    model.isDuiBiCha = false;
                    if (model.tenementReportArr.length > 0) {
                        templateDataArr = model.tenementReportArr[0].dataArr;
                    } else {
                        templateDataArr = m_obj.dataList;
                    }
                    for (var l = 0; l < m_obj.dataList.length; l++) {
                        var oneData = m_obj.dataList[l];
                        oneData["realTime"] = oneData.x;
                        switch (data.density) {
                            case 1:
                                if (new Date(data.endDate.replace(/-/g, '/')) - new Date(data.startDate.replace(/-/g, '/')) <= 7 * 24 * 60 * 60 * 1000) {
                                    //按小时算
                                    oneData.x = templateDataArr[l].x;
                                } else {
                                    oneData.x = '2016-01-01' + (oneData.x).substring(10, 19);
                                }
                                model.isShowIndex = false;
                                break;
                            case 2:
                                oneData.x = l + 1;
                                model.isShowIndex = true;
                                break;
                            case 4:
                                oneData.x = l + 1;
                                model.isShowIndex = true;
                                break;
                        }
                    }
                    if (data.density == 2 && $('#dataComparaTimer').psel().timeType == 'M') {
                        for (var q = 0; q < 31; q++) {
                            if (m_obj.dataList.length == 31) {
                                break
                            }
                            m_obj.dataList.push({
                                realTime: "2017-11-01 00:00:00",
                                x: 31,
                                y: null
                            })
                        }
                    }
                    model.chartData = m_obj.dataList;
                    var sumData = null;
                    for (var i = 0; i < m_obj.dataList.length; i++) {
                        if (m_obj.dataList[i].y == null) {
                            continue;
                        }
                        sumData += m_obj.dataList[i].y;
                    }
                    sumData = controller.numberFormat(sumData, fixType);
                    if (data.density == 4) {
                        var shortDate = (data.startDate).substring(0, 7).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 7);
                    } else {
                        var shortDate = (data.startDate).substring(0, 10).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 10);
                    }
                    model.tenementReportArr.push({
                        _tenentId: obj.id,
                        _timeId: data.startDate + '_' + data.endDate,
                        name: getFloorByWatch(obj) + '-' + obj.name,
                        showName: getFloorByWatch(obj) + '-' + obj.name + '-' + shortDate + " (" + obj.unit + ") ",
                        dataArr: m_obj.dataList,
                        color: color,
                        shortDate: shortDate,
                        sumData: sumData,
                        unit: m_obj.unit
                    });
                    controller.compareIndex++;
                    if (dataComparaChart) {
                        dataComparaChart.setTitle({
                            text: "单位: " + obj.unit
                        });
                    }
                    dataComparaChart.addSeries({
                        _tenentId: obj.id,
                        _timeId: data.startDate + '_' + data.endDate,
                        name: obj.name,
                        data: model.isShowIndex ? model.chartData : getMilliseconds(model.chartData, 0),
                        color: color,
                        _unit: obj.unit,
                        _density: data.density
                    });
                } else {
                    var mathArr = [];
                    model.isDuiBiCha = true;
                    if (m_obj.yiBiaoList.length > 0) mathArr.push(m_obj.yiBiaoList);
                    if (m_obj.xiaoHaoList.length > 0) mathArr.push(m_obj.xiaoHaoList);
                    if (m_obj.duiBiChaList.length > 0) mathArr.push(m_obj.duiBiChaList);
                    for (var w = 0; w < mathArr.length; w++) {
                        color = model.colorAttr[controller.compareIndex % model.colorAttr.length];
                        var templateDataArr = mathArr[w];
                        for (var l = 0; l < templateDataArr.length; l++) {
                            var oneData = templateDataArr[l];
                            oneData["realTime"] = oneData.x;
                            switch (data.density) {
                                case 1:
                                    if (new Date(data.endDate.replace(/-/g, '/')) - new Date(data.startDate.replace(/-/g, '/')) <= 7 * 24 * 60 * 60 * 1000) {
                                        //按小时算
                                        oneData.x = templateDataArr[l].x;
                                    } else {
                                        oneData.x = '2016-01-01' + (oneData.x).substring(10, 19);
                                    }
                                    model.isShowIndex = false;
                                    break;
                                case 2:
                                    oneData.x = l + 1;
                                    model.isShowIndex = true;
                                    break;
                                case 4:
                                    oneData.x = l + 1;
                                    model.isShowIndex = true;
                                    break;
                            }
                        }
                        if (data.density == 2 && $('#dataComparaTimer').psel().timeType == 'M') {
                            for (var q = 0; q < 31; q++) {
                                if (templateDataArr.length == 31) {
                                    break
                                }
                                templateDataArr.push({
                                    realTime: "2017-11-01 00:00:00",
                                    x: 31,
                                    y: null
                                })
                            }
                        }
                        model.chartData = templateDataArr;
                        var sumData = null;
                        for (var i = 0; i < templateDataArr.length; i++) {
                            if (templateDataArr[i].y == null) {
                                continue;
                            }
                            sumData += templateDataArr[i].y;
                        }
                        sumData = controller.numberFormat(sumData, fixType);
                        if (data.density == 4) {
                            var shortDate = (data.startDate).substring(0, 7).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 7);
                        } else {
                            var shortDate = (data.startDate).substring(0, 10).replace(/-/g, '.') + '~' + (new Date(new Date(data.endDate.replace(/-/g, '/')) - 1000).format('y.M.d h:m:s')).substring(0, 10);
                        }
                        var nameT;
                        if (model._unit == '元') {
                            nameT = w == 0 ? '仪表金额' : w == 1 ? '消费金额' : '金额差';
                        } else {
                            nameT = w == 0 ? '仪表能耗' : w == 1 ? '消费能耗' : '能耗差';
                        }
                        nameT = nameT + '-' + obj.name;

                        model.tenementReportArr.push({
                            _tenentId: obj.id,
                            _timeId: data.startDate + '_' + data.endDate,
                            name: nameT,
                            showName: nameT,
                            dataArr: templateDataArr,
                            color: color,
                            shortDate: shortDate,
                            sumData: sumData,
                            unit: m_obj.unit
                        });
                        controller.compareIndex++;
                        if (dataComparaChart) {
                            dataComparaChart.setTitle({
                                text: "单位: " + obj.unit
                            });
                        }
                        dataComparaChart.addSeries({
                            _tenentId: obj.id,
                            _timeId: data.startDate + '_' + data.endDate,
                            name: nameT,
                            data: model.isShowIndex ? model.chartData : getMilliseconds(model.chartData, 0),
                            type: w == 2 ? 'column' : 'line',
                            color: color,
                            _unit: obj.unit,
                            _density: data.density
                        });
                    }
                }
            },
            error: function () {},
            complete: function () {}
        });
    },
    //获取报警类型
    getAlarmTypes: function (call) {
        var model = monitorModel.instance();
        pajax.post({
            url: 'FNMAlarmParentLimitListService',
            data: {},
            success: function (data) {
                var result = data || [];
                model.alarmTypes = result;
            },
            error: function (err) {
                console.error('controller.getAlarmTypes err:' + err);
            },
            complete: function () {
                if (typeof call == "function") {
                    call();
                }
            }
        });
    },
    //获取报警记录列表3.6版本
    getAlarms: function (flag) {
        var model = monitorModel.instance();
        if (!flag) {
            model.pageIndex = 0;
        }
        if (model.currAlarmType == 1) {
            model.currAlarmType = null;
        }
        var param = {
            buildingId: model.selBuild.id,
            id: model.currAlarmType,
            tenantId: model.tenantId,
            status: model.currStatus,
            timeFrom: model.startTime,
            timeTo: model.endTime,
            pageIndex: model.pageIndex,
            pageSize: model.pageSize
        }
        $("#loading").pshow();
        pajax.post({
            url: 'FNMAlarmListService',

            data: param,
            success: function (result) {
                var dataList = (result[0] || {}).list || [];
                model.alarms = dataList;
                model.alarmDetail = {};
                var count = (result[0] || {}).count || 0;
                var pageCount = Math.max(Math.ceil(count / model.pageSize), 1);
                $('#alarmNewsPage').pcount(pageCount);
                if (dataList.length == 0) {
                    $("#divAlarmType .arrows").parent().next().slideDown();
                    $("#divAlarmType .arrows").children("div").text("b");
                }
            },
            error: function (err) {
                $("#loading").phide();
                console.error('controller.getAlarms err:' + err);
            },
            complete: function () {
                $("#loading").phide();
                $("#ulalarmsgrid").scrollTop(0);
                $("#ulalarmsgrid").children("li").removeClass("active");
                $("#divAlarmType .temp-title").toggleClass("show");
                $("#divAlarmType .arrows").parent().toggleClass("show");
                $("#divAlarmType .arrows").parent().next().slideDown();
                $("#divAlarmType .arrows").find("div").text("b");
            }
        });
    },

    getPermissionId: function (id) {
        var model = monitorModel.instance();
        pajax.post({
            url: 'FNTCommonUserPermissionService',
            data: {
                permissionId: id
            },
            success: function (res) {
                if (res && res.length) {
                    model[id] = res[0].isHave;
                }

            },
            error: function (err) {
                console.error('controller.getPermissionId err:' + err);
            },
            complete: function () {

            }
        });
    },
};