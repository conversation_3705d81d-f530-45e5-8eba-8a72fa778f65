/*日能耗*/
function compare(propertyName) {
    return function (object1, object2) {
        var value1 = object1[propertyName];
        var value2 = object2[propertyName];
        if (value2 < value1) {
            return -1;
        } else if (value2 > value1) {
            return 1;
        } else {
            return 0;
        }
    }
}

function tentchart() {

}
var imgSrc = ['/img/PropertyMonitor/max.png', '/img/PropertyMonitor/min.png'];

function chartExtremeValue(chart) {
    var series = chart.series;
    var isMinus = false;
    for (var i = 0; i < series.length; i++) {
        var currSeri = series[i];
        var plotLeft = currSeri.chart.plotLeft;

        var maxIndex = -11;
        var minIndex = -11;
        for (var m = 0; m < currSeri.data.length; m++) {
            var yValue = currSeri.data[m].y;
            if (yValue === null) {
                continue
            }
            if (maxIndex == -11) {
                maxIndex = m;
                minIndex = m;
                continue;
            }
            if (currSeri.data[maxIndex].y < yValue) {
                maxIndex = m;
            }
            if (currSeri.data[minIndex].y > yValue) {
                if (yValue < 0) {
                    isMinus = true;
                }
                minIndex = m;
            }
        }

        for (var j = 0; j < currSeri.data.length; j++) {
            if (i == 0 && j == /*0*/ maxIndex) {
                var currPoint = currSeri.data[j];
                var pointWidth = currPoint.pointWidth || 24;
                var X = currPoint.plotX;
                var Y = currPoint.plotY;
                //('../imgs/max.png', X, Y , 23, 28)********(url,距离左侧，距离上面，宽，高)
                chart.img = chart.renderer.image(imgSrc[0], X + plotLeft - 14, Y - 20, 23, 28).attr({
                    zIndex: 6,
                }).add();
            } else if (i == 0 && j == /*(currSeri.data.length - 1)*/ minIndex) {
                var currPoint = currSeri.data[j];
                var pointWidth = currPoint.pointWidth || 24;
                var X = currPoint.plotX;
                var Y = currPoint.plotY;
                var _y = currPoint.plotY - currPoint.yBottom;
                if (isMinus) {
                    Y = currPoint.plotY - _y;
                }
                //('../imgs/max.png', X, Y , 23, 28)********(url,距离左侧，距离上面，宽，高)
                chart.img = chart.renderer.image(imgSrc[1], X + plotLeft - 14, Y - 20, 23, 28).attr({
                    zIndex: 6,
                }).add();
            }
        };

    }
    series = [];
}

function setWranPoint(chart) {
    var series = chart.series;
    for (var i = 0; i < series.length; i++) {
        var currSeri = series[i];
        for (var j = 0; j < currSeri.data.length; j++) {
            var currPoint = currSeri.data[j];
            if (currPoint.iswran) {
                currPoint.update({
                    color: '#f77c7c',
                    marker: {
                        enabled: true,
                        radius: 6,
                        symbol: 'circle',
                        states: {
                            hover: {
                                fillColor: '#db1048'
                            }
                        }
                    }
                });
            }
        };

    }
    series = [];
}

tentchart.chart_MothTip = function () {
    var html = "";
    var pointlen = this.points.length;
    for (var i = 0; i < pointlen; i++) {
        html += '<span style="color:#333333;">' + this.points[i].y + ' ' + monitorModel.instance().selectedType.unit + '</span>' +
            '<span style="color:#6d6d6d;">' + new Date(this.points[i].x).getFullYear() + '.' + (new Date(this.points[i].x).getMonth() > 8 ? new Date(this.points[i].x).getMonth() + 1 : '0' + (new Date(this.points[i].x).getMonth() + 1)) + '</span>' + '<span style="color:#637e99;cursor: pointer;padding-left:20px;border-top: 1px dashed #d2d2d2;margin:0 10px" onclick="enterMonthDetail(this)">进入该月详情 ></span>';

    }
    return html;
}
tentchart.compare_linechart = function (itemId, series) {
    var xType = 'datetime';
    var model = monitorModel.instance();
    if (model.isShowIndex) {
        xType = 'category';
    }
    var chart = new Highcharts.Chart({
        chart: {
            type: 'line',
            renderTo: itemId,
            marginTop: 28,
            backgroundColor: "rgba(0,0,0,0)",
        },
        exporting: {
            enabled: false, //导出模块
        },
        //版权的一些事
        credits: {
            enabled: false,
        },
        legend: { //图例说明是包含图表中数列标志和名称的容器。
            enabled: false,
            align: 'right',
            verticalAlign: 'top',
            itemStyle: {
                color: '#000000',
                fontWeight: 'normal',
                radius: 5
            },
            // useHTML: true,
            itemHiddenStyle: {},
            x: 0,
            y: 10
        },
        title: {
            text: '',
            x: 0,
            y: 10,
            align: 'right',
            style: {
                color: '#6d6d6d',
                fontSize: '14px',
                fontWeight: '100',
                fontFamily: 'Microsoft Yahei , Arial'
            }
        },
        subtitle: {
            text: null,
        },
        xAxis: {
            maxPadding: 0,
            lineWidth: 1, //刻度线整条线的长度
            tickColor: '#ccc',
            tickLength: 6,
            tickmarkPlacement: 'on',
            minorTickLength: 1,
            type: xType,
            labels: {
                formatter: function () {
                    if (model.isShowIndex) {
                        return this.value;
                    } else {
                        return Highcharts.dateFormat('%H:%M', this.value);
                    }
                }
            }
        },

        yAxis: {
            title: {
                text: null,
            },
            gridLineColor: '#dddddd', //x轴网格线的颜色
            gridLineDashStyle: 'ShortDash', //x轴网格线的样式
            gridLineWidth: 1, //x轴网格线
            endOnTick: true,
            maxPadding: 0.25
        },
        tooltip: {
            enabled: true,
            animation: true,
            borderColor: null,
            borderWidth: 0,
            shadow: false,
            backgroundColor: null,
            useHTML: true,
            hideDelay: 200,
            formatter: function () {
                var html = "";
                var pointlen = this.points.length;
                var model = monitorModel.instance();
                var chartDataName = model.selecteddataCpmparaType.name;
                for (var i = 0; i < pointlen; i++) {
                    var point = this.points[i];
                    var _density = ptool.formatGranularityToJava($('#dataComparaTimer'));
                    var _realTime = (point.point.realTime + "").replace(/-/g, '/');
                    var xtime = (point.point.realTime + "").replace(/-/g, '.');
                    switch (_density) {
                        case 2:
                            xtime = new Date(_realTime).format('yyyy.MM.dd');
                            break;
                        case 4:
                            xtime = new Date(_realTime).format('yyyy.MM');
                            break;
                    }
                    html += '<span><b style="position:relative;top:-2px;margin-right:5px;display:inline-block;width:6px;height:6px;border-radius: 50%;background: ' +
                        point.color + ';"></b>' + point.series.name + " " + xtime + '&nbsp;&nbsp;' + point.y + ' ' + point.series.options._unit + '</span>';
                }
                return html;
            },
            style: {},
            crosshairs: {
                width: 1,
                color: 'gray',
                dashStyle: 'shortdot'
            },
            shared: true,
        },
        plotOptions: { //绘图区选项
            line: {
                marker: {
                    states: {
                        hover: {
                            enabled: false
                        }
                    },
                    lineColor: '#fff',
                    lineWidth: 2,
                    radius: 5,
                    symbol: 'circle'

                }
            },
            series: { //绘图区数列
                connectNulls: false, //是否连接一条线穿过空值点。
                stickyTracking: false, //粘连追踪
                events: { //节点事件
                    afterAnimate: function () { //已经完成了最初的动画,或在动画是禁用的情况下,立即显示系列。4.0版本以上才有
                    },
                    show: function () {

                    }
                }
            }
        },
        series: series
    });
    return chart;
}
tentchart.charttooltips = function (timer, unit) {

}
//电功率的tips
tentchart.powerTooltip = function () {
    var html = "";
    var pointlen = this.points.length;
    for (var i = 0; i < pointlen; i++) {
        html += '<span><b style="position:relative;top:-2px;margin-right:5px;display:inline-block;width:6px;height:6px;border-radius: 50%;background: ' + this.points[i].color + ';"></b>' + this.points[i].series.name + '&nbsp;&nbsp;' + this.points[i].y + ' kW&nbsp;&nbsp;<b class="' + (this.points[i].point.a ? "charttips_alarm" : "") + '" >' + controller.numberFormat(this.points[i].point.z, controller.fixType_percent, true) + '%</b></span>';
    }

    return '<span>瞬时负荷率  ' + (new Date(this.points[0].x).getHours() > 9 ? new Date(this.points[0].x).getHours() : '0' + new Date(this.points[0].x).getHours()) + ':' + (new Date(this.points[0].x).getMinutes() > 9 ? new Date(this.points[0].x).getMinutes() : '0' + new Date(this.points[0].x).getMinutes()) + '</span>' + html;
}
//其他的tips
tentchart.otherTooltip = function (_format) {
    var xData;
    switch (_format) {
        case 1:
            xData = new Date(this.x).format('y.M.d hh:mm');
            break;
        case 2:
            xData = new Date(this.x).format('M.d');
            break;
        case 4:
            xData = new Date(this.x).format('y.M');
            break;
    }
    var html = '<span> ' + xData + '</span><span>' + monitorModel.instance().selecteddataCpmparaType.name + '：' + this.y + monitorModel.instance().selecteddataCpmparaType.unit + '</span>';
    return html;
};
//详情chart
tentchart.columnChart_Month = function (_format, type, series, tooltipFun) {
    var _xType = controller.isFiveMin ? 'category' : 'datetime';
    var tooltips = tooltipFun || function () {
        var xData;
        switch (_format) {
            case 1:
                xData = new Date(this.x).format('y.M.d hh:mm');
                break;
            case 2:
                xData = new Date(this.x).format('M.d');
                break;
            case 4:
                xData = new Date(this.x).format('y.M');
                break;
            case 5:
                xData = new Date(this.x).format('y');
                break;
        }
        var html = '<span> ' + xData + '</span><span>' + monitorModel.instance().selecteddataCpmparaType.name + '：' + tentchart.formattingY(this.y, this.points[0].series.options.unit) + this.points[0].series.options.unit + '</span>';
        return html;
    };
    var chart = new Highcharts.Chart({
        chart: {
            type: type,
            renderTo: 'chart_expend',
            //zoomType: 'x',
            paddingTop: 300,
        },
        title: {
            text: ' ',
            align: 'left',
            x: 0,
            style: {
                color: '#333333',
                fontSize: '14px',
                fontWeight: '100',
                fontFamily: '微软雅黑'
            }
        },
        credits: {
            enabled: false
        },
        legend: {
            enabled: false
        },
        xAxis: {
            type: _xType,
            lineWidth: 1,
            lineColor: '#cacaca',
            tickWidth: 1,
            tickLength: 5,
            tickColor: '#ccc',
            tickInterval: controller.isFiveMin ? (60 * 60 * 1000) : null,
            labels: {
                useHTML: true,
                formatter: function () {
                    switch (_format) {
                        case 1:
                            return '<span>' + Highcharts.dateFormat('%H:%M', this.value) + '</span>';
                            break;
                        case 2:
                            return '<span class="likeLink">' + Highcharts.dateFormat('%m.%d', this.value) + '</span>';
                            break;
                        case 4:
                            return '<span class="likeLink">' + Highcharts.dateFormat('%Y.%m', this.value) + '</span>';
                            break;
                        case 5:
                            return '<span class="likeLink">' + Highcharts.dateFormat('%Y', this.value) + '</span>';
                            break;
                    }

                }
            }
        },
        yAxis: {
            title: false,
            allowDecimals: true,
            gridLineColor: '#ccc',
            gridLineDashStyle: 'ShortDot',
        },
        tooltip: {
            enabled: true,
            animation: true,
            borderColor: null,
            borderWidth: 0,
            shadow: true,
            shared: true,
            backgroundColor: null,
            useHTML: true,
            formatter: tooltips
        },
        plotOptions: { //绘图区选项
            line: {
                marker: {
                    symbol: 'circle',
                    states: {
                        hover: {
                            enabled: false
                        }
                    },
                    radius: 5,
                }
            },
            column: {
                cursor: _format == 1 ? 'default' : 'pointer',
                events: {
                    click: gg
                },
                stacking: 'normal',
                borderWidth: 0,
                states: {
                    hover: {
                        // enabled: false
                        color: '#029AC2'
                    }
                }
            },
            series: { //绘图区数列
                color: '#02A9D1',
                connectNulls: false, //是否连接一条线穿过空值点。
                stickyTracking: false, //粘连追踪
                borderColor: "red",
                turboThreshold: 0
            }
        },
        series: series
    });
    return chart;
};

tentchart.formattingY = function (value, unit) {
    if (value == null) return;
    switch (unit) {
        case '元':
            return (value).toFixed(2);
            break;
        default:
            return Math.abs(value) >= 1 ? (value).toFixed(1) : Math.abs(value) > 0 ? (value).toFixed(3) : value;
            break;
    }
};