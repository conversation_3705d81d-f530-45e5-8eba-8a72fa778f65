function monitorModel() {}

monitorModel.instance = function () {
    if (!monitorModel._instance) {
        monitorModel._instance = new Vue({
            el: '#tenementMonitor',
            data: {
                model: {},
                isfold: false, //房间类型 列表菜单 全部展开（应该是这个意思）
                isshowMoney: true, //true :租户 false:房间
                currentPage: 'overviewPage', //当前页面
                showTime: new Date().format('yyyy.MM'), //详情页时间控件1显示的时间
                showTime2: new Date().format('yyyy.MM.dd'), //详情页时间控件2显示的时间
                showTime3: '', //某日逐时能耗时间控件显示时间
                isGlobal: true, //是否为全局报警
                alarmSets: [], //报警配置信息
                alarmSetsCopy: [], //报警配置信息备份
                customAlarmSets: [], //自定义报警配置信息
                customAlarmSetsCopy: [], //自定义报警配置信息备份
                copybuilds: [], //复制建筑，数据分析里用
                copyselBuild: {},
                alltements: [],
                builds: [], //建筑
                selBuild: {}, //当前选择的建筑
                selBuild2: {}, //选择的可能会切换到的建筑
                curBuild: true, //选中的建筑是否为当前建筑
                switchBuild: false, //切换建筑(选择tab)
                floors: '', //楼层及下属租户，array类型
                allFloors: '', //详情查看左侧列表显示全部楼层
                allFloorsToXunjian: [], //巡检左侧全部楼层
                businesss: '', //业态及下属租户，array类型
                allBusinesss: '', //巡检查看左侧列表显示全部业态
                showFloorOrBusinesss: '', //巡检查看-右侧显示的楼层或业态
                energys: [], //能耗及能耗下报警信息
                selEnergyXunjian: {}, //巡检页面选择的能耗类型
                //xunjianFtIndex: 0,               //巡检页面楼层业态tab的索引  0 楼层  1 业态
                floorBusinessIndex: 0, //楼层业态tab的索引  0 楼层  1 业态
                selectedFloorId: '', //选择的楼层id
                selectedBusinessId: '', //选择的业态id
                selectedFloor: {}, //详情页选择的楼层(xxx或业态xxx)
                selectedTenement: {}, //已选择的租户
                selectedFloor1: {}, //详情页选择的业态
                selectedTenement1: {}, //业态已选择的租户
                selectedEnergyType: {}, //详情页选择的能耗类型
                selectedType: {}, //详情页选择的类型
                selectedTime: {
                    time: new Date().format('yyyy-MM') + '-01 00:00:00',
                    timeType: 'M'
                }, //详情页选择的时间
                selectedTimeValue2: '', //某日逐时能耗选择的时间值
                energyDetailTypes: [], //详情查看页可选择的类型
                surplusCostType: 0, //详情查看页 0 剩余量/剩余金额 1 每日费用
                allMeter: {}, //租户所有能耗对应的所有表具
                selectedMeter: {}, //详情页选中的表具
                currEnergysPage: 1, //当前能耗页面，1 某月逐日能耗 2 某日逐时能耗

                tenementNoEnergy: '', //租户是否有该能耗，boolean类型
                gettedEnergyAndAlarm: false,

                gettedBusinesss: false,

                monthlyEnergys: [], //逐月能耗
                dailyEnergy: {}, //某月逐日能耗
                hourlyEnergy: {}, //某日逐时能耗
                electricPower: {}, //逐时电功率
                prepaidSurplus: {}, //预付费剩余量或剩余金额逐时信息
                energyCost: {}, //能耗每日费用
                coldHeatPower: {}, //逐时冷热量功率
                fcuStatus: {}, //风机盘管逐时状态
                muteSetTimeLimit: 1, //详情页租户报警静音设置 静音时限
                muteSetEndTime: getBeforeOrAfterDate(1), //详情页租户报警静音设置 截止日期
                content: '', //详情页租户报警批注 添加的批注内容

                allTenements: [], //所有的租户
                allTenements2: ['Bodyshop1(1501,1502) 3层', 'Bodyshop2 3层'],
                isGlobal: '', //是否是全局报警设置

                //静态数据
                //muteTimeLimits: ['7天', '6天', '5天', '4天', '3天', '2天', '1天'],
                muteTimeLimits: [{
                        value: 7,
                        name: '7天'
                    },
                    {
                        value: 6,
                        name: '6天'
                    },
                    {
                        value: 5,
                        name: '5天'
                    },
                    {
                        value: 4,
                        name: '4天'
                    },
                    {
                        value: 3,
                        name: '3天'
                    },
                    {
                        value: 2,
                        name: '2天'
                    },
                    {
                        value: 1,
                        name: '1天'
                    }
                ],

                muteSetTimeLimits: [{
                        value: 1
                    },
                    {
                        value: 2
                    },
                    {
                        value: 3
                    },
                    {
                        value: 4
                    },
                    {
                        value: 5
                    },
                    {
                        value: 6
                    },
                    {
                        value: 7
                    }
                ],

                tests: [], //
                hasMonthlyEnergysData: true,
                detailpanelstate: false,
                /**** begin: 数据分析***/
                density: 1, //选择时间的密度  // 1时  2日   4月   5年
                comparedensity: 1, //数据对比时间密度 1时  2日   4月   5年
                realdensity: 'd',
                newshowTime: new Date().format('yyyy.MM.dd'), //详情页的时间显示
                searchText: '', //输入的关键字
                dataCompara_searchArr: [], //搜索的结果
                downArr: ['图表', '报表'], //下载项，下载+‘’
                world: [],
                clickSecondTimer: 0, //点击‘添加时间’的次数
                clickSecondTent: 0, //点击‘租户’的次数
                chooseCompareArr: [], //被多选择的租户
                cooseCompareStr: '', //被单选的租户
                choosecom: null,
                chooseTimeArr: [], //被选择的时间
                _selectedtementArr: [],
                selecteddataCpmparaType: {}, //已选中的参数
                selecteddataCpmpara: {}, //已选中的参数数据分析
                dataChooseTypes: [], //选择参数
                dataChooseTypesCompare: [], //选择参数数据分析
                timefirst: '', //是否第一次添加时间
                tentfirst: '', //是否第一次添加租户
                timerbetween: 0, //选择时间的步长
                customAlarmSetArr: {
                    "setUp": 1,
                    "typeList": []
                }, //自定义报警
                copycustomAlarmSetArr: { //复制自定义报警
                },
                globalAlarmSetArr: {
                    "setUp": 0,
                    "typeList": []
                }, //全局报警
                markedwords: '',
                markedwordsobj: {
                    'empty': '报警门限值不能为空',
                    'positive': '格式不正确，请填写正整数',
                    'minus': '格式不正确，请填写非负数',
                    'max100': '不能超过100%，请重新填写',
                    'max3': '不能超过999天，请重新填写'
                }, // 报警输入框验证提示语
                selectedchunk: {
                    floorName: '',
                    tenementName: '',
                    roomsNo: '',
                    tenantId: '',
                    _startDate: ""
                }, //被选中的租户块todo
                floorTable: {}, //楼层对象
                alarmTement: {
                    "alarmList": [],
                    "roomList": []
                }, //租户的报警详情
                /**** end: 数据分析***/
                // 存放渲染巡检的数据
                pollingExamineData: [],
                // 存放租户列表模式的数据
                lesseeListData: [],
                // 存放业态模式的数据
                typeBusinessData: [],
                //巡检查看的 tab
                energysType: [],
                //选择的tab项
                selectenergysType: {},
                //能耗统计
                energyInfo: {},
                //chart的值
                chartData: [],
                _unit: "",
                aList: [],
                bList: [],
                cList: [],
                dataList: [],
                //表具的值
                meterArr: [],
                //选中的值
                selectedmeterArr: {},
                //保存全部的被选中租户id
                selectedAllid: [],
                selectedAllname: [], //上面对应的name
                dataStartTime: '', //数据对比起止时间
                dataEndTime: '', //数据对比起止时间
                colorAttr: ["#02a9d1", "#e26db4", "#f79862", "#d2e500", "#ffcd02", "#8b70e2", "#61d6bf", "#4a74e0", "#af23d2", "#99f090", "#04e143", "#008ba4", "#ed7cf7", "#008c1b", "#973c08", "#da603a", "#d19505", "#1313d8", "#db0f48", "#93ea8a", "#4566b8", "#45b4b8", "#45b848", "#b1b845", "#b87b45", "#b84586", "#8e45b8", "#6150ff", "#129dd1", "#18eedc", "#ecf249", "#e29c22"],
                tementcounter: 0, //租户的计数器
                timecounter: 0, //时间的计数器
                selectedAlltementObj: [],
                tenementReportArr: [],
                isAddTimer: true,
                detailTitleTime: '', //详情的标题时间显示
                isShowIndex: false, //数据分析是否显示索引
                isdiyAlarm: false, //是否查看自定义报警
                copydiyAlarm: [], //存储用，自定义报警的设置
                isdiy: false,
                topSearchArr: [],
                $defaultType: {},
                prevDetailTime: {
                    timeType: 'd',
                    startTime: new Date().getTime()
                },
                roomwatch: [],
                showChart: true, //显示图表信息
                currStatus: null, //当前消息状态,null代表查询全部，0：未恢复，1：已恢复，2：已过期
                currAlarmType: null, //当前报警类型,null代表全部
                startTime: null, //开始时间
                endTime: null, //结束时间
                startTimeIE: null, //开始时间
                endTimeIE: null, //结束时间
                tenantId: null, //租户ID,获取报警记录,null代表获取全部
                pageIndex: 0, //当前页数
                pageSize: 50, //每页限制数据条数
                alarmTypes: [], //所有报警类型
                alarms: [], //报警记录列表数据
                alarmDetail: {}, //某条报警记录详情
                GobalAlarmSet: 0, //全局报警开关显示状态GobalAlarmSet
                TenantDetail: 0, //跳转到'租户详情'按钮显示状态TenantDetail
                sumShow: true, //控制右侧合计显示
                isDuiBiCha: false, //图表 是否为对比值
            },
            methods: {
                //总览页面租户hover事件
                overviewTenementHover: function (event, item) {
                    var self = event.currentTarget;
                    $('#floorTable .alarmContent').hide();
                    $('#globalBuild .combobox-menu-bottom').slideUp();
                    $(self).find('.across').css({
                        'z-index': 2
                    });
                    if (controller.xhr) {
                        controller.xhr.abort();
                    }
                    if (item.isAlarm) {
                        controller.getFNMTenantHoverInfoService(self, {
                            tenantId: item.tenantId
                        });
                    } else {
                        $(self).find('.alarmContent').show();
                        positionTips(self);
                    }
                },
                //总览页面租户out事件
                overviewTenementOut: function (event, item) {
                    $('#floorTable .alarmContent').hide();
                    $(event.currentTarget).find('.across').css({
                        'z-index': 1
                    });
                    if (item.isAlarm) {
                        controller.xhr.abort();
                    }
                    hideTentTip(event);
                },
                //建筑选择事件
                buildSelEvent: function (item) {
                    if (item.id === this.selBuild.id) return;
                    $('#currentBuild').removeClass('leftCross');
                    this.selBuild2 = item;
                    this.curBuild = false;
                },
                setAlarm: function (item, event) {
                    item.valid = !item.valid;
                    var _this = $(event.currentTarget);
                    _this.parents('.gaBPart').find('.gabpBody .errorTips').html('');
                },
                setDiyAlarm: function (item, event) {
                    var index = $(event.currentTarget).attr('index');
                    this.customAlarmSetArr.typeList[index].valid = event.pEventAttr.state;
                    var _this = $(event.currentTarget);
                    _this.parents('.gaBPart').find('.gabpBody .errorTips').html('');
                },
                setMute: function (event) {
                    var index = event.$index;
                    var alarmSets = !this.isGlobal ? this.customAlarmSets : this.alarmSets;
                    var alarmSet = alarmSets[index];
                    var oldMuteStatus = alarmSet.muteStatus;
                    alarmSet.muteStatus = oldMuteStatus == 0 ? 1 : 0;
                    if (alarmSet.muteStatus === 1) {
                        alarmSet.muteEndTime = getBeforeOrAfterDate('7');
                        alarmSet.muteTimeLimit = '7';
                    }
                },
                toggleMuteCombobox: function (index, e) {
                    e = e || window.event;
                    e.stopPropagation();
                    var node = !this.isGlobal ? $($('.muteTimeCombobox2')[index]) : $($('.muteTimeCombobox')[index]);
                    node.is(':hidden') ? node.slideDown() : node.slideUp();
                },
                selMuteTimeLimit: function (item, parentIndex, event) {
                    var alarmSet = !this.isGlobal ? this.customAlarmSets[parentIndex] : this.alarmSets[parentIndex];
                    alarmSet.muteEndTime = getBeforeOrAfterDate(item.value);
                    alarmSet.muteTimeLimit = item.value;
                    $($('.muteTimeCombobox')[parentIndex]).slideUp();
                    $(".muteTimeCombobox2").slideUp();
                    $(".muteTimeCombobox").slideUp();
                },
                selEnergyTab: function (event, index) {
                    $('#globalBuild .combobox-menu-bottom').slideUp();
                    if (event) {
                        $(event.currentTarget).addClass('cur').siblings().removeClass('cur');
                    }
                    var index = index;
                    this.selEnergyXunjian = this.energysType[index];
                    switch (this.selEnergyXunjian.typeId) {
                        case 'Dian':
                            this.$defaultType = {
                                id: "Dian_HaoDianLiang",
                                name: "耗电量"
                            };
                            break;
                        case 'Shui':
                            this.$defaultType = {
                                id: "Shui_HaoShuiLiang",
                                name: "耗水量"
                            };
                            break;
                        case 'ReShui':
                            this.$defaultType = {
                                id: "ReShui_HaoReShuiLiang",
                                name: "耗热水量"
                            };
                            break;
                        case 'RanQi':
                            this.$defaultType = {
                                id: "RanQi_HaoRanQiLiang",
                                name: "耗燃气量"
                            };
                            break;
                    }
                    if (this.floorBusinessIndex == 0) { //楼层
                        controller.getFNMFloorEnergyInfoService({
                            buildingId: this.selBuild.id,
                            energyTypeId: this.energysType[index].typeId
                        });
                    } else { //业态
                        controller.getFNMIndustryEnergyInfoService({
                            buildingId: this.selBuild.id,
                            energyTypeId: this.energysType[index].typeId
                        });
                    }
                },
                selFloor: function (item) {
                    this.showFloorOrBusinesss = JSON.parse(JSON.stringify([item]));
                    this.selectedFloorId = item.id;
                },
                selAllFloor: function () {
                    this.showFloorOrBusinesss = JSON.parse(JSON.stringify(this.allFloors));
                    this.selectedFloorId = '';
                },
                selBusiness: function (item) {
                    this.showFloorOrBusinesss = JSON.parse(JSON.stringify([item]));
                    this.selectedBusinessId = item.id;
                },
                selAllBusiness: function () {
                    this.showFloorOrBusinesss = JSON.parse(JSON.stringify(this.allBusinesss));
                    this.selectedBusinessId = '';
                },
                selFloorOrBusiness: function (floorOrBusiness, tabIndex) {
                    this.selectedFloor = JSON.parse(JSON.stringify(floorOrBusiness));
                    selectFloor(this.selectedFloor.id);
                },
                selTenement: function (item, floor) {
                    this.selectedTenement = item;
                    this.selectedFloor = floor;
                    this.currEnergysPage = 1;
                    var timeChartType = this.selectedType.timeType == 1 /* && model.selectedTenement.payType != 0*/ ? 1 : 2;
                    var timeChartId = timeChartType == 1 ? "#detailPage_tc" : "#detailPage_tc2";
                    var timeObj = $(timeChartId).pgetTime();
                    if (!this.selectedTenement.hasEnergy[this.selectedEnergyType.id]) {
                        showNoDataPrompt('#tenementNoEnergy', '.tip2', '.tip1');
                    } else if (isTimeValid(timeObj, 'daily')) {
                        controller.getMeters();
                    }
                },
                filterTenement: function (event) {
                    $('#divxxx').pctlRecover(true);
                    $('.deleteX').hide();
                    controller.getMeters();
                    var item = event.pEventAttr.arr[event.pEventAttr.index];
                    for (var i = 0; i < this.selectedFloorOrBusiness.length; i++) {
                        for (var j = 0; j < this.selectedFloorOrBusiness[i].tenements.length; j++) {
                            if (this.selectedFloorOrBusiness[i].tenements[j].id == item.tenementId) {
                                this.selectedTenement = this.selectedFloorOrBusiness[i].tenements[j];
                                this.selectedFloor = this.selectedFloorOrBusiness[i];
                            }
                        }
                    }
                },
                selType: function (item, parentItem) {
                    if (item.typeId === this.selectedType.typeId && parentItem.id === this.selectedEnergyType.id) return;
                    $("#chooseType .combobox-level-menu").slideUp();
                    if (!this.selectedTenement.hasEnergy[parentItem.id]) { //这里可视情况改成绑定
                        showNoDataPrompt('#tenementNoEnergy', '.tip2', '.tip1');
                        this.tenementNoEnergy = true;
                        this.selectedType = item;
                        this.selectedEnergyType = parentItem;
                        return;
                    }
                    var oldTimeType = this.selectedType.timeType;
                    if (oldTimeType == 1 && item.timeType == 2) {
                        this.showTime2 = this.showTime + '.01';
                        $('#detailPage_tc2').psetTime({
                            y: this.showTime.slice(0, 4),
                            M: this.showTime.slice(5, 7),
                            d: '01'
                        })
                        this.selectedTime = {
                            time: this.showTime2.replace(/\./g, '-') + ' 00:00:00'
                        }
                    } else if (oldTimeType == 2 && item.timeType == 1) {
                        this.showTime = this.showTime2.slice(0, 7);
                        $('#detailPage_tc').psetTime({
                            y: this.showTime2.slice(0, 4),
                            M: this.showTime2.slice(5, 7)
                        })
                        this.selectedTime = {
                            time: this.showTime.replace(/\./g, '-') + '-01 00:00:00'
                        }
                    }
                    var timeChartType = item.timeType == 1 ;
                    var timeChartId = timeChartType == 1 ? "#detailPage_tc" : "#detailPage_tc2";
                    var timeObj = $(timeChartId).pgetTime();
                    if (parentItem.id === this.selectedEnergyType.id) {
                        this.selectedType = item;
                        if (isTimeValid(timeObj, 'daily')) { //设置时间后，这里主要验证时间是否在激活日期之前
                            getChartData();
                        }
                    } else {
                        this.selectedType = item;
                        this.selectedEnergyType = parentItem;
                        if (isTimeValid(timeObj, 'daily')) { //设置时间后，这里主要验证时间是否在激活日期之前
                            if (this.floorBusinessIndex === 0) {
                                controller.getFloorsAndTenements(this.selectedEnergyType.id, true, true, null, null, null, null, true);
                            } else {
                                controller.getBusinessAndTenements(this.selectedEnergyType.id, true, true, null, true);
                            }
                        }
                    }

                },
                selMeter: function (event) {
                    this.selectedMeter = this.curMeters[event.$index];
                    if (this.selectedEnergyType.id == 'electric' && this.selectedType.typeId === 2) {
                        controller.getElectricPower();
                    } else if (this.selectedEnergyType.id == 'coldHeat' && this.selectedType.typeId === 2) {
                        controller.getColdHeatPower();
                    } else if (this.selectedType.typeId === 5) {
                        controller.getFcuStatus();
                    } else if (this.selectedType.typeId === 3 && this.selectedTenement.payType == 0 && this.surplusCostType == 0) {
                        controller.getPrepaidSurplus();
                    }
                },
                openMuteSet: function (muteEndTime) {
                    this.muteSetTimeLimit = muteEndTime ? Math.ceil((new Date(muteEndTime).getTime() - new Date(new Date().format('yyyy-MM-dd')).getTime()) / (1000 * 60 * 60 * 24)) : 1;
                    this.muteSetEndTime = muteEndTime ? muteEndTime : getBeforeOrAfterDate(1);
                    $('#muteSetCombo').psel(this.muteSetTimeLimit - 1, false);
                    $("#modal-voice").pshow();
                },
                openAlarmComments: function () {
                    this.content = '';
                    $("#float-postil").show();
                    //}
                },
                selMuteTimeLimit2: function (event) {
                    var value = this.muteSetTimeLimits[event.$index].value;
                    this.muteSetTimeLimit = value;
                    this.muteSetEndTime = getBeforeOrAfterDate(value);
                },
                goToAlarmDetail: function (parentItem, index, index1, type) { //进入详情
                    this.index = index;
                    this.index1 = index1;
                    var item;
                    $('#globalBuilds ._combobox_bottom ').hide();
                    restoreSearch();
                    this.selectedchunk = {};
                    var roomsNo = [];
                    $('.alarmContent').hide();
                    switch (this.currentPage) {
                        case 'overviewPage':
                            item = parentItem;
                            this.selectedchunk.floorName = this.floorTable.floorList[index].floorName;
                            this.selectedchunk.tenementName = item.tenantName;
                            this.selectedchunk.tenantId = item.tenantId;
                            this.selectedchunk.activeTime = item.activeTime;
                            this.selectedchunk.roomsNo = item.roomCodes || '--';
                            this.tenantId = item.tenantId;
                            this.showChart = true;
                            break;
                        case 'roundsPage':
                            item = parentItem;
                            this.tenantId = item.tenantId;
                            this.selectedchunk.floorName = this.pollingExamineData[index].floorName || this.pollingExamineData[index].typeName;
                            this.showChart = true;
                            this.selectedchunk.tenementName = item.tenantName;
                            this.selectedchunk.tenantId = item.tenantId;
                            this.selectedchunk.activeTime = item.activeTime;
                            item.roomList.forEach(function (item) {
                                roomsNo.push(item.roomId)
                            });
                            this.selectedchunk.roomsNo = roomsNo.toString();
                            break;
                    }
                    //
                    // var locationFloor = this.currentPage === 'roundsPage' && this.floorBusinessIndex === 1 ? false : true;
                    // selTab(null, 2, tenement, floor, locationFloor);
                    //selectFloor(floor.id);
                    //$("#teMonTab").psel(2);//wyy      //zy: 上面的方法已做处理，需设置选中的租户，再调用方法
                    $('#detailFloat').pshow();
                    $('.maskdetailFloat').show();
                    this.newshowTime = new Date(new Date().getTime()).format('y.M.d');
                    if (this.currentPage == 'roundsPage') {
                        controller.getFNMEnergyTypeTreeService({
                            buildingId: this.selBuild.id,
                            doType: 0,
                            tenantId: this.selectedchunk.tenantId
                        }, this.$defaultType);
                    } else {
                        controller.getFNMEnergyTypeTreeService({
                            buildingId: this.selBuild.id,
                            doType: 0,
                            tenantId: this.selectedchunk.tenantId
                        });
                    }
                    var date = new Date();
                    var y = date.getFullYear();
                    var m = date.getMonth();
                    var d = date.getDate();
                    var startTime = new Date(y, m, d, 0, 0, 0).getTime();
                    var endTime = new Date(y, m, d, 23, 59, 59).getTime();
                    $('#detailTimerAll').psel({
                        timeType: 'd',
                        startTime: startTime
                    }, false);
                    $('#detailTimerexceptY').psel({
                        timeType: 'd',
                        startTime: startTime
                    }, false);
                    $('#detailTimerOnlyD').psel({
                        timeType: 'd',
                        startTime: startTime
                    }, false);
                    loadChart();
                },
                limitInput: function (index, item, attrName, type) { //值为item[attrName]的情形

                    var value = item[attrName];
                    switch (type) {
                        case 1: //限制输入两位小数
                            if (isNaN(value)) { //限制只能输入数字字符（正数）
                                item[attrName] = value.slice(0, value.length - 1);
                            } else if (!/^\d+\.\d{0,1}$/.test(value)) {
                                var index = value.indexOf('.');
                                if (index > -1) {
                                    value = value.slice(0, index + 3);
                                    item[attrName] = value;
                                }
                            }
                            break;
                        case 2: //限制输入正整数
                            if (!/^\d+$/.test(value)) {
                                item[attrName] = value.slice(0, value.length - 1);
                            }
                            break;
                        case 3:
                            if (isNaN(value)) { //限制只能输入数字字符（正数）
                                item[attrName] = value.slice(0, value.length - 1);
                            }
                            break;
                        default:
                            break;
                    }
                },
                //顶部搜索的结果面板
                // closedetailPanel: function () {
                //     this.detailpanelstate = false;
                // },
                /*begin:数据对比*/
                emptyselectedtement: function () { //清空右侧已选中租户
                    var model = this;
                    model.chooseTimeArr = [];
                    while (model.tenementReportArr.length > 0) {
                        controller.removeTenentChart(model.tenementReportArr[0]._tenentId);
                    }
                    for (var i = 0; i < this.world.length; i++) {
                        var orgTenantList = this.world[i].tenantList;
                        for (var j = 0; j < orgTenantList.length; j++) {
                            orgTenantList[j].isChecked = false;
                        }
                    }
                    for (var i = 0; i < model.roomwatch.length; i++) {
                        var _world = model.roomwatch[i];
                        for (var j = 0; j < _world.dataList.length; j++) {
                            var watch = _world.dataList[j].dataList;
                            for (var l = 0; l < watch.length; l++) {
                                if (watch[l].isChecked) {
                                    Vue.set(watch[l], 'isChecked', false)
                                }
                            }
                        }
                    }
                    controller.compareIndex = -1;
                },
                toggleTimeChart: function (event) {
                    var tag = $(event.currentTarget),
                        offx = tag[0].offsetLeft,
                        offy = tag[0].offsetTop;
                    $('#time-chart').show().css({
                        'position': 'absolute',
                        'left': offx,
                        'top': offy
                    });
                }, //打开时间控件
                addTimer: function () { //添加时间
                    if (this.timefirst) { //第一次选择时间

                    }
                },
                reduceTimer: function (_index, _item, _arr) {
                    $('#dataComparaTimer > ._combobox_bottom').hide();
                    var startDate = _item.dataStartTime;
                    var endDate = _item.dataEndTime;
                    controller.removeTenentChart(undefined, startDate + "_" + endDate);
                    this.chooseTimeArr.splice(_index, 1);
                },
                clearKeyword: function () {
                    var searchArrlen = controller.searchArr.length;
                    var _counter = 0;
                    var allArr = [];
                    for (var i = 0; i < this.world.length; i++) {
                        var _world = this.world[i];
                        for (var j = 0, len = _world.tenantList.length; j < len; j++) {
                            var _tenantList = _world.tenantList[j];
                            if (_tenantList.isShow) {
                                continue
                            };
                            _tenantList.isShow = true;
                            if ((++_counter) == searchArrlen.length) {
                                break;
                            }
                        }
                    }

                    this.searchText = '';
                    $('#searchTement').pctlRecover();
                    $('#popTree-wrap').hide();
                    $('#dataCompara_tementTree').show();

                },
                selSearchSquare: function (event, item) { //todo
                    /*单选：*/
                    this.dataCompara_searchArr = [];
                    $('#popTree-wrap').hide();
                    $('#dataCompara_tementTree').show();
                    //绘图
                    /* 多选：*/

                },
                testInput: function (index, event, item) {
                    var _this = event.currentTarget;
                    var tips = $(_this).parents('.gabpBody').find('.errorTips')[0];
                    var value = item.limit;
                    tips.innerHTML = '';
                    if (value === '') {
                        tips.innerHTML = this.markedwordsobj.empty;
                    } else {
                        if (item.unit === '%') {
                            if (isNaN(value)) {
                                tips.innerHTML = this.markedwordsobj.minus;
                            } else if (value < 0) {
                                tips.innerHTML = this.markedwordsobj.minus;
                            } else if (value > 100) {
                                tips.innerHTML = this.markedwordsobj.max100;
                            }
                        } else {
                            if (isNaN(value)) {
                                tips.innerHTML = this.markedwordsobj.positive;
                            } else if (!/^[1-9]\d*$/.test(value)) {
                                tips.innerHTML = this.markedwordsobj.positive;
                            } else if (value > 999) {
                                tips.innerHTML = this.markedwordsobj.max3;
                            }
                        }
                    }
                    if (item.limit.indexOf('.') > 0) {
                        item.limit = Number(item.limit).toFixed(1);
                    }
                },
                changeCompareBUild: function (item) { //切换对比建筑
                    $('#searchTement').pval('');
                    this.copyselBuild.id = item.id;
                    if ($('#buttonTab').psel() === 0) {
                        controller.getFNMFloorTreeService({
                            buildingId: item.id,
                            typeId: this.selecteddataCpmpara.id
                        });
                    } else {
                        controller.getroomlist({
                            buildingId: item.id,
                            typeId: this.selecteddataCpmpara.id
                        });
                    }
                },
                /*end:数据对比*/
                chooseMeter: function (item) {
                    var model = this;
                    this.selectedmeterArr = item.meterId;
                    var timeObj = $(getdetailTimerId()).psel();
                    loadChart();
                    controller.getFNMEnergyDataListService({
                        density: model.density,
                        doType: 0,
                        endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
                        startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
                        meterId: model.selectedmeterArr,
                        tenantId: model.selectedchunk.tenantId,
                        typeId: model.selecteddataCpmparaType.id
                    });
                    controller.getFNMEnergyInfoService({
                        density: model.density,
                        endDate: new Date(timeObj.realEndTime).format('yyyy-MM-dd hh:mm:ss'),
                        startDate: new Date(timeObj.startTime).format('yyyy-MM-dd hh:mm:ss'),
                        tenantId: model.selectedchunk.tenantId,
                        typeId: model.selecteddataCpmparaType.id,
                        meterId: model.selectedmeterArr
                    })
                },
                removeOne: function (tenetId, timeId) {
                    $('#dataComparaTimer >._combobox_bottom').hide();
                    if ((this.tenementReportArr.length == 1 && this.chooseTimeArr.length == 1) || this.chooseTimeArr.length > 1) {
                        for (var l = 0; l < this.chooseTimeArr.length; l++) {
                            var itemTime = this.chooseTimeArr[l];
                            if (itemTime.timeId == timeId) {
                                this.chooseTimeArr.splice(l, 1);
                                break;
                            }
                        }
                    }
                    controller.removeTenentChart(tenetId, timeId);
                    var had = false;
                    for (var j = 0; j < this.tenementReportArr.length; j++) {
                        var oneItem = this.tenementReportArr[j];
                        if (oneItem._tenentId == tenetId) {
                            had = true;
                            break;
                        }
                    }
                    if (!had) {
                        if ($('#buttonTab').psel() == 1) {
                            for (var q = 0; q < this.roomwatch.length; q++) {
                                var _world = this.roomwatch[q];
                                for (var p = 0; p < _world.dataList.length; p++) {
                                    var watch = _world.dataList[p].dataList;
                                    for (var l = 0; l < watch.length; l++) {
                                        if (watch[l].id == tenetId) {
                                            watch[l].isChecked = false
                                        }
                                    }
                                }
                            }
                        } else {
                            for (var i = 0; i < this.world.length; i++) {
                                var orgTenantList = this.world[i].tenantList;
                                for (var j = 0; j < orgTenantList.length; j++) {
                                    if (orgTenantList[j].id == tenetId) {
                                        orgTenantList[j].isChecked = false;
                                        return;
                                    }
                                }
                            }
                        }
                    }
                },
                selBuildAlram: function (event) {
                    var model = this;
                    if (model.selBuild.id === this.builds[event.$index].id) return;
                    switch (this.currentPage) {
                        case 'overviewPage':
                            controller.getFNMFloorAlarmService();
                            break;
                        case 'roundsPage':
                            controller.getFNMEnergyTypeListService();
                            break;
                        case 'dataAnalysispage':
                            controller.getFNMFloorTreeService();
                            controller.getFNMEnergyTypeTreeService({
                                buildingId: model.selBuild.id,
                                doType: 1,
                                tenantId: null
                            });
                            break;

                    }
                    controller.getFNMAlarmLimitListService();
                },
                editTimer: function (item) { //编辑当前时间
                    var model = this;
                    model.isAddTimer = false;
                    model.oldTimeId = item.timeId;
                    var timeStr = item.dataStartTime;
                    var timeEnd = new Date(new Date(item.dataEndTime.replace(/-/g, '/')) - 1000).format('y-M-d h:m:s');
                    var timeType = item.timeType;
                    if (model.chooseTimeArr.length > 1) {
                        return;
                    }
                    if ($('#dataComparaTimer > ._combobox_bottom').is(':visible')) {
                        $('#dataComparaTimer >._combobox_bottom').hide();
                    } else {
                        $("#dataComparaTimer .per-calendar_details_nav li[navili='M']").pdisable(false);
                        $("#dataComparaTimer .per-calendar_details_nav li[navili='d']").pdisable(false);
                        $("#dataComparaTimer").psel({
                            timeType: timeType,
                            startTime: timeStr.replace(/-/g, '/'),
                            endTime: timeEnd.replace(/-/g, '/')
                        }, false);
                        $('#dataComparaTimer >._combobox_bottom').show();
                        if ($("#dataComparaTimer .per-calendar-lock .icon").html() == 's') {
                            $("#dataComparaTimer").plock(false);
                        }
                    }
                },
                changeBuilds: function (item, event) {
                    var index = $('#globalBuilds').psel().index;
                    var temp = this.builds[index];
                    if (temp.id == this.selBuild.id) {
                        return
                    }
                    this.selBuild = temp;
                    var model = this;
                    model.switchBuild = true;
                    model.gettedEnergyAndAlarm = false;
                    model.gettedBusinesss = false;
                    switch (model.currentPage) {
                        case 'overviewPage':
                            controller.getFNMFloorAlarmService();
                            break;
                        case 'roundsPage':
                            controller.getFNMEnergyTypeListService();
                            controller.getFNMFloorAlarmService();
                            break;
                        case 'dataAnalysispage':
                            controller.getFNMFloorTreeService();
                            controller.getFNMEnergyTypeTreeService({
                                buildingId: model.selBuild.id,
                                doType: 1,
                                tenantId: null
                            });
                            break;
                        case "alarmLists":
                            initParameters(model);
                            initTime("tenantCalendar");
                            $("#alarmNewsPage").psel(1);
                            controller.getAlarmTypes(controller.getAlarms);
                            initClass();
                    }
                },
                hideinit: function () {
                    $('.topSearch').pctlRecover(true);
                    $('.topSearch .topdelete').hide();
                    $('.topSearch  input').blur();
                },
                chooseTopsearch: function (event, item) {
                    var model = monitorModel.instance();
                    var tempArr = [];
                    if (model.currentPage == 'overviewPage') {
                        tempArr = model.floorTable.floorList;
                    } else if (model.currentPage == 'roundsPage') {
                        tempArr = model.pollingExamineData;
                    }
                    for (var i = 0; i < tempArr.length; i++) {
                        for (var j = 0; j < tempArr[i].tenantList.length; j++) {
                            var cur = tempArr[i].tenantList[j];
                            if (cur.tenantId == item.tenantId) {
                                model.index = i;
                                model.index1 = j;
                                break;
                            }
                        }
                    }
                    model.tenantId = item.tenantId;
                    model.showChart = true; //控制报警记录页面和数据分析显示切换
                    initTime("detailTenantCalendar");
                    controller.getAlarmTypes(controller.getAlarms);
                    initClass();
                    var model = this;
                    var roomsNo = [];
                    monitorModel._instance.detailpanelstate = true;
                    //todo
                    model.selectedchunk = {
                        tenantId: item.tenantId,
                        tenementName: item.tenantName,
                        activeTime: item.activeTime
                    };
                    model.selectedchunk.roomsNo = item.roomCodes;
                    var floorArr = model.floorTable.floorList;
                    var read = JSON.parse(localStorage.getItem('key'));
                    for (var i = 0; i < floorArr.length; i++) {
                        for (var j = 0; j < floorArr[i].tenantList.length; j++) {
                            var _tenent = floorArr[i].tenantList[j];
                            if (_tenent.tenantId == item.tenantId) {
                                model.selectedchunk.floorName = floorArr[i].floorName;
                                model.selectedchunk.floorId = floorArr[i].floorId;
                                break;
                            }
                        }
                    }
                    this.newshowTime = new Date(new Date().getTime()).format('y.M.d');
                    if (model.selecteddataCpmparaType.name === '剩余量') {
                        var detailTimer = '#detailTimerexceptY';
                    } else if (model.selecteddataCpmparaType.name === '电功率') {
                        var detailTimer = '#detailTimerOnlyD';
                    } else {
                        var detailTimer = '#detailTimerAll';
                    }
                    var date = new Date();
                    var y = date.getFullYear();
                    var m = date.getMonth();
                    var d = date.getDate();
                    var startTime = new Date(y, m, d, 0, 0, 0).getTime();
                    var endTime = new Date(y, m, d, 23, 59, 59).getTime();
                    $('#detailTimerAll').psel({
                        timeType: 'd',
                        startTime: startTime
                    }, false);
                    $('#detailTimerexceptY').psel({
                        timeType: 'd',
                        startTime: startTime
                    }, false);
                    $('#detailTimerOnlyD').psel({
                        timeType: 'd',
                        startTime: startTime
                    }, false);
                    controller.getFNMEnergyTypeTreeService({
                        buildingId: model.selBuild.id,
                        doType: 0,
                        tenantId: model.selectedchunk.tenantId
                    });
                    $('#detailFloat').pshow();
                    $('.maskdetailFloat').show();
                    $('.searchHint').hide();
                    loadChart();
                },

                //左侧消息状态选择
                alarmTypeSel: function (event, item) {
                    var model = monitorModel.instance();
                    var $target = $(event.currentTarget);
                    $target.addClass("pitch").siblings().removeClass("pitch");
                    this.currStatus = item;
                    $("#alarmNewsPage").psel(1);
                    controller.getAlarms();
                },
                //报警类型的选择
                alarmTypeSelEvent: function (event, item) {
                    $(".alarm-type-tree").find(".pitch").removeClass("pitch");
                    $(event.currentTarget).addClass("pitch");
                    this.currAlarmType = item.id;
                    $("#alarmNewsPage").psel(1);
                    controller.getAlarms();
                },
                //某条报警记录详情展示
                alarmSelEvent: function (event, item) {
                    var model = monitorModel.instance();
                    var $target = $(event.currentTarget);
                    $target.addClass('bold').siblings().removeClass("bold");
                    $target.addClass("active").siblings().removeClass("active");
                    for (var i = 0; i < model.alarms.length; i++) {
                        var cur = model.alarms[i];
                        if (cur.alarmId == item.alarmId) {
                            model.alarmDetail = model.alarms[i];
                        }
                    }
                }

            },
            events: {},
            props: {},
            computed: {
                selectedFloorOrBusiness: function () {
                    return this.floorBusinessIndex == 0 ? this.floors : this.businesss;
                },
                curMeters: function () {
                    var curMeters = [];
                    switch (this.selectedEnergyType.id) {
                        case 'electric':
                            curMeters = this.allMeter.electricMeter;
                            break;
                        case 'water':
                            curMeters = this.allMeter.waterMeter;
                            break;
                        case 'hotWater':
                            curMeters = this.allMeter.hotWaterMeter;
                            break;
                        case 'gas':
                            curMeters = this.allMeter.gasMeter;
                            break;
                        case 'coldHeat':
                            curMeters = this.allMeter.coldHeatMeter;
                            break;
                        case 'fcu':
                            curMeters = this.allMeter.fcuMeter;
                            break;
                        default:
                            break;
                    }
                    if (curMeters.length > 0) {
                        $('#meterCombobox').psel(0, false);
                        this.selectedMeter = curMeters[0];
                    } else {
                        this.selectedMeter = null;
                    }
                    return curMeters;
                },
                curMuteComment: function () {
                    var curMuteComment;
                    if (this.currEnergysPage == 2) {
                        curMuteComment = this.hourlyEnergy;
                    } else if (this.selectedEnergyType.id == 'electric' && this.selectedType.typeId === 2) {
                        curMuteComment = this.electricPower;
                    } else if (this.selectedType.typeId == 3 && this.selectedEnergyType.id != 'fcu' && this.selectedTenement.payType == 0 && this.surplusCostType == 0) {
                        curMuteComment = this.prepaidSurplus;
                    }
                    return curMuteComment;
                },
                monthCount: function () {
                    var date1 = new Date(this.selectedTenement.activatedTime).format('yyyy-MM-dd');
                    var date2 = new Date().format('yyyy-MM-dd');
                    return getMonths(date1, date2);
                },
                curAllAlarmCount: function () { //To Delete
                    var count = 0;
                    var arr = this.floorBusinessIndex == 0 ? this.allFloors : this.allBusinesss;
                    for (var i = 0; i < arr.length; i++) {
                        count += arr[i].alarmCount;
                    }
                    return count;
                },
                /*begin: 数据分析*/
                formType: function () {
                    if (this.selectedAllid.length === 2 && this.chooseTimeArr.length <= 1) {
                        return 'checkbox';
                    }
                    if (this.chooseTimeArr.length === 2) {
                        return 'radio';
                    }
                    return 'checkbox';
                },
                choosecompareSate: function () {
                    return this.formType === 'checkbox' ? [] : '';
                },
                currentbuildChangebtn: function () {
                    $('.combobox-con').hide();
                    switch (this.currentPage) {
                        case 'overviewPage':
                            return 'buildChangebtn';
                            break;
                        case 'roundsPage':
                            return 'buildChangebtnround';
                            break;
                        case 'dataAnalysispage':
                            return 'buildChangebtndataCompara';
                            break;
                    }
                },
                densityMap: function () {
                    switch (this.density) {
                        case 1:
                            return '日';
                        case 2:
                            return '月';
                        case 4:
                            return '年';
                    }
                },
                timeMap: function () {
                    switch (this.density) {
                        case 4:
                            return this.newshowTime.substring(0, 4) + '年';
                            break;
                        case 2:
                            return this.newshowTime.slice(0, 4) + '年' + this.newshowTime.slice(5, 7) + '月';
                            break;
                        case 1:
                            return this.newshowTime.slice(0, 4) + '年' + this.newshowTime.slice(5, 7) + '月' + this.newshowTime.slice(8, 10) + '日';
                            break;
                    }
                },
                realfloor: function () {
                    var floor = [{
                            "floorName": "30F",
                            "floorId": "30F",
                            "tenantList": [{
                                "tenantId": "33330",
                                "isAlarm": true,
                                "tenantName": "租户0"
                            }]
                        },
                        {
                            "floorName": "29F",
                            "floorId": "29F",
                            "tenantList": [{
                                "tenantId": "33331",
                                "isAlarm": true,
                                "tenantName": "租户1"
                            }]
                        },
                        {
                            "floorName": "28F",
                            "floorId": "28F",
                            "tenantList": [{
                                "tenantId": "33332",
                                "isAlarm": true,
                                "tenantName": "租户2"
                            }]
                        },
                        {
                            "floorName": "27F",
                            "floorId": "27F",
                            "tenantList": [{
                                "tenantId": "33333",
                                "isAlarm": true,
                                "tenantName": "租户3"
                            }]
                        },
                        {
                            "floorName": "26F",
                            "floorId": "26F",
                            "tenantList": [{
                                "tenantId": "33334",
                                "isAlarm": true,
                                "tenantName": "租户4"
                            }]
                        },
                        {
                            "floorName": "25F",
                            "floorId": "25F",
                            "tenantList": [{
                                "tenantId": "33335",
                                "isAlarm": true,
                                "tenantName": "租户5"
                            }]
                        },
                        {
                            "floorName": "24F",
                            "floorId": "24F",
                            "tenantList": [{
                                "tenantId": "33336",
                                "isAlarm": true,
                                "tenantName": "租户6"
                            }]
                        },
                        {
                            "floorName": "23F",
                            "floorId": "23F",
                            "tenantList": [{
                                "tenantId": "33337",
                                "isAlarm": true,
                                "tenantName": "租户7"
                            }]
                        },
                        {
                            "floorName": "22F",
                            "floorId": "22F",
                            "tenantList": [{
                                "tenantId": "33338",
                                "isAlarm": true,
                                "tenantName": "租户8"
                            }]
                        },
                        {
                            "floorName": "21F",
                            "floorId": "21F",
                            "tenantList": [{
                                "tenantId": "33339",
                                "isAlarm": true,
                                "tenantName": "租户9"
                            }]
                        },
                        {
                            "floorName": "20F",
                            "floorId": "20F",
                            "tenantList": [{
                                "tenantId": "333310",
                                "isAlarm": true,
                                "tenantName": "租户10"
                            }]
                        },
                        {
                            "floorName": "19F",
                            "floorId": "19F",
                            "tenantList": [{
                                "tenantId": "333311",
                                "isAlarm": true,
                                "tenantName": "租户11"
                            }]
                        },
                        {
                            "floorName": "18F",
                            "floorId": "18F",
                            "tenantList": [{
                                "tenantId": "333312",
                                "isAlarm": true,
                                "tenantName": "租户12"
                            }]
                        },
                        {
                            "floorName": "17F",
                            "floorId": "17F",
                            "tenantList": [{
                                "tenantId": "333313",
                                "isAlarm": true,
                                "tenantName": "租户13"
                            }]
                        },
                        {
                            "floorName": "16F",
                            "floorId": "16F",
                            "tenantList": [{
                                "tenantId": "333314",
                                "isAlarm": true,
                                "tenantName": "租户14"
                            }]
                        },
                        {
                            "floorName": "15F",
                            "floorId": "15F",
                            "tenantList": [{
                                "tenantId": "333315",
                                "isAlarm": true,
                                "tenantName": "租户15"
                            }]
                        },
                        {
                            "floorName": "14F",
                            "floorId": "14F",
                            "tenantList": [{
                                "tenantId": "333316",
                                "isAlarm": true,
                                "tenantName": "租户16"
                            }]
                        },
                        {
                            "floorName": "13F",
                            "floorId": "13F",
                            "tenantList": [{
                                "tenantId": "333317",
                                "isAlarm": true,
                                "tenantName": "租户17"
                            }]
                        },
                        {
                            "floorName": "12F",
                            "floorId": "12F",
                            "tenantList": [{
                                "tenantId": "333318",
                                "isAlarm": true,
                                "tenantName": "租户18"
                            }]
                        },
                        {
                            "floorName": "11F",
                            "floorId": "11F",
                            "tenantList": [{
                                "tenantId": "333319",
                                "isAlarm": true,
                                "tenantName": "租户19"
                            }]
                        },
                        {
                            "floorName": "10F",
                            "floorId": "10F",
                            "tenantList": [{
                                "tenantId": "333320",
                                "isAlarm": true,
                                "tenantName": "租户20"
                            }]
                        },
                        {
                            "floorName": "9F",
                            "floorId": "9F",
                            "tenantList": [{
                                "tenantId": "333321",
                                "isAlarm": true,
                                "tenantName": "租户21"
                            }]
                        },
                        {
                            "floorName": "8F",
                            "floorId": "8F",
                            "tenantList": [{
                                "tenantId": "333322",
                                "isAlarm": true,
                                "tenantName": "租户22"
                            }]
                        },
                        {
                            "floorName": "7F",
                            "floorId": "7F",
                            "tenantList": [{
                                "tenantId": "333323",
                                "isAlarm": true,
                                "tenantName": "租户23"
                            }]
                        },
                        {
                            "floorName": "6F",
                            "floorId": "6F",
                            "tenantList": [{
                                "tenantId": "333324",
                                "isAlarm": true,
                                "tenantName": "租户24"
                            }]
                        },
                        {
                            "floorName": "5F",
                            "floorId": "5F",
                            "tenantList": [{
                                "tenantId": "333325",
                                "isAlarm": true,
                                "tenantName": "租户25"
                            }]
                        },
                        {
                            "floorName": "4F",
                            "floorId": "4F",
                            "tenantList": [{
                                "tenantId": "333326",
                                "isAlarm": true,
                                "tenantName": "租户26"
                            }]
                        },
                        {
                            "floorName": "3F",
                            "floorId": "3F",
                            "tenantList": [{
                                "tenantId": "333327",
                                "isAlarm": true,
                                "tenantName": "租户27"
                            }]
                        },
                        {
                            "floorName": "2F",
                            "floorId": "2F",
                            "tenantList": [{
                                "tenantId": "333328",
                                "isAlarm": true,
                                "tenantName": "租户28"
                            }]
                        },
                        {
                            "floorName": "1F",
                            "floorId": "1F",
                            "tenantList": [{
                                "tenantId": "333329",
                                "isAlarm": true,
                                "tenantName": "租户29"
                            }]
                        },
                        {
                            "floorName": "B01",
                            "floorId": "B01",
                            "tenantList": [{
                                "tenantId": "333330",
                                "isAlarm": true,
                                "tenantName": "租户30"
                            }]
                        },
                        {
                            "floorName": "B02",
                            "floorId": "B02",
                            "tenantList": [{
                                "tenantId": "333331",
                                "isAlarm": true,
                                "tenantName": "租户31"
                            }]
                        },
                        {
                            "floorName": "B03",
                            "floorId": "B03",
                            "tenantList": [{
                                "tenantId": "333332",
                                "isAlarm": true,
                                "tenantName": "租户32"
                            }]
                        }
                    ];
                },
                /*end: 数据分析*/
                applyRequestValues: function () {
                    var data = [];
                    var tenantIdArr = this.selectedAllid;
                    var timeArr = this.chooseTimeArr;
                    if (!timeArr.length || !tenantIdArr.length) {
                        return data;
                    }
                    for (var i = 0; i < tenantIdArr.length; i++) {
                        for (var j = 0; j < timeArr.length; j++) {
                            data.push({
                                density: this.comparedensity,
                                doType: 1,
                                endDate: timeArr[j].dataEndTime,
                                startDate: timeArr[j].dataStartTime,
                                meterId: null,
                                tenantId: tenantIdArr[i],
                                typeId: this.selecteddataCpmparaType.id
                            });
                        }
                    }
                    return data;
                },
                treeTypemap: function () {
                    switch ((this.selecteddataCpmparaType.id + '').split('_')[0]) {
                        case 'Dian':
                            return '电';
                        case 'Shui':
                            return '水';
                        case 'ReShui':
                            return '热水';
                        case 'RanQi':
                            return '燃气';
                    }
                },
                reportTimeArr: function () {
                    var rstArr = [];
                    if (this.tenementReportArr.length > 0) {
                        var dataArr = this.tenementReportArr[0].dataArr;
                        for (var i = 0; i < dataArr.length; i++) {
                            if (this.isShowIndex) {
                                rstArr.push(i + 1);
                            } else {
                                var x = dataArr[i]['x'];
                                var h = ('' + x).substring(11, 14);
                                var m = ('' + x).substring(14, 16);
                                rstArr.push(h + m + "~" + h + "59");
                            }
                        }
                    }
                    return rstArr;
                },
                downReportDataArr: function () {
                    //下载时的参数
                    var rstArr = [];
                    var row1 = ["时间"];
                    for (var i = 0; i < this.tenementReportArr.length; i++) {
                        row1.push(this.tenementReportArr[i].showName);
                    }
                    rstArr.push(row1);
                    var timeArr = this.reportTimeArr || [];
                    for (var i = 0; i < timeArr.length; i++) {
                        var tempRow = [timeArr[i]];
                        for (var j = 0; j < this.tenementReportArr.length; j++) {
                            var t = this.tenementReportArr[j];
                            if (t.dataArr[i] == undefined) {
                                continue
                            }
                            tempRow.push(t.dataArr[i].y + "");
                        }
                        rstArr.push(tempRow);
                    }
                    return rstArr;
                },
                checkedCount: function () {
                    var model = this;
                    var count = 0;
                    for (var i = 0; i < model.world.length; i++) {
                        var orgTenantList = model.world[i].tenantList;
                        for (var j = 0; j < orgTenantList.length; j++) {
                            if (orgTenantList[j].isChecked) {
                                count++
                            }
                        }
                    }
                    return count;
                },
                pointerMap: function () {
                    return (this.chooseTimeArr.length > 1) ? '' : 'pointer';
                },
                widthMap: function () {
                    return !(this.chooseTimeArr.length == 1 && this.tenementReportArr.length > 1) ? 'calc(100% - 31px)' : '100%';
                },
                alltement: function () {
                    var temp = [];
                    for (var i = 0; i < this.world.length; i++) {
                        var tenantList = this.world[i].tenantList || [];
                        for (var j = 0; j < tenantList.length; j++) {
                            temp.push(tenantList[j]);
                        }
                    }
                    return temp
                }, //所有租户
                copySelBuildId: function () {
                    return this.selBuild.id
                },
                detailTitle: function () {
                    return this.selBuild.name + '-' + this.selectedchunk.floorName + '-' + this.selectedchunk.tenementName + "(" + (this.selectedchunk.roomsNo || '--') + ")";
                }
            },
            filters: {},
            watch: {}
        });
    }
    return monitorModel._instance;
};