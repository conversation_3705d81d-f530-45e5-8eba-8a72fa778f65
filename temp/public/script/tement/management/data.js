﻿//页面布局模型
function dataModel() { }
dataModel.data = {
    //属性正常命名（语义化，防止和 managementModel.data 中属性名冲突）
    meterConfigArr: [],             // 仪表配置后台返回数据
    basicsConfigArr: [],            // 基础配置后台返回数据
    bulidingsArr: [],               // 建筑列表数据
    energyTypeArr: [],             // 能耗类型数据
    setTypeArr: [
        {id:0,name:'剩余量清零 '},
        {id:1,name:'保电'},
        {id:2,name:'分合闸'},
        {id:3,name:'透支金额'},
        {id:4,name:'更新价格'},
    ],                              // 仪表类型数据
    recordType: [],                 // 操作记录类型
    recordBuildingsArr: [],         // 操作记录中的建筑列表
    recordGridArr: [],              // 操作记录表格数据
    colspanShowFlagObj: {},         // 隐藏列标识
    recordGridTittleObj: {},        // 表格表头所需数据
    configPage: '',                     //1后台配置页显示
    recordPage: '',                     //1操作记录页显示
    projectPage:"",                    //1  项目管理页显示
    batchPage:"",                          // 1 批量操作显示
    buildPage:"",                      //1  建筑配置页显示
    userPage:"",                      //1  用户页显示
    projectGridArr:[],                //项目管理表格列表数据
    buildGridArr:[],                 //建筑配置表格列表数据
    userGridArr:[],                  //用户管理表格列表数据
    projectDetailIsShow:true,       //默认详情页面显示
    curPage:"listPage",             //列表页面(添加：addPage 详情：detailPage)
    projectPage:"",                 //项目侧弹框展示页面
    tabName:"",                     //当前点击的TAB的名字
    itemInfo:{},                   //当前项目、建筑、用户详情
    itemToUpdate:{},              //用于更新数据
    framePage:"",                 //用户侧弹框展示页面
    roleList:[],
    functionList: [],                           // 3.6版本菜单权限
    permissionList:[],                         // 3.6版本功能权限                         // 3.6版本功能权限//用户职位
    saveBtn:true,                 //新建用户、编辑用户按钮显示
    gridArr:  [{ name: '小王1', age: '18', hobby: '唱歌' }
        , { name: '歌唱中华人民共和国共产主义接班人祖国美好新时代五星红旗飘扬', age: '18', hobby: '唱歌' }
        , { name: '歌唱中华人民共和国共产主义接班人祖国美好新时代五星红旗飘扬', age: '歌唱中华人民共和国共产主义接班人祖国美好新时代五星红旗飘扬', hobby: '歌唱中华人民共和国共产主义接班人祖国美好新时代五星红旗飘扬' }
        , { name: '小王4', age: '18', hobby: '歌唱中华人民共和国共产主义接班人祖国美好新时代五星红旗飘扬' }
        , { name: '小王5', age: '18', hobby: '唱歌' }
        , { name: '小王6', age: '18', hobby: '唱歌' }
        , { name: '歌唱中华人民共和国共产主义接班人祖国美好新时代五星红旗飘扬', age: '18', hobby: '唱歌' }
        , { name: '小王8', age: '18', hobby: '唱歌' }
        , { name: '小王9', age: '18', hobby: '歌唱中华人民共和国共产主义接班人祖国美好新时代五星红旗飘扬' }
        , { name: '小王10', age: '18', hobby: '唱歌' }
        , { name: '小王1', age: '歌唱中华人民共和国共产主义接班人祖国美好新时代五星红旗飘扬', hobby: '唱歌' }

    ],
    //批量删除列表获取数据
    batchArr:[] ,
    batchLength:''  ,
    comboboxArr:[],
    comboboxArr1:[{priceName:11.11,pr:11},{priceName:22.22,pr:22},{priceName:33.33,pr:33}],
    comboboxItem:[]  //复选框选中的数据   批量操作
}

// 控件库需要的数据（全局）
// tab 页签标题数据（以 _ 开头和结尾，防止和 window 对象属性重名）
var _tabTitleArr_ = [{name:"项目管理",icon:"R"},{name:"建筑配置",icon:"?"},{name:"用户管理",icon:"B"},{ name: "后台配置", icon: 'M' }, { name: "操作记录", icon: 'G' }];
// , { name: "批量操作", icon: 'A' }
// 操作记录下拉框数据
dataModel.data.recordTypeArr = [
    {
        recordType: 0,
        recordName: "缴费记录"
    },
    {
        recordType: 1,
        recordName: "充值记录"
    },
    {
        recordType: 2,
        recordName: "结算记录"
    },
    {
        recordType: 3,
        recordName: "激活记录"
    },
    {
        recordType: 4,
        recordName: "退租记录"
    },
    {
        recordType: 5,
        recordName: "变更价格方案记录"
    },
    {
        recordType: 6,
        recordName: "故障记录"
    },
    {
        recordType: 7,
        recordName: "仪表操作记录"
    },
    {
        recordType: 8,
        recordName: "退费记录"
    },
    {
        recordType: 9,
        recordName: "发送短信记录"
    },
    {
        recordType: 10,
        recordName: "仪表设置记录"
    }
]



// 处理数据方法
/**
 * 将后台返回的配置数据转换为表格显示数据
 * @param { Array } configArr       后台返回的配置数据
 * @param { Number } type           配置类型（ 0 仪表配置，1 基础配置 ）
 * @param { String } buildingId     建筑 Id
 */
dataModel.configDataToGirdArr = function (configArr, configType, buildingId) {
    // 根据不同配置设置建筑 Id
    var buildingIdStr = buildingId ? buildingId : null;

    // 遍历后台返回的配置数据
    for(var i = 0; i < configArr.length; i++) {
        var configArrItem = configArr[i];

        // 判断文件是否上传，并处理未上传时文件名称及上传时间的显示问题
        if(!configArrItem.fileId) {
            configArrItem.fileName = '--';
            configArrItem.uploadTime = '';
        } else {
            configArrItem.fileName += '.xlsx';
        }
        // 将需要的信息存入配置表格数据的数组中
        configArrItem.configType = configType;  // 配置类型（ 0 仪表配置，1 基础配置 ）
        configArrItem.buildingId = buildingId;  // 建筑 Id
    }
}



/**
 * 将后台返回的配置数据转换为表格显示数据
 * @param { Array } girdArr             操作记录表格数据
 */
dataModel.addTimeFlagForGirdArr = function (girdArr) {
    girdArr.forEach(function (item) {
        var time = item.createTime || item.faultTime;
        item.timeFlag = new Date(time).getTime();
    });
}



/**
 * 将表格数据按照一定的顺序进行排序
 * @param { Array } girdArr       传入需要排序的表格数组
 * @param { String } attrName     按照排序的字段名
 * @param { Boolean } flag        是否倒序
 */
dataModel.sortGirdArr = function (girdArr, attrName, flag) {
    girdArr.sort(function (a, b) {
        if(flag) {
            return b[attrName] - a[attrName];
        } else {
            return a[attrName] - b[attrName];
        }
    });
}




/**
 * 处理金额和能耗的小数点
 * @param { Array } girdArr       传入需要排序的表格数组
 */
dataModel.toFixedEvent = function (girdArr) {
    var fixedNum = '';
    girdArr.forEach(function (item) {
        if(item.amount != undefined) {
            fixedNum = 'amount';
        }
        if(item.currentSumEnergy != undefined) {
            fixedNum = 'currentSumEnergy';
        }
        if(item.money != undefined) {
            if(item.money!=='--'){
                item.money = parseFloat(item.money).toFixed(2);
            }
        }
        if(item[fixedNum] != undefined && item[fixedNum] < 1) {
            item[fixedNum] = parseFloat(item[fixedNum]).toFixed(3);
        } else if(item[fixedNum] != undefined && item[fixedNum] >= 1) {
            item[fixedNum] = parseFloat(item[fixedNum]).toFixed(1);
        }
    });
}


/**
 * 合并对象的方法
 * @param { Object } target         合并的目标对象
 * @param { Array } objsArr         被合并的对象数组
 */
dataModel.extendObjs = function (target, objsArr) {
    for(var i = 0; i < objsArr.length; i++) {
        var objArrItem = objsArr[i];
        for(var k in objArrItem) {
            if(!objArrItem.hasOwnProperty(k)) continue;
            target[k] = objArrItem[k];
        }
    }
    return target;
}



/**
 * 将毫秒数转成正常的时间格式
 * @param { Number } time       传入需要格式化的时间毫秒数
 */
dataModel.forMatTime = function (time) {
    var date = new Date(time);
    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' 00:00:00';
}



/**
 * 设置时间控件初始时间为本月的方法
 */
dataModel.initTimeController = function () {
    var date = new Date();
    var startTime = date.getFullYear() + '/' + (date.getMonth() + 1) + '/1 00:00:00';
    var endTime = date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate() + ' 00:00:00';
    $('#time_controller').psel({
        startTime: startTime,
        endTime: endTime
    }, false);
}


/**
 * 初始化参数状态
 * @param { String } operationName       操作名称
 * @param { Object } dataSource          操作参数对象
 */
dataModel.initRecordParams = function (operationName, dataSource) {
    switch(operationName) {
        case 'changeRecordType':
            // 初始化下载状态为查询（0 查询，1下载）
            managementModel.data.m_isDownload = 0;

            // 初始化页码参数
            managementModel.data.m_pageIndex = 0;
            $('#page_controller_select').psel(1, false);

            // 去掉暂无数据
            $("#operating_record_gird").pcount(1);

            if(dataSource) {
                // 当操作记录类型变化时，初始化能耗类型为全部
                if(managementModel.data.m_recordType != dataSource.recordType) {
                    $('#energy_type').psel(0, false);
                    managementModel.data.m_energyTypeId = null;
                };

                // 根据不同的操作记录类型设置能耗类型下拉框、建筑列表下拉框、下载按钮、激活或退租的请求类型值
                switch(dataSource.recordType) {
                    case 0:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = false;
                        managementModel.data.m_downloadBtnFlag = true;
                        managementModel.data.m_buildingFlag = true;
                        managementModel.data.m_type = null;
                        break;
                    case 1:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = true;
                        managementModel.data.m_downloadBtnFlag = true;
                        managementModel.data.m_buildingFlag = true;
                        managementModel.data.m_type = null;
                        break;
                    case 2:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = true;
                        managementModel.data.m_downloadBtnFlag = true;
                        managementModel.data.m_buildingFlag = true;
                        managementModel.data.m_type = null;
                        break;
                    case 3:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = false;
                        managementModel.data.m_downloadBtnFlag = false;
                        managementModel.data.m_buildingFlag = true;
                        managementModel.data.m_type = 1;
                        break;
                    case 4:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = false;
                        managementModel.data.m_downloadBtnFlag = false;
                        managementModel.data.m_buildingFlag = true;
                        managementModel.data.m_type = 2;
                        break;
                    case 5:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = true;
                        managementModel.data.m_downloadBtnFlag = false;
                        managementModel.data.m_buildingFlag = true;
                        managementModel.data.m_type = null;
                        break;
                    case 6:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = true;
                        managementModel.data.m_downloadBtnFlag = true;
                        managementModel.data.m_buildingFlag = false;
                        managementModel.data.m_type = null;
                        break;
                    case 7:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = false;
                        managementModel.data.m_downloadBtnFlag = true;
                        managementModel.data.m_buildingFlag = false;
                        managementModel.data.m_type = null;
                        break;
                    case 8:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = true;
                        managementModel.data.m_downloadBtnFlag = true;
                        managementModel.data.m_buildingFlag = true;
                        managementModel.data.m_type = null;
                        break;
                    case 9:
                        managementModel.data.m_setTypeFlag = false;
                        managementModel.data.m_recordEnergyTypeFlag = false;
                        managementModel.data.m_downloadBtnFlag = true;
                        managementModel.data.m_buildingFlag = true;
                        managementModel.data.m_type = null;
                        $("#operating_record_gird").addClass("msg")
                        break;
                    case 10:
                        managementModel.data.m_setTypeFlag = true;
                        managementModel.data.m_recordEnergyTypeFlag = false;
                        managementModel.data.m_downloadBtnFlag = false;
                        managementModel.data.m_buildingFlag = false;
                        managementModel.data.m_type = null;
                        break;
                }
            }
            break;
        case 'changeEnergyType':
            // 初始化下载状态为查询（0 查询，1下载）
            managementModel.data.m_isDownload = 0;

            // 初始化页码参数
            managementModel.data.m_pageIndex = 0;
            $('#page_controller_select').psel(1, false);

            // 去掉暂无数据
            $("#operating_record_gird").pcount(1);
            break;
        case 'changeTimeRange':
            // 初始化下载状态为查询（0 查询，1下载）
            managementModel.data.m_isDownload = 0;

            // 初始化页码参数
            managementModel.data.m_pageIndex = 0;
            $('#page_controller_select').psel(1, false);

            // 去掉暂无数据
            $("#operating_record_gird").pcount(1);
            break;
        case 'changeBuilding':
            // 初始化下载状态为查询（0 查询，1下载）
            managementModel.data.m_isDownload = 0;

            // 初始化页码参数
            managementModel.data.m_pageIndex = 0;
            $('#page_controller_select').psel(1, false);

            // 去掉暂无数据
            $("#operating_record_gird").pcount(1);
            break;
        case 'changePageIndex':
            // 初始化下载状态为查询（0 查询，1下载）
            managementModel.data.m_isDownload = 0

            // // 将是否为点击页码切换按钮发送请求改为 true
            // managementModel.data.m_isFromPage = true;
            break;
        case 'changePageSize':
            // 初始化下载状态为查询（0 查询，1下载）
            managementModel.data.m_isDownload = 0;

            // // 将是否点击确定设置每页条数发送请求 true
            // managementModel.data.m_isFromSetPage = true;

            // 初始化页码参数
            managementModel.data.m_pageIndex = 0;
            $('#page_controller_select').psel(1, false);

            // 去掉暂无数据
            $("#operating_record_gird").pcount(1);
            break;
        case 'downloadList':
            // 将是否下载标识更改（0 查询，1下载）
            managementModel.data.m_isDownload = 1;
            break;
    }

    // 收起时间控件
    $('#time_controller').find('.per-calendar-con').hide();
}