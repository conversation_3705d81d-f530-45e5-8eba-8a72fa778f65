﻿function managementController() {}

// 检测请求是否完成的对象
managementController.asynTool = new AsynTool();


// 权限验证
managementController.getUserPermission = function () {
    pajax.post({
        url: 'FNCUserPermissionService',
        data: {},
        success: function (res) {
            //_tabTitleArr_ = [{ name: "后台配置", icon: 'M' }, { name: "操作记录", icon: 'G' },{name:"项目管理",icon:"R"},{name:"建筑配置",icon:"g"},{name:"用户管理",icon:"#"}];
            if (res[0]) {
                // console.log(res);
                dataModel.configPage = res[0].configPage;
                dataModel.recordPage = res[0].recordPage;
                dataModel.peojectPage = res[0].projectPage;
                // dataModel.peojectPage = "1";
                dataModel.buildPage = res[0].buildingPage;
                // dataModel.buildPage = "1";
                dataModel.userPage = res[0].userPage;
                // dataModel.userPage = "1";
                dataModel.batchPage =res[0].batchMeterSettingPage;
            } else {
                dataModel.configPage = 0;
                dataModel.recordPage = 0;
                dataModel.peojectPage = 0;
                dataModel.buildPage = 0;
                dataModel.userPage = 0;
                dataModel.batchPage = 0;
            }
            var tempArr = [
                dataModel.peojectPage,
                dataModel.buildPage,
                dataModel.userPage,
                dataModel.configPage,
                dataModel.recordPage,
                dataModel.batchPage
            ];
            var selWho = 0;
            for (var i = 0; i < tempArr.length; i++) {
                if (tempArr[i] == 1) {
                    selWho = i;
                    break;
                }
            }
            // dataModel.configPage = 0;//后台配置 1:显示，0：隐藏
            // dataModel.recordPage = 1;//操作记录 1:显示，0：隐藏

            if (!dataModel.configPage && !dataModel.recordPage && !dataModel.peojectPage && !dataModel.buildPage && !dataModel.userPage && !dataModel.batchPage) {
                $("#tab_page").css("display", "none");
                return;
            } else {
                if (!dataModel.userPage) {
                    $("#tab_page .per-tab-navigation_title .per-tab-navigation_item:nth-child(3)").css("display", "none");
                    $("#userManagement").css("display", "none");
                }
                if (!dataModel.buildPage) {
                    $("#tab_page .per-tab-navigation_title .per-tab-navigation_item:nth-child(2)").css("display", "none");
                    $("#buildManagement").css("display", "none");
                }
                if (!dataModel.peojectPage) {
                    $("#tab_page .per-tab-navigation_title .per-tab-navigation_item:nth-child(1)").css("display", "none");
                    $("#projectManagement").css("display", "none");
                }
                if (!dataModel.recordPage) {
                    $("#tab_page .per-tab-navigation_title .per-tab-navigation_item:nth-child(5)").css("display", "none");
                    $("#operating_record").css("display", "none");
                }
                if (!dataModel.configPage) {
                    $("#tab_page .per-tab-navigation_title .per-tab-navigation_item:nth-child(4)").css("display", "none");
                    $("#backstage_config").css("display", "none");
                }
                $("#tab_page").psel(selWho);
            }

            /*if (!dataModel.configPage&&!dataModel.recordPage&&!dataModel.peojectPage && !dataModel.buildPage&&!dataModel.userPage) {
                $("#tab_page").css("display","none");
                return;
            } else if(!dataModel.recordPage){
                $("#tab_page .per-tab-navigation_title .per-tab-navigation_item:nth-child(2)").css("display","none");
                $("#operating_record").css("display","none");
                return;
            }else if(!dataModel.configPage){
                    $("#tab_page").psel(1)
                $("#tab_page .per-tab-navigation_title .per-tab-navigation_item:nth-child(1)").css("display","none");
                $("#backstage_config").css("display","none");
            }*/

        },
        error: function () {
            common.msgFailure("获取用户权限信息失败");
        }
    })

}

/**
 * 获取仪表配置数据
 */
managementController.getMeterConfigData = function () {
    pajax.post({
        url: 'FNCConfigMeterQueryService',
        success: function (dataJson) {

            // 添加配置类型（ 0 仪表配置，1 基础配置 ）
            var configType = 0;

            // 将后台返回的仪表配置数据转换为仪表配置表格数据存入数据模型
            dataModel.configDataToGirdArr(dataJson, configType);

            // 将后台返回的表格进行排序
            dataModel.sortGirdArr(dataJson, 'orderBy');

            // 将后台返回的仪表配置数据存入数据模型
            managementModel.data.meterConfigArr = dataJson;

            // 检测若数据为空，则取消加载
            if (!dataJson.length) {
                $("#gloadLoading").phide();
            }

            // 去掉暂无数据
            $("#operating_record_gird").pcount(1);
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "获取失败",
                state: "failure"
            });
            console.log("请求仪表配置数据错误");
        },
        complete: function () {
            // 如果没有数据显示暂无数据
            if (managementModel.data.meterConfigArr.length == 0) $('#meter_config_table').pcount(0);

            // 检测是否完成
            managementController.asynTool.fire('managementController.getMeterConfigData', 1);
        }
    });
}



/**
 * 获取基础配置数据
 * @param { String } buildingId   //建筑 Id
 */
managementController.getBasicsConfigData = function (buildingId) {
    pajax.post({
        url: 'FNCConfigBaseQueryService',
        data: {
            buildingId: buildingId
        },
        success: function (dataJson) {

            // 添加配置类型（ 0 仪表配置，1 基础配置 ）
            var configType = 1;

            // 将后台返回的基础配置数据转换为基础配置表格数据存入数据模型
            dataModel.configDataToGirdArr(dataJson, configType, buildingId);

            // 将后台返回的表格进行排序
            dataModel.sortGirdArr(dataJson, 'orderBy');

            // 将后台返回的基础配置数据存入数据模型
            managementModel.data.basicsConfigArr = dataJson;

            // 去掉暂无数据
            $("#operating_record_gird").pcount(1);
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "获取失败",
                state: "failure"
            });
            console.log("请求基础配置数据错误");
        },
        complete: function () {

            // 如果没有数据显示暂无数据
            if (managementModel.data.basicsConfigArr.length == 0) $('#basics_config_table').pcount(0);

            // 检测是否完成
            managementController.asynTool.fire('managementController.getBasicsConfigData', 3);

            // 隐藏加载，定时器用于异步排队，保证在数据渲染后执行
            setTimeout(function () {
                $("#gloadLoading").phide();
            }, 0);
        }
    });
}



/**
 * 获取建筑列表数据
 */
managementController.getBulidingsData = function () {
    pajax.post({
        url: 'FNTCommonBuildingListService',
        success: function (dataJson) {

            // 将返回的建筑列表数据存入数据模型
            managementModel.data.bulidingsArr = dataJson;

            // 深复制数据处理后用于操作记录下拉框
            managementModel.data.recordBuildingsArr = JSON.parse(JSON.stringify(dataJson));

            // 加入第一项为全部
            managementModel.data.recordBuildingsArr.unshift({
                id: null,
                name: "全部"
            });

            // 下拉框默认选中第一项
            setTimeout(function () {
                $('#select_box').psel(0, true);
                $('#choose_building').psel(0, false);
            }, 0);

            // 检测若数据为空，则取消加载
            if (!dataJson.length) {
                $("#gloadLoading").phide();
            }
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "获取失败",
                state: "failure"
            });
            console.log("请求建筑列表数据错误");
        },
        complete: function () {
            // 检测是否完成
            managementController.asynTool.fire('managementController.getBulidingsData', 2);
        }
    });
}

/**
 * 获取能耗类型
 */
managementController.getEnergyTypeData = function () {
    pajax.post({
        url: 'FNTCommonEnergyTypeListService',
        success: function (dataJson) {

            // 将能耗类型数据赋值到数据模型
            managementModel.data.energyTypeArr = dataJson;

            // 加入第一项为全部
            managementModel.data.energyTypeArr.unshift({
                id: null,
                name: "全部"
            });

            // 默认选中第一项
            setTimeout(function () {
                $('#energy_type').psel(0, false);
            }, 0);
        },
        error: function () {
            console.log('获取能耗类型失败');
        },
        complete: function () {

        }
    });
}
/**
 * 仪表类型
 */
managementController.getsetTypeData = function () {
    setTimeout(function () {
        $('#set_type').psel(0);
    }, 0);
}



/**
 * 获取操作记录列表数据和下载列表文件
 * @param { Object } recordParams        查询或下载操作记录列表的参数集合
 */
managementController.getRecordrecordGridData = function (recordParams) {

    // 显示加载
    $('#partLoading').pshow();

    var model = managementModel.instance();
    // 获取当前的操作记录类型
    var recordType = recordParams.recordType;
    // 获取操作记录名称
    var notice = model.recordTypeArr[recordType].recordName;
    var url = '';

    // 保正设置的每页数据个数在没点确定的情况下恢复输入前
    model.m_pageSizeText = model.m_pageSize;

    // 根据类型获取对应接口
    if (recordType == 0) {
        url = 'FNCRecordPostPayService';
    } else if (recordType == 1) {
        url = 'FNCRecordPrePayService';
    } else if (recordType == 2) {
        url = 'FNCRecordPostClearingPayService';
    } else if (recordType == 3 || recordType == 4) {
        url = 'FNCRecordTenantOperateService';
    } else if (recordType == 5) {
        url = 'FNCRecordPriceChangeService';
    } else if (recordType == 6) {
        url = 'FNCRecordFaultService';
    } else if (recordType == 7) {
        url = 'FNCRecordMeterOperateService';
    } else if (recordType == 8) {
        url = 'FNCRecordReturnService';
    } else if (recordType == 9) {
        url = 'FNCRecordMessageService';
    } else if (recordType == 10) {
        url = 'FNCRecordMeterSetService';
    }

    // console.log(recordParams)
    if (recordParams.isDownload == 0) {
        // 查询的请求
        pajax.post({
            url: url,
            data: recordParams,
            success: function (dataJson) {
                if (dataJson instanceof Array && dataJson.length > 0) {
                    // 获取记录总条数
                    var count = dataJson[0].count;


                    // 默认选中第一项

                    // 获取总页数
                    var pageCount = Math.ceil(count / model.m_pageSize);

                    // 设置总页数
                    $('#page_controller_select').pcount(pageCount);

                    // 获取数据列表
                    var dataList = dataJson[0].dataList || [];

                    // 判断存储列表数据的数组长度是否大于0，大于零按照操作类型处理数据
                    if (dataList.length > 0) {
                        switch (recordType) {
                            case 6:
                                // 处理故障记录数据
                                for (var i = 0; i < dataList.length; i++) {
                                    var dataListItem = dataList[i];
                                    dataListItem.meterId = dataListItem.code;
                                    dataListItem.faultPlace = dataListItem.name;
                                    // 故障类型目前只有一个
                                    switch (dataListItem.type) {
                                        case 0:
                                            dataListItem.faultName = '通讯异常';
                                            break;
                                    }
                                }
                                break;

                            case 7:
                                // 处理仪表设置记录
                                for (var i = 0; i < dataList.length; i++) {
                                    var operateType = dataList[i];
                                    // 将仪表类型转换成类型名称
                                    switch (operateType.operateType) {
                                        case 0:
                                            operateType.operateTypeName = '剩余清零';
                                            break;
                                        case 1:
                                            operateType.operateTypeName = '保电';
                                            break;
                                        case 2:
                                            operateType.operateTypeName = '分合闸';
                                            break;
                                        case 3:
                                            operateType.operateTypeName = '透支';
                                            break;
                                        case 4:
                                            operateType.operateTypeName = '更新价格';
                                            break;
                                        case 5:
                                            operateType.operateTypeName = '充值';
                                            break;
                                        case 6:
                                            operateType.operateTypeName = '退费';
                                            break;
                                    }

                                }
                                break;
                            case 10:
                                // 仪表设置记录
                                for (var i = 0; i < dataList.length; i++) {
                                    var dataListItem = dataList[i];
                                    // 将仪表类型转换成类型名称
                                    switch (dataListItem.setType) {
                                        case 0:
                                            dataListItem.setTypeName = '剩余清零';
                                            break;
                                        case 1:
                                            dataListItem.setTypeName = '保电';
                                            break;
                                        case 2:
                                            dataListItem.setTypeName = '分合闸';
                                            break;
                                        case 3:
                                            dataListItem.setTypeName = '透支';
                                            break;
                                        case 4:
                                            dataListItem.setTypeName = '更新价格';
                                            break;

                                    }
                                }
                                break;
                        }
                    }

                    // 给充值记录表头单位变量赋值
                    model.recordGridTittleObj.amountUnit = dataJson[0].amountUnit;

                    // 给结算记录表头单位变量赋值
                    model.recordGridTittleObj.currentSumEnergyUnit = dataJson[0].currentSumEnergyUnit;

                    // 给操作记录数据添加排序属性
                    dataModel.addTimeFlagForGirdArr(dataList);

                    // 按照排序属性进行排序
                    dataModel.sortGirdArr(dataList, 'timeFlag', true);

                    // 处理金额和能耗的小数保留规则
                    dataModel.toFixedEvent(dataList);

                    // 将数据赋值到表格数据模型
                    model.recordGridArr = dataList;
                    // 将列显示标识赋值到数据模型
                    model.colspanShowFlagObj = model.colspanShowFlags;
                    // 将表格表头数据与数据模型进行合并
                    dataModel.extendObjs(model.recordGridTittleObj, [model.recordGridTittles]);

                    // 控制表格根据数据数量自适应
                    staticEvent.girdHeight(dataList.length);

                    //// 获取操作记录成功通知
                    //if(!managementModel.data.m_isFromTab && !managementModel.data.m_isFromPage && !managementModel.data.m_isFromSetPage) {
                    //    $("#message").pshow({ text: "获取" + notice + "成功", state: "success" });
                    //}
                    // console.log(count);
                    // console.log(dataJson);
                    // console.log(managementModel.data.recordGridTittleObj);
                } else {
                    // 获取操作记录失败通知
                    $("#message").pshow({
                        text: "获取" + notice + "失败",
                        state: "failure"
                    });
                    // console.log('获取' + notice + '失败');
                }
            },
            error: function () {
                // 获取操作记录失败通知
                $("#message").pshow({
                    text: "获取" + notice + "失败",
                    state: "failure"
                });
                // console.log('获取' + notice + '失败');
            },
            complete: function () {
                // 隐藏加载
                $('#partLoading').phide();
                $('#page_text').precover();
                $('.page_set_btn').pdisable(false);

                // // 将是否从 tab 页发送请求改为 false
                // managementModel.data.m_isFromTab = false;

                // // 将是否为点击页码切换按钮发送请求改为 false
                // managementModel.data.m_isFromPage = false;

                // // 将是否点击确定设置每页条数发送请求 true
                // managementModel.data.m_isFromSetPage = false;
            }
        });
    } else {
        // 下载的请求
        managementController.downloadFile(url, recordParams);

        // 隐藏加载
        $('#partLoading').phide();
    }
}



/**
 * 下载 Excel 文件请求
 * @param { Object } fName           下载的接口名
 * @param { Object } downConfig      下载所需的参数集合
 */
managementController.downloadFile = function (fName, downConfig) {
    // 调用下载文件方法
    pajax.downloadByParam(fName, downConfig);
}



/**
 * 后台配置--上传文件之前获取配置的 resourceId
 * @param { Object } data           取 resourceId 所需的参数集合
 * @param { Object } uploadParams   点击当前行的所有数据
 */
managementController.uploadFile = function (data, uploadParams) {
    var data = data || {};

    // 获取当前时间
    var uploadTime = new Date();

    // 上传所需参数集合
    var param = {
        type: uploadParams.configType, // 配置类型
        buildingId: uploadParams.buildingId, // 建筑 Id
        logicCode: uploadParams.logicCode, // 后台配置编码
        attachments: {
            file: data.file, // 上传文件流
            subdirectory: data.subdirectory, // 文件上传路径
            toPro: "resourceId", // 固定 resourceId
            multiFile: false,
            fileName: data.fileName, // 文件名
            fileSuffix: data.fileSuffix, // 文件后缀名
            fileType: 2
        }
    }
    pajax.updateBeforeWithFile({
        url: 'FNCConfigTemplateUploadService',
        data: param,
        success: function (dataJson) {
            // console.log(dataJson);
            var state,
                text;
            // 确定提示框的颜色和提示文本
            if (dataJson instanceof Array && dataJson.length > 0) {
                if (dataJson[0].result == 0) {
                    state = 'success';
                    text = '上传成功';
                } else {
                    state = 'failure';
                    text = dataJson[0].message;
                }
            } else {
                state = 'failure';
                text = '上传失败';
            }

            // 调用提示框
            $("#message").pshow({
                text: text,
                state: state
            });

            // 判断上传类型，并更新上传数据
            if (uploadParams.configType) {
                managementController.getBasicsConfigData(uploadParams.buildingId);
            } else {
                managementController.getMeterConfigData();
            }
        },
        error: function (err) {
            $("#message").pshow({
                text: "上传失败",
                state: "failure"
            });
        },
        complete: function () {
            // 检测是否完成
            $("#gloadLoading").phide();
            uploadParams.flag = false;
            $("#upload_input_excel").val(""); //清空一下；
            $("#upload_input_excel").off();
        }
    });
}



/**
 * 后台配置--删除配置文件请求
 * @param { Object } model    被删除行的数据集合
 */
managementController.deleteConfigFile = function (model) {
    pajax.post({
        url: 'FNCConfigDeleteService',
        data: {
            type: model.configType,
            buildingId: model.buildingId,
            logicCode: model.logicCode
        },
        success: function (dataJson) {
            // 设置删除已上传文件后，重置上传状态、文件名和上传日期
            model.fileName = '--';
            model.uploadTime = '';
            model.isExsit = 0;

            // 删除成功提示
            $("#message").pshow({
                text: "删除成功",
                state: "success"
            });
        },
        error: function () {
            // 删除失败提示
            $("#message").pshow({
                text: "删除失败",
                state: "failure"
            });
        },
        complete: function () {
            // 隐藏加载
            $("#gloadLoading").phide();
        }
    });
}

managementController.getProjectListData = function () { //获取项目列表数据
    pajax.post({
        url: 'ACProjectListService',
        success: function (result) {
            var data = result[0].dataList || [];
            managementModel.data.projectGridArr = data;
            if (data.length >= 1) {
                $("#addProject").pdisable(true);
                $(".per-grid-normal_main").css("border-bottom", "none");
            } else {
                $("#projectManagement_gird").phide();
                $(".per-grid-nodata").pshow();
            }
            $("#gloadLoading").phide();
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "获取失败",
                state: "failure"
            });
        },
        complete: function () {

        }
    });
};
managementController.getBuildListData = function () { //获取建筑列表数据
    pajax.post({
        url: 'ACBuildingListService',
        success: function (result) {
            var data = result[0].dataList || [];
            managementModel.data.buildGridArr = data;
            managementModel.data.bulidingsArr = data;
            staticEvent.girdHeightBack("#buildManagement", "#buildManagement_gird", data.length);
            // 检测若数据为空，则取消加载
            if (!result.length) {
                $("#gloadLoading").phide();
            }
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "获取失败",
                state: "failure"
            });
            console.log("请求建筑列表数据错误");
        },
        complete: function () {

        }
    });
};
managementController.getUserListData = function () { //获取用户列表数据
    pajax.post({
        url: 'ACUserListService',
        success: function (result) {
            var data = result[0].dataList || [];
            staticEvent.girdHeightBack("#userManagement", "#userManagement_gird", data.length);
            managementModel.data.userGridArr = data;

            // 检测若数据为空，则取消加载
            if (!result.length) {
                $("#gloadLoading").phide();
            }
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "获取失败",
                state: "failure"
            });
            console.log("请求用户列表数据错误");
        },
        complete: function () {

        }
    });
};
managementController.getOne = function (url, dataParameter) {
    pajax.post({
        url: url,
        data: dataParameter,
        success: function (result) {
            var data = ((result || [])[0]) || {};
            var position = "";
            if (data.roleList && data.roleList.length > 0) {
                for (var i = 0; i < data.roleList.length; i++) {
                    var curItem = data.roleList[i];
                    position += curItem.name + ",";
                }
                position = position.substring(0, position.length - 1);
            }
            data.position = position;
            managementModel.data.itemInfo = data;
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "获取失败",
                state: "failure"
            });
            console.log("请求项目数据错误");
        },
        complete: function () {

        }
    });
};
managementController.addSave = function (url, dataParameter, name) { //添加保存
    pajax.post({
        url: url,
        data: dataParameter,
        success: function (result) {
            $("#message").pshow({
                text: "添加成功！",
                state: "success"
            });
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "添加失败",
                state: "failure"
            });
            console.log("添加数据错误");
        },
        complete: function () { //添加成功后获取最新数据
            switch (name) {
                case '项目管理':
                    // window.parent.location.reload();
                    managementController.getProjectListData();
                    break;
                case '建筑配置':
                    managementController.getBuildListData();
                    break;
                case "用户管理":
                    managementController.getUserListData();
                    break;
            }
        }
    });
};
managementController.editSave = function (url, dataParameter, name) { //编辑保存
    pajax.post({
        url: url,
        data: dataParameter,
        success: function () {
            $("#message").pshow({
                text: "编辑成功！",
                state: "success"
            });
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "编辑失败",
                state: "failure"
            });
            console.log("编辑数据错误");
        },
        complete: function () {
            switch (name) {
                case '项目管理':
                    // window.parent.location.reload();
                    managementController.getProjectListData();
                    break;
                case '建筑配置':
                    managementController.getBuildListData();
                    break;
                case "用户管理":
                    managementController.getUserListData();
            }
        }
    });
};
managementController.delete = function (url, id, name) {
    pajax.post({
        url: url,
        data: {
            id: id
        },
        success: function () {
            $("#message").pshow({
                text: "删除成功！",
                state: "success"
            });
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "删除失败",
                state: "failure"
            });
            console.log("删除数据错误");
        },
        complete: function () {
            name == "建筑配置" ? managementController.getBuildListData() : managementController.getUserListData();
        }
    });
};

managementController.queryPosition = function (productId, itemInfoCopy) { //查询职位
    pajax.post({
        url: "ACSystemRoleListService",
        data: {
            productId: productId
        },
        success: function (result) {
            var data = result[0] || [];
            if (itemInfoCopy) {
                if (itemInfoCopy.roleList && itemInfoCopy.roleList.length > 0) {
                    for (var i = 0; i < data.length; i++) {
                        for (var j = 0; j < itemInfoCopy.roleList.length; j++) {
                            if (data[i].id == itemInfoCopy.roleList[j].id) {
                                data[i].checkInput = "checked";
                            }
                        }
                    }
                }
            }
            managementModel.data.roleList = data;
        },
        error: function () {
            console.log("查询职位数据错误");
        },
        complete: function () {}
    });
};
managementController.resetPassword = function (id) {
    pajax.post({
        url: "ACPasswordResetService",
        data: {
            id: id
        },
        success: function () {

            $("#message").pshow({
                text: "重置成功",
                state: "success"
            });
            $("#passwordResetConfirm").phide();
            $("#userFloatWindow").phide();
        },
        error: function () {
            $("#message").pshow({
                text: "重置失败",
                state: "failure"
            });
            $("#passwordResetConfirm").phide();
            $("#userFloatWindow").phide();
            console.log("重置密码错误");
        },
        complete: function () {}
    });
};



managementController.gangweixuanze = function (permdata) { //新的3.6新建用户

    pajax.post({
        url: "ACUserAuthorizationQueryService",
        data: permdata,
        success: function (result) {
            var data = result[0] || [];
            managementModel.data.functionList = data.functionList;
            managementModel.data.permissionList = data.permissionList;
        },
        error: function () {
            console.log("数据错误");
        },
        complete: function () {

        }
    });
};