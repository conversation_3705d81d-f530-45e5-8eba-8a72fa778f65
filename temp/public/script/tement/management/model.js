﻿function managementModel() {};
managementModel.data = {
    //属性以“m_”开头，比如 m_name:"tom"（防止和 data.data 中命名冲突）
    m_recordEnergyTypeFlag: false, // 能耗类型下拉框显示标识（true 显示，false 不显示）
    m_setTypeFlag: false, // 能耗类型下拉框显示标识（true 显示，false 不显示）
    m_buildingFlag: true, // 建筑列表下拉框显示标识（true 显示，false 不显示）
    m_downloadBtnFlag: true, // 下载按钮显示标识（true 显示，false 不显示）
    m_downloadBtnText: '缴费记录', // 下载按钮文本
    // m_isFromTab: true,                              // 是否从 tab 切换时发送请求
    // m_isFromPage: true,                             // 是否点击页码切换发送请求
    // m_isFromSetPage: true,                          // 是否点击确定设置每页条数发送请求
    m_changeRecordListFlag: 'changeRecordType', // 改变列表出发重新请求的操作类型
    m_recordType: 0, // 操作记录类型
    m_setType: 0,
    m_energyTypeId: null, // 能耗类型 Id
    m_buildingId: null, // 建筑 Id
    m_name: null,
    m_type: null, // 激活或退租（1 激活，2 退租）
    m_timeFrom: '', // 起始时间
    m_timeTo: '', // 结束时间
    m_isDownload: 0, // 是否下载标识（0 查询，1下载）
    m_pageIndex: 0, // 当前请求页数
    m_pageSizeText: 100, // 存储表格下设置数据条数文本框的值
    m_pageSize: 100, // 请求数据条数参数
    m_checkName: null, // 模糊查询关键字
    m_batchArr: '',
    m_comboboxArr: '', //
};

/**
 * 创建 Vue 实例方法
 */
managementModel.createModel = function () {
    managementModel.instance();
}

// 单例模式，每次创建返回同一个 Vue 实例
managementModel.instance = function () {
    if (!managementModel._instance) {
        // 将 data.js 中的数据模型合并到 Vue 实例的 data 中（方便协同开发）
        dataModel.extendObjs(managementModel.data, [dataModel.data]);

        managementModel._instance = new Vue({
            el: '#tab_page',
            // Vue 数据模型
            data: managementModel.data,
            // Vue 绑定的事件方法
            methods: {
                /**
                 * 后台配置--下载配置文件
                 * @param { Object } $event       Vue 事件对象
                 * @param { Object } model        点击按钮当前行数据模型
                 */
                downloadFileEvent: function (model, $event) {
                    var fName = '';
                    var downConfig = {
                        type: model.configType,
                        buildingId: model.buildingId || null,
                        logicCode: model.logicCode,
                        hintText: '配置'
                    };

                    // console.log(model);
                    setTimeout(function () {
                        // data-flag：下载的文件标识，0 空文件模板，1 当前行已上传的文件
                        var downloadFlag = parseInt($($event.target).attr('data-flag'));

                        // 根据下载标识判断处理接口和参数
                        if (downloadFlag == 0) {
                            downConfig.buildingId = null;
                            downConfig.type = null;
                            downConfig.hintText = '模板';
                            fName = 'FNCConfigTemplateDownloadService';
                        } else {
                            fName = 'FNCConfigDownloadService';
                        }

                        // 调用下载文件的方法
                        managementController.downloadFile(fName, downConfig);
                    }, 0);
                },


                /**
                 * 后台配置--上传配置文件
                 * @param { Object } model        点击按钮当前行数据模型
                 */
                uploadFileEvent: function (model) {
                    model.flag = true;
                    // 主动触发上传公用的 input 标签, 并初始化内容
                    $('#upload_input_excel').on('change', function () {
                        if (!model.flag) return;
                        // 显示加载
                        $("#gloadLoading").pshow();
                        // 存储上传文件所需参数的数组
                        var fileObj = {};
                        // 获取上传的文件集合
                        var uploadFiles = $(this).get(0).files;
                        var _file = uploadFiles[0];
                        fileObj = {
                            file: _file,
                            fileName: _file.name.split('.')[0],
                            fileSuffix: _file.name.split('.')[1],
                            subdirectory: '/finein/tenant_manage/back_config/config'
                        };
                        // 调用上传文件的方法
                        managementController.uploadFile(fileObj, model);
                        setTimeout(function () {
                            model.flag = true;
                        }, 0);
                    }).click();
                },


                /**
                 * 后台配置--删除文件方法
                 * @param { Object } $event       Vue 事件对象
                 * @param { Object } model        点击按钮当前行数据模型
                 */
                deleteConfigFileEvent: function (model) {
                    // 显示加载
                    $("#gloadLoading").pshow();

                    // 调用删除配置文件请求
                    managementController.deleteConfigFile(model);
                },


                /**
                 * 操作记录--设置每页请求数据条数
                 */
                setPageSizeEvent: function () {
                    // 操作名称
                    var changeRecordListFlag = 'changePageSize';

                    // 将操作名称更新到数据模型
                    this.m_changeRecordListFlag = changeRecordListFlag;

                    // 初始化参数状态
                    dataModel.initRecordParams(changeRecordListFlag);

                    // 将条数更新到每页数据条数的参数上
                    this.m_pageSize = this.m_pageSizeText;
                    dataModel.initRecordParams(changeRecordListFlag);
                    // 更新列表数据
                    managementController.getRecordrecordGridData(this.params);
                }
            },
            computed: {
                // 查询下载参数
                params: function () {
                    return {
                        recordType: this.m_recordType,
                        type: this.m_type,
                        energyTypeId: this.m_energyTypeId,
                        buildingId: this.m_buildingId,
                        timeFrom: this.m_timeFrom,
                        timeTo: this.m_timeTo,
                        isDownload: this.m_isDownload,
                        pageIndex: this.m_pageIndex,
                        pageSize: this.m_pageSize,
                        checkName: this.m_checkName,
                        setType: this.m_setType,
                        name: this.m_name
                    };
                },


                // 列显示标识
                colspanShowFlags: function () {
                    // 获取操作记录类型
                    var recordType = this.m_recordType;


                    return {
                        colspanFlag_1: recordType != 6 && recordType != 10, // 操作时间
                        colspanFlag_2: recordType == 7, // 操作类型
                        colspanFlag_3: recordType == 7 || recordType == 10, // 操作值
                        colspanFlag_4: recordType == 6, // 故障时间
                        colspanFlag_5: recordType == 0, // 账单
                        colspanFlag_6: recordType == 0 || recordType == 1 || recordType == 2 || recordType == 8, // 充值/帐单号
                        colspanFlag_7: recordType != 6, // 租户名称/名称（编号）
                        colspanFlag_8: recordType == 1 || recordType == 6 || recordType == 7 || recordType == 8 || recordType == 10, // 仪表 Id
                        colspanFlag_9: recordType == 7, // 协议编码
                        colspanFlag_10: recordType == 7, // 请求
                        colspanFlag_11: recordType == 7, // 呼应
                        colspanFlag_12: recordType == 0 || recordType == 1, // 缴费/充值金额（元）
                        colspanFlag_13: recordType == 1, // 充值量（kWh）
                        colspanFlag_14: recordType == 3 || recordType == 4, // 激活/退租时间
                        colspanFlag_15: recordType == 2, // 上次结算日期
                        colspanFlag_16: recordType == 2, // 本次结算日期
                        colspanFlag_17: recordType == 2, // 本次结算能耗（kWh）
                        colspanFlag_18: recordType == 2, // 本期结算金额（元）
                        colspanFlag_19: recordType == 5, // 修改前价格方案
                        colspanFlag_20: recordType == 5, // 修改后价格方案
                        colspanFlag_21: recordType != 6 && recordType != 9, // 操作人
                        colspanFlag_22: recordType != 1 && recordType != 6 && recordType != 8, // 租户编号
                        colspanFlag_23: recordType == 3 || recordType == 4 || recordType == 5, // 所在建筑
                        colspanFlag_24: recordType != 1 && recordType != 6 && recordType != 7 && recordType != 8 && recordType != 9 && recordType != 10, // 房间编号
                        colspanFlag_25: recordType == 6, // 安装位置
                        colspanFlag_26: recordType == 6, // 故障类型
                        colspanFlag_27: recordType == 8, //退费金额
                        colspanFlag_28: recordType == 8, //退费量
                        // 20180608wp++
                        colspanFlag_29: recordType == 9, //短信内容
                        colspanFlag_30: recordType == 9, //收信人
                        colspanFlag_31: recordType == 9, //手机号
                        colspanFlag_32: recordType == 9, //发送状态
                        //3.6版本
                        colspanFlag_33: recordType == 10, //房间编号
                        colspanFlag_34: recordType == 10, //设置类型
                        colspanFlag_35: recordType == 10, //能源类型
                        colspanFlag_36: recordType == 10,
                    }
                },
                // 表头变量
                recordGridTittles: function () {
                    // 获取操作记录类型
                    var recordType = this.m_recordType;
                    // 存储表头变量
                    var titlesObj = {};

                    // 充值/账单号
                    titlesObj.billNum = recordType == 1 ? '充值' : '账';
                    // 租户名称/租户名称（编号）
                    titlesObj.tenementName = recordType == 1 ? '（编号）' : '';
                    // 缴费/充值金额（元）
                    titlesObj.amountMoney = recordType == 0 ? '缴费' : '充值';
                    // 激活/退租时间
                    titlesObj.tenementTime = recordType == 3 ? '激活' : '退租';

                    return titlesObj;
                },

            },
            // 钩子函数：Vue 实例创建后执行
            created: function () {
                // 显示加载
                $("#gloadLoading").pshow();

                // 监测页面初始化请求
                managementController.asynTool.all('managementController.getUserPermission', 'managementController.getMeterConfigData', 'managementController.getBulidingsData', 'managementController.getBasicsConfigData',function () {
                    //隐藏加载，定时器用于异步排队，保证在数据渲染后执行
                    setTimeout(function () {
                        $("#gloadLoading").phide();
                    }, 0);
                });

                //请求页面初始化需要加载的数据
                managementController.getUserPermission(); //获取权限
                managementController.getMeterConfigData(); // 获取仪表配置数据
                managementController.getBulidingsData(); // 获取建筑列表数据
                managementController.getEnergyTypeData(); // 获取能耗类型数据
            }
        });
    }
    return managementModel._instance; // 返回 Vue 实例
}