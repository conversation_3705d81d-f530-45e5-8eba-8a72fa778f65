function staticEvent() {}

$(function () {
    // 初始化 Vue 对象
    managementModel.createModel();

    // tab 页默认选中第一页
    $('#tab_page').psel(0, false);
    $('.batchPage').pdisable(true);
    managementModel.instance().name = _tabTitleArr_[0].name;
    // 操作记录类型默认选中第一项
    $('#record_type').psel(0, false);

    // 设置时间控件初始时间为本月
    dataModel.initTimeController();

    managementController.getProjectListData(); //获取项目列表

    // 浏览器窗口变化时，重新自适应表格高度
    $(window).on('resize', function () {
        var length = managementModel.data.recordGridArr.length;
        staticEvent.girdHeight(length);
    });

    // 使时间控件在展开状态时点击其他区域可以收起
    $(document).on('click', function (e) {
        if (e.target == document.body) return;

        // 收起时间控件
        $('#time_controller').find('.per-calendar-con').hide();

    });
    //收起下拉框
    $('#operating_record_gird').on('mousedown', function (e) {
        e.stopPropagation();
        $('#record_type ._combobox_bottom ').hide();
        $('#energy_type ._combobox_bottom ').hide();
        $('#choose_building ._combobox_bottom ').hide();
    });
});

// 控件库事件方法
/**
 * tab 页切换事件
 * @param { Object } pEvent       控件库事件对象
 */
staticEvent.tabPageEvent = function (item, pEvent) {
    $("#buildFloatWindow").phide();
    $("#userFloatWindow").phide();
    $("#projectFloatWindow").phide();
    var index = pEvent.pEventAttr.index;
    var name = item.name;
    var model = managementModel.instance();
    model.name = name; //记录当前点击的TAB名称
    model.curPage = "listPage"; //页卡切换时显示列表页面
    model.itemInfo = {};
    model.itemToUpdate = {};

    // 获取时间毫秒数集合
    var timeObj = $('#time_controller').psel();

    // 将时间范围存入数据模型
    managementModel.data.m_timeFrom = dataModel.forMatTime(timeObj.startTime);
    managementModel.data.m_timeTo = dataModel.forMatTime(timeObj.realEndTime);

    $('.tab_select > div').eq(index).show().siblings().hide();

    switch (name) {
        case '后台配置':
            // // 当 tab 切换回去的时候将是否从，时下一次切换 tab 页发请求和初始化的标识相同
            // managementModel.data.m_isFromTab = true;

            // 将操作名称恢复默认值
            managementModel.data.m_changeRecordListFlag = 'changeRecordType';
            break;
        case '操作记录':
            // 初始化参数状态（页数、下载标识）
            var changeRecordListFlag = managementModel.data.m_changeRecordListFlag;
            dataModel.initRecordParams(changeRecordListFlag);

            // 发送查询请求，初始化操作记录列表
            // console.log(model.params);
            managementController.getRecordrecordGridData(model.params);
            break;
        case '项目管理':
            managementController.getProjectListData(); //获取项目列表
            break;
        case '建筑配置':
            managementController.getBuildListData(); //获取建筑列表
            break;
        case '用户管理':
            managementController.getUserListData(); //获取用户列表
            break;
    }
}


/**
 * 选择后台配置建筑下拉框选项
 * @param { Object } dataSource       下拉框关联的数据源
 * @param { Object } pEvent           控件库事件对象
 */
staticEvent.selectBuildingItemEvent = function (dataSource, pEvent) {
    // 显示加载
    $("#gloadLoading").pshow();

    // 获取点击建筑的建筑 Id
    var index = pEvent.pEventAttr.index;
    var buildingId = managementModel.data.bulidingsArr[index].id;

    // 请求建筑对应的基础配置信息
    managementController.getBasicsConfigData(buildingId);
}


/**
 * 选择操作记录建筑下拉框选项
 * @param { Object } dataSource       下拉框关联的数据源
 * @param { Object } pEvent           控件库事件对象
 */
staticEvent.selectRecordBuildingItemEvent = function (dataSource, pEvent) {
    var model = managementModel.instance();
    // 操作名称
    var changeRecordListFlag = 'changeBuilding';

    // 将操作名称更新到数据模型
    managementModel.data.m_changeRecordListFlag = changeRecordListFlag;

    // 初始化参数状态（页数、下载标识）
    dataModel.initRecordParams(changeRecordListFlag, dataSource);

    // 将建筑 Id 存入数据模型
    managementModel.data.m_buildingId = dataSource.id;

    // 发送查询请求
    managementController.getRecordrecordGridData(model.params);
}


/**
 * 选择操作记录能耗类型下拉框选项
 * @param { Object } dataSource       下拉框关联的数据源
 * @param { Object } pEvent           控件库事件对象
 */
staticEvent.selectRecordEnergyItemEvent = function (dataSource, pEvent) {
    var model = managementModel.instance();

    // 操作名称
    var changeRecordListFlag = 'changeEnergyType';

    // 将操作名称更新到数据模型
    managementModel.data.m_changeRecordListFlag = changeRecordListFlag;

    // 初始化参数状态（页数、下载标识）
    dataModel.initRecordParams(changeRecordListFlag, dataSource);

    // 将能耗类型 Id 存入数据模型
    managementModel.data.m_energyTypeId = dataSource.id;

    // 发送查询请求
    managementController.getRecordrecordGridData(model.params);
}

//仪表设置类型
staticEvent.selectRecordsetItemEvent = function (dataSource, pEvent) {
    var model = managementModel.instance();
    // 操作名称
    var changeRecordListFlag = 'changeEnergyType';

    // 将操作名称更新到数据模型
    managementModel.data.m_changeRecordListFlag = changeRecordListFlag;

    // 初始化参数状态（页数、下载标识）
    dataModel.initRecordParams(changeRecordListFlag, dataSource);

    // 将能耗类型 Id 存入数据模型
    managementModel.data.m_setType = dataSource.id;

    // 发送查询请求
    managementController.getRecordrecordGridData(model.params);
}


/**
 * 选择操作记录类型下拉框选项
 * @param { Object } dataSource       下拉框关联的数据源
 * @param { Object } pEvent           控件库事件对象
 */
staticEvent.selectRecordTypeItemEvent = function (dataSource, pEvent) {
    var model = managementModel.instance();

    // 操作名称
    var changeRecordListFlag = 'changeRecordType';

    // 将操作名称更新到数据模型
    managementModel.data.m_changeRecordListFlag = changeRecordListFlag;

    // 初始化参数状态（页数、下载标识）
    dataModel.initRecordParams(changeRecordListFlag, dataSource);

    // 将当前记录类型更新到数据模型
    managementModel.data.m_recordType = dataSource.recordType;

    // 将下载按钮文本更新到数据模型
    managementModel.data.m_downloadBtnText = dataSource.recordName;

    // 表头单位变量赋初始值（防止报错）
    managementModel.data.recordGridTittleObj.amountUnit = '';
    managementModel.data.recordGridTittleObj.currentSumEnergyUnit = '';

    // 发送查询请求
    // console.log(model.params);
    managementController.getRecordrecordGridData(model.params);
}


/**
 * 获取时间控件的时间范围
 * @param { Object } pEvent           控件库事件对象
 */
staticEvent.getTimeRangeEvent = function (pEvent) {
    var model = managementModel.instance();

    // 获取时间范围毫秒数集合
    var timeObj = $('#time_controller').psel();
    // console.log(timeObj);
    // console.log(dataModel.forMatTime(timeObj.startTime));
    // console.log(dataModel.forMatTime(timeObj.endTime));

    // 操作名称
    var changeRecordListFlag = 'changeTimeRange';

    // 将操作名称更新到数据模型
    managementModel.data.m_changeRecordListFlag = changeRecordListFlag;

    // 将事件范围格式化并存入数据模型
    managementModel.data.m_timeFrom = dataModel.forMatTime(timeObj.startTime);
    managementModel.data.m_timeTo = dataModel.forMatTime(timeObj.realEndTime);

    // 初始化参数状态（页数、下载标识）
    dataModel.initRecordParams(changeRecordListFlag);
    // console.log("开始时间"+managementModel.data.m_timeFrom)
    // console.log("结束时间"+managementModel.data.m_timeTo)
    // 发送查询请求
    // console.log(model.params);
    managementController.getRecordrecordGridData(model.params);
}


/**
 *  验证和设置页码事件
 */
staticEvent.verifyPageSizeEvent = function () {

    // 获取输入内容是否合法（布尔值）
    var isVerifySuccess = $('#page_text').pverifi();

    // 获取当前输入框内的值
    var pageSize = parseInt($('#page_text').pval());

    // 判断输入内容是否合法
    if (isVerifySuccess) {

        // 合法--对数字进行验证并解除确定按钮的禁用状态
        $('.page_set_btn').pdisable(false);

        // 分不同情况设置输入框为空、上限、下限
        if (!pageSize) {
            pageSize = 100;
        } else if (pageSize < 20) {
            pageSize = 20
        } else if (pageSize > 5000) {
            pageSize = 5000;
        } else {
            pageSize = pageSize;
        }

        // 设置每页条数的值
        $('#page_text').pval(pageSize);
        managementModel.data.m_pageSizeText = pageSize;
    } else {

        // 不合法--禁用确定按钮
        $('.page_set_btn').pdisable(true);
    }
    // console.log(pageSize);
    // console.log(isVerifySuccess);
}


/**
 * 页码切换事件
 * @param { Object } pEvent           控件库事件对象
 */
staticEvent.pageChangeEvent = function (pEvent) {
    var model = managementModel.instance();

    // 获取当前页码数
    var index = pEvent.pEventAttr.index;

    // 操作名称
    var changeRecordListFlag = 'changePageIndex';

    // 将操作名称更新到数据模型
    managementModel.data.m_changeRecordListFlag = changeRecordListFlag;

    // 初始化参数状态
    dataModel.initRecordParams(changeRecordListFlag);

    // 将页码数更新到数据模型（下载标识，点击页码切换按钮发送请求标识）
    managementModel.data.m_pageIndex = index;

    // 发送查询请求
    // console.log(model.params);
    managementController.getRecordrecordGridData(model.params);
}


/**
 * 操作记录--下载操作记录列表
 */
staticEvent.downloadRecordListEvent = function () {
    var model = managementModel.instance();

    // 操作名称
    var changeRecordListFlag = 'downloadList';

    // 将操作名称更新到数据模型
    managementModel.data.m_changeRecordListFlag = changeRecordListFlag;

    // 初始化参数状态（下载标识、时间控件隐藏状态）
    dataModel.initRecordParams(changeRecordListFlag);

    // 发送下载请求
    // console.log(model.params);
    managementController.getRecordrecordGridData(model.params);
}


/**
 * 表格高度自适应方法
 * @param { Number } length        存储表格数据的数组的长度
 */
staticEvent.girdHeight = function (length) {
    // 获取表格最大高度（css 设置）
    var maxHeight = $('#operating_record .operating_record_body').outerHeight() - 70;

    // 获取当前表格数据的总高度
    var nowHeight = length * ($('#operating_record .per-grid-dynamic-wrap li').outerHeight() || 36) + $('#operating_record .per-grid-dynamic_title').outerHeight();
    var girdHeight = 0;

    // 判断表格的长度
    if (length != 0) {
        // 如果表格长度大于最大范围，设置表格高度为最大高度，并且恢复下边框，否则设置当前数据高度，去掉下边框
        if (nowHeight >= maxHeight) {
            girdHeight = maxHeight + 70 - 54;
            $('#operating_record .per-grid-dynamic_main').removeClass('no_border_bottom');

            // 去掉暂无数据
            $("#operating_record_gird").pcount(1);
        } else {
            girdHeight = nowHeight + 75 - 54;
            $('#operating_record .per-grid-dynamic_main').addClass('no_border_bottom');

            // 去掉暂无数据
            $("#operating_record_gird").pcount(1);
        }
        $('#operating_record .operating_record_body .per-grid-dynamic_wrap').css('height', girdHeight);
    } else {
        // 显示暂无数据
        $("#operating_record_gird").pcount(0);
        $('#operating_record .operating_record_body .per-grid-dynamic_wrap').css('height', maxHeight + 70);
    }
};

//-----------add by LiuChang
staticEvent.girdHeightBack = function (container, box, length) {
    var maxHeight = $(container + ' .content_body').outerHeight() - 70;
    var nowHeight = null;
    var girdHeight = 0;
    if (length == 0) {
        nowHeight = 0;
        // 显示暂无数据
        $(".per-grid-normal_main").addClass('no_border_bottom');
        $(".per-grid-nodata").pshow();
    } else {
        nowHeight = length * ($(container + ' .per-grid-dynamic-wrap li').outerHeight() || 38) + 30;
        if (nowHeight >= maxHeight) {
            girdHeight = maxHeight + 70 + 2;
            $(container + ' .per-grid-dynamic_main').removeClass('no_border_bottom');
        } else {
            girdHeight = nowHeight + 70;
            $(container + ' .per-grid-dynamic_main').addClass(".no_border_bottom");
        }
        $(box).css('height', girdHeight);

    }

};

//添加建筑控制页面的显示
staticEvent.add = function () {
    // var model = managementModel.instance();
    // model.curPage = "addPage";
    // model.itemToUpdate = {};
    // $(".addPage").phideTextTip("");
    $("#addBuildID input").val("");
    $("#addBuildName input").val("");
    $("#addBuildAddress input").val("");
    $("#addBuildFloatWindow").pshow({
        title: "添加建筑"
    });
};
//添加项目、
staticEvent.addProject = function () {
    $("#addProjectFloatWindow").pshow({
        title: "添加项目"
    });
};
//添加项目、建筑 验证 
staticEvent.addVerify = function (id) {
    let text = $("#" + id).pval();
    if (/^[\u4E00-\u9FA5A-Za-z0-9_-]{1,50}$/.test(text) == false) {
        $("#" + id).children(".error-tip").css("display", "block").text("请输入正确格式内容！");
        $("#" + id).children("input").addClass("input-error");
        return;
    }
}
//编辑项目、建筑   验证
staticEvent.editVerify = function (id) {
    let text = $("#" + id).val();
    if (/^[\u4E00-\u9FA5A-Za-z0-9_-]{1,50}$/.test(text) == false) {
        $("#" + id).siblings("p").text("请输入正确格式内容！").css("color", "#f97c7c");
        $("#" + id).css("border-color", "#f97c7c");
        return;
    }
}
staticEvent.addSave = function (event) { //新建保存
    if ($('.verificationForphone .per-input-basic > input').val() == '11111111111') {
        $('.verificationForphone .per-input-basic > input').val('1111111111')
        return
    }
    var value = $('.verificationFor').pverifi();
    if (!value) return;
    var model = managementModel.instance();
    var name = model.name;
    var parameters = {};
    switch (name) {
        case '项目管理':
            let addProjectID = $("#addProjectID input").val();
            let addProjectName = $("#addProjectName input").val();
            let addProjectAddress = $("#addProjectAddress input").val();
            if ($("#addProjectID").pverifi() == false) {
                return;
            }
            if ($("#addProjectName").pverifi() == false) {
                return;
            } else {
                if (/^[\u4E00-\u9FA5A-Za-z0-9_-]{1,50}$/.test(addProjectName) == false) {
                    $("#addProjectName .error-tip").css("display", "block").text("请输入正确格式内容！");
                    $('#addProjectName input').addClass("input-error");
                    return;
                }
            }
            if ($("#addProjectAddress").pverifi() == false) {
                return;
            } else {
                if (/^[\u4E00-\u9FA5A-Za-z0-9_-]{1,50}$/.test(addProjectAddress) == false) {
                    $("#addProjectAddress .error-tip").css("display", "block").text("请输入正确格式内容！");
                    $('#addProjectAddress input').addClass("input-error");
                    return;
                }
            }
            parameters = {
                id: addProjectID,
                name: addProjectName,
                address: addProjectAddress
            };
            managementController.addSave("ACProjectAddService", parameters, name);
            model.curPage = "listPage";
            $("#addProjectFloatWindow").phide();
            break;
        case '建筑配置':
            var addBuildID = $("#addBuildID input").val();
            var addBuildName = $("#addBuildName input").val();
            var addBuildAddress = $("#addBuildAddress input").val();
            var buildingsArr = managementModel.data.bulidingsArr;
            var isBuildingRepeat = buildingsArr.some(item => {
                return item["name"].indexOf(addBuildName) != -1;
            })
            if(isBuildingRepeat) {
                $("#addBuildName .error-tip").css("display", "block").text("与已有建筑名称重复！");
                return ;
            }
            if ($("#addBuildID").pverifi() == false) {
                return;
            }
            if ($("#addBuildName").pverifi() == false) {
                return;
            } else {
                if (/^[\u4E00-\u9FA5A-Za-z0-9_-]{1,50}$/.test(addBuildName) == false) {
                    $("#addBuildName .error-tip").css("display", "block").text("请输入正确格式内容！");
                    $('#addBuildName input').addClass("input-error");
                    return;
                }
            }
            if ($("#addBuildAddress").pverifi() == false) {
                return;
            } else {
                if (/^[\u4E00-\u9FA5A-Za-z0-9_-]{1,50}$/.test(addBuildAddress) == false) {
                    $("#addBuildAddress .error-tip").css("display", "block").text("请输入正确格式内容！");
                    $('#addBuildAddress input').addClass("input-error");
                    return;
                }
            }
            parameters = {
                id: addBuildID,
                name: addBuildName,
                address: addBuildAddress
            };
            managementController.addSave("ACBuildingAddService", parameters, name);
            model.curPage = "listPage";
            $("#addBuildFloatWindow").phide();
            model.curPage = "listPage";
            break;
        case "用户管理":
            var functionList = staticEvent.oneChoice2("checks");
            var permissionList = staticEvent.oneChoice4("checks");
            if (functionList.length == 0) {
                $("#message").pshow({
                    text: "请至少选择一个菜单权限",
                    state: "failure"
                });
            } else {
                var parameters = {
                    name: model.itemToUpdate.name,
                    showName: model.itemToUpdate.showName,
                    mobile: model.itemToUpdate.mobile,
                    email: model.itemToUpdate.email,
                    functionList: functionList,
                    permissionList: permissionList
                }
                managementController.addSave("ACUserAddService", parameters, name);
                $("#userFloatWindow").phide();
                model.curPage = "listPage";

            }

    }
};
staticEvent.addUser = function () { //用户添加、用户密码重置弹窗显示
    $("#userFloatWindow").pshow({
        title: "+添加用户"
    });
    var buildFloathight = window.innerHeight - 80 - 70;
    $(".buildFloat").css("height", buildFloathight + 'px');
    var model = managementModel.instance();
    model.framePage = "addPage";
    model.saveBtn = true;
    model.itemToUpdate = {};
    model.roleList = {};
    model.permissionList = {};
    model.functionList = {};
    $(".buildFloat").precover();
    $(".buildFloat").phideTextTip("");
    managementController.queryPosition("Finein"); //根据产品ID获取对应的职位
    permdata = {
        productId: "Finein",
        roleId: '',
        id: ''
    }
    managementController.gangweixuanze(permdata);

};
staticEvent.showDetail = function (obj, event) { //详情展示
    var model = managementModel.instance();
    var buildFloathight = window.innerHeight - 80 - 70;
    $(".buildFloat").css("height", buildFloathight + 'px');
    var name = model.name;
    if (name == "项目管理") {
        $("#projectFloatWindow").pshow({
            title: "编辑项目"
        });
        dataParameter = {
            id: obj.id,
        };
        managementController.getOne("ACProjectDetailService", dataParameter);
    }
    if (name == "建筑配置") {
        $("#buildFloatWindow").pshow({
            title: "建筑详情"
        });
        dataParameter = {
            id: obj.id,
        };
        managementController.getOne("ACBuildingDetailService", dataParameter);
    }
    if (name == "用户管理") {

        $("#userFloatWindow").pshow({
            title: "用户详情"
        });
        model.framePage = "detailPage";
        dataParameter = {
            id: obj.id,
            productId: 'Finein'
        };
        managementController.getOne("ACUserDetailService", dataParameter);
    }

};
staticEvent.edit = function () { //编辑项目、建筑、用户
    var model = managementModel.instance();
    var name = model.name;
    switch (name) {
        case "项目管理":
            break;
        case "建筑配置":
            break;
        case "用户管理": 
            model.framePage = "editPage";
            model.saveBtn = false;
            var permdata = {};
            // model.itemToUpdate = {};
            model.itemToUpdate = model.itemInfo;
            var itemInfoCopy = JSON.parse(JSON.stringify(model.itemInfo)); //备份详情信息
            permdata = {
                productId: "Finein",
                id: itemInfoCopy.id
            }
            managementController.gangweixuanze(permdata);
    }
};

//编辑保存操作
staticEvent.editSave = function (event) {
    var value = $('.verificationFor').pverifi();
    if (!value) return;
    var model = managementModel.instance();
    var name = model.name;
    if (name == "项目管理") {
        let RegExp = /^[\u4E00-\u9FA5A-Za-z0-9_-]{1,50}$/;
        if (RegExp.test(model.itemInfo.name) == false) {
            $("#editProjectName").siblings("p").text("请输入正确格式内容！").css("color", "#f97c7c");
            $("#editProjectName").css("border-color", "#f97c7c");
            return;
        };
        if (RegExp.test(model.itemInfo.address) == false) {
            $("#editProjectAddress").siblings("p").text("请输入正确格式内容！").css("color", "#f97c7c");
            $("#editProjectAddress").css("border-color", "#f97c7c");
            return;
        }
        managementController.editSave("ACProjectUpdateService", {
            id: model.itemInfo.id,
            name: model.itemInfo.name,
            address: model.itemInfo.address
        }, name);
        $("#projectFloatWindow").phide();
        model.curPage = "listPage";
    }
    if (name == "建筑配置") { //建筑配置的详情为右侧弹框显示
        var buildingsArr = managementModel.data.bulidingsArr;
        var isBuildingRepeat = buildingsArr.some(item => {
            return item["name"].indexOf(model.itemInfo.name) != -1;
        })
        if(isBuildingRepeat) {
            $("#editBuildName").siblings("p").text("与已有建筑名称重复！").css("color", "#f97c7c");
            return ;
        }
        let RegExp = /^[\u4E00-\u9FA5A-Za-z0-9_-]{1,50}$/;
        if (RegExp.test(model.itemInfo.name) == false) {
            $("#editBuildName").siblings("p").text("请输入正确格式内容！").css("color", "#f97c7c");
            $("#editBuildName").css("border-color", "#f97c7c");
            return;
        };
        if (RegExp.test(model.itemInfo.address) == false) {
            $("#editBuildAddress").siblings("p").text("请输入正确格式内容！").css("color", "#f97c7c");
            $("#editBuildAddress").css("border-color", "#f97c7c");
            return;
        }
        managementController.editSave("ACBuildingUpdateService", {
            id: model.itemInfo.id,
            name: model.itemInfo.name,
            address: model.itemInfo.address
        }, name);
        $("#buildFloatWindow").phide();
        model.curPage = "listPage";
    }
    if (name == "用户管理") {
        if ($('.verificationForphone .per-input-basic > input').val() == '11111111111') {
            $('.verificationForphone .per-input-basic > input').val('1111111111')
            return
        }
        var value = $('.verificationFor').pverifi();
        if (!value) return;
        var functionList = staticEvent.oneChoice2("checks");
        var permissionList = staticEvent.oneChoice4("checks");
        
        if (functionList.length == 0) {
            $("#message").pshow({
                text: "请至少选择一个菜单权限",
                state: "failure"
            });
        } else {
            var parameters = {
                id: model.itemInfo.id,
                name: model.itemToUpdate.name,
                showName: model.itemToUpdate.showName,
                mobile: model.itemToUpdate.mobile,
                email: model.itemToUpdate.email,
                functionList: functionList,
                permissionList: permissionList
            }
            managementController.editSave("ACUserUpdateService", parameters, name);
            $("#userFloatWindow").phide();
            model.curPage = "listPage";
        }

    }

};
//用户管理 手机号
staticEvent.textphoneFun = function (event) {
    if ($('.verificationForphone .per-input-basic > input').val() == '11111111111') {
        $('.verificationForphone .per-input-basic > input').val('1111111111')
    }
}

// 删除二次弹框

staticEvent.confirmDel = function () { //删除建筑、用户
    var model = managementModel.instance();
    var name = model.name;
    if (name == "建筑配置") {
        managementController.delete("ACBuildingDeleteService", model.itemInfo.id, name);
        $("#confirmWindow").phide();
        $("#buildFloatWindow").phide(); //侧弹框隐藏
    } else {
        managementController.delete("ACUserDeleteService", model.itemInfo.id, name);
        $("#confirmWindow").phide();
        $("#userFloatWindow").phide(); //侧弹框隐藏
    }


};
staticEvent.confirmHide = function () { //取消删除用户、建筑
    $("#confirmWindow").phide();
    $("#buildFloatWindow").phide();
    $("#passwordResetConfirm").phide();
    $("#userFloatWindow").phide(); //侧弹框隐藏
};
staticEvent.passWordResetConfirm = function () { //确认重置密码
    var model = managementModel.instance();
    managementController.resetPassword(model.itemInfo.id);
};
staticEvent.passWordResetCancel = function () { //取消重置密码
    var model = managementModel.instance();
    var name = model.name;
    if (name == "用户管理") {
        $("#passwordResetConfirm").phide();
        model.framePage = "detailPage";
    }


};

staticEvent.deleteBuild = function () { //删除当前建筑
    $("#confirmWindow").pshow({
        title: '您确定要删除建筑吗？',
        subtitle: '被删除的建筑将无法恢复'
    });
};
staticEvent.deleteUser = function () { //删除用户
    $("#confirmWindow").pshow({
        title: "您确定删除该用户吗？",
        subtitle: "删除的用户数据无法恢复"
    });
};
staticEvent.resetPassword = function () { //重置密码
    var model = managementModel.instance();
    //model.framePage = "resetPage";
    $("#passwordResetConfirm").pshow({
        title: "您确定重置该用户密码吗？",
        subtitle: "密码将会被重置为123456"
    });
};

staticEvent.oneChoice = function (parameter) { //input的选择事件
    var inputAryList = $('[name1=' + parameter + ']');
    var hasCheckAry = [];
    for (var k = 0; k < inputAryList.length; k++) {
        if (inputAryList[k].checked)
            hasCheckAry.push(inputAryList[k].value);
    }
    permdata = {
        productId: "Finein",
        roleIds: hasCheckAry
    }
    managementController.gangweixuanze(permdata);
    return hasCheckAry;

};
staticEvent.oneChoice4 = function (parameter) { //input的选择事件
    var permdata = {};
    var inputAryList = $('[name4=' + parameter + ']');
    var hasCheckAry = [];
    for (var k = 0; k < inputAryList.length; k++) {
        if (inputAryList[k].checked)
            hasCheckAry.push(inputAryList[k].value);
    }
    return hasCheckAry;

};
staticEvent.oneChoice2 = function (parameter) { //input的选择事件
    var inputAryList = $('[name2=' + parameter + ']');
    var inputAryList3 = $('[name3=' + parameter + ']');
    var hasCheckAry = [];
    for (var k = 0; k < inputAryList.length; k++) {
        if (inputAryList[k].checked)
            hasCheckAry.push(inputAryList[k].value);
    }
    for (var k = 0; k < inputAryList3.length; k++) {
        if (inputAryList3[k].checked)
            hasCheckAry.push(inputAryList3[k].value);
    }
    return hasCheckAry;

};
//子项点击
staticEvent.checkedForFarter = function (a, b) {
    a.isHave = a.isHave == 0 ? 1 : 0;
    var childrenCheckedCount = 0;
    for (var i = 0; i < b.List.length; i++) {
        if (b.List[i].isHave == 1) {
            childrenCheckedCount++;
        }
    }
    b.isHave = childrenCheckedCount > 0 ? 1 : 0;
}

//父级方法
staticEvent.checkedForchildren = function (a) {
    a.isHave = a.isHave == 0 ? 1 : 0;
    for (var i = 0; i < a.List.length; i++) {
        a.List[i].isHave = a.isHave;
    }
}