function staticEvent() {
}
var aaa=0;
$(function () {
    // logger.init();
    // 初始化 Vue 对象
    tenantMngModel.createModel();
    function ellipsis(e) { //鼠标滑过显示全称
        var target = e.target;
        var containerLength = $(target).width();
        var textLength = target.scrollWidth;
        var paddingL = parseInt($(target).css('padding-left'));
        var paddingR = parseInt($(target).css('padding-right'));
        var text = $(target).text().replace(/\s/g, ''); //去除所有空格
        if (textLength > (containerLength + paddingL + paddingR)) {
            $(target).attr('title', text);
        }
    }

    $("body").on("mouseenter", ".slh", ellipsis);


    // 退租输入框事件
    staticEvent.inputEventSurrenderTenancy();
    // 创建表底数小时下拉框数据
    staticEvent.creatHoursSelectData();

    $(document).on('click', function (e) {
        e.stopPropagation();
        $('.searchHint').fadeOut();

        $('.opt_btns').hide();
        $('#grid1 .per-grid-dynamic_con, #grid1  .per-scrollbar, #grid1  .per-scrollbar_wrap').removeClass('overflowVisble');

        // 使时间控件在展开状态时点击其他区域可以收起
        if(e.target == document.body) return;
        // 收起时间控件
        $('#tenantManage').find('.per-calendar-con').hide();
        $('.g_r_s_t_box').find('.per-calendar-con').hide();
        $('.g_r_box').find('.per-calendar-con').hide();
        // staticEvent.billHide();//结算 账单明细弹窗
        // staticEvent.floatHide();//修改价格方案弹窗

    });

    // 仪表设置记录滚动操作
    $(function () {
        $(function () {
            var pre_scrollTop = 0;
            pre_scrollTop = $("#listBox").scrollTop();
            $(".tableBox").on("scroll", function () {
                var scrollTop = $(".tableBox").scrollTop();
                if (pre_scrollTop != scrollTop) {
                    pre_scrollTop = scrollTop;
                    $(".list_title").css("top", pre_scrollTop);
                }
            })
        });
    });

    $(window).on('resize', function () {
        // 首页表格
        var maxHeight = $('#grid1 .per-grid-dynamic_wrap').outerHeight();
        $('#grid1 .per-grid-dynamic_wrap .per-grid-dynamic_con').attr('style','max-width: 100%; max-height:'+(maxHeight - 102)+'px !important');
        $('#grid1 .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar').attr('style','max-width: 100%; max-height:'+(maxHeight - 103)+'px !important');
        $('#grid1 .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar_wrap').attr('style','max-width: calc(100% + 17px); height:'+(maxHeight - 86)+'px !important');


        $('#RemotetopupCount .per-grid-dynamic_wrap ').attr('style','max-width: 100%; max-height:'+400+'px !important');
        $('#RemotetopupCount .per-grid-dynamic_wrap .per-grid-dynamic_con').attr('style','max-width: 100%; max-height:'+400+'px !important');
        $('#RemotetopupCount .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar').attr('style','max-width: 100%; max-height:'+400+'px !important');
        $('#RemotetopupCount .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar_wrap').attr('style','max-width: calc(100% + 17px); max-height:'+ 265+'px !important');

        $('#messageBlacklist .per-grid-dynamic_wrap .per-grid-dynamic_con').attr('style','max-width: 100%; max-height:'+240+'px !important');
        $('#messageBlacklist .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar').attr('style','max-width: 100%; max-height:'+240+'px !important');
        $('#messageBlacklist .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar_wrap').attr('style','max-width: calc(100% + 17px); max-height:'+ 240+'px !important');

    })


    //暂时放着
    Highcharts.setOptions({
        global: {
            timezoneOffset: -8 * 60
        }
    });
    // staticEvent.elecMeterdetailChart=staticEvent.lineChart();

    // 租户详情--移入表具单元格事件
    // staticEvent.mouseInMeterCellEvent();

    // 租户详情--点击换表
    // staticEvent.changeMeterEvent();
});

// 自定义报警和全局报警的切换事件
staticEvent.globalAlarmTypeEvent = function (e) {
    var model = tenantMngModel.instance();
    if(e.pEventAttr.state) {
        var flag = $(e.target).parent().attr('data-flag') || $(e.target).attr('data-flag');
        tenantCtrl.getGlobalAlarmArr(flag);
    }
}
staticEvent.customAlarmTypeEvent = function (e) {
    if(e.pEventAttr.state) {
        var flag = $(e.target).parent().attr('data-flag') || $(e.target).attr('data-flag');
        tenantCtrl.getGlobalAlarmArr(flag);
    }
}

// 剩余量/金额/天数报表下载事件
staticEvent.downloadRemainingAmountReport = function () {
    var model = tenantMngModel.instance();
    model.isDownload = 1;
    tenantCtrl.getRemainingAmountReport();
}


// 创建表底数小时下拉框数据()
staticEvent.creatHoursSelectData = function () {
    var model = tenantMngModel.instance();
    for(var i = 0; i < 24; i++) {
        var hour = '';

        if(i < 10) {
            hour = '0' + i + ':00';
        } else {
            hour = i + ':00';
        }

        model.hourArrForGauge.push({
            hour: hour
        });
    }
}

// 批量操作 -- 点击表底数记录按钮
staticEvent.gaugeRecordBtn = function () {
    var model = tenantMngModel.instance();
    // 表底数记录 -- 初始化时间控件的默认时间
    staticEvent.initTimeController('time_controller');

    var timeObj = $('#time_controller').psel();
    var index = 0;

    model.beforeCheckBoxtTime = staticEvent.forMatTime(timeObj.startTime);
    model.nowCheckedTime = staticEvent.forMatTime(timeObj.startTime);
    model.nowCheckedEnergyId = model.energyType;
    model.nowCheckedHour = '00:00:00';
    model.beforeCheckBoxtHour = '00:00:00';
    // model.gaugeRecordArr = [];

    model.energyTypeArrForGauge.forEach(function (val, idx) {
        if(val.id == model.energyType) {
            index = idx;
        }
    });

    // 初始化下拉框状态
    $('#check_activate_time').psel(false, false);
    $('#check_activate_time').psel(true, true);
    $('#energy_type').psel(index, false);
    $('#time_hour').precover('选择小时');

    // 页面跳转
    model.currentPage = "gaugeRecord";
    // tenantCtrl.getGaugeRecordData();
}


// 表底数记录 -- 初始化时间控件的默认时间
staticEvent.initTimeController = function (selector) {
    var date = new Date();
    var time = date.getFullYear() + '/' + (date.getMonth() + 1) + '/' + date.getDate();
    $('#' + selector).psel({
        startTime: time
    }, false);
}


// 表底数记录 -- 时间格式化方法
staticEvent.forMatTime = function (time) {
    var date = new Date(time);
    return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
}

// 表底数记录 -- 勾选切换为激活时间，并设置时间的默认值
staticEvent.checkActivateTime = function (e) {
    var model = tenantMngModel.instance();
    var state = e.pEventAttr.state;

    if(state) {
        model.nowCheckedTime = '';
        model.nowCheckedHour = '';
    } else {
        model.nowCheckedTime = model.beforeCheckBoxtTime;
        model.nowCheckedHour = model.beforeCheckBoxtHour;
    }
    document.body.click();
    $('#time_controller').pdisable(state);
    $('#time_hour').pdisable(state);
    tenantCtrl.getGaugeRecordData();
}

// 表底数记录 -- 切换能耗类型
staticEvent.selectEnergyTypeGaugeRecord = function (dataSource) {
    var model = tenantMngModel.instance();
    model.nowCheckedEnergyId = dataSource.id;
    tenantCtrl.getGaugeRecordData();
}

// 表底数记录 -- 时间控件选择时间
staticEvent.chooseTimeRangeGaugeRecord = function () {
    var model = tenantMngModel.instance();
    var timeObj = $('#time_controller').psel();
    model.beforeCheckBoxtTime = staticEvent.forMatTime(timeObj.startTime);
    model.nowCheckedTime = staticEvent.forMatTime(timeObj.startTime);
    tenantCtrl.getGaugeRecordData();
}

// 表底数记录 -- 选择小时
staticEvent.chooseHourGaugeRecord = function (dataSource) {
    var model = tenantMngModel.instance();
    model.nowCheckedHour = dataSource.hour + ':00';
    model.beforeCheckBoxtHour = dataSource.hour + ':00';
    tenantCtrl.getGaugeRecordData();
}

// 表底数记录 -- 下载报表
staticEvent.downloadGaugeRecordReport = function () {
    var model = tenantMngModel.instance();
    model.isDownload = 1;
    // console.log(model.gaugeRecordParams);
    tenantCtrl.getGaugeRecordData();
}


// 表底数记录（单租户）-- 点击按钮
staticEvent.gaugeRecordSingleTenantBtn = function (fromPage) {
    var model = tenantMngModel.instance();

    // 表底数记录（单租户） -- 初始化时间控件的默认时间
    staticEvent.initTimeController('time_controller_single');

    var timeObj = $('#time_controller_single').psel();
    model.beforeCheckBoxtTimeSingle = staticEvent.forMatTime(timeObj.startTime);
    model.nowCheckedTimeSingle = staticEvent.forMatTime(timeObj.startTime);
    model.nowCheckedEnergyIdSingle = null;
    model.nowCheckedHourSingle = '00:00:00';
    model.beforeCheckBoxtHourSingle = '00:00:00';
    // model.gaugeRecordSingleTenantArr = [];

    $('#check_activate_time_single').psel(false, false);
    $('#check_activate_time_single').psel(true, true);
    $('#energy_type_single').psel(0, false);
    $('#time_hour_single').precover('选择小时');

    model.currentPage = "gaugeRecordSingleTenant";
    model.gaugeRecordSingleTenantFromPage = fromPage;
    // tenantCtrl.getGaugeSingleTenantRecordData();
}

// 表底数记录（单租户） -- 勾选切换为激活时间，并设置时间的默认值
staticEvent.checkActivateTimeSingle = function (e) {
    var model = tenantMngModel.instance();
    var state = e.pEventAttr.state;

    if(state) {
        model.nowCheckedTimeSingle = '';
        model.nowCheckedHourSingle = '';
    } else {
        model.nowCheckedTimeSingle = model.beforeCheckBoxtTimeSingle;
        model.nowCheckedHourSingle = model.beforeCheckBoxtHourSingle;
    }

    document.body.click();
    $('#time_controller_single').pdisable(state);
    $('#time_hour_single').pdisable(state);
    tenantCtrl.getGaugeSingleTenantRecordData();
}

// 表底数记录（单租户） -- 切换能耗类型
staticEvent.selectEnergyTypeGaugeRecordSingle = function (dataSource) {
    var model = tenantMngModel.instance();
    model.nowCheckedEnergyIdSingle = dataSource.id;
    tenantCtrl.getGaugeSingleTenantRecordData();
}

// 表底数记录（单租户） -- 时间控件选择时间
staticEvent.chooseTimeRangeGaugeRecordSingle = function () {
    var model = tenantMngModel.instance();
    var timeObj = $('#time_controller_single').psel();
    model.beforeCheckBoxtTimeSingle = staticEvent.forMatTime(timeObj.startTime);
    model.nowCheckedTimeSingle = staticEvent.forMatTime(timeObj.startTime);
    tenantCtrl.getGaugeSingleTenantRecordData();
}

// 表底数记录（单租户） -- 选择小时
staticEvent.chooseHourGaugeRecordSingle = function (dataSource) {
    var model = tenantMngModel.instance();
    model.nowCheckedHourSingle = dataSource.hour + ':00';
    model.beforeCheckBoxtHourSingle = dataSource.hour + ':00';
    tenantCtrl.getGaugeSingleTenantRecordData();
}

// 表底数记录（单租户） -- 下载报表
staticEvent.downloadGaugeRecordReportSingle = function () {
    var model = tenantMngModel.instance();
    model.isDownload = 1;
    // console.log(model.gaugeRecordSingleTenantParams);
    tenantCtrl.getGaugeSingleTenantRecordData();
}

staticEvent.goBackForGaugeRecordReportSingle = function () {
    var model = tenantMngModel.instance();
    model.gaugeRecordSingleTenantArr = [];
    // model.customAlarmPanelIsShow = !model.customAlarmPanelIsShow;
    model.currentPage = model.gaugeRecordSingleTenantFromPage;
}

// 退费功能
// 各种弹窗显示隐藏

// 退费弹窗
//软充软扣
staticEvent.showRefundCostSoftwareEvent = function (energyTypeId,prePayType,meterId) {
    var beforeRefundCostData = tenantMngModel.instance().beforeRefundCostData;
    // var model = tenantMngModel.instance();
    // var selTenant = model.selTenant
    // var recharge = selTenant.recharge;
    // model.oldRemainData = recharge.remainData;
    $("#refund_soft_soft").pval("");
    $("#refund_soft_soft").precover();
    $('#refund_cost_software').pshow();
    $('#partLoading_in_refund_cost').pshow();
    $("#partLoadingSoft").pshow();
    tenantCtrl.getBeforeRefundCostData(energyTypeId, beforeRefundCostData.refundValue,prePayType,meterId);
}
//软充表扣
staticEvent.showRefundCostMeterEvent = function (energyTypeId,prePayType,meterId) {
    var beforeRefundCostData = tenantMngModel.instance().beforeRefundCostData;
    $("#refund_meter_meter").pval("");
    $("#refund_meter_meter").precover();
    $('#refund_cost_meter').pshow();
    $('#partLoading_in_refund_cost').pshow();
    $("#partLoadingMeter").pshow();
    tenantCtrl.getBeforeRefundCostData(energyTypeId, beforeRefundCostData.refundValue,prePayType,meterId);
}
//软充软扣 关闭弹窗
staticEvent.hideRefundCostSoftwareEvent = function () {
    $('#refund_cost_software').phide();
    // setTimeout(function () {
    //     $('#refund_cost_soft_btn').pdisable(true);
    // }, 500);
}
//软充表扣 关闭弹窗
staticEvent.hideRefundCostMeterEvent = function () {
    $('#refund_cost_meter').phide();
    // setTimeout(function () {
    //     $('#refund_cost_soft_btn').pdisable(true);
    // }, 500);
}

// 退费成功弹窗 软充软扣
staticEvent.showRefundCostSuccessEvent = function () {
    var beforeRefundCostData = tenantMngModel.instance().beforeRefundCostData;
    var oldValue = beforeRefundCostData.remainData;
    beforeRefundCostData.oldValue = oldValue;
    $('#refund_cost_success').pshow();
    Vue.nextTick(function(){
        tenantCtrl.refreshRechargeData(2)
    })
}

staticEvent.hideRefundCostSuccessEvent = function () {
    $('#refund_cost_success').phide();
}
// 退费成功弹窗 软充表扣++
staticEvent.showRefundCostMeterSuccessEvent = function () {
    var beforeRefundCostData = tenantMngModel.instance().beforeRefundCostData;
    var oldValue = beforeRefundCostData.remainData;
    beforeRefundCostData.oldValue = oldValue;
    $('#refund_cost_meter_success').pshow();
    $('#refundLoadingMeterSuccess').pshow();
    Vue.nextTick(function(){
        tenantCtrl.refreshRechargeData(1)
    })
}
// 退费记录 软充表扣++
staticEvent.meterReturnPaySaveService = function () {
    $('#return_success').pdisable(true);
    var model = tenantMngModel.instance();
    var beforeRefundCostData = model.beforeRefundCostData;
    var params = {
        tenantId: model.selTenant.tenantId,
        energyTypeId: beforeRefundCostData.energyTypeId,
        value: beforeRefundCostData.refundValue,
        remainData: beforeRefundCostData.remainData,
        billingType: beforeRefundCostData.billingType,
        meterId: beforeRefundCostData.meterId,
        prePayType: beforeRefundCostData.prePayType,
    };
    setTimeout(function(){
        $('#return_success').pdisable(false);
    }, 4000);
    // console.log(params);
    pajax.post({
        url: 'FNTReturnPaySaveService',
        data: params,
        success: function (resJson) {
            staticEvent.hideRefundCostMeterSuccessEvent();
        },
    });

}

staticEvent.hideRefundCostMeterSuccessEvent = function () {
    $('#refund_cost_meter_success').phide();
}

// 退费失败弹窗 软充软扣
staticEvent.showRefundCostErrorEvent = function () {
    var beforeRefundCostData = tenantMngModel.instance().beforeRefundCostData;
    tenantCtrl.getBeforeRefundCostData(beforeRefundCostData.energyTypeId, beforeRefundCostData.refundValue,beforeRefundCostData.prePayType,beforeRefundCostData.meterId);
    $('#refund_cost_error').pshow();
}

staticEvent.hideRefundCostErrorEvent = function () {
    $('#refund_cost_error').phide();
}
// 退费失败弹窗 软充表扣++
staticEvent.showRefundCostMeterErrorEvent = function () {
    var beforeRefundCostData = tenantMngModel.instance().beforeRefundCostData;
    tenantCtrl.getBeforeRefundCostData(beforeRefundCostData.energyTypeId, beforeRefundCostData.refundValue,beforeRefundCostData.prePayType,beforeRefundCostData.meterId);
    $('#refund_cost_meter_error').pshow();
}

staticEvent.hideRefundCostMeterErrorEvent = function () {
    $('#refund_cost_meter_error').phide();
}

// 退费中
staticEvent.refundCostLoadingEvent = function (flag) {
    if(flag == 'show') {
        $('#refund_cost_loading').pshow();
    } else {
        $('#refund_cost_loading').phide();
    }
}

// 退费二次密码确认弹窗
staticEvent.showPassWordForRefundCost = function () {
    $('#refund_cost').pshow();
}

staticEvent.hidePassWordForRefundCost = function () {
    var model = tenantMngModel.instance();
    $('#refund_cost').phide();
    setTimeout(function () {
        model.verifyPasswordTip = '';
        model.verifyPasswordValue = '';
        model.passwordIsEmpty = false;
        model.isThroughVerification = 0;
        // $('#refund_cost_details').pdisable(true);
    }, 500);
}

// 点击退费按钮 软充软扣
staticEvent.refund_cost_soft = function (selector) {
    var beforeRefundCostData = tenantMngModel.instance().beforeRefundCostData;
    var $el = $("#" + selector);
    if (!$el.pverifi()) {
        return;
    }
    var value = parseFloat($el.pval());
    beforeRefundCostData.refundValue = value.toFixed(2);
    staticEvent.hideRefundCostSoftwareEvent();
    staticEvent.showPassWordForRefundCost();
}

// 点击退费按钮 软充表扣
staticEvent.refund_cost_meter = function (selector) {
    var beforeRefundCostData = tenantMngModel.instance().beforeRefundCostData;
    // console.log(beforeRefundCostData);

    var $el = $("#" + selector);
    if (!$el.pverifi()) {
        return;
    }
    var value = parseFloat($el.pval());
    beforeRefundCostData.refundValue = value.toFixed(2);
    staticEvent.hideRefundCostMeterEvent();
    staticEvent.showPassWordForRefundCost();
}

// 预付费退费二次确认
staticEvent.refund_cost_second = function () {
    var model = tenantMngModel.instance();
    // var selTenant = model.selTenant
    // var recharge = selTenant.recharge;
    // var oldRemainData = recharge.remainData;

    var value = model.beforeRefundCostData.refundValue;
    staticEvent.hidePassWordForRefundCost();
    staticEvent.refundCostLoadingEvent('show');
    tenantCtrl.tenantRefundCost(value);
}

// 充值/退费失去焦点事件//
staticEvent.textBlurEvent = function (model, event, selector) {
    var $el = $(event.target).parent().parent();
    var pverifi = $el.pverifi();
    var value = parseFloat($el.pval());
    if (value > 9999999) {
        $el.pshowTextTip("最大支持9999999");
    }
    // $('#' + selector).pdisable(!(pverifi&&(value < 9999999)));
    // let value = $("#" + event._currentTarget.id).pval();
    if (value === null || value === "") {
        return;
    }
    let patten = /^(\-)?\d+(\.\d{0,4})$/;
    let patten1 = /^[0-9]*$/;
    if (patten1.test(value) || patten.test(value)) {
        $("#"+selector).pdisable(false);
        $("#refundButton").pdisable(false);
        return;
    } else {
        $("#"+selector).pdisable(true);
        $("#refundButton").pdisable(true);
        $("#message").pshow({
            text: "请输入正确的数字(小数点后最多四位)",
            state: "failure"
        });
    }
}

// 仪表设置弹窗
staticEvent.showInstrumentSetWindow = function () {
    $('#instrument_set').pshow();
    $('#close_meter_setting').fadeIn();
    staticEvent.reductionPasswordForMeterSet();
}

staticEvent.hideInstrumentSetWindow = function () {
    var model = tenantMngModel.instance();
    model.verifyPasswordTip = '';
    model.verifyPasswordValue = '';
    model.passwordIsEmpty = false;
    model.isThroughVerification = 0;
    model.instrumentalData='';
    setTimeout(function () {
        model.meterSetTypeName = '仪表设置';
        $('.window_instrument_set').find('.per-modal-custom_title').text('仪表设置');
    }, 500);
    $('#instrument_set').phide();
    $('#close_meter_setting').fadeOut();
}

// 仪表设置给输入框回复初始状态
staticEvent.reductionIptOfMeterSet = function () {
    $("#overdraft_amount").pval("");
    $("#overdraft_amount").precover();
    $("#electricity_price_ipt_L").pval("");
    $("#electricity_price_ipt_L").precover();
    for(var i = 0; i < 4; i++) {
        $("#electricity_price_ipt_" + i).pval("");
        $("#electricity_price_ipt_" + i).precover();
    }
}

// 仪表设置恢复密码输入状态
staticEvent.reductionPasswordForMeterSet = function () {
    var model = tenantMngModel.instance();
    setTimeout(function () {
        model.verifyPasswordTip = '';
        model.verifyPasswordValue = '';
        model.passwordIsEmpty = false;
        model.isThroughVerification = 0;
        // $('#confirm_meter_set_change').pdisable(true);
    }, 500);
}

// 表格高度自适应
staticEvent.girdHeight = function (girdData, selector) {
    var maxHeight = $('.per-grid-normal_r').outerHeight();
    var nowHeight = girdData.length * ($('.per-grid-nomal-wrap li').outerHeight() || 38) + $('.per-grid-normal_title').outerHeight();
    var girdHeight = 0;
    if(girdData.length != 0) {
        if(nowHeight >= maxHeight) {
            girdHeight = maxHeight;
            $('.per-grid-normal_main').removeClass('no_border_bottom');
            $(selector).pcount(1);
        } else {
            girdHeight = nowHeight;
            $('.per-grid-normal_main').addClass('no_border_bottom');
            $(selector).pcount(1);
        }
        $('.c_m_r_gird > div').css('height', girdHeight + 2);
    } else {
        $(selector).pcount(0);
    }
}

staticEvent.comboboxhide = function () {
    $('.per-combobox-basic .per-combobox-wrap').hide();
}
staticEvent.comboboxhide1 = function () {
    $('.per-combobox-basic .per-combobox-wrap').hide();
}

staticEvent.verifyPageSizeEvent = function () {

    // 获取输入内容是否合法（布尔值）
    var isVerifySuccess = $('#page_text').pverifi();

    // 获取当前输入框内的值
    var pageSize = parseInt($('#page_text').pval());

    // 判断输入内容是否合法
    if(isVerifySuccess) {

        // 合法--对数字进行验证并解除确定按钮的禁用状态
        $('#page_determine').pdisable(false);

        // 分不同情况设置输入框为空、上限、下限
        if(!pageSize) {
            pageSize = 100;
        } else if(pageSize < 20) {
            pageSize = 20
        } else if(pageSize > 5000) {
            pageSize = 5000;
        } else {
            pageSize = pageSize;
        }

        // 设置每页条数的值
        $('#page_text').pval(pageSize);
        tenantMngModel.data.pageSize = pageSize;
    } else {

        // 不合法--禁用确定按钮
        $('#page_determine').pdisable(true);
    }
    // console.log(pageSize);
    // console.log(isVerifySuccess);
}
staticEvent.verifyPageSizeEvent1 = function () {

    // 获取输入内容是否合法（布尔值）
    var isVerifySuccess = $('#page_text1').pverifi();
    // 获取当前输入框内的值
    var pageSize = parseInt($('#page_text1').pval());
    // 判断输入内容是否合法
    if (isVerifySuccess) {
        // 合法--对数字进行验证并解除确定按钮的禁用状态
        $('#sr_page_determine').pdisable(false);
        // 分不同情况设置输入框为空、上限、下限
        if (!pageSize) {
            pageSize = 100;
        } else if (pageSize < 2) {
            pageSize = 3
        } else if (pageSize > 5000) {
            pageSize = 5000;
        } else {
            pageSize = pageSize;
        }
        // 设置每页条数的值
        $('#page_text1').pval(pageSize);
        tenantMngModel.data.pageSize1 = pageSize;
    } else {
        // 不合法--禁用确定按钮
        $('#sr_page_determine1').pdisable(true);
    }
    // console.log(pageSize);
    // console.log(isVerifySuccess);
}


staticEvent.verifyPageSizeEvent_sr = function () {
    // 获取输入内容是否合法（布尔值）
    var isVerifySuccess = $('#sr_page_text').pverifi();
    // 获取当前输入框内的值
    var pageSize = parseInt($('#sr_page_text').pval());
    // 判断输入内容是否合法
    if (isVerifySuccess) {
        // 合法--对数字进行验证并解除确定按钮的禁用状态
        $('#sr_page_determine').pdisable(false);
        // 分不同情况设置输入框为空、上限、下限
        if (!pageSize) {
            pageSize = 100;
        } else if (pageSize < 20) {
            pageSize = 20
        } else if (pageSize > 5000) {
            pageSize = 5000;
        } else {
            pageSize = pageSize;
        }
        // 设置每页条数的值
        $('#sr_page_text').pval(pageSize);
        tenantMngModel.data.pageSize = pageSize;
    } else {
        // 不合法--禁用确定按钮
        $('#sr_page_determine').pdisable(true);
    }
    // console.log(pageSize);
    // console.log(isVerifySuccess);
}


// 清空页码输入框状态
staticEvent.pageSizeDefault = function () {
    $('#page_text').precover();
    $('#page_determine').pdisable(false);
    $('#page_text').pval(tenantCtrl.pageSize);
}

// 退租输入框事件
staticEvent.inputEventSurrenderTenancy = function () {
    var iptVal;
    // $('table tr input').on({
    //     focus: function () {
    //         $(this).siblings().show();
    //     }
    // }).parent().prev().on({
    //     mouseover: function () {
    //         $(this).css('backgroundColor', '#F8F8F8').find('i').show();
    //     },
    //     mouseout: function () {
    //         $(this).css('backgroundColor', '#FFF').find('i').hide();
    //     },
    //     click: function () {
    //         iptVal = $('table tr input').val();
    //         $(this).hide().next().show().find('input').val('').focus().val(iptVal);
    //     }
    // }).next().find('.delete').on('click', function () {
    //     console.log(iptVal);
    //     $(this).parent().hide().find('input').val(iptVal).parent().prev().show().find('span').text(iptVal);
    // }).prev().on('click', function () {
    //     $(this).parent().hide().prev().show().find('span').text($(this).parent().find('input').val());
    // });
    $('.edit').on('click', function () {
        $(this).parent().find('input').focus();
    })
}
// 退租弹出框
staticEvent.confirmSurrenderTenancy = function () {
    $("#confirm_urrender_enancy").pshow({ title: '确定要清算并退租吗？', subtitle: '确定后该租户的所有账单将被缴清' });
}
// 退租弹出框取消
staticEvent.confirmSurrenderTenancyHide = function () {
    $("#confirm_urrender_enancy").phide();
}
// 退租弹出框确认
staticEvent.confirmSurrenderTenancySend = function () {
    tenantCtrl.leaveTenant();
    $("#confirm_urrender_enancy").phide();
}

// 租户详情--点击发送缴费信息提醒弹窗
staticEvent.sendMassageToMaster = function (energyTypeId, payType) {
    var selTenant = tenantMngModel.instance().selTenant;
    var tenantId = selTenant.tenantId;
    tenantCtrl.getMeterIds(tenantId,energyTypeId,payType);
    // $("#pay_type_name").text(payType == 0 ? "充值" : "缴费");
    // $('#send_massage_btn').pshow({ title: payType == 0 ? '发送充值提醒' : '发送缴费提醒' })
    // $('#send_massage_btn').data("param", {
    //     energyTypeId: energyTypeId,
    //     payType: payType
    // });
}
// 租户详情--点击发送缴费信息提醒弹窗--确认发送
staticEvent.sendMassageYes = function () {
    var param = $('#send_massage_btn').data("param");
    var selTenant = tenantMngModel.instance().selTenant;
    var tenantArr = [{
        "tenantId": selTenant.tenantId,
        "tenantName": selTenant.tenantName,
        "contactName": selTenant.contactName,
        "contactMobile": selTenant.contactMobile,
        "buildingId": selTenant.buildingId,
    }];
    param.payType = parseInt(param.payType);
    tenantCtrl.sendMessage(param.energyTypeId, param.payType, tenantArr,selTenant.selectedMeterId);
}
// 租户详情--点击发送缴费信息提醒弹窗--取消
staticEvent.sendMassageHide = function () {
    $('#send_massage_btn').phide();
}
// 租户详情--点击删除按钮弹窗
staticEvent.deleteLesseeBtn = function () {
    $('#delete_lessee_btn').pshow({ title: '确定要删除租户吗', subtitle: '删除后不可恢复' });
}
// 租户详情--点击删除按钮弹窗--取消
staticEvent.deleteLesseeBtnHide = function () {
    $('#delete_lessee_btn').phide();
}
// 租户详情--点击激活按钮弹窗
staticEvent.startLesseeBtn = function (e) {
    var date = new Date();
    $('.t_d_box .start_lessee_confirm .t_d_choose_date_error_msg_lt').hide();
    $('.t_d_box .start_lessee_confirm .go_start > div').pdisable(false);
    $('#start_lessee_btn').pshow();
    $("#start_lessee_time").psel({ y: date.getFullYear(), M: date.getMonth() + 1, d: date.getDate() + 1 }, false);
}
// 租户详情--验证激活的选择时间
staticEvent.proofStartLesseeTime = function () {
    var date = new Date();
    var nowMonth = date.getMonth() + 1;
    var nowDate = date.getDate();
    nowMonth = nowMonth < 10 ? '0' + nowMonth : '' + nowMonth;
    nowDate = nowDate < 10 ? '0' + nowDate : '' + nowDate;
    var nowTime = date.getFullYear() + nowMonth + nowDate;
    var selectTime = $('#start_lessee_time').psel().startTime;
    if (selectTime.replace(/-/g, '') - nowTime <= 0) {
        $('.t_d_box .start_lessee_confirm .t_d_choose_date_error_msg_lt').show();
        $('.t_d_box .start_lessee_confirm .go_start > div').pdisable(true);
    } else {
        $('.t_d_box .start_lessee_confirm .t_d_choose_date_error_msg_lt').hide();
        $('.t_d_box .start_lessee_confirm .go_start > div').pdisable(false);
    }
}
// 租户详情--点击激活按钮弹窗--确认激活
staticEvent.startLesseeYes = function (e) {
    tenantCtrl.activeTenant(e);
}
// 租户详情--点击激活按钮弹窗--取消
staticEvent.startLesseeHide = function () {
    $('#start_lessee_btn').phide();
}

// 租户列表--点击发送缴费信息提醒弹窗
// staticEvent.sendMassageFromListToMaster = function (energyTypeId, payType) {
//     $("#pay_type_name").text(payType == 0 ? "充值" : "缴费");
//     $('#send_massage_from_list_btn').pshow({ title: payType == 0 ? '发送充值提醒' : '发送缴费提醒' })

//     $('#send_massage_from_list_btn').data("param", {
//         energyTypeId: energyTypeId,
//         payType: payType
//     });
// }
// 租户列表--点击发送缴费信息提醒弹窗--确认发送
staticEvent.sendMassageFromListYes = function () {
    var param = $('#send_massage_from_list_btn').data("param");
    var selTenant = tenantMngModel.instance().selTenant;
    var tenantArr = [{
        "tenantId": selTenant.tenantId,
        "tenantName": selTenant.tenantName,
        "contactName": selTenant.contactName,
        "contactMobile": selTenant.contactMobile,
        "buildingId": selTenant.buildingId
    }];
    param.payType = parseInt(param.payType);
    tenantCtrl.sendMessage(param.energyTypeId, param.payType, tenantArr);
}
// 租户列表--点击发送缴费信息提醒弹窗--取消
staticEvent.sendMassageFromListHide = function () {
    $('#send_massage_from_list_btn').phide();
}

// 租户列表--点击激活按钮弹窗
staticEvent.startLesseeFromListBtn = function () {
    var date = new Date();
    $('.t_p_box .start_lessee_confirm .t_d_choose_date_error_msg_lt').hide();
    $('.t_p_box .start_lessee_confirm .go_start > div').pdisable(false);
    $('#start_lessee_from_list_btn').pshow();
    $("#start_lessee_from_list_time").psel({ y: date.getFullYear(), M: date.getMonth() + 1, d: date.getDate() + 1 }, false);
}
// 租户列表--验证激活的选择时间
staticEvent.proofStartLesseeTimeFromList = function () {
    var date = new Date();
    var nowMonth = date.getMonth() + 1;
    var nowDate = date.getDate();

    nowMonth = nowMonth < 10 ? '0' + nowMonth : '' + nowMonth;
    nowDate = nowDate < 10 ? '0' + nowDate : '' + nowDate;
    var nowTime = date.getFullYear() + nowMonth + nowDate;
    var selectTime = $('#start_lessee_from_list_time').psel().startTime;
    if (selectTime.replace(/-/g, '') - nowTime <= 0) {
        $('.t_p_box .start_lessee_confirm .t_d_choose_date_error_msg_lt').show();
        $('.t_p_box .start_lessee_confirm .go_start > div').pdisable(true);
    } else {
        $('.t_p_box .start_lessee_confirm .t_d_choose_date_error_msg_lt').hide();
        $('.t_p_box .start_lessee_confirm .go_start > div').pdisable(false);
    }
}
// 租户列表--点击激活按钮弹窗--确认激活
staticEvent.startLesseeFromListYes = function (e) {
    tenantCtrl.activeTenant(e);
}
// 租户列表--点击激活按钮弹窗--取消
staticEvent.startLesseeFromListHide = function () {
    $('#start_lessee_from_list_btn').phide();
}

// 租户列表--点击删除租户按钮弹窗
staticEvent.deleteLesseeFromListBtn = function () {
    $('#delete_lessee_from_list_btn').pshow({ title: '确定要删除租户吗', subtitle: '删除后不可恢复' });
}
// 租户列表--点击删除租户按钮弹窗--取消
staticEvent.deleteLesseeFromListBtnHide = function () {
    $('#delete_lessee_from_list_btn').phide();
}

// 租户详情--移入表具单元格事件
staticEvent.mouseInMeterCellEvent = function () {
    $('.t_d_content_info_box_gird_body').on('mouseenter', '.meter_item', function () {
        $(this).find('.change_meter').show();
    }).on('mouseleave', '.meter_item', function () {
        $(this).find('.change_meter').hide();
    });
}

// 租户详情--点击缴费弹窗
staticEvent.payTheFeesPostPaidEvent = function (energyTypeId, order) {
    tenantCtrl.beforePayOrder(energyTypeId, [order]);
    $('#pay_the_fees_post_paid').pshow();
    $("#partLoading_in_pay_the_fees").pshow();
}

//合并缴费
staticEvent.payTheFeesPostPaidEvent_combine = function (energyTypeId, orderList, yingJiaoJinE) {
    tenantCtrl.beforePayOrder(energyTypeId, orderList, yingJiaoJinE);
    $('#pay_the_fees_post_paid').pshow();
    $("#partLoading_in_pay_the_fees").pshow();
}

// 租户详情--点击缴费弹窗——关闭
staticEvent.payTheFeesPostPaidCloseEvent = function () {
    $('#pay_the_fees_post_paid').phide();
}

// 租户详情--点击缴费弹窗——缴费中
staticEvent.payTheFeesLodingEvent = function (flag) {
    var model = tenantMngModel.instance();
    model.payPostMoneyEntryFlag = flag;
    staticEvent.payTheFeesErrorCloseEvent();
    staticEvent.payTheFeesPostPaidCloseEvent();
    staticEvent.showPassWordForPay();
}

// 二次密码确认缴费 -- 租户列表
staticEvent.payInshowSecond = function () {
    var model = tenantMngModel.instance();
    var flag = model.payPostMoneyEntryFlag;
    staticEvent.hidePassWordForPay();
    if(flag == 'normal') {
        staticEvent.pay_ing("show");
        tenantCtrl.payBill(model.selTenant, "single");
    } else if(flag == 'bat_single') {
        staticEvent.payInshow();
    }
}


//缴费二次密码验证
staticEvent.showPassWordForPay = function () {
    $('#pay_the_fees').pshow();
}
staticEvent.hidePassWordForPay = function () {
    var model = tenantMngModel.instance();
    $('#pay_the_fees').phide();
    setTimeout(function () {
        model.verifyPasswordTip = '';
        model.verifyPasswordValue = '';
        model.passwordIsEmpty = false;
        model.isThroughVerification = 0;
        // $('#confirm_payment_list').pdisable(true);
    }, 500);
}

staticEvent.pay_ing = function (flag) {
    if (flag == "show") {
        $('#pay_the_fees_loading').pshow();
    } else {
        $('#pay_the_fees_loading').phide();
    }
}

// 租户详情--点击缴费弹窗——缴费成功
staticEvent.payTheFeesSuccessEvent = function () {
    $('#pay_the_fees_success').pshow();
}

// 租户详情--点击缴费弹窗——缴费成功--关闭
staticEvent.payTheFeesSuccessCloseEvent = function () {
    $('#pay_the_fees_success').phide();
}

// 租户详情--点击缴费弹窗——缴费失败
staticEvent.payTheFeesErrorEvent = function () {
    $('#pay_the_fees_error').pshow();
}
// 租户详情--点击缴费弹窗——缴费失败--关闭
staticEvent.payTheFeesErrorCloseEvent = function () {
    $('#pay_the_fees_error').phide();
}

// 预付费--软件充表扣
staticEvent.topUpRechargeMeterEvent = function (energyTypeId, prePayType, meterId) {
    var model = tenantMngModel.instance();
    model.prePayTypeForPassword = prePayType;
    tenantCtrl.beforeRecharge(energyTypeId, prePayType, meterId);
    staticEvent.topUpRechargeMeterShowEvent();
}
staticEvent.topUpRechargeMeterShowEvent = function () {
    $("#charge_soft_meter").pval("");
    $("#charge_soft_meter").precover();
    $('#top_up_recharge_meter').pshow();
    $("#rechargeLoadingMeter").pshow();
    $('#partLoading_in_top_up_recharge').pshow();
}
// 预付费--软件充表扣--关闭
staticEvent.topUpRechargeMeterCloseEvent = function () {
    $('#top_up_recharge_meter').phide();
    // setTimeout(function () {
    //     $('#recharge_soft_meter_btn').pdisable(true);
    // }, 500);
}
// 预付费--软件充表扣--返回界面
staticEvent.topUpRechargeMeterBackEvent = function () {
    $('#top_up_recharge_meter_back').pshow();
    $('#rechargeLoadingMeterSuccess').pshow();
}
// 预付费--软件充表扣--返回界面--关闭
staticEvent.topUpRechargeMeterBackCloseEvent = function () {
    $('#top_up_recharge_meter_back').phide();
}


// 充值二次密码确认弹出框
staticEvent.showPassWordForTopUp = function () {
    $('#top_up').pshow();
}

staticEvent.hidePassWordForTopUp = function () {
    var model = tenantMngModel.instance();
    $('#top_up').phide();
    setTimeout(function () {
        model.verifyPasswordTip = '';
        model.verifyPasswordValue = '';
        model.passwordIsEmpty = false;
        model.isThroughVerification = 0;
        // $('#recharge_details').pdisable(true);
    }, 500);
}




// 预付费--软件充软件扣
staticEvent.topUpRechargeSoftwareEvent = function (energyTypeId, prePayType) {
    var model = tenantMngModel.instance();
    model.prePayTypeForPassword = prePayType;
    $("#charge_soft_soft").pval("");
    $("#charge_soft_soft").precover();
    $('#partLoading_in_top_up_recharge').pshow();
    tenantCtrl.beforeRecharge(energyTypeId, prePayType, undefined);
    $('#top_up_recharge_software').pshow();
    $("#rechargeLoadingSoft").pshow();
}
// 预付费--软件充软件扣-重新充值
staticEvent.retryRecharge_soft_soft = function () {
    var recharge = tenantMngModel.instance().selTenant.recharge;
    staticEvent.topUpRechargeSoftwareEvent(recharge.energyTypeId, 2);
}

// 预付费--软件充软件扣--关闭
staticEvent.topUpRechargeSoftwareCloseEvent = function () {
    $('#top_up_recharge_software').phide();
    // setTimeout(function () {
    //     $('#recharge_soft_soft_btn').pdisable(true);
    // }, 500);

}
// 预付费--软件充软件扣--充值成功
staticEvent.topUpRechargeSoftwareSuccessEvent = function () {
    $('#top_up_recharge_software_success').pshow();
}
// 预付费--软件充软件扣--充值成功--关闭
staticEvent.topUpRechargeSoftwareSuccessCloseEvent = function () {
    $('#top_up_recharge_software_success').phide();
}
// 预付费--软件充软件扣--充值失败
staticEvent.topUpRechargeSoftwareErrorEvent = function () {
    $('#top_up_recharge_software_error').pshow();
}
// 预付费--软件充软件扣--充值成功--关闭
staticEvent.topUpRechargeSoftwareErrorCloseEvent = function () {
    $('#top_up_recharge_software_error').phide();
}

// 预付费--软件充表扣--充值中
staticEvent.recharge_soft_meter = function (selector) {
    var recharge = tenantMngModel.instance().selTenant.recharge;
    var $el = $("#" + selector);
    if (!$el.pverifi()) {
        return;
    }
    var value = parseFloat($el.pval());
    recharge.rechargeValue = value.toFixed(2);
    staticEvent.topUpRechargeMeterCloseEvent();
    staticEvent.showPassWordForTopUp();
}

// 预付费--软件充表扣二次密码确认
staticEvent.recharge_second = function (selector) {
    var $el = $("#" + selector);
    if (!$el.pverifi()) {
        return;
    }
    var value = parseFloat($el.pval());
    if (value > 9999999) {
        $el.pshowTextTip("最大支持9999999");
        return;
    }
    staticEvent.hidePassWordForTopUp();
    staticEvent.recharge_ing("show");
    tenantCtrl.tenantRecharge(value);
}


// 预付费--软件充软件扣--充值中
staticEvent.recharge_soft_soft = function (selector) {
    var recharge = tenantMngModel.instance().selTenant.recharge;
    var $el = $("#" + selector);
    if (!$el.pverifi()) {
        return;
    }
    var value = parseFloat($el.pval());
    recharge.rechargeValue = value.toFixed(2);
    staticEvent.showPassWordForTopUp();
    staticEvent.topUpRechargeSoftwareCloseEvent();
}

staticEvent.recharge_ing = function (flag) {
    if (flag == "show") {
        $('#top_up_recharge_loading').pshow();
    } else {
        $('#top_up_recharge_loading').phide();
    }
}

//设置报警门限方法
staticEvent.policeshow = function () {
    // 清空页码输入框状态
    staticEvent.pageSizeDefault();
    // $('#ZHBJ_15').precover();
    // $('#ZHBJ_05').precover();
    // $('#ZHBJ_07').precover();
    // $('#ZHBJ_09').precover();
    tenantCtrl.getGlobalAlarmArr();
    setTimeout(function(){
    $("#policeWindow").pshow();
    }, 10);
}
// 批量修改电价设置
staticEvent.batchupdateElecshow = function (){
    setTimeout(function(){
            $("#floatWindowPrice").pshow({
                title: '电价方案设置'
            });
        }, 10);
   
},
// 电价方案设置日志（批量） -- 时间控件选择时间
staticEvent.chooseDateRecord = function () {
    var model = tenantMngModel.instance();
    var timeObj = $('#datePicker').psel();
    model.startTime = staticEvent.forMatTime(timeObj.startTime);
    model.endTime = staticEvent.forMatTime(timeObj.startTime);
    tenantCtrl.viewLog(0);
}

//设置短信自动发送方法 start 201806060wp++
staticEvent.autoMsgshow = function () {
    var model = tenantMngModel.instance();
    var autoMsg = model.autoMsg;
    //查询
    //console.log(autoMsg)
    pajax.post({
        url:"FNTSendMessageBeforeService",
        success: function (res) {
            // console.log(res);
            var resulte = res[0];
            autoMsg.sendStuats = resulte.sendStuats;
            // console.log(autoMsg.sendStuats);
                autoMsg.index1 = resulte.timeList[0];
                autoMsg.index2 = resulte.timeList[1]||'';
            $("#autoTime1").psel(model.autoMsg.index1,false);
            $("#autoTime2").psel(model.autoMsg.index2,false);
            $("#autoMsgWindow").pshow();
            setTimeout(function() {
                var model = tenantMngModel.instance();
            var obj = {list:model.messageBlacklist};
            pajax.post({
                url: 'FNTMessageBlacklistSaveService',
                data: obj,
                success: function (result) {
                    
                },
                error: function () {
                    
                },
                complete: function () { //天机成功后获取最新数据
                    tenantCtrl.messageBlacklist();
                    tenantCtrl.addtenantname();   
                }
            });
            })
            
            $('#messageBlacklist .per-scrollbar').attr('style','height: 380px !important');
        },
        error:function (res) {
            $("#message").pshow({ text: "获取失败", state: "failure" });
            console.log("自动发送短信设置前查询失败");
        }
    })
}
staticEvent.autoMsghide = function () {
    $("#autoMsgWindow").phide();
    tenantCtrl.messageBlacklist();
}


staticEvent.addtenantnamehide=function () {
    $("#addtenantnamemodalWindow").phide();
    var model = tenantMngModel.instance();
    model.addtenantName1=[];
    setTimeout(function () {
        $("#blackList").precover()
        model.addtenantName1=model.addtenantName.slice(0,30);
        var count = model.addtenantName.length;
        $("#blackList").pcount(count);
        var pageCount = Math.ceil(count / 30);
        $("#page_simple1").pcount(Math.max(1, pageCount));
    },10)
    model.reserveMessageBlacklist=[];

}


staticEvent.addtenantnamehideSave = function () {//新建确定
    $("#addtenantnamemodalWindow").phide();
    var model = tenantMngModel.instance();
    for (var i = 0; i < model.xMessageBlacklist.length; i++) {
        remove(model.addtenantName, model.xMessageBlacklist[i]);
    }
      function remove(arr, item) {
           var result = [];
           for (var i = 0; i < arr.length; i++) {
               if (arr[i].tenantId != item) {
                   result.push(arr[i]);
               }
           }
           tenantMngModel.instance().addtenantName=result;
       }


    model.addtenantName1=[];
    for(var i = 0; i < model.reserveMessageBlacklist.length;i++){
        model.messageBlacklist.unshift(model.reserveMessageBlacklist[i])
    }
    if(model.messageBlacklist.length > 0) {
        $('.per-grid-nodata').hide();
        $('.per-grid-dynamic_con').show();
    }
    setTimeout(function () {
        model.addtenantName1=model.addtenantName.slice(0,30);
        var count = model.addtenantName.length;
        $("#blackList").pcount(count);
        var pageCount = Math.ceil(count / 30);
        $("#page_simple1").pcount(Math.max(1, pageCount));
    },10)
    model.reserveMessageBlacklist=[];
};

staticEvent.remotetopupSaveHide = function () {//返回
    var model = tenantMngModel.instance();
    //model.cutRemotetopup1=[];
    model.remotetopup=[];
    $("#RemotetopupaddforWindow").phide();
    model.Remotetopup2=[];

    setTimeout(function () {
        $("#rechage").precover()
        model.Remotetopup2=model.Remotetopup1.slice(0,30);
        var count = model.Remotetopup1.length;
        $("#rechage").pcount(count);
        var pageCount = Math.ceil(count / 30);
        $("#page_simple2").pcount(Math.max(1, pageCount));
    },10)

};

//设置远程充值黑名单 3.6++
staticEvent.Remotetopupshow = function () {
    tenantCtrl.addRemotetopup();
    var model = tenantMngModel.instance();
    var buildingId = null;
    var buildingSel = $("#t_building_cbx").psel();
    if (buildingSel) {
        buildingId = model.buildingArr[buildingSel.index].id;
    }
    buildingId = buildingId == "_all" ? null : buildingId;
    var param = {
        buildingId: buildingId
    };
    //查询
    pajax.post({
        url:"FNTPrePayBlacklistQueryService",
        data:param,
        success: function (res) {
            var resulte = res[0];
            model.Remotetopup=resulte;
            $("#RemotetopupWindow").pshow();
        },
        error:function (res) {
            $("#message").pshow({ text: "获取失败", state: "failure" });
            console.log("自动发送短信设置前查询失败");
        }
    })
}
staticEvent.Remotetopuphide = function () {
    $("#RemotetopupWindow").phide();

}
//设置远程充值黑名单保存


staticEvent._console  = function (event) {//选中词条
    var model = tenantMngModel.instance();
    var b = {
        buildingId: event.buildingId,
        tenantId: event.tenantId,
        tenantName: event.tenantName,
        buildingName: event.buildingName
    }
    //model.reserveMessageBlacklist.unshift(b);
    //model.xMessageBlacklist.push(event.tenantId);
    pushData (b)
    unshiftData (b)
    function pushData (b) {
        var index = -1
        for (var i = 0; i < model.reserveMessageBlacklist.length; i++) {
            if (model.reserveMessageBlacklist[i].tenantId == b.tenantId) {
                index = i
                break
            }
        }
        if (index !== -1) {
            model.reserveMessageBlacklist.splice(i, 1);
        } else {
            model.reserveMessageBlacklist.push(b);
        }
    }
    function unshiftData (a) {
        var index = -1
        for (var i = 0; i < model.xMessageBlacklist.length; i++) {
            if (model.xMessageBlacklist[i] == b.tenantId) {
                index = i
                break
            }
        }
        if (index !== -1) {
            model.xMessageBlacklist.splice(i, 1);
        } else {
            model.xMessageBlacklist.unshift(event.tenantId);
        }
    }
}

staticEvent.remotetListclick  = function (event) {//选中词条
    var model = tenantMngModel.instance();
    var b = {
        buildingId: event.buildingId,
        tenantId: event.tenantId,
        tenantName: event.tenantName,
        buildingName: event.buildingName
    }
    //model.remotetopup.push(b);
    pushData (b);
    unshiftData (b);
    function pushData (b) {
        var index = -1
        for (var i = 0; i < model.remotetopup.length; i++) {
            if (model.remotetopup[i].tenantId == b.tenantId) {
                index = i
                break
            }
        }
        if (index !== -1) {
            model.remotetopup.splice(i, 1);
        } else {
            model.remotetopup.push(b);
        }
    }
    function unshiftData (a) {
        var index = -1
        for (var i = 0; i < model.xremotetopup.length; i++) {
            if (model.xremotetopup[i] == b.tenantId) {
                index = i
                break
            }
        }
        if (index !== -1) {
            model.xremotetopup.splice(i, 1);
        } else {
            model.xremotetopup.unshift(event.tenantId);
        }
    }


   // model.xremotetopup.unshift(event.tenantId);
}

staticEvent.remotetopupHideSave = function () {//新建保存
    var model = tenantMngModel.instance();

    for (var i = 0; i < model.xremotetopup.length; i++) {
        remove(model.Remotetopup1, model.xremotetopup[i]);
    }
    function remove(arr, item) {
        var result = [];
        for (var i = 0; i < arr.length; i++) {
            if (arr[i].tenantId != item) {
                result.push(arr[i]);
            }
        }
        tenantMngModel.instance().Remotetopup1=result;
    }

    model.Remotetopup2=[];
    for(var i = 0; i < model.remotetopup.length;i++){
        model.Remotetopup.unshift(model.remotetopup[i])
    }
    setTimeout(function () {
        model.Remotetopup2=model.Remotetopup1.slice(0,30);
        var count = model.Remotetopup1.length;
        $("#rechage").pcount(count);
        var pageCount = Math.ceil(count / 30);
        $("#page_simple2").pcount(Math.max(1, pageCount));
    },10)

    $("#RemotetopupaddforWindow").phide();

    model.remotetopup=[];

};

staticEvent.eventCall=function () {
    var model = tenantMngModel.instance();
 /*   for (var i = 0; i < model.xMessageBlacklist.length; i++) {
        remove(model.addtenantName, model.xMessageBlacklist[i]);
    }
      function remove(arr, item) {
           var result = [];
           for (var i = 0; i < arr.length; i++) {
               if (arr[i].tenantId != item) {
                   result.push(arr[i]);
               }
           }
           tenantMngModel.instance().addtenantName=result;
       }*/
    model.reserveMessageBlacklist=[];
    model.xMessageBlacklist=[];
    $("#blackList").precover();
    var keyWord=$('#ddSearch').pval().key;

    var len = model.addtenantName.length;
    var arr = [];
    for(var i=0;i<len;i++){
        if(model.addtenantName[i].tenantId.indexOf(keyWord)>=0 || model.addtenantName[i].tenantName.indexOf(keyWord)>=0){
            arr.unshift(model.addtenantName[i]);
        }
    }
    model.addtenantName1=[];
    setTimeout(function () {
        model.addtenantName1=arr;
        var count = arr.length;
        $("#blackList").pcount(count);
        if(count<30){
            model.addtenantName1=arr.slice(0,count);
        }else{
            model.addtenantName1=arr.slice(0,30);
        }
        var pageCount = Math.ceil(count / 30);
        $("#page_simple1").pcount(Math.max(1, pageCount));
    }, 0);
}

staticEvent.searchCall=function () {
    var model = tenantMngModel.instance();
    model.remotetopup=[];
    model.xremotetopup=[];
    $("#rechage").precover();
    var keyWord=$('#reachageSearch').pval().key;
         var len = model.Remotetopup1.length;
          var arr = [];
          for(var i=0;i<len;i++){
              if(model.Remotetopup1[i].tenantId.indexOf(keyWord)>=0 || model.Remotetopup1[i].tenantName.indexOf(keyWord)>=0){
                  arr.unshift(model.Remotetopup1[i]);
              }
          }
    model.Remotetopup2=[];
    setTimeout(function () {
       model.Remotetopup2=arr;
        var count = arr.length;
        $("#rechage").pcount(count);
        if(count<30){
            model.Remotetopup2=arr.slice(0,count);
        }else{
            model.Remotetopup2=arr.slice(0,30);
        }
        var pageCount = Math.ceil(count / 30);
        $("#page_simple2").pcount(Math.max(1, pageCount));

    }, 0);

}


//黑名单删除
staticEvent.deletenantnamemoda= function(){
    /* event.stopPropagation();*/
    var e=arguments.callee.caller.arguments[0] || window.event;
     var x=e.target.id
     var messageBlacklist = tenantMngModel.instance().messageBlacklist;
     
    remove(messageBlacklist, x);
    function remove(arr, item) {
        var result = [];
        for (var i = 0; i < arr.length; i++) {
            if (arr[i].tenantId != item) {
                result.push(arr[i]);
            }
        }
        tenantMngModel.instance().messageBlacklist=result;
    }
}
//远程充值黑名单删除
staticEvent.deleteRemotetopupfor= function(){
    /* event.stopPropagation();*/
    var e=arguments.callee.caller.arguments[0] || window.event;
    var x=e.target.id
    var Remotetopup = tenantMngModel.instance().Remotetopup;
    remove(Remotetopup, x);
    function remove(arr, item) {
        var result = [];
        for (var i = 0; i < arr.length; i++) {
            if (arr[i].tenantId != item) {
                result.push(arr[i]);
            }
        }
        tenantMngModel.instance().Remotetopup=result;
    }
}

//增加一条时间
staticEvent.addTimer = function(){
    var model = tenantMngModel.instance();
    var autoMsg = model.autoMsg;
    if (autoMsg.sendStuats) {
        if (autoMsg.index1=='') {
            autoMsg.index1 = 9;
            $("#autoTime1").psel(autoMsg.index1,false);
            return;
        }else if(autoMsg.index2==''){
            autoMsg.index2 = 17;
            $("#autoTime2").psel(autoMsg.index2,false);
            return;
        }
    }else{
        console.log(autoMsg.sendStuats)
    }
}
//删除一条时间
staticEvent.delTimer = function(e){
    var autoMsg = tenantMngModel.instance().autoMsg;
    autoMsg['index'+e] = '';
    $("#autoTime"+e).psel('',false);
}
//自动发送 开关
staticEvent.switchClick = function(){
    var autoMsg = tenantMngModel.instance().autoMsg;
    // console.log(autoMsg.sendStuats);
        autoMsg.sendStuats = !autoMsg.sendStuats;
        $("#autoMsgBtn").psel(autoMsg.sendStuats,false);
    if (autoMsg.sendStuats==1&&autoMsg.index1==''&&autoMsg.index2=='') {
        autoMsg.index1==9;
        $("#autoTime1").psel(9,false);
    }
}
//自动发送短信保存
staticEvent.saveTime = function (){
    var autoMsg = tenantMngModel.instance().autoMsg;
    // console.log(autoMsg);
        autoMsg.index1 = typeof(autoMsg.index1)=='number'?$("#autoTime1").psel().index:autoMsg.index1;
        autoMsg.index2 = typeof(autoMsg.index2)=='number'?$("#autoTime2").psel().index:autoMsg.index2;
        // console.log(autoMsg.index1,autoMsg.index2);

    var timeList =[];
        if (typeof(autoMsg.index1)=='number') {
            timeList.push(autoMsg.index1)
        }
        if (typeof(autoMsg.index2)=='number') {
            timeList.push(autoMsg.index2)
        }
    var params = {
        sendStuats: +autoMsg.sendStuats,
        timeList: timeList
    }
    if (autoMsg.sendStuats==1&&timeList.length==0) {
        // console.log(1111);
        $("#message").pshow({ text: "至少设置一个时间", state: "failure" });
        return;
    }

    //3.6版本保存黑名单
    var model = tenantMngModel.instance();
    var obj = {list:model.messageBlacklist}
    /*tenantCtrl.messageBlacklist();*/
    tenantCtrl.addtenantnamehideSave(obj);

    pajax.post({
        url: "FNTSendMessageSetService",
        data: params,
        success: function(){
            $("#message").pshow({ text: "设置成功", state: "success" });
            staticEvent.autoMsghide();
        },
        error: function(){
            $("#message").pshow({ text: "设置失败", state: "failure" });
        }
    })
}


//远程充值保存
staticEvent.Remotetopupsave = function (){
    //3.6远程充值保存
    var model = tenantMngModel.instance();
    var obj = {list:model.Remotetopup}
    tenantCtrl.RemotetopuphideSave(obj);
    $("#RemotetopupWindow").phide();
}


// end 201806060wp++

// 设置报警门限 - 失去焦点验证 text内容是否合法，不合法禁用保存按钮
staticEvent.validationAlarmValue = function () {
    var typeList = tenantMngModel.instance().alarmSearchData.typeList;
    var isRealGlobal = tenantMngModel.instance().alarmSearchData.isRealGlobal;
    var typeId = '';
    var typeDom = '';
    // 用于存储所有输入框的验证是否通过的值
    var verifiFlagArr = [];

    if(isRealGlobal) {
        typeDom = 'determine_global';
    } else {
        typeDom = 'determine_custom';
    }

    typeList.forEach(function (val) {
        if(isRealGlobal) {
            typeId = val.typeId;
        } else {
            typeId = 'C_' + val.typeId;
        }
        // console.log(typeId);
        verifiFlagArr.push($('#' + typeId).pverifi());
    });
    // console.log(verifiFlagArr);
    var verifiFlag = verifiFlagArr.every(function (val) {
        return val;
    });

    // if(verifiFlag) {
    //     $('#' + typeDom).pdisable(false);
    // } else {
    //     $('#' + typeDom).pdisable(true);
    // }
}

staticEvent.policehide = function () {
    $("#policeWindow").hide();
}
// 确定按钮
staticEvent.policeDetermine = function (e) {
    var flag = $(e.target).attr('data-flag') || $(e.target).parent().attr('data-flag');
    // console.log(flag);
    var typeList = tenantMngModel.instance().alarmSearchData.typeList;
    for (var i = 0; i < typeList.length; i++) {
        var item = typeList[i].alarmList;
        var verifiFlag = item.every(function (val) {
            if (val.valid) {
                $('#policeWindow'+' '+'#' + val.typeId).pverifi();
               return $('#' + val.typeId).pverifi();
            }else{
                return true;
            }
        });
        if (!verifiFlag) {
            return;
        }
    }
    tenantCtrl.setGlobalAlarmArr(flag);
}

staticEvent.upt_price_bat = function () {

    $("#floatText").precover("--");
    var tempArr = tenantMngModel.instance().checkedTenantArr;
    var tenantIdArr = [];
    for (var i = 0; i < tempArr.length; i++) {
        tenantIdArr.push(tempArr[i].tenantId);
    }
    tenantCtrl.getSamePriceBeforeUpdate(tenantIdArr);
    $("#upt_price_bat_tip").css('display', 'black');
    staticEvent.floatShow();
}
// 修改价格方案方法
staticEvent.floatShow = function () {
    $("#floatWindow").pshow({ title: '修改价格方案' });
}
staticEvent.floatHide = function () {
  console.log('123456')
    $("#floatWindow").phide();
    $("#floatWindowPrice").phide();
}

//发送充值提醒方法
staticEvent.modal2show = function () {
    //发送充值提醒判断

    var model = tenantMngModel.instance();
    if(model.checkedTenantContactArr.length < 5) {
        $('#reminder .per-scrollbar').attr('style','height:'+(model.checkedTenantContactArr.length*36)+'px !important');
        $('#reminder .per-scrollbar_wrap').attr('style','height:'+(model.checkedTenantContactArr.length*36+17)+'px !important');
    }else {
        $('#reminder .per-scrollbar').attr('style','height: 180px !important');
        $('#reminder .per-scrollbar_wrap').attr('style','height: 197px !important');
    }
    $("#modal2Window").pshow();
}
staticEvent.modal2hide = function () {
    $("#modal2Window").phide();
}


// 缴费-电  缴费页面
staticEvent.payshow = function (model, event) {
    $("#payWindow").pshow();
    tenantMngModel.instance().payBillBat_selTenant = model;
}
staticEvent.payhide = function () {
    $("#payWindow").phide();
}

// 缴费-电  缴费中
staticEvent.payInshow = function () {
    $("#payWindow").phide();
    $("#payBothWindow").phide();
    $("#payErrorWindow").phide();
    var model = tenantMngModel.instance();
    staticEvent.payInhide("show"); // 出现转圈的 loading
    tenantCtrl.payBill(model.payBillBat_selTenant, "bat_single"); //批量中的单个
}

staticEvent.payInhide = function (state) {
    if (state == "hide") {
        $("#partLoading").phide();
        $("#payInWindow").phide();
    } else {
        $("#payInWindow").pshow();
        $("#partLoading").pshow();
    }
}
staticEvent.payBothInhide = function (state) {
    if (state == "hide") {
        $("#payBothInWindow #partLoading").phide();
        $("#payBothInWindow").phide();
    } else {
        $("#payBothInWindow").pshow();
        $("#payBothInWindow #partLoading").pshow();
    }
}

// 缴费-电   缴费成功
staticEvent.paySuccessShow = function () {
    $("#paySuccessWindow").pshow();
}
staticEvent.paySuccessHide = function () {
    $("#paySuccessWindow").phide();
}

// 缴费-电 缴费失败
staticEvent.payErrorShow = function () {
    $("#payErrorWindow").pshow();
}
staticEvent.payErrorHide = function () {
    $("#payErrorWindow").phide();
}

//确定全部缴费
staticEvent.payBothOk = function () {
    staticEvent.payBothErrorHide();
    staticEvent.payBothHide();
    staticEvent.showPassWordForAllPay();
}

// 二次密码确认全部缴费
staticEvent.payBothOkSecond = function () {
    var model = tenantMngModel.instance();
    staticEvent.hidePassWordForAllPay();
    staticEvent.payBothInhide("show"); // 出现转圈的 loading
    tenantCtrl.payBill_bat();   // 缴费的请求
}

//全部缴费二次密码验证
staticEvent.showPassWordForAllPay = function () {
    $('#pay_the_fees_all').pshow();
}
staticEvent.hidePassWordForAllPay = function () {
    var model = tenantMngModel.instance();
    $('#pay_the_fees_all').phide();
    setTimeout(function () {
        model.verifyPasswordTip = '';
        model.verifyPasswordValue = '';
        model.passwordIsEmpty = false;
        model.isThroughVerification = 0;
        // $('#confirm_payment_all').pdisable(true);
    }, 500);
}

// 缴费-电  全部缴费
staticEvent.payBothShow = function () {
    $("#payBothWindow").pshow();
}
staticEvent.payBothHide = function () {
    $("#payBothWindow").phide();
}

staticEvent.payBothErrorHide = function () {
    $("#payBothErrWindow").phide();
}

staticEvent.payBothErrorShow = function () {
    $("#payBothErrWindow").pshow();
}

staticEvent.payBothSuccessHide = function () {
    $("#payBothSuccWindow").phide();
}
staticEvent.payBothSuccessShow = function () {
    $("#payBothSuccWindow").pshow();
}

// 结算  账单明细
staticEvent.billShow = function () {
    $("#billWindow").pshow();
}
staticEvent.billHide = function () {
    $("#billWindow").phide();
}


// 充值二次密码确认弹出框
staticEvent.showPassWordForSettlement = function () {
    $('#settlement_remaining').pshow();
}

staticEvent.hidePassWordForSettlement = function () {
    var model = tenantMngModel.instance();
    $('#settlement_remaining').phide();
    setTimeout(function () {
        model.verifyPasswordTip = '';
        model.verifyPasswordValue = '';
        model.passwordIsEmpty = false;
        model.isThroughVerification = 0;
        // $('#settlement_remaining_btn').pdisable(true);
    }, 500);
}

// 结算二次密码确认事件
staticEvent.settlementSendSecond = function () {
    var model = tenantMngModel.instance();
    staticEvent.hidePassWordForSettlement();
    if(model.currentPage == 'settlement') {
        tenantCtrl.saveSettle_bat();
    } else if(model.currentPage == 'settleAccounts') {
        tenantCtrl.saveSettle_single();
    }
}


//chart
staticEvent.lineChart = function (unit) {
    var _granularity = ptool.getDataGranularity($('#meterDetailCalendar'));
    var chart = new Highcharts.Chart({
        chart: {
            type: 'line',
            renderTo: 'elecMeterdetailChart',
            marginRight: 12,
            marginTop: 40,
            backgroundColor: "rgba(0,0,0,0)",
        },
        exporting: {
            enabled: false,//导出模块
        },
        //版权的一些事
        credits: {
            enabled: false,
        },
        legend: {//图例说明是包含图表中数列标志和名称的容器。
            align: 'right',
            verticalAlign: 'top',
            x: -90,
            y: -2,
            itemStyle: {
                color:'#333333',
                fontSize:'14px',
                fontWeight: '500',
                fontFamily: '微软雅黑',
                fill:'#333333',
                cursor:'default'
            },
            reversed:true
        },
        title: {
            text: '单位：' + unit,
            align: 'right',
            x: 0,
            y: 17,
            style: {
                color: '#333333',
                fontSize: '14px',
                fontWeight: '500',
                fontFamily: '微软雅黑'
            }
        },
        subtitle: {
            text: null,
        },
        xAxis: {
            // startOnTick: true,//是否强制轴线在标线处开始
            // endOnTick: true,//结束于标线；是否强制轴线在标线处结束
            maxPadding: 0,
            lineWidth: 1, //刻度线整条线的长度
            //lineColor: '#ccc',
            tickColor: '#ccc',
            tickLength: 6,
            tickmarkPlacement: 'on',
            // minorTickLength: 1,
            type: 'datetime',
            labels: {
                formatter: function () {
                    var date = new Date(this.value);
                    switch (_granularity) {
                        case 'yy': return date.format('y.M');
                        case 'y': return date.format('M.d');
                        case 'M': return date.format('M.d');
                        case 'd': return date.format('h:m');
                    }
                }
            }
        },

        yAxis: {
            title: {
                text: null,
            },
            gridLineColor: '#dddddd',//x轴网格线的颜色
            gridLineDashStyle: 'ShortDash',//x轴网格线的样式
            gridLineWidth: 1,//x轴网格线
            endOnTick: true,
            maxPadding: 0.25,
            labels: {
                formatter: function () {return this.value.toFixed(2)}
            }
        },
        tooltip: {
            enabled: true,
            animation: true,
            borderColor: null,
            borderWidth: 0,
            shadow: false,
            backgroundColor: null,
            useHTML: true,
            formatter: function () {
                var html = '<span> ' + new Date(this.x).format('y.M.d  hh:mm') + '</span><br><span> '+  (this.y).toFixed(2) + " " + unit + '</span>';
                return '<div class="chartTips">' + html + '</div>';
            },
            style: {},
        },
        plotOptions: {//绘图区选项
            spline: {
                marker: {
                    states: {
                        hover: {
                            enabled: false,
                            // fillColor:'#f77c7c'
                        }
                    },
                    radius: 0,
                }
            },
            series: {//绘图区数列
                turboThreshold: 0,
                connectNulls: false,//是否连接一条线穿过空值点。
                stickyTracking: false,//粘连追踪
                events: {//节点事件
                    afterAnimate: function () {//已经完成了最初的动画,或在动画是禁用的情况下,立即显示系列。4.0版本以上才有哦
                        // if (itemExtreme == true) {
                        //     chartExtremeValue(chart);
                        // } else {
                        //     return;
                        // }
                    },
                    legendItemClick:function () {
                      return false;
                    }, show: function () {
                        // console.log(111);
                    }
                }
            }
        },
        series: []
        //        {
        //        name: '',
        //        color: '#07ABD2',
        //        data: []
        //}
    });
    return chart;
};

staticEvent.elecMeterdetailChart = null;

//结算成功
staticEvent.settleState = function (state) {
    var id = state == "success" ? "settle_success" : "settle_failure";
    $("#" + id).show().siblings().hide();
}

//结算成功
staticEvent.bat_settleState = function (state) {
    // $("#bat_settle_content").hide();
    if (state == "success") {
        // $("#bat_settle_failure").hide();
        // $("#bat_settle_success").show();
        common.msgSuccess("结算成功！");
        tenantCtrl.getTenantArr();
        ldp.backToTemenListPage();
    } else {
        common.msgFailure("结算失败，请重新结算！");
        // $("#bat_settle_success").hide();
        // $("#bat_settle_failure").show();

    }
}

//重新结算
staticEvent.reSettle = function () {
    // $("#settle_content").show().siblings().hide();
    tenantCtrl.saveSettle_single();
}


//重新结算
staticEvent.bat_reSettle = function () {
    // $("#bat_settle_success").hide();
    // $("#bat_settle_failure").hide();
    // $("#bat_settle_content").show();
    tenantCtrl.saveSettle_bat();
}


// 判断换表时输入读数是否符合要求
staticEvent.isMeterOk = function (functionId) {
    var model = tenantMngModel.instance();
    var changeMeter = model.changeMeter;
    var errNumber = 0;//定义错误数
    var check = /^\d+(\.{0,1}\d+){0,1}$/;  //非负数正则
    // 非多费率电表验证
    if(changeMeter.functionList.length == 1) {

        var value = $('#_' + functionId).pval();

        if(check.test(value)) {
            changeMeter.isMeter = false;
        }else {
            changeMeter.isMeter = true;
        }

    }else {
        // 多费率电表验证
        for(var i = 0; i < changeMeter.functionList.length; i++) {
            var functionItem = changeMeter.functionList[i];

            var value = $('#_' + functionItem.functionId).pval();

            if (!check.test(value)) {
                changeMeter.isMeter = true;
                errNumber ++ ;
            }else {
                if(errNumber == 0) {
                    changeMeter.isMeter = false;
                }
            }
        }
    }

}

// 选择表号
staticEvent.selMeterId = function () {
    var selTenant = tenantMngModel.instance().selTenant;
    $("#allMeter li").each(function(){
        if ($(this).psel()) {
            selTenant.selectedMeterId = $(this).text();
        }
    });

}

// 打开异常账单列表
staticEvent.abnormalBillShow = function(){
    tenantMngModel.instance().abnormalBillList = [];
    tenantMngModel.data.currentPage = 'abnormalBill';
    tenantMngModel.instance().$nextTick(function(){
        $("#abnormalBillEnergyCombo").psel('全部',true);
        $("#abnormalBillTypeCombo").psel('全部',true);
        $("#abnormalBillPage").pcount(1,false);
        $("#abnormalBillPage").psel(1,false);
    });
}

// 获取异常账单列表
staticEvent.getAbnormalBillList = function(isPage,event){
    var index = isPage ? event.pEventAttr.pageIndex - 1 : 0;
    var param = {
        "energyTypeId" : $("#abnormalBillEnergyCombo").psel().id === 'null' ? null : $("#abnormalBillEnergyCombo").psel().id,
        "status" : $("#abnormalBillTypeCombo").psel().id === 'null' ? null : Number($("#abnormalBillTypeCombo").psel().id),
        "timeFrom": new Date($("#abnormalBillTime").psel().startTime).format('yyyy-MM-dd hh:mm:ss'),
        "timeTo": new Date($("#abnormalBillTime").psel().endTime).format('yyyy-MM-dd hh:mm:ss'),
        "pageIndex": index,
        "pageSize": 15,
        'isPage': isPage
    };
    tenantCtrl.abnormalBillList(param);
}

// 异常账单处理
staticEvent.abnormalBillOperate = function(model,type){
    model.operateType = type;
    tenantMngModel.data.abnormalBillInfo = JSON.parse(JSON.stringify(model));
    $("#abnormalBillPassword").pval('');
    if(type === 0){
        $("#abnormalBillConfirmWindow").pshow({title: '重新充值'});
    }else{
        $("#abnormalBillConfirmWindow").pshow({title: '关闭'});
    };
}
// 关闭重新充值弹窗
staticEvent.closeAbnormalBillWindow = function(){
    $("#abnormalBillConfirmWindow").phide();
}
// 重新充值/关闭 弹窗确认
staticEvent.abnormalBillSubmit = function(){
    $("#gloadLoading").pshow();
    tenantCtrl.userVerify({
        password: $("#abnormalBillPassword").pval()
    },function(data){
        if(data == 1){
            tenantCtrl.errOrderDispoes({
                "orderId": tenantMngModel.data.abnormalBillInfo.orderId,                //类型：String  必有字段  备注：账单Id
                "type": tenantMngModel.data.abnormalBillInfo.operateType                //类型：Number  必有字段  备注：0再次处理,1关闭
            },function(){
                $("#gloadLoading").phide();
                $("#message").pshow({ text: "操作成功", state: "success" });
                $("#abnormalBillConfirmWindow").phide();
            },function(){
                $("#gloadLoading").phide();
                $("#message").pshow({ text: "操作失败", state: "failure" });
            });
        }else{
            $("#gloadLoading").phide();
            $("#message").pshow({ text: "密码错误", state: "failure" });
        }
    });
}

