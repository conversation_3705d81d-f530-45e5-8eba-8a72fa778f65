// <reference path="../common/common.js" />
// <reference path="model.js" />

// <reference path="http://localhost:1379/pweb_commonLib/public/scripts/tool/pajax.js" />
// <reference path="http://localhost:1379/pweb_commonLib/public/scripts/tool/ptool.js" />
// <reference path="http://localhost:1379/pweb_commonLib/public/scripts/extend/String.js" />
// <reference path="http://localhost:1379/pweb_commonLib/public/scripts/extend/Number.js" />
// <reference path="http://localhost:1379/pweb_commonLib/public/scripts/extend/Date.js" />
// <reference path="http://localhost:1379/pweb_commonLib/public/pcontrol/flatBlueSeries_min.js" />
// <reference path="http://localhost:1379/pweb_commonLib/public/scripts/tool/asynTool.js" />

function tenantCtrl() {}
$(function () {
    // tenantCtrl.setPageSizeFunction(100);
    tenantCtrl.initialize();
    // tenantCtrl.getPdfIpConfig(); // 获取pdf打印的配置地址
    //tenantCtrl.getPageSizeDefault(); // 获取分页器默认值配置
    tenantCtrl.getTenantTypeArr();
    tenantCtrl.getEnergyTypeArr();
    tenantCtrl.getOperationPermissionsInfo(); // 获取用户权限

    $('#grid1 .icon').addClass('sortGray');

    //确定每页行数支持回车
    $('#page_text').keydown(function (e) {
        if (e.keyCode == 13) {
            staticEvent.verifyPageSizeEvent();
            tenantCtrl.setPageSize();
        }
    });
    //确定每页行数支持回车
    $('#sr_page_text').keydown(function (e) {
        if (e.keyCode == 13) {
            tenantCtrl.setPageSize_sr();
        }
    });
    $('#page_text1').keydown(function (e) {
        if (e.keyCode == 13) {
            tenantCtrl.setPageSize1();
        }
    });
});

// 首页列表显示问题解决方法
tenantCtrl.gridHeight = function () {
    var model = tenantMngModel.instance();

    var gridHeight = $('#grid1 .per-grid-dynamic_wrap').outerHeight() - 102; //表格主体高度
    // console.log(gridHeight);

    $('#grid1 .per-grid-dynamic_wrap .per-grid-dynamic_con').attr('style', 'max-width: 100%; max-height:' + (model.tenantArr.length * 36 + 1) + 'px !important');
    $('#grid1 .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar').attr('style', 'max-width: 100%; max-height:' + (model.tenantArr.length * 36) + 'px !important');
    $('#grid1 .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar_wrap').attr('style', 'max-width: calc(100% + 17px); height:' + (model.tenantArr.length * 36 + 17) + 'px !important');

    if (gridHeight < model.tenantArr.length * 36) {
        $('#grid1 .per-grid-dynamic_wrap .per-grid-dynamic_con').attr('style', 'max-width: 100%; max-height:' + gridHeight + 'px !important');
        $('#grid1 .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar').attr('style', 'max-width: 100%; max-height:' + (gridHeight - 1) + 'px !important');
        $('#grid1 .per-grid-dynamic_wrap .per-grid-dynamic_con .per-scrollbar_wrap').attr('style', 'max-width: calc(100% + 17px); height:' + (gridHeight + 16) + 'px !important');
    }
}

//初始化
tenantCtrl.initialize = function () {
    //
    tenantCtrl.asynTool = new AsynTool();
    tenantCtrl.asynTool.all("tenantCtrl.getBuildingArr", "tenantCtrl.getEnergyTypePayTypeArr", "tenantCtrl.getPageSizeDefault", function () {
        Vue.nextTick(function () {
            $("#t_building_cbx").psel(0, false); //默认选择全部建筑
            $("#t_energy_type").psel(0, false); //能耗类型默认选择全部
        });
        tenantCtrl.getTenantArr();
        tenantCtrl.messageBlacklist();
        tenantCtrl.addRemotetopup();
        tenantCtrl.addtenantname();

    });
    setTimeout(function () {
        $("#t_status_cbx").psel(1, false); //租户状态默认选择已激活
    }, 0);
    tenantCtrl.getBuildingArr();
    tenantCtrl.getEnergyTypePayTypeArr();
    tenantCtrl.getPageSizeDefault();
}

// 设置页面分页器初始值方法
tenantCtrl.setPageSizeFunction = function (num) {
    this.pageSize = num; //默认30条
    this.pageSize_sr = num; //默认30条
    this.pageSize1 = num; //默认30条
    if (localStorage) { //支持本地存储
        var simple_pageCount = window.localStorage.getItem("simple_pageCount");
        if (simple_pageCount) {
            simple_pageCount = simple_pageCount.trim();
            if (simple_pageCount.pisPositiveInt()) {
                this.pageSize = parseInt(simple_pageCount);
            } else {
                window.localStorage.setItem("simple_pageCount", this.pageSize);
            }
        } else {
            window.localStorage.setItem("simple_pageCount", this.pageSize);
        }

        var simple_pageCount_sr = window.localStorage.getItem("simple_pageCount_sr");
        if (simple_pageCount_sr) {
            simple_pageCount_sr = simple_pageCount_sr.trim();
            if (simple_pageCount_sr.pisPositiveInt()) {
                this.pageSize_sr = parseInt(simple_pageCount_sr);
            } else {
                window.localStorage.setItem("simple_pageCount_sr", this.pageSize_sr);
            }
        } else {
            window.localStorage.setItem("simple_pageCount_sr", this.pageSize_sr);
        }

        var simple_pageCount1 = window.localStorage.getItem("simple_pageCount1");
        if (simple_pageCount1) {
            simple_pageCount1 = simple_pageCount1.trim();
            if (simple_pageCount1.pisPositiveInt()) {
                this.pageSize1 = parseInt(simple_pageCount1);
            } else {
                window.localStorage.setItem("simple_pageCount1", this.pageSize1);
            }
        } else {
            window.localStorage.setItem("simple_pageCount1", this.pageSize1);
        }
    };
    $("#page_text").pval(tenantCtrl.pageSize);
    $("#sr_page_text").pval(tenantCtrl.pageSize_sr);
    $("#page_text1").pval(tenantCtrl.pageSize1);
}

/**新增***/
// 获取pdf配置服务器地址方法
// tenantCtrl.getPdfIpConfig = function () {
//     var model = tenantMngModel.instance();
//     $.ajax({
//         url: '/pdfipconfig',
//         data: {},
//         success: function (resJson) {
//             var resJson = resJson || '';
//             model.pdfIpConfig = resJson;
//             // console.log(resJson);
//         },
//         error: function () {
//             common.msgFailure("获取pdf配置项失败");
//         }
//     });
// }

// 获取分页器配置项的方法
tenantCtrl.getPageSizeDefault = function () {
    var model = tenantMngModel.instance();
    $.ajax({
        url: '/pagesizedefault',
        data: {},
        success: function (resJson) {
            var resJson = resJson || 100;
            model.pageSizeDefault = parseInt(resJson);
            // console.log( model.pageSizeDefault);
            tenantCtrl.setPageSizeFunction(model.pageSizeDefault);
        },
        complete: function () {
            tenantCtrl.asynTool.fire("tenantCtrl.getPageSizeDefault", 3);
        },
        error: function () {
            common.msgFailure("获取分页器初始值失败");
        }
    });
}

// 获取用户权限信息
tenantCtrl.getOperationPermissionsInfo = function () {
    var model = tenantMngModel.instance();
    pajax.get({
        url: "FNTCommonUserPermissionService",
        data: {},
        success: function (resJson) {
            var resJson = resJson || [];
            resJson.forEach(function (val) {
                model.operationPermissions[val.permissionId] = val.isHave;
            });
            //  console.log(resJson);
            // console.log(model.operationPermissions);
        },
        error: function () {
            common.msgFailure("获取用户权限信息失败");
        }
    });
}

// 报警门限查询（物业监控统一）
tenantCtrl.getGlobalAlarmArr = function (flag) {
    var model = tenantMngModel.instance();
    model.alarmSearchData = {};
    var params = {};
    $('#ZHBJ_15').precover();
    $('#ZHBJ_05').precover();
    $('#ZHBJ_07').precover();
    $('#ZHBJ_09').precover();
    $('#C_ZHBJ_15').precover();
    $('#C_ZHBJ_05').precover();
    $('#C_ZHBJ_07').precover();
    $('#C_ZHBJ_09').precover();
    $('#determine_global').pdisable(false);
    $('#determine_custom').pdisable(false);

    if (flag == 'custom') {
        params.buildingId = model.selTenant.buildingId;
        params.tenantId = model.selTenant.tenantId;
    }
    pajax.get({
        url: "FNTAlarmLimitQueryUnifiedService",
        data: params,
        success: function (resJson) {
            var resJson = resJson || [];
            if (resJson instanceof Array && resJson.length != 0) {
                //20180606wp
                model.alarmSearchData = resJson[0];
                if (flag == 'custom') {
                    model.alarmSearchData.isRealGlobal = false;
                } else {
                    model.alarmSearchData.isRealGlobal = true;
                }
            }
        },
        error: function () {
            common.msgFailure("获取报警门限失败");
        }
    })
}


// 报警门限保存（物业监控统一）
tenantCtrl.setGlobalAlarmArr = function (flag) {
    var model = tenantMngModel.instance();
    var params = tenantMngModel.instance().alarmSearchData;
    params.buildingId = model.selTenant.buildingId || null;
    params.tenantId = model.selTenant.tenantId || null;
    if (flag == 'custom' && !params.isRealGlobal) {
        params.setUp = 1;
    } else if (flag == 'global') {
        params.setUp = 0;
        params.buildingId = null;
        params.tenantId = null;
    } else {
        params.setUp = 0;
    }
    //临时列表
    var temTypeList = [];
    for (var i = 0; i < params.typeList.length; i++) {
        var cur1 = params.typeList[i];
        for (var j = 0; j < cur1.alarmList.length; j++) {
            var cur2 = cur1.alarmList[j]
            temTypeList.push(cur2);
        }
    }
    //改变结构的params
    var paramsSend = {
        buildingId: params.buildingId,
        tenantId: params.tenantId,
        setUp: params.setUp,
        typeList: temTypeList
    }
    pajax.post({
        url: 'FNTAlarmLimitUpdateUnifiedService',
        data: paramsSend,
        success: function (resJson) {
            $("#policeWindow").hide();
            // $('#custom_radio').psel(true, true);
            model.customAlarmPanelIsShow = false;
            common.msgSuccess("保存成功");
        },
        error: function () {
            common.msgFailure("设置报警门限失败");
        }
    })
}

// 获取剩余量/金额/天数数据
tenantCtrl.getRemainingAmountReport = function () {
    var model = tenantMngModel.instance();
    var params = JSON.parse(JSON.stringify(model.remainingAmountParams));
    var fName = 'FNTBatchRemainGridService';
    var notice = '';
    common.showPartLoading();
    model.currentPage = "remainingAmountReport";
    switch (model.selShowData.id) {
        case -1:
            notice = '天数';
            break;
        case 0:
            notice = '量';
            break;
        case 1:
            notice = '金额';
            break;
    }
    // console.log(params);
    if (params.isDownload == 0) {
        $('#rArScroll').psetScroll(0);
        pajax.post({
            url: fName,
            data: params,
            success: function (resJson) {
                var resJson = resJson || [];
                if (resJson instanceof Array && resJson.length != 0) {
                    var remainingAmountReportList = model.remainingAmountReportList = resJson[0];
                    remainingAmountReportList.notice = notice;
                    remainingAmountReportList.remainType = params.remainType;
                    var tenantList = remainingAmountReportList.tenantList;
                    if (tenantList.length != 0) {
                        var boxHeight = tenantList.length * 37;
                        if (boxHeight > $('.r_a_r_gird').outerHeight() - $('.r_a_r_gird_tit').outerHeight()) {
                            boxHeight = $('.r_a_r_gird').outerHeight() - $('.r_a_r_gird_tit').outerHeight() - 2;
                        }
                        $('.r_a_r_gird_body').outerHeight(boxHeight);
                    } else {
                        $('.r_a_r_box .per-grid-nodata').show();
                    }
                }

            },
            error: function () {
                common.msgFailure("获取剩余" + notice + "失败");
            },
            complete: function () {
                common.hidePartLoading();
            }
        });
    } else {
        pajax.downloadByParam(fName, params);
        model.isDownload = 0;
        common.hidePartLoading();
    }
}
// 获取表底数记录
tenantCtrl.getGaugeRecordData = function () {
    var model = tenantMngModel.instance();
    var params = JSON.parse(JSON.stringify(model.gaugeRecordParams));
    var fName = 'FNTBatchMeterDataGridService';
    common.showPartLoading();
    if (params.isDownload == 0) {
        $('#GRScroll').psetScroll(0);
        pajax.post({
            url: fName,
            data: params,
            success: function (resJson) {
                var resJson = resJson || [];
                var gaugeRecordArr = model.gaugeRecordArr = resJson;
                if (resJson instanceof Array && resJson.length != 0) {
                    var numArrOne = [],
                        numArrTwo = [];
                    for (var i = 0; i < resJson.length; i++) {
                        var roomList = resJson[i].roomList;
                        for (var j = 0; j < roomList.length; j++) {
                            var meterList = roomList[j].meterList;
                            numArrOne.push(meterList.length);
                            for (var k = 0; k < meterList.length; k++) {
                                var energyUnit = meterList[k].energyUnit;
                                var meterType = meterList[k].meterType;
                                var list = meterList[k].list;
                                numArrTwo.push(list.length);
                                for (var l = 0; l < list.length; l++) {
                                    list[l].energyUnit = energyUnit;
                                    list[l].meterType = meterType;
                                    if (list[l].value != null) {
                                        list[l].value = list[l].value.toFixed(2);
                                    }
                                }
                            }
                        }
                    }


                    Vue.nextTick(function () {
                        for (var i = 1; i < 7; i++) {
                            var count = 0;
                            $('.g_r_box .room_code_' + i).each(function (idx) {
                                var sum = 0;
                                for (var j = 0; j < numArrOne[idx]; j++) {
                                    sum += numArrTwo[count];
                                    count++;
                                }
                                $(this).css('height', sum * 36 + 'px');
                            });
                            $('.g_r_box .sort_code_' + i).each(function (idx) {
                                $(this).css('height', numArrTwo[idx] * 36 + 'px');
                            });
                        }

                        if (gaugeRecordArr.length != 0) {
                            var heightArr = [];

                            $('.g_r_gird_item_box').find('li').each(function () {
                                heightArr.push($(this).outerHeight());
                            })

                            var boxHeight = heightArr.reduce(function (prev, cur, index, arr) {
                                return prev + cur;
                            });

                            if (boxHeight > $('.g_r_gird').outerHeight() - $('.g_r_gird_tit').outerHeight()) {
                                boxHeight = $('.g_r_gird').outerHeight() - $('.g_r_gird_tit').outerHeight();
                            }
                            $('.g_r_gird_body').outerHeight(boxHeight + 1);
                            $('.g_r_box .per-grid-nodata').hide();
                        } else {
                            $('.g_r_box .per-grid-nodata').show();
                        }
                    });

                } else {
                    $('.g_r_gird_body').css('height', '');
                    $('.g_r_box .per-grid-nodata').show();
                }
            },
            error: function () {
                common.msgFailure("获取表底数失败");
            },
            complete: function () {
                common.hidePartLoading();
            }
        });
    } else {
        pajax.downloadByParam(fName, params);
        model.isDownload = 0;
        common.hidePartLoading();
    }


}

// 获取表底数记录（单租户）
tenantCtrl.getGaugeSingleTenantRecordData = function () {
    var model = tenantMngModel.instance();
    var params = JSON.parse(JSON.stringify(model.gaugeRecordSingleTenantParams));
    var fName = 'FNTMeterDataGridService';
    common.showPartLoading();
    if (params.isDownload == 0) {
        $('#GRSTScroll').psetScroll(0);
        pajax.post({
            url: fName,
            data: params,
            success: function (resJson) {
                var resJson = resJson || [];
                var gaugeRecordSingleTenantArr = model.gaugeRecordSingleTenantArr = resJson;
                if (resJson instanceof Array && resJson.length != 0) {
                    var numArrOne = [],
                        numArrTwo = [];

                    for (var i = 0; i < resJson.length; i++) {
                        var meterList = resJson[i].meterList;
                        numArrOne.push(meterList.length);
                        for (var j = 0; j < meterList.length; j++) {
                            var energyUnit = meterList[j].energyUnit;
                            var meterType = meterList[j].meterType;
                            var list = meterList[j].list;
                            numArrTwo.push(list.length);
                            for (var k = 0; k < list.length; k++) {
                                list[k].energyUnit = energyUnit;
                                list[k].meterType = meterType;
                                if (list[k].value != null) {
                                    list[k].value = list[k].value.toFixed(2);
                                }
                            }
                        }
                    }

                    Vue.nextTick(function () {

                        for (var i = 1; i < 6; i++) {
                            $('.g_r_s_t_box .sort_code_' + i).each(function (idx) {
                                $(this).css('height', numArrTwo[idx] * 36 + 'px');
                            });
                        }

                        if (gaugeRecordSingleTenantArr.length != 0) {
                            var heightArr = [];

                            $('.g_r_s_t_gird_item_box').find('li').each(function () {
                                heightArr.push($(this).outerHeight());
                            })

                            var boxHeight = heightArr.reduce(function (prev, cur, index, arr) {
                                return prev + cur;
                            });

                            if (boxHeight > $('.g_r_s_t_gird').outerHeight() - $('.g_r_s_t_gird_tit').outerHeight()) {
                                boxHeight = $('.g_r_s_t_gird').outerHeight() - $('.g_r_s_t_gird_tit').outerHeight();
                            }
                            $('.g_r_s_t_gird_body').outerHeight(boxHeight + 1);
                            $('.g_r_s_t_box .per-grid-nodata').hide();
                        } else {
                            $('.g_r_s_t_box .per-grid-nodata').show();
                        }
                    });

                } else {
                    $('.g_r_s_t_gird_body').css('height', '');
                    $('.g_r_s_t_box .per-grid-nodata').show();
                }
            },
            error: function () {
                common.msgFailure("获取表底数失败");
            },
            complete: function () {
                common.hidePartLoading();
            }
        });
    } else {
        pajax.downloadByParam(fName, params);
        model.isDownload = 0;
        common.hidePartLoading();
    }
}

tenantCtrl.verifyPasswordEvent = function (selector) {
    // 密码 60s验证方法
    function timer () {
        var storage = window.localStorage;
        var newTenantId = storage.getItem("newTenantId");
        storage.setItem(newTenantId, 60);
        var total = storage.getItem(newTenantId);
        if (newTenantId != undefined && newTenantId != 'NaN' && newTenantId != 'null') {
            //cookie存在倒计时          
            if (total != undefined && total != 'NaN' && total != 'null') {
                //cookie存在倒计时          
                timekeeping();
            } else {
                //cookie 没有倒计时
                $("#recharge_details").pdisable(true);
            }
        } else {
            //cookie 没有倒计时
            $("#recharge_details").pdisable(true);
        }

        function timekeeping() {
            //把按钮设置为不可以点击
            $("#recharge_details").pdisable(true);
            tenantCtrl.interval = setInterval(function () {
                //每秒读取一次cookie
                //从cookie 中读取剩余倒计时
                // total=total; 
                //在发送按钮显示剩余倒计时
                $("#recharge_details").text(total + '秒后重新充值');
                //把剩余总倒计时减掉1
                total--;
                if (total == 0) { //剩余倒计时为零，则显示 重新发送，可点击
                    //清除定时器
                    clearInterval(tenantCtrl.interval);
                    //删除cookie
                    storage.removeItem(newTenantId, total);

                    //显示重新发送
                    $("#recharge_details").text('重新充值');
                    //把发送按钮设置为可点击
                    $("#recharge_details").pdisable(false);;
                } else { //剩余倒计时不为零
                    $("#recharge_details").pdisable(true);
                    //重新写入总倒计时
                    storage.getItem(newTenantId, total);

                }
            }, 1000);
        }
    }

    $('#' + selector).pdisable(true);
    var model = tenantMngModel.instance();
    var password = model.verifyPasswordValue;
    if (password == '') {
        model.isThroughVerification = 1;
        model.passwordIsEmpty = true;
        setTimeout(function () {
            $('#' + selector).pdisable(false);
        }, 1000);
        return;
    }

    pajax.post({
        url: "FNTCommonCheckPasswordService",
        data: {
            password: password
        },
        success: function (resJson) {
            var resJson = resJson || [];
            model.isThroughVerification = resJson[0].status;
            model.verifyPasswordTip = password;
            if (selector && !resJson[0].status) {
                $('#' + selector).pdisable(true);
                model.isThroughVerification = 0;
                model.passwordIsEmpty = false;
                return;
            } else {
                $('#' + selector).pdisable(false);
                model.isThroughVerification = 1;
                model.passwordIsEmpty = false;
                switch (selector) {
                    case 'refund_cost_details': // 退费
                        staticEvent.refund_cost_second();
                        break;
                    case 'settlement_remaining_btn': // 结算
                        staticEvent.settlementSendSecond();
                        break;
                    case 'recharge_details': // 充值
                        timer();
                        staticEvent.recharge_second(tenantMngModel.data.prePayTypeForPassword == 1 ? 'charge_soft_meter' : 'charge_soft_soft');
                        break;
                    case 'confirm_payment_list': // 缴费
                        staticEvent.payInshowSecond();
                        break;
                    case 'confirm_payment_all': // 全部缴费
                        staticEvent.payBothOkSecond();
                        break;
                    case 'confirm_meter_set_change': // 仪表设置
                        model.meterSetStep = 4;
                        model.meterSetState = 0;
                        tenantCtrl.sendInstrumentalInfo(model.meterIdForMeterSet, model.meterSetType, model.instrumentalData.iptValue);
                        break;
                }
            }
        },
        error: function () {
            common.msgFailure("密码验证失败");
        },
        complete: function () {
            setTimeout(function () {
                $('#' + selector).pdisable(false);
            }, 3000);
        }
    });
}

//确认密码获取焦点事件（清空提示框）
tenantCtrl.inputFocus = function () {
    var model = tenantMngModel.instance();
    model.isThroughVerification = 1;
    model.passwordIsEmpty = false;
}

// 退费前查询
tenantCtrl.getBeforeRefundCostData = function (energyTypeId, value, prePayType, meterId) {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    var energyTypeName = tenantCtrl.getEnergyTypeName(energyTypeId);
    var recharge = {
        energyTypeId: energyTypeId,
        energyTypeName: energyTypeName,
        meterId: meterId,
        billingType: "",
        remainData: "",
        dataUnit: "",
        refreshTime: "",
        typeName: energyTypeName + "-预付费-" + (prePayType == 1 ? "软件充表扣" : "软件充软件扣"),
        prePayType: prePayType,
        orgRemainData: "",
        rechargeValue: "",
        weiDaoZhangCount: ""
    }
    selTenant.recharge = recharge;
    pajax.post({
        url: 'FNTReturnBeforePayService',
        data: {
            tenantId: model.selTenant.tenantId,
            energyTypeId: energyTypeId,
            prePayType: prePayType,
            meterId: meterId
        },
        success: function (resJson) {
            var resJson = resJson || [];
            if (resJson instanceof Array && resJson.length != 0) {
                var beforeRefundCostData = model.beforeRefundCostData = resJson[0];
                beforeRefundCostData.energyTypeName = energyTypeName;
                beforeRefundCostData.isRefundCost = true;
                beforeRefundCostData.typeName = energyTypeName + "-预付费-软件充软件扣";
                beforeRefundCostData.energyTypeId = energyTypeId;
                beforeRefundCostData.prePayType = prePayType;
                model.$set(beforeRefundCostData, 'refundValue', value);
            }
            //退费 全部金额
            if (resJson[0].remainData == null) {
                $(".allAmount").css("display", "none").unbind("click");
            }
            staticEvent.allAmount = function () {
                prePayType == 2 ? $("#refund_soft_soft").pval(resJson[0].remainData) : $("#refund_meter_meter").pval(resJson[0].remainData);
            }
        },
        error: function () {
            common.msgFailure("服务异常,退费失败");
        },
        complete: function () {
            $("#partLoadingSoft").phide();
            $("#partLoadingMeter").phide();
        }
    });
}

// 退费并保存记录
tenantCtrl.tenantRefundCost = function (value) {
    var model = tenantMngModel.instance();
    var beforeRefundCostData = model.beforeRefundCostData;
    var prePayType = beforeRefundCostData.prePayType;
    var params = {
        tenantId: model.selTenant.tenantId,
        energyTypeId: beforeRefundCostData.energyTypeId,
        value: beforeRefundCostData.refundValue,
        remainData: beforeRefundCostData.remainData,
        billingType: beforeRefundCostData.billingType,
        meterId: beforeRefundCostData.meterId,
        prePayType: prePayType,
    };
    pajax.post({
        url: 'FNTReturnPayService',
        data: params,
        success: function (resJson) {
            var resJson = resJson || [];
            if (resJson instanceof Array && resJson.length != 0) {
                var resCode = resJson[0].result;
                if (prePayType == 1) {
                    if (resCode == 0) {
                        staticEvent.showRefundCostMeterSuccessEvent(); //表扣
                    }
                    if (resCode == 2) {
                        common.msgFailure("不支持此类型");
                    }
                }
                if (prePayType == 2) {
                    if (resCode == 0) {
                        staticEvent.showRefundCostSuccessEvent(); //软扣
                    }
                }
            } else {
                staticEvent.showRefundCostErrorEvent();
            }

        },
        error: function () {
            common.msgFailure("服务异常,退费失败");
        },
        complete: function () {
            staticEvent.refundCostLoadingEvent('hide');
        }
    });
}

// 查看退费记录（单租户）
tenantCtrl.goRemainingAmountReportPage = function (energyTypeId, num, prePayType) {
    common.showPartLoading();
    var model = tenantMngModel.instance();
    model.prePayType = prePayType;
    model.energyCosttempId = num;
    var energyCostReport = new energyCostReportVM();
    energyCostReport.energyTypeId = energyTypeId;
    model.selTenant.energyCostReport = energyCostReport;
    model.currentPage = "energyCostReport";
    model.pdfPage = "returnPremiumRecord";
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth();
    var startTime = new Date(y, m, 1, 0, 0, 0).getTime();
    var endTime = new Date(y, m, date.getMonthLength(), 0, 0, 0).getTime();
    Vue.nextTick(function () {
        $("#reportCalendar").psel({
            startTime: startTime,
            endTime: endTime
        }, false);
        model.selTenant.energyCostReport.timeFrom = new Date($('#reportCalendar').psel().startTime).format('y.M.d');
        model.selTenant.energyCostReport.timeTo = new Date($('#reportCalendar').psel().endTime).format('y.M.d');
        // 获取退费记录
        tenantCtrl.getReturnPremiumRecord();
    });
}

// 获取退费记录（单租户）
tenantCtrl.getReturnPremiumRecord = function () {
    var timeObj = $("#reportCalendar").psel();
    var timeFrom = new Date(timeObj.startTime).format("y-M-d h:m:s");
    var timeTo = new Date(timeObj.realEndTime).format("y-M-d h:m:s");
    var selTenant = tenantMngModel.instance().selTenant;
    var report = selTenant.energyCostReport;
    report.timeFrom = new Date(timeObj.startTime).format("y.M.d");
    report.timeTo = new Date(timeObj.endTime).format("y.M.d");
    var energyTypeId = report.energyTypeId;
    report.energyTypeName = tenantCtrl.getEnergyTypeName(energyTypeId);
    var param = {
        "energyTypeId": energyTypeId,
        "tenantId": selTenant.tenantId,
        "timeFrom": timeFrom,
        "timeTo": timeTo,
    };
    pajax.get({
        url: "FNTReturnRecordService",
        data: param,
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var result = resJson[0];
                report.billingType = result.billingType;
                report.billingTypeUnit = result.billingTypeUnit;
                report.dataList = result.dataList;
            }
        },
        error: function () {
            common.msgFailure("获取租户退费记录失败");
        },
        complete: function () {
            common.hidePartLoading();
        }
    });
}

// 进入退费记录（批量）
tenantCtrl.goRefundRent_bat = function () {
    var model = tenantMngModel.instance();
    var timeController = 'refund_rent_time_controller';
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth();
    var startTime = new Date(y, m, 1, 0, 0, 0).getTime();
    var endTime = new Date(y, m, date.getMonthLength(), 0, 0, 0).getTime();

    Vue.nextTick(function () {
        $("#" + timeController).psel({
            startTime: startTime,
            endTime: endTime
        }, false);
        tenantCtrl.getRefundRent_bat(false, timeController);
    });
}

// 获取退费记录（批量）
tenantCtrl.getRefundRent_bat = function (isDownload, timeController) {
    var model = tenantMngModel.instance();

    var fName = 'FNTBatchReturn3GridService';
    model.currentPage = "refundRentRecord";

    var timeObj = $("#" + timeController).psel();
    var timeFrom = new Date(timeObj.startTime).format("y-M-d h:m:s");
    var timeTo = new Date(timeObj.realEndTime).format("y-M-d h:m:s");
    var checkedTenantArr = model.checkedTenantArr;
    var t_map = {};
    var tenantIdList = [];
    for (var i = 0; i < checkedTenantArr.length; i++) {
        var t_i = checkedTenantArr[i];
        var tenantId = t_i.tenantId;
        tenantIdList.push(tenantId);
        if (!isDownload) {
            t_i.energyCostDataList = [];
            t_map[tenantId] = t_i;
        }
    }

    var param = {
        "timeType": ldp.downExcTimeType(timeObj.timeType),
        "energyTypeId": model.energyType,
        "timeFrom": timeFrom,
        "timeTo": timeTo,
        tenantList: tenantIdList,
        isDownload: isDownload ? 1 : 0
    }

    common.showPartLoading();

    if (isDownload) {
        pajax.downloadByParam(fName, param);
        common.hidePartLoading();
    } else {
        pajax.get({
            url: fName,
            data: param,
            success: function (resJson) {
                resJson = resJson || [];
                if (resJson instanceof Array && resJson.length > 0) {
                    var refundRentRecordsData = model.refundRentRecordsData = resJson[0];
                    var tenantList = refundRentRecordsData.tenantList;
                    var numArrOne = [];

                    for (var i = 0; i < tenantList.length; i++) {
                        var orderList = tenantList[i].orderList;
                        numArrOne.push(orderList.length || 1);
                    }

                    Vue.nextTick(function () {

                        for (var i = 1; i < 7; i++) {
                            $('.r_r_r_box .room_code_' + i).each(function (idx) {
                                $(this).css('height', (numArrOne[idx] || 1) * 36 + 'px');
                            });
                        }

                        if (tenantList.length != 0) {
                            var heightArr = [];

                            $('.r_r_r_gird_item_box').find('li').each(function () {
                                heightArr.push($(this).outerHeight());
                            })

                            var boxHeight = heightArr.reduce(function (prev, cur, index, arr) {
                                return prev + cur;
                            });

                            if (boxHeight > $('.r_r_r_gird').outerHeight() - $('.r_r_r_gird_tit').outerHeight()) {
                                boxHeight = $('.r_r_r_gird').outerHeight() - $('.r_r_r_gird_tit').outerHeight();
                            }
                            $('.r_r_r_gird_body').outerHeight(boxHeight + 1);
                        } else {
                            $('.r_r_r_box .per-grid-nodata').show();
                        }
                    });
                } else {
                    common.msgFailure("获取退费记录失败");
                }
            },
            error: function () {
                common.msgFailure("获取退费记录失败");
            },
            complete: function () {
                common.hidePartLoading();
            }
        });
    }
}

// 仪表设置功能查询
tenantCtrl.getInstrumentalSupportFunctions = function (roomCode, energyTypeName, meterId, energyTypeId) {
    var model = tenantMngModel.instance();
    model.meterIdForMeterSet = meterId;
    model.roomCodeForMeterSet = roomCode;
    model.energyTypeIdForMeterSet = energyTypeId;
    model.meterSetStep = 1;
    pajax.post({
        url: 'FNTMeterSetFunctionService',
        data: {
            meterId: meterId
        },
        success: function (resJson) {
            var resJson = resJson || [];
            if (resJson instanceof Array && resJson.length != 0) {
                if (resJson[0].status == 1) {
                    staticEvent.showInstrumentSetWindow();
                    var instrumentalSupportFunctions = model.instrumentalSupportFunctions = resJson[0].result;
                    model.$set(instrumentalSupportFunctions, 'meterId', meterId);
                    model.$set(instrumentalSupportFunctions, 'roomCode', roomCode);
                    model.$set(instrumentalSupportFunctions, 'energyTypeName', energyTypeName);
                } else {
                    common.msgFailure(resJson[0].reason);
                }
            } else {
                common.msgFailure("查询仪表功能失败");
            }
        },
        error: function () {
            common.msgFailure("查询仪表功能失败");
        }
    })
}

// 更新价格输入框失去焦点验证
tenantCtrl.updatePriceTexeBlur = function (value) {
    let patten = /^(\-)?\d+(\.\d{0,4})$/;
    let patten1 = /^[0-9]*$/;
    if (patten1.test(value) || patten.test(value)) {
        return true;
    } else {
        $("#message").pshow({
            text: "请输入正确的数字(小数点后最多四位)",
            state: "failure"
        });
        return false;
    }
}

// 仪表设置前查询数据
tenantCtrl.getInstrumentalData = function (meterId, setType) {
    var model = tenantMngModel.instance();
    var meterType = model.instrumentalSupportFunctions.meterType;

    staticEvent.reductionIptOfMeterSet();
    pajax.post({
        url: 'FNTMeterSetBeforeService',
        data: {
            setType: setType,
            meterId: meterId
        },
        success: function (resJson) {
            var resJson = resJson || [];
            if (resJson instanceof Array && resJson.length != 0) {
                var instrumentalData = model.instrumentalData = resJson[0];
                var value = instrumentalData.value;
                if (setType == 1) {
                    if (value == 0) {
                        instrumentalData.value = '保电';
                    } else if (value == 1) {
                        instrumentalData.value = '不保电';
                    } else if (value == 2) {
                        instrumentalData.value = '--';
                        if (model.meterSetStep == 2) {
                            common.msgFailure("该表不支持查询保电状态");
                        }
                    }
                } else if (setType == 2) {
                    if (value == 0) {
                        instrumentalData.value = '合闸';
                    } else if (value == 1) {
                        instrumentalData.value = '分闸';
                    } else if (value == 2) {
                        instrumentalData.value = '--';
                        if (model.meterSetStep == 2) {
                            common.msgFailure("该表不支持查询分合闸状态");
                        }
                    }
                } else if (setType == 4) {
                    if (meterType == 1) {
                        instrumentalData.result.forEach(function (val) {
                            var typeName = '';
                            switch (val.type) {
                                case 'L':
                                    typeName = '';
                                    break;
                                case 'J':
                                    typeName = '尖峰';
                                    break;
                                case 'F':
                                    typeName = '高峰';
                                    break;
                                case 'G':
                                    typeName = '低谷';
                                    break;
                                case 'P':
                                    typeName = '平段';
                                    break;
                            }
                            val.typeName = typeName;
                        });
                    }
                }
            } else {
                common.msgFailure("查询仪表数据失败");
            }

        },
        error: function () {
            common.msgFailure("查询仪表数据失败");
        },
        complete: function () {
            tenantCtrl.refreshIconStop();
        }
    });
}
// 管理价格弹框
tenantCtrl.managementPrice = function (num, type) {
    $('#instrument_set').css("display", "none")
    console.log("type000000000000",type)
    //显示管理价格方案
    var model = tenantMngModel.instance();
    switch (type) {
        case "电":
            type = "Dian"
            break;
        case "热水":
            type = "ReShui"
            break;
        case "燃气":
            type = "RanQi"
            break;
        case "水":
            type = "Shui"
            break;
        default:
            break;
    }
    model.energyCosttempId = num;
    var energyTypeId = type;
    var energyTypeArr = model.energyTypeArr;
    for (var i = 0; i < energyTypeArr.length; i++) {
        var typeObj = energyTypeArr[i];
        if (energyTypeId == typeObj.id) {
            model.selEnergyType = typeObj;
            break;
        }
    }
    model.currentPage = 'managePrivcePlanPage';
}

// 仪表设置
tenantCtrl.sendInstrumentalInfo = function (meterId, setType, value) {
    var model = tenantMngModel.instance();
    var params = {
        meterId: meterId,
        tenantId: model.selTenant.tenantId,
        setType: setType,
        type: null,
        value: null,
        price: null
    };

    switch (setType) {
        case 1:
            params.type = model.isProtectionCircuit ? 1 : 0;
            break;
        case 2:
            params.type = model.isCloseBrake ? 1 : 0;
            break;
        case 3:
            params.value = value;
            break;
        case 4:
            params.price = model.instrumentalData.iptValueArr;
            break;
    }
    pajax.post({
        url: 'FNTMeterSetService',
        data: params,
        success: function (resJson) {
            var resJson = resJson || [];
            if (resJson instanceof Array && resJson != 0) {
                var result = resJson[0].result;
                if (result == 0) {
                    model.instrumentalData = '';
                    model.meterSetStep = 5;
                    model.meterSetState = 1;
                    tenantCtrl.getInstrumentalData(meterId, setType);
                } else if (result == 1) {
                    model.meterSetStep = 6;
                } else if (result == 2) {
                    model.meterSetStep = 1;
                    model.meterSetState = 1;
                    common.msgFailure("该表不支持" + model.meterSetTypeName);
                }
            }
        },
        error: function () {
            model.meterSetStep = 6;
            common.msgFailure("仪表设置失败");
        }
    });
}

// 保存仪表设置记录
tenantCtrl.saveMeterSetRecord = function (meterSetParams) {
    var model = tenantMngModel.instance();
    var params = {
        meterId: meterSetParams.meterId,
        tenantId: model.selTenant.tenantId,
        roomCode: meterSetParams.roomCode,
        energyTypeId: meterSetParams.energyTypeId,
        setType: meterSetParams.setType,
        type: null,
        value: null,
        price: null
    };

    switch (meterSetParams.setType) {
        case 1:
            params.type = model.isProtectionCircuit ? 1 : 0;
            break;
        case 2:
            params.type = model.isCloseBrake ? 1 : 0;
            break;
        case 3:
            params.value = meterSetParams.value;
            break;
        case 4:
            let updatePriceId = window.localStorage.getItem("updatePriceId")
            params.priceId = updatePriceId;
            params.price = meterSetParams.price;
            break;
    }
    pajax.post({
        url: 'FNTMeterSetSaveService',
        data: params,
        success: function (resJson) {
            var resJson = resJson || [];
            if (resJson instanceof Array && resJson.length != 0) {
                var result = resJson[0].result;
                tenantCtrl.getEnergyTypeArr();
                tenantCtrl.getTenantDetail(model.selTenant);
                if (result == 1) {
                    common.msgFailure("保存仪表设置记录失败");
                }
            }
        },
        error: function () {
            common.msgFailure("保存仪表设置记录失败");
        }
    });
}

/**新增***/
//获取建筑列表
tenantCtrl.getBuildingArr = function () {
    pajax.get({
        url: "FNTCommonBuildingListService",
        data: {},
        success: function (resJson) {
            var resJson = resJson || [];
            for (var i = 0; i < resJson.length; i++) {
                var item_i = resJson[i];
                item_i["floorArr"] = [];
                item_i["roomArr"] = [];
                item_i["roomShowArr"] = [];
            }
            var model = tenantMngModel.instance();
            model.buildingArr2 = resJson;
            var newArr = [{
                "id": "_all",
                "name": "全部",
                "floorArr": [],
                "roomArr": [],
                "roomShowArr": [],
            }].concat(resJson);
            model.buildingArr = newArr;
        },
        complete: function () {
            tenantCtrl.asynTool.fire("tenantCtrl.getBuildingArr", 1);
        },
        error: function () {
            common.msgFailure("获取建筑列表失败");
        }
    });
}

//获取能耗类型列表
tenantCtrl.getEnergyTypeArr = function () {
    pajax.get({
        url: "FNTCommonEnergyTypeListService",
        data: {},
        success: function (resJson) {
            resJson = resJson || [];
            for (var i = 0; i < resJson.length; i++) {
                var item_i = resJson[i];
                item_i["priceArr"] = [];
                item_i["priceUnit"] = [];
                item_i["priceArr"] = [];
                tenantCtrl.getPriceArr(item_i);
            }
            tenantMngModel.instance().energyTypeArr = resJson;
            tenantMngModel.instance().sr_energyTypeArr = [{
                id: null,
                code: null,
                name: "全部"
            }].concat(resJson);

            // 处理表底数使用的数据
            tenantMngModel.instance().energyTypeArrForGauge = JSON.parse(JSON.stringify(resJson));
            tenantMngModel.instance().energyTypeArrForGauge.unshift({
                id: null,
                name: "全部"
            });
            setTimeout(function () {
                $('#energy_type').psel(0, false);
            }, 0);
        },
        error: function () {
            common.msgFailure("获取能耗类型失败");
        }
    });
};

//获取租户自己的能耗类型
tenantCtrl.getSelfEnergyTypeArr = function (param) {
    pajax.get({
        url: "FNTCommonEnergyTypeListService",
        data: param,
        success: function (resJson) {
            resJson = resJson || [];
            tenantMngModel.instance().selfenergyTypeArr = resJson;
            Vue.nextTick(function () {
                $("#report_energy_cbx").psel(0);
            });
        },
        error: function () {
            common.msgFailure("获取能耗类型失败");
        }
    });
};
//获取能耗-付费类型列表
tenantCtrl.getEnergyTypePayTypeArr = function () {
    pajax.get({
        url: "FNTCommonEnergyTypePayTypeListService",
        data: {},
        success: function (resJson) {
            resJson = resJson || [];
            resJson = [{
                "id": "_all",
                "name": "全部",
            }].concat(resJson);
            tenantMngModel.instance().energyTypePayTypeArr = resJson;
            tenantMngModel.instance().selEnergyTypePayType = resJson[0];
        },
        complete: function () {
            tenantCtrl.asynTool.fire("tenantCtrl.getEnergyTypePayTypeArr", 2);
        },
        error: function () {
            common.msgFailure("能耗-付费类型失败");
        }
    });
}

//获取房间列表
tenantCtrl.getRoomArr = function (obj,callback) {
    var callback = callback || function() {};
    $('#chooseRoomLoadingListen').pshow();
    pajax.post({
        url: "FNTCommonRoomListService",
        data: {
            "buildingId": obj.id,
            floorId: null
        },
        success: function (resJson) {
            if (resJson.length > 0) {
                var roomArr = resJson[0].roomList || [];
                if(roomArr.length === 0) {
                    common.msgFailure("该建筑下已经没有房间可租,请重新选择");
                }else {
                    callback();
                }
                for (var i = 0, length_i = roomArr.length; i < length_i; i++) {
                    var room_i = roomArr[i];
                    room_i["isChecked"] = false;
                    room_i["meterList"] = [];
                    room_i["isGetMeterReady"] = false;
                }
                obj.roomArr = roomArr;
                if (tenantMngModel.instance().addOrEdit == 0) { //添加
                    obj.roomShowArr = roomArr.filter(function (x) {
                        return x.status == 0;
                    })
                } else { //编辑未激活
                    var model = tenantMngModel.instance();
                    var selTenant = model.selTenant;
                    if (selTenant.tenantStatus == 0) {
                        tenantCtrl.dealTenantRoom();
                    }
                }
            } else {
                common.msgFailure("获取建筑房间列表失败");
            }
        },
        error: function () {
            common.msgFailure("获取建筑房间列表失败");
        },
        complete: function () {
            $('#chooseRoomLoadingListen').phide();
            if (tenantMngModel.instance().addOrEdit == 1 && ldp.editTementshow.anyncTool) {
                ldp.editTementshow.anyncTool.fire("tenantCtrl.getRoomArr_" + obj.id, 1);
            }
        }
    });
}

//编辑租户时处理房间列表
tenantCtrl.dealTenantRoom = function () {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    var addTenantSelBuilding = model.addTenantSelBuilding;
    var edit_roomArr = selTenant.roomList;
    var roomArr = addTenantSelBuilding.roomArr;
    for (var i = 0, length_i = edit_roomArr.length; i < length_i; i++) {
        for (var j = 0, length_j = roomArr.length; j < length_j; j++) {
            var item_j = roomArr[j];
            if (edit_roomArr[i].roomId == item_j.id) {
                item_j.isChecked = true;
                item_j.status = 0;
                tenantCtrl.getRoomMeter(addTenantSelBuilding.id, item_j); // 获取房间的仪表
                break;
            }
        }
    }
    var showArr = roomArr.filter(function (x) {
        return x.status == 0;
    });
    addTenantSelBuilding.roomShowArr = showArr;
}

//获取房间仪表
tenantCtrl.getRoomMeter = function (buildingId, obj) {
    pajax.post({
        url: "FNTCommonMeterListService",
        data: {
            "buildingId": buildingId, //建筑id
            "roomId": obj.id
        },
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var model = tenantMngModel.instance();
                var meterList = resJson[0].meterList || [];
                obj.meterList = meterList;
                var energyTypeArr = model.energyTypeArr;
                for (var i = 0; i < energyTypeArr.length; i++) {
                    var energyTypeId = energyTypeArr[i].id;
                    var idArr = [];
                    var meterArr = [];
                    for (var j = 0; j < meterList.length; j++) {
                        var item_j = meterList[j];
                        if (item_j.energyTypeId == energyTypeId) {
                            meterArr.push(item_j);
                            idArr.push(item_j.meterId);
                        }
                    }
                    Vue.set(obj, energyTypeId + "MeterArr", meterArr);
                    Vue.set(obj, energyTypeId + "MeterIdStr", idArr.join(','));
                }
            } else {
                common.msgFailure("获取房间仪表失败");
            }
        },
        error: function () {
            common.msgFailure("获取房间仪表失败");
        },
        complete: function () {
            obj.isGetMeterReady = true;
        }
    });
}

//获取业态列表
tenantCtrl.getTenantTypeArr = function () {
    pajax.post({
        url: "FNTCommonTenantTypeListService",
        data: {},
        success: function (resJson) {
            resJson = resJson || [];
            tenantMngModel.instance().tenantTypeArr = resJson;
        },
        error: function () {
            common.msgFailure("获取业态数据失败");
        }
    });
}

//获取楼层列表
tenantCtrl.getFloorArr = function (obj) {
    pajax.post({
        url: "FNTCommonFloorListService",
        data: {
            "buildingId": obj.id, //建筑id
        },
        success: function (resJson) {
            resJson = resJson || [];
            resJson = [{
                "id": "_all",
                "name": "全部",
            }].concat(resJson);
            obj.floorArr = resJson;
        },
        error: function () {
            common.msgFailure("获取楼层数据失败");
        }
    });
}

//添加租户
tenantCtrl.addTenant = function (addParm) {
    pajax.post({
        url: "FNTTenantAddService",
        data: addParm,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length > 0) {
                var resData = resJson[0];
                if (resData.result == 0) {
                    tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
                    common.msgSuccess("添加租户成功");
                    var model = tenantMngModel.instance();
                    model.currentPage = "tementListPage";
                } else {
                    common.msgFailure("添加租户失败：" + resData.message);
                }
            } else {
                common.msgFailure("添加租户失败");
            }
        },
        error: function () {
            common.msgFailure("添加租户失败");
        }
    });
}



//获取租户列表筛选参数组合
tenantCtrl.getTenantFilterParam = function () {
    tenantMngModel.data.selLimitType = 0;
    var model = tenantMngModel.instance();
    model.tenantArr = [];
    var b_sel = $("#t_building_cbx").psel();
    var buildingId = null;
    if (b_sel) {
        buildingId = tenantMngModel.data.buildingArr[b_sel.index].id;
        buildingId = buildingId == "_all" ? null : buildingId;
    }
    var tenantStatus = tenantMngModel.data.tenantStatusArr[$("#t_status_cbx").psel().index].id;
    tenantStatus = tenantStatus == -1 ? null : tenantStatus;
    var energyType = null
    if (tenantStatus != 2) {
        energyType = tenantMngModel.data.energyTypePayTypeArr[$("#t_energy_type").psel().index].id;
        energyType = energyType == "_all" ? null : energyType;
    }
    pajax.post({
        url: "FNTTenantListParamService",
        data: {
            "tenantStatus": tenantStatus,
            "energyType": energyType,
            "buildingId": buildingId, //null 代表全部
        },
        success: function (resJson) {
            resJson = resJson || [];
            var rstArr = [];
            if (energyType != null) {
                var tranName = energyType.indexOf("0") > 0 ? "tranFrom_YuFuFei" : "tranFrom_HouFuFei";
                var fieldName = energyType.indexOf("0") > 0 ? "chargeType" : "amountType";
                for (var i = 0; i < resJson.length; i++) {
                    var tempArr = resJson[i][fieldName];
                    for (var j = 0; j < tempArr.length; j++) {
                        rstArr.push(tenantFilterParam[tranName](tempArr[j]));
                    }
                }
            }

            model.tenantFilterParam = rstArr;
            if (rstArr.length > 0) {
                var selFeeType = rstArr[0]
                model.selFeeType = selFeeType;
                if (selFeeType.childArr.length > 0) {
                    model.selShowData = selFeeType.childArr[0];
                }
            }
            tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
        },
        error: function () {
            common.msgFailure("获取租户筛选参数失败");
        }
    });
}

//获取租户列表
tenantCtrl.getTenantArr = function () {
    common.showPartLoading();
    var model = tenantMngModel.instance();
    model.tenantArr = [];
    var tenantStatus = model.selTenantStatus.id;
    tenantStatus = tenantStatus == -1 ? null : tenantStatus;
    var energyType = model.selEnergyTypePayType.id;
    energyType = energyType == "_all" || energyType == 2 ? null : energyType;
    var buildingId = null;
    var buildingSel = $("#t_building_cbx").psel();
    if (buildingSel) {
        buildingId = model.buildingArr[buildingSel.index].id;
    }
    buildingId = buildingId == "_all" ? null : buildingId;
    var pageIndex = $("#page_simple").psel() - 1;
    var param = {
        tenantStatus: tenantStatus,
        energyType: energyType,
        buildingId: buildingId,
        pageIndex: pageIndex,
        pageSize: tenantCtrl.pageSize,
    };
    var sortParam = [{
        "field": model.columnSort_field,
        "type": model.columnSort_type
    }];
    var prePayParam = {};
    if (model.payType == "0") {
        prePayParam["prePayType"] = model.selFeeType.id;
        if (tenantStatus == 1) {
            prePayParam["remainType"] = model.selShowData.id;
            prePayParam["limit"] = model.selLimitType;
        }
    }
    var postPayParam = {};
    if (model.payType == "1") {
        postPayParam["amountType"] = model.selFeeType.id;
        postPayParam["limit"] = model.selLimitType;
    }
    param.sort = sortParam;
    param.prePayParam = prePayParam;
    param.postPayParam = postPayParam;
    pajax.post({
        url: "FNTTenantListService",
        data: param,
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var result = resJson[0];
                var count = result.count;
                var tenantArr_unit = result.energyUnit;
                var tenantArr = [];
                for (var i = 0; i < result.tenantList.length; i++) {
                    var newItem = new tenantVM();
                    var org = result.tenantList[i];
                    newItem["lastClearingTime"] = org["lastClearingTime"];
                    newItem["tagId"] = i;
                    tenantArr.push(tenantVM.tran(org, newItem));
                }
                model.tenantArr = tenantArr;
                model.tenantArr_unit = tenantArr_unit;
                $('.opt_btns').stop().hide(); //换页时把最后的操作栏隐藏
                $("#grid1").pcount(count);
                var pageCount = Math.ceil(count / tenantCtrl.pageSize);
                $("#page_simple").pcount(Math.max(1, pageCount));

                // 首页列表显示异常解决方法
                setTimeout(function () {
                    tenantCtrl.gridHeight();
                }, 10);

            } else {
                common.msgFailure("获取租户列表失败");
            }
        },
        complete: function () {
            /* 20181018 wp+ */
            var index = window.location.href.indexOf('&tid')
            if (index > 0) {
                var arr = window.location.href.split('&');
                var tenantId = decodeURI(arr[arr.length - 2].split('=')[1]);
                var buildingId = decodeURI(arr[arr.length - 1].split('=')[1]);
                var newItem = new tenantVM();
                newItem.tenantId = tenantId;
                newItem.buildingId = buildingId;
                tenantCtrl.goTenantDetail(newItem);
            }
            /* end */
            common.hidePartLoading();
            model.isGetTenantArrReady = true;
            // 清空页码输入框状态
            staticEvent.pageSizeDefault();
        },
        error: function () {
            common.msgFailure("获取租户列表失败");
        }
    });

}


tenantCtrl.tenantPageing = function () {
    var model = tenantMngModel.instance();
    var blackList = $('#blackList').psel();
    model.addtenantName1 = [];
    setTimeout(function () {
        model.addtenantName1 = model.addtenantName.slice((blackList - 1) * 30, 30 * blackList);
    }, 0);
}

tenantCtrl.rechagePageing = function () {
    var model = tenantMngModel.instance();
    var rechage = $('#rechage').psel();
    model.Remotetopup2 = [];
    setTimeout(function () {
        model.Remotetopup2 = model.Remotetopup1.slice((rechage - 1) * 30, 30 * rechage);
    }, 0);
}




//还原表格并重新获取租户列表
tenantCtrl.precoverGrid = function () {
    $("#grid1").precover(false);
    tenantCtrl.getTenantArr();
    tenantCtrl.messageBlacklist();
    tenantCtrl.addRemotetopup();
    tenantCtrl.addtenantname();
}

//设置报警门限
//ZHBJ_05  水
//ZHBJ_07  热水
//ZHBJ_09  燃气
//ZHBJ_15  电费
tenantCtrl.setAlarmLimit = function (ZHBJ_15, ZHBJ_05, ZHBJ_07, ZHBJ_09) {
    var arr = [{
            "alarmTypeId": "ZHBJ_15", //报警门限编码
            "limitValue": ZHBJ_15
        },
        {
            "alarmTypeId": "ZHBJ_05", //报警门限编码
            "limitValue": ZHBJ_05
        },
        {
            "alarmTypeId": "ZHBJ_07", //报警门限编码
            "limitValue": ZHBJ_07
        },
        {
            "alarmTypeId": "ZHBJ_09", //报警门限编码
            "limitValue": ZHBJ_09
        }
    ];
    pajax.get({
        url: "FNTAlarmLimitUpdateService",
        data: {
            buildingId: null,
            alarmLimitList: arr,
        },
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 0) {
                $("#policeWindow").hide(); //TODO
                common.msgSuccess();
            } else {
                common.msgFailure("设置报警门限失败");
            }
        },
        error: function () {
            common.msgFailure("设置报警门限失败");
        }
    });
}

//获取报警门限
tenantCtrl.getAlarmLimitArr = function () {
    pajax.get({
        url: "FNTAlarmLimitQueryService",
        data: {
            buildingId: null,
        },
        success: function (resJson) {
            resJson = resJson || [];
            var alarmSetParam = {};
            for (var i = 0; i < resJson.length; i++) {
                var item_i = resJson[i];
                alarmSetParam[item_i.alarmTypeId] = item_i.limitValue;
            }
            tenantMngModel.instance().alarmSetParam = alarmSetParam;
        },
        error: function () {
            common.msgFailure("获取报警门限失败");
        }
    });
}

// 管理价格方案 跳转到管理价格方案页面
tenantCtrl.tomanagePrivcePlan = function (num) {
    //显示管理价格方案
    var model = tenantMngModel.instance();
    model.energyCosttempId = num;
    var energyTypeId = model.energyType;
    if (num == 6) {
        staticEvent.floatHide();
         energyTypeId = 'Dian';
    }
    var energyTypeArr = model.energyTypeArr;
    for (var i = 0; i < energyTypeArr.length; i++) {
        var typeObj = energyTypeArr[i];
        if (energyTypeId == typeObj.id) {
            model.selEnergyType = typeObj;
            break;
        }
    }
    model.currentPage = 'managePrivcePlanPage';
}

//删除电价方案
tenantCtrl.deletePrice = function () {
    ldp.hideFloat();
    var model = tenantMngModel.instance();
    var obj = model.confirmDelPrice;
    pajax.get({
        url: "FNTPriceDeleteService",
        data: {
            "id": obj.id,
        },
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 1) {
                if (resJson[0].result == 0) {
                    common.msgSuccess("删除成功");
                    tenantCtrl.getPriceArr(model.selEnergyType);
                    if (obj.id == model.selPriceId) {
                        $("#" + model.selEnergyType.id + "_price_sel").precover();
                        $("#" + model.selEnergyType.id + "_price_sel").parents('.pricePlan_combox').siblings('.checkPriceDetail').pdisable(true);
                    }
                } else {
                    setTimeout(function () {
                        $("#message").pshow({
                            text: "有租户正在使用此价格方案，暂时无法删除",
                            state: "failure"
                        });
                    }, 500);
                }
            } else {
                common.msgFailure("删除失败");
            }
        },
        error: function () {
            common.msgFailure("删除失败");
        }
    });
}

//更新电价方案
tenantCtrl.updatePrice = function (priceId, name, type, content) {
    pajax.get({
        url: "FNTPriceUpdateService",
        data: {
            "id": priceId,
            "name": name,
            "type": type, //0 平均 1 分时
            "content": content,
        },
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 0) {
                var model = tenantMngModel.instance();
                tenantCtrl.getPriceArr(model.selEnergyType);
                if (model.currentPage == "tenementDetails") {
                    tenantCtrl.getTenantDetail(model.selTenant);
                }
                common.msgSuccess("保存成功");
                $('#build_edit_pricePlan').phide();
            } else if (resJson[0] != null) {
                common.msgFailure(resJson[0]);
            } else {
                common.msgFailure("保存失败");
            }
        },
        error: function () {
            common.msgFailure("保存失败");
        }
    });
}

//获取价格方案详情
tenantCtrl.getPriceDetail = function (energyTypeId, priceObj) {
    pajax.get({
        url: "FNTPriceDetailService",
        data: {
            "energyTypeId": energyTypeId,
            "id": priceObj.id
        },
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var item_i = resJson[0];
                if (item_i.type == 0) {
                    priceObj.content = {
                        PJ: item_i.content
                    };
                } else {
                    var newContent = {};
                    for (var j = 0; j < item_i.content.length; j++) {
                        var item_j = item_i.content[j];
                        newContent[item_j.type] = item_j.value;
                    }
                    priceObj.content = newContent;
                }
            }
        },
        error: function () {
            common.msgFailure("获取电价详情失败");
        }
    });
}

//添加价格方案
tenantCtrl.addPrice = function (selEnergyType, name, type, content) {
    pajax.get({
        url: "FNTPriceAddService",
        data: {
            name: name,
            "energyTypeId": selEnergyType.id,
            "type": type,
            "content": content,
        },
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 0) {
                tenantCtrl.getPriceArr(selEnergyType);
                common.msgSuccess("添加成功");
                $('#build_edit_pricePlan').phide();
            } else {
                common.msgFailure("添加失败");
            }
        },
        error: function () {
            common.msgFailure("添加失败");
        }
    });
}

//获取价格方案列表
tenantCtrl.getPriceArr = function (obj) {
    var model = tenantMngModel.instance();
    var $dom;
    if (model.energyCosttempId == 0) { //租户列表
        $dom = $("#floatText");
    } else { //添加租户
        $dom = $("#" + obj.id + "_price_sel");
    }
    var orgPriceId;
    if ($dom) {
        var selObj = $dom.psel();
        if (selObj) {
            orgPriceId = obj.priceArr[selObj.index].id;
        }
    }
    pajax.get({
        url: "FNTPriceListService",
        data: {
            "energyTypeId": obj.id,
        },
        success: function (resJson) {
            resJson = resJson || [];
            var priceArr = [];
            if (resJson.length > 0) {
                priceArr = resJson[0].priceList || [];
                for (var i = 0; i < priceArr.length; i++) {
                    var item_i = priceArr[i];
                    if (item_i.type == 0) {
                        item_i.content = {
                            PJ: item_i.content
                        };
                    } else {
                        var newContent = {};
                        for (var j = 0; j < item_i.content.length; j++) {
                            var item_j = item_i.content[j];
                            newContent[item_j.type] = item_j.value;
                        }
                        item_i.content = newContent;
                    }
                }
            }
            var unit = "";
            switch (obj.id) {
                case "Dian":
                    unit = "元/kWh";
                    break;
                case "Shui":
                case "ReShui":
                case "RanQi":
                    unit = "元/m³";
                    break;
            }
            obj.priceArr = priceArr;
            obj.priceUnit = unit;
            Vue.nextTick(function () {
                if (orgPriceId) {
                    var had = false;
                    for (var i = 0; i < priceArr.length; i++) {
                        if (orgPriceId == priceArr[i].id) {
                            $dom.psel(i);
                            had = true;
                            break;
                        }
                    }
                    if (!had) {
                        $dom.precover("--");
                    }
                }
            });
        },
        error: function () {
            common.msgFailure("获取价格方案失败");
        }
    });
}
// 批量修改电价方案
tenantCtrl.batchPriceSet = function(flag){
    var model = tenantMngModel.instance();
    var priceSel = $("#floatTextPrice").psel();
    var priceTempldateId = model.dianPriceArr[priceSel.index].id;
    pajax.post({
        url:'FnTimingUpdatePriceSaveService',
        data:{
            energyTypeId:'Dian',
            priceTemplateId:priceTempldateId,
            isExecute: flag
        },
        success:function(res){
            // console.log(res)
            staticEvent.floatHide()
            common.msgSuccess("保存成功");
        },
        error: function () {
            common.msgFailure("批量设置电价失败");
        },
    })
}
// 电价方案设置 / 查看日志
tenantCtrl.viewLog = function(isDownload,iptVal){
    staticEvent.floatHide();
    var model = tenantMngModel.instance();
    var timeObj = $("#datePicker").psel();
    var timeFrom = new Date(timeObj.startTime).format("y-M-d h:m:s");
    var timeTo = new Date(timeObj.endTime).format("y-M-d h:m:s");
    var pageIndex = $("#page_logs").psel() - 1;
    var resultID = null;
    var result = $("#results_type").psel();
    if (result) {
        resultID = model.resultArr[result.index].id;
    }
    var params = {
        pageIndex:pageIndex,
        pageSize:tenantCtrl.pageSize_sr,
        isDownload:isDownload,
        result:resultID,
        timeFrom:timeFrom,
        timeTo:timeTo,
        tenantName:iptVal
     }
     var fName = 'FNTimingUpdatePriceRecordService'
     if(isDownload==1){
        pajax.downloadByParam(fName, params);
     } else {
        pajax.post({
            url: fName,
            data:params,
            success:function(resJson){
                if (resJson instanceof Array && resJson.length != 0){
                    var count = (resJson[0] || {}).count || 0;
                    var pageCount = Math.ceil(count / tenantCtrl.pageSize_sr);
                    $('#page_logs').pcount(Math.max(1, pageCount));
                   model.BatchRecordArr = resJson[0].dataList;
               } else {
                   common.msgFailure("获取日志失败");
               }
            },
            error:function(){
               common.msgFailure("获取日志失败");
            }
        })
     }
   
    model.currentPage = 'logs';
}
// 执行结果
tenantCtrl.result = function(){
    var model = tenantMngModel.instance();
    tenantCtrl.viewLog(0);
}
//批量更新电价前获取共同的价格方案
let checkedTenantTypes = [];
tenantCtrl.tenantCheck = function (obj) {
    obj["isChecked"] = !obj["isChecked"];
    tenantMngModel.data.checkedTenant = obj;
    let type = obj.prePayType;
    let objState = event.pEventAttr["state"];
    if (objState == true) {
        checkedTenantTypes.push(type)
    } else if (objState == false) {
        for (var i = 0; i < checkedTenantTypes.length; i++) {
            let typeIndex = checkedTenantTypes.indexOf(type);
            if (typeIndex > -1) {
                checkedTenantTypes.splice(typeIndex, 1)
            }
        }
    };
};

//租赁管理  修改电价
tenantCtrl.getSamePriceBeforeUpdate = function (tenantIdList) {
    // 筛选复选框选中的租户是否包含软件软扣
    let type = $.inArray(1, checkedTenantTypes);
    if (type == -1) {
        $("#floatWindow").pshow({
            title: '修改价格方案'
        });
    } else {
        $("#floatWindow").phide({
            title: '修改价格方案'
        });
        $("#message").pshow({
            text: "修改价格时，租户充值类型不能选择软充表扣的租户",
            state: "failure"
        });
    };
    $("#buttonCenter").data("idlist", tenantIdList);
    var model = tenantMngModel.instance();
    pajax.get({
        url: "FNTBatchPriceQueryService",
        data: {
            "energyTypeId": model.energyType,
            "tenantList": tenantIdList
        },
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var priceId = resJson[0].priceTempldateId;
                if (priceId) {
                    for (var i = 0; i < model.bat_upt_priceArr.length; i++) {
                        if (priceId == model.bat_upt_priceArr[i].id) {
                            $("#floatText").psel(i);
                            break;
                        }
                    }
                }
            }
        },
        error: function () {
            common.msgFailure("根据租户列表获取电价失败");
        }
    });
}

//批量更新租户所选价格方案
tenantCtrl.updatePriceBat = function () {
    var tenantIdList = $("#buttonCenter").data("idlist");
    var model = tenantMngModel.instance();
    var priceSel = $("#floatText").psel();
    if (!priceSel) {
        common.msgFailure("请选择电价");
        return;
    }
    var priceTempldateId = model.bat_upt_priceArr[priceSel.index].id;
    pajax.get({
        url: "FNTBatchPriceUpdateService",
        data: {
            "buildingId": null, //建筑id
            "energyTypeId": model.energyType,
            "tenantList": tenantIdList,
            priceTempldateId: priceTempldateId
        },
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length > 0) {
                var content = resJson[0];
                if (content.result == 0) {
                    common.msgSuccess("保存成功");
                    staticEvent.floatHide();
                } else {
                    common.msgFailure(content.reason);
                }
            } else {
                common.msgFailure("保存失败");
            }
        },
        error: function () {
            common.msgFailure("保存失败");
        }
    });
}

//获取单租户历史账单
tenantCtrl.getHistoryBillArr = function () {
    var selTenant = tenantMngModel.instance().selTenant;
    var timeObj = $("#reportCalendar").psel();
    var timeFrom = new Date(timeObj.startTime).format("y-M-d h:m:s");
    var timeTo = new Date(timeObj.realEndTime).format("y-M-d h:m:s");
    var report = selTenant.energyCostReport;
    report.timeFrom = timeFrom;
    report.timeTo = new Date(timeObj.endTime).format("y-M-d h:m:s");
    var param = {
        "buildingId": selTenant.buildingId, //建筑id
        "energyTypeId": report.energyTypeId,
        "tenantId": selTenant.tenantId,
        "timeFrom": timeFrom,
        "timeTo": timeTo,
    };
    pajax.get({
        url: "FNTPostPayArrearageHistoryGridService",
        data: param,
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var result = resJson[0];
                report.energyTypeName = result.energyTypeName;
                report.energyUnit = result.energyUnit;
                report.totalMoney = result.totalMoney;
                report.dataList = result.orderList;
            } else {
                common.msgFailure("获取租户历史账单失败");
            }
        },
        error: function () {
            common.msgFailure("获取租户历史账单失败");
        },
        complete: function () {
            common.hidePartLoading();
        }
    });
}

//单租户欠费帐单查询
tenantCtrl.getNoPayBillArr = function (orderId) {
    var selTenant = tenantMngModel.instance().selTenant;
    var report = selTenant.energyCostReport;
    var param = {
        "buildingId": selTenant.buildingId, //建筑id
        "energyTypeId": report.energyTypeId,
        "tenantId": selTenant.tenantId,
        orderId: orderId ? orderId : null
    };
    pajax.get({
        url: "FNTPostPayArrearageGridService",
        data: param,
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var result = resJson[0];
                report.energyUnit = result.energyUnit;
                report.energyTypeName = result.energyTypeName;
                report.totalMoney = result.totalMoney;
                report.remainMoney = result.remainMoney;
                report.priceTemplateId = result.priceTemplateId;
                report.priceTemplateName = result.priceTemplateName;
                report.dataList = result.orderList;
                if (result.orderList.length > 0) {
                    var first = result.orderList[0];
                    var timeArr = (first.orderTime + "").split("~");
                    if (timeArr.length > 0) {
                        var timeFrom = timeArr[0];
                    }
                    var last = result.orderList[result.orderList.length - 1];
                    var timeArr2 = (last.orderTime + "").split("~");
                    if (timeArr2.length > 0) {
                        var timeTo = timeArr[0];
                    }
                    report.timeFrom = timeFrom;
                    report.timeTo = timeTo;
                }
            } else {
                common.msgFailure("获取租户欠费账单失败");
            }
        },
        error: function () {
            common.msgFailure("获取租户欠费账单失败");
        },
        complete: function () {
            common.hidePartLoading();
        }
    });
}

//能耗费用报表查询（单租户）
tenantCtrl.getTenantEnergyMoneyReport = function () {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    var report = selTenant.energyCostReport;
    var selfenergyTypeArr = model.selfenergyTypeArr;
    var energyType = selfenergyTypeArr[$("#report_energy_cbx").psel().index];
    if (!energyType) {
        common.msgFailure("未获取到能源类型");
        return;
    }
    var energyTypeId = energyType.id;
    var energyTypeName = energyType.name;
    var timeObj = $("#reportCalendar").psel();
    var timeFrom = new Date(timeObj.startTime).format("y-M-d h:m:s");
    var timeTo = new Date(timeObj.realEndTime).format("y-M-d h:m:s");
    report.timeFrom = timeFrom;
    report.timeTo = new Date(timeObj.endTime).format("y-M-d h:m:s");
    report.energyTypeName = energyTypeName;
    var param = {
        "buildingId": selTenant.buildingId, //建筑id
        "energyTypeId": energyTypeId,
        "tenantId": selTenant.tenantId,
        "timeFrom": timeFrom,
        "timeTo": timeTo,
    }
    pajax.get({
        url: "FNTEnergyMoneyGridService",
        data: param,
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var result = resJson[0];
                report.energyUnit = result.energyUnit;
                report.totalEnergy = result.totalEnergy;
                report.totalMoney = result.totalMoney;
                report.dataList = result.dataList;
                $('#pdf_wrapper .noReport').hide().siblings().show();
            } else {
                common.msgFailure("获取能耗费用报告失败");
            }
        },
        error: function () {
            common.msgFailure("获取能耗费用报告失败");
        },
        complete: function () {
            common.hidePartLoading();
        }
    });
}

//能耗费用报表查询（批量）
tenantCtrl.getTenantEnergyMoneyReport_bat = function (isDownload) {
    var model = tenantMngModel.instance();
    var checkedTenantArr = model.checkedTenantArr;
    var timeObj = $("#energy_cost_calendar").psel();
    var timeFrom = new Date(timeObj.startTime).format("y-M") + "-01 00:00:00";
    var timeTo = new Date(timeObj.realEndTime).format("y-M") + "-01 00:00:00";
    var t_map = {};
    var tenantIdList = [];
    for (var i = 0; i < checkedTenantArr.length; i++) {
        var t_i = checkedTenantArr[i];
        var tenantId = t_i.tenantId;
        tenantIdList.push(tenantId);
        if (!isDownload) {
            t_i.energyCostDataList = [];
            t_map[tenantId] = t_i;
        }
    }
    var param = {
        "timeType": ldp.downExcTimeType(timeObj.timeType),
        "energyTypeId": model.energyType,
        "timeFrom": timeFrom,
        "timeTo": timeTo,
        tenantList: tenantIdList,
        isDownload: isDownload ? 1 : 0
    }
    var fName = "FNTBatchEnergyMoneyGridService";
    if (isDownload) {
        pajax.downloadByParam(fName, param);
    } else {
        pajax.get({
            url: fName,
            data: param,
            success: function (resJson) {
                resJson = resJson || [];
                if (resJson.length > 0) {
                    var result = resJson[0];
                    model.bat_energyUnit = result.energyUnit;
                    model.energyCostTimeList = result.timeList;
                    var tempArr = result.tenantList;
                    for (var i = 0; i < tempArr.length; i++) {
                        var item_i = tempArr[i];
                        var item_id = item_i.tenantId;
                        t_map[item_id].energyCostDataList = item_i.dataList;
                    }
                    // 能耗费用报表定义宽度
                    $(".energyCost_table_body .per-scrollbar").attr('style', ' max-height:100%');
                    $(".energyCost_table_body .per-scrollbar-wrap").attr('style', 'max-height:100%');

                    $(".energyCost_table").attr('style', 'min-width:' + (model.energyCostTimeList.length * 202 + 698) + 'px');
                    $(".energyCost_table_top_right, .energyCost_table_center_right , .energyCost_table_body_bottom").attr('style', 'min-width:' + (model.energyCostTimeList.length * 202) + 'px');

                } else {
                    common.msgFailure("获取能耗费用报告失败");
                }
            },
            error: function () {
                common.msgFailure("获取能耗费用报告失败");
            },
            complete: function () {
                common.hidePartLoading();
            }
        });
    }
}

//生成报告按钮
tenantCtrl.createReport_single = function () {
    common.showPartLoading();
    var pdfPage = tenantMngModel.instance().pdfPage;
    switch (pdfPage) {
        case "energyCosttemp": //能耗费用报告
            tenantCtrl.getTenantEnergyMoneyReport();
            break;
        case "historybillingtemp": //历史账单
            tenantCtrl.getHistoryBillArr();
            break;
        case "energyCostcharge": //充值记录
            tenantCtrl.getRechargeRecord();
            break;
        case "energyCostarrears": //欠费账单
            tenantCtrl.getNoPayBillArr();
            break;
    }
    $('#reportCalendar ._combobox_bottom ').hide();
}

//获取单租户充值记录
tenantCtrl.getRechargeRecord = function () {
    var timeObj = $("#reportCalendar").psel();
    var timeFrom = new Date(timeObj.startTime).format("y-M-d h:m:s");
    var timeTo = new Date(timeObj.realEndTime).format("y-M-d h:m:s");
    var selTenant = tenantMngModel.instance().selTenant;
    var report = selTenant.energyCostReport;
    report.timeFrom = new Date(timeObj.startTime).format("y.M.d");
    report.timeTo = new Date(timeObj.endTime).format("y.M.d");
    var energyTypeId = report.energyTypeId;
    report.energyTypeName = tenantCtrl.getEnergyTypeName(energyTypeId);
    var param = {
        "energyTypeId": energyTypeId,
        "tenantId": selTenant.tenantId,
        "timeFrom": timeFrom,
        "timeTo": timeTo,
    };
    pajax.get({
        url: "FNTPrePayRecordService",
        data: param,
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var result = resJson[0];
                report.prePayType = result.prePayType;
                report.billingType = result.billingType;
                report.billingTypeUnit = result.billingTypeUnit;
                report.dataList = result.dataList;
                report.channelType = result.channelType;
            }
        },
        error: function () {
            common.msgFailure("获取租户充值记录失败");
        },
        complete: function () {
            common.hidePartLoading();
        }
    });
}

//批量发送消息
tenantCtrl.sendMsgBat = function () {
    var model = tenantMngModel.instance();
    tenantCtrl.sendMessage(model.energyType, parseInt(model.payType), model.checkedTenantContactArr);
}

//2.发送消息（预付费，后付费）
tenantCtrl.sendMessage = function (energyTypeId, payType, tenantList, selectedMeterId) {
    for (var i = 0; i < tenantList.length; i++) {
        if (tenantList[i].contactMobile == "" || tenantList[i].contactMobile == null) {
            $("#message").pshow({
                text: "手机号不可为空",
                state: "failure"
            });
            return;
        }
    }
    var param = {
        "energyTypeId": energyTypeId,
        "payType": payType,
        "tenantList": tenantList,
        "meterId": selectedMeterId
    };
    pajax.get({
        url: "FNTMessageSendService",
        data: param,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 0) {
                staticEvent.sendMassageHide();
                staticEvent.sendMassageFromListHide();
                staticEvent.modal2hide();
                common.msgSuccess("发送成功");
                setTimeout(function () {
                    if ($("#allMeter li")[0]) {
                        $("#allMeter li")[0].psel(true, false);
                    }
                    tenantMngModel.instance().selTenant.selectedMeterId = '';
                }, 500);
            } else {
                common.msgFailure("发送失败");
            }
        },
        error: function () {
            common.msgFailure("发送失败");
        },
        complete: function () {

        }
    });
}

//获取租户详情
tenantCtrl.getTenantDetail = function (obj, callback) {

    // 清空页码输入框状态
    staticEvent.pageSizeDefault();
    pajax.get({
        url: "FNTTenantDetailService",
        data: {
            "tenantId": obj.tenantId, //租户编码
        },
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var detail = resJson[0];
                // 增加字段用与判断是租户是否含有预付费
                detail.ishasPrepayment = detail.energyList.some(function (val) {
                    return val.payType == 0;
                });

                tenantVM.tran(detail, obj);
                if (typeof (callback) == "function") {
                    callback();
                }
                tenantCtrl.getGlobalAlarmArr('custom');
            } else {
                common.msgFailure("获取租户详情失败");
            }
        },
        error: function () {
            common.msgFailure("获取租户详情失败");
        }
    });
}

//获取租户表号
tenantCtrl.getMeterIds = function (tenantId, energyTypeId, payType) {
    var model = tenantMngModel.instance();
    pajax.post({
        url: "FNTTenantMeterQueryService",
        data: {
            energyTypeId: energyTypeId,
            tenantId: tenantId
        },
        success: function (res) {

            model.meterList = res[0];
        },
        error: function () {
            common.msgFailure("获取表号失败");
        },
        complete: function () {
            $("#pay_type_name").text(payType == 0 ? "充值" : "缴费");
            $('#send_massage_btn').pshow({
                title: payType == 0 ? '发送充值提醒' : '发送缴费提醒'
            })
            $('#send_massage_btn').data("param", {
                energyTypeId: energyTypeId,
                payType: payType
            });
            if (model.meterList.length > 0) {
                setTimeout(function () {
                    $("#allMeter li")[0].psel(true, false);
                }, 0);
            }

        }
    })
}

//获取租户微信充值状态
tenantCtrl.getWXPayState = function (obj) {
    var model = tenantMngModel.instance();
    pajax.post({
        url: "FNTRemoteRechargeStatusQueryService",
        data: {
            tenantId: obj.tenantId
        },
        success: function (res) {
            model.remoteRechargeStatus = res[0].remoteRechargeStatus;
            $("#switch1").psel(!!model.remoteRechargeStatus, false);
        },
        error: function () {
            common.msgFailure("获取状态失败");
        },
        complete: function () {
            $("#switch1").pdisable(!model.operationPermissions.WXPrePay);
        }
    })
}

//编辑租户获取详情
tenantCtrl.getUpdateTenantDetail = function (obj) {
    var model = tenantMngModel.instance();
    pajax.get({
        url: "FNTTenantEditService",
        data: {
            "tenantId": obj.tenantId, //租户编码
        },
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var detail = resJson[0];
                obj["roomList"] = detail.roomList;
                obj["contactList"] = detail.contactList;
                obj["remark"] = detail.remark;
                obj["update_energyList"] = detail.energyList;
                obj["energySplitExpressionList"] = detail.energySplitExpressionList;
                obj["tenantTypeId"] = detail.tenantTypeId;
                obj["tenantTypeName"] = detail.tenantTypeName;
                for (var i = 0; i < model.tenantTypeArr.length; i++) {
                    if (obj.tenantTypeId == model.tenantTypeArr[i].id) {
                        $("#parentYetai").psel(i);
                        break;
                    }
                }
                model.meterformulalist = detail.energySplitExpressionList;
                model.contacts = [{
                    contactName: obj.contactName,
                    contactMobile: obj.contactMobile
                }].concat(obj.contactList);
                Vue.nextTick(function () {
                    for (var i = 0; i < detail.energyList.length; i++) {
                        var i_item = detail.energyList[i];
                        var energyTypeId = i_item.energyTypeId;
                        $("#" + energyTypeId + "_system").psel(true);
                        var ffType = i_item.ffType;
                        if (ffType == 0) {
                            $("#" + energyTypeId + "_0_payment_types").psel(true);
                            $("#" + energyTypeId + "_payment_name").text("预付费");
                            var kfType = i_item.kfType;
                            $("#" + energyTypeId + "_" + kfType + "_deduction_types").psel(true);
                            var cztype = i_item.cztype;
                            $("#" + energyTypeId + "_" + cztype + "_recharge_types").psel(true);
                        } else {
                            $("#" + energyTypeId + "_1_payment_types").psel(true);
                            $("#" + energyTypeId + "_payment_name").text("后付费");
                        }
                        var priceId = i_item.priceTemplate.id;
                        for (var j = 0; j < model.energyTypeArr.length; j++) {
                            var j_item = model.energyTypeArr[j];
                            if (j_item.id == energyTypeId) {
                                var priceArr = j_item.priceArr;
                                for (var k = 0; k < priceArr.length; k++) {
                                    if (priceArr[k].id == priceId) {
                                        $("#" + energyTypeId + "_price_sel").psel(k);
                                        break;
                                    }
                                }
                                break;
                            }
                        }
                    }
                    for (var i = 0; i < detail.energySplitExpressionList.length; i++) {
                        var energyTypeId = detail.energySplitExpressionList[i].energyTypeId;
                        for (var j = 0; j < model.energyTypeArr.length; j++) {
                            if (energyTypeId == model.energyTypeArr[j].id) {
                                $("#" + energyTypeId + "_formula_type").psel(i);
                                break;
                            }
                        }
                    }
                });
            } else {
                common.msgFailure("编辑租户获取详情失败");
            }
            if (obj["update_energyList"][0].kfType == 1) {
                $('#pricePlan').pdisable(true);
            } else if (obj["update_energyList"][0].kfType == 2) {
                $('#pricePlan').pdisable(false);
            }
        },
        error: function () {
            common.msgFailure("编辑租户获取详情失败");
        },
        complete: function () {
            if (ldp.editTementshow.anyncTool) {
                ldp.editTementshow.anyncTool.fire("tenantCtrl.getUpdateTenantDetail_" + obj.tenantId, 2);
            }
        }
    });
}

//更新租户
tenantCtrl.updateTenant = function (updateParam) {
    pajax.post({
        url: "FNTTenantUpdateService",
        data: updateParam,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 0) {
                common.msgSuccess("保存成功");
                var model = tenantMngModel.instance();
                tenantCtrl.precoverGrid();
                tenantCtrl.getTenantDetail(model.selTenant);
                model.currentPage = "tenementDetails";
            } else {
                common.msgFailure("保存失败");
            }
        },
        error: function () {
            common.msgFailure("保存失败");
        }
    });
}

/***
 *验证租户编号
 *tenantId 租户编号
 *callback 回调函数 function(result) {},result boolean  true  可以使用，false 不可以使用
 */
tenantCtrl.verifyTenantId = function (tenantId, callback) {
    var result = false;
    pajax.get({
        url: "FNTTenantIdCheckService",
        data: {
            "tenantId": tenantId,
        },
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                result = resJson[0].isExsit == 0; //验证通过
            }
        },
        complete: function () {
            if (typeof callback == 'function') {
                callback(result);
            }
        },
        error: function () {
            common.msgFailure("验证租户编号错误");
        }
    });
}

//验证拆分公式
tenantCtrl.verifyFormula = function (expressionList, callback) {
    var result = [];
    pajax.get({
        url: "FNTEnergySplitExressionVerifyService",
        data: {
            "expressionList": expressionList,
        },
        success: function (resJson) {
            result = resJson;
        },
        complete: function () {
            if (typeof callback == 'function') {
                callback(result);
            }
        },
        error: function () {
            common.msgFailure("验证拆分公式服务异常");
        }
    });
}

//尖峰谷平类型
tenantCtrl.priceTypeMap = {
    L: "",
    J: "尖峰",
    F: "高峰",
    G: "低谷",
    P: "平段"
}
tenantCtrl.getPriceTypeMap = function (type) {
    return tenantCtrl.priceTypeMap[type]
}
//获取退租详情
tenantCtrl.getLeaveTenantDetail = function () {
    var model = tenantMngModel.instance();
    var date = new Date();
    var selTenant = model.selTenant;
    var lastClearTime = model.maxDateInSurrenderTenancy;
    var selectTime = $("#surrender_tenancy_time").psel().startTime;
    var leaveTime = selectTime + " 00:00:00";
    var nowMonth = date.getMonth() + 1;
    var nowDate = date.getDate();

    nowMonth = nowMonth < 10 ? '0' + nowMonth : '' + nowMonth;
    nowDate = nowDate < 10 ? '0' + nowDate : '' + nowDate;
    var nowTime = date.getFullYear() + nowMonth + nowDate;

    if (lastClearTime && (selectTime.replace(/-/g, '') - lastClearTime < 0)) {
        // 错误提示显示
        $('.s_t_choose_date_error_msg_lt').hide().prev().show();
        $('#leave_btn').pdisable(true);
        return;
    } else if (selectTime.replace(/-/g, '') - nowTime > 0) {
        // 错误提示显示
        $('.s_t_choose_date_error_msg_bf').hide().next().show();
        $('#leave_btn').pdisable(true);
        return;
    } else {
        // 错误提示隐藏
        $('.s_t_choose_date_error_msg_bf').hide();
        $('.s_t_choose_date_error_msg_lt').hide();
        $('#leave_btn').pdisable(false);
    }
    pajax.get({
        url: "FNTTenantLeaveQueryService",
        data: {
            "tenantId": selTenant.tenantId, //租户编码
            leaveTime: leaveTime
        },
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var detail = resJson[0];
                var canClearing = detail.canClearing == 1;
                if (canClearing) {
                    $("#leave_btn").pdisable(false);
                    var energyTypeList = detail.energyTypeList || [];
                    var dataArr_Hou = energyTypeList.filter(function (x) {
                        return x.payType == 1;
                    });
                    var dataArr_Yu = energyTypeList.filter(function (x) {
                        return x.payType == 0;
                    });
                    var totalMoney = 0;
                    var totalCount = 0;
                    for (var i = 0; i < dataArr_Hou.length; i++) {
                        var hou = dataArr_Hou[i];
                        var orderCount = 0;
                        var meterCount = 0;
                        var postPay = hou.postPay;
                        var orderList = postPay.orderList || [];
                        var order_length = orderList.length;
                        orderCount += order_length;
                        for (var j = 0; j < order_length; j++) {
                            totalMoney += orderList[j].money;
                        }
                        var meterList = postPay.meterList || [];
                        var meter_length = meterList.length;
                        meterCount += meter_length;
                        for (var j = 0; j < meter_length; j++) {
                            var meter_j = meterList[j];
                            var meterTotalMoney = 0;
                            var functionList = meter_j.functionList || [];
                            for (var k = 0; k < functionList.length; k++) {
                                var func = functionList[k];
                                func["typeName"] = tenantCtrl.priceTypeMap[func.type];
                                meterTotalMoney += func.money;
                            }
                            meter_j["totalMoney"] = meterTotalMoney;
                            totalMoney += meterTotalMoney;
                        }
                        hou["orderCount"] = orderCount;
                        hou["meterCount"] = meterCount
                        totalCount += orderCount;
                        totalCount += meterCount;
                    }
                    selTenant.leaveContent = {
                        dataArr_Hou: dataArr_Hou,
                        totalMoney_Hou: totalMoney,
                        totalCount_Hou: totalCount,
                        dataArr_Yu: dataArr_Yu,
                    }
                } else {
                    $("#leave_btn").pdisable(true);
                    common.msgFailure(detail.reason);
                }
            } else {
                $("#leave_btn").pdisable(true);
                common.msgFailure("退租租户获取详情失败");
            }
        },
        error: function () {
            $("#leave_btn").pdisable(true);
            common.msgFailure("退租租户获取详情失败");
        }
    });
}

//租户退租
tenantCtrl.leaveTenant = function () {
    var selTenant = tenantMngModel.instance().selTenant;
    var leaveTime = $("#surrender_tenancy_time").psel().startTime + " 00:00:00";
    var leaveContent = selTenant.leaveContent;
    var energyTypeList = leaveContent.dataArr_Hou.concat(leaveContent.dataArr_Yu);
    var param = {
        tenantId: selTenant.tenantId,
        leaveTime: leaveTime,
        energyTypeList: energyTypeList
    }
    pajax.get({
        url: "FNTTenantLeaveService",
        data: param,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length > 0) {
                var resultObj = resJson[0];
                if (resultObj.result == 0) {
                    common.msgSuccess("退租成功");
                    tenantCtrl.getTenantDetail(selTenant);
                    tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
                    staticEvent.confirmSurrenderTenancyHide();
                    // 退租后跳转到租户详情页
                    tenantMngModel.instance().currentPage = 'tenementDetails';
                } else {
                    common.msgFailure("退租失败," + resultObj.message);
                }
            } else {
                common.msgFailure("退租失败");
            }
        },
        error: function () {
            common.msgFailure("退租失败");
        }
    });
}

//获取租户的换表记录
tenantCtrl.getMeterChangeRecordArr = function () {
    var tenantObj = tenantMngModel.instance().selTenant;
    pajax.get({
        url: "FNTMeterChangeRecordService",
        data: {
            "tenantId": tenantObj.tenantId,
        },
        success: function (resJson) {
            resJson = resJson || [];
            tenantObj.meterChangeRecordArr = resJson;
            staticEvent.girdHeight(resJson, '#c_m_r_gird_content');
        },
        error: function () {
            common.msgFailure("获取换表记录失败");
        },
        complete: function () {
            if (tenantObj.meterChangeRecordArr.length == 0) $('#c_m_r_gird_content').pcount(0);
            common.hidePartLoading();
        }
    });
}

// 获取仪表功能号
tenantCtrl.getchangeMeter = function (meterId) {
    var model = tenantMngModel.instance();
    var changeMeter = model.changeMeter;
    pajax.get({
        url: "FNTMeterFunctionService",
        data: {
            "meterId": meterId, //仪表id
        },
        success: function (resJson) {
            var resJson = resJson || [];
            if (resJson.length > 0) {
                var rstData = resJson[0];
                changeMeter.functionList = rstData.functionList;
            } else {
                common.msgFailure("获取仪表功能号失败");
            }
        },
        error: function () {
            common.msgFailure("获取仪表功能号失败");
        }
    });
}

//更换表
tenantCtrl.changeMeter = function () {
    var changeMeter = tenantMngModel.instance().changeMeter;
    var changeTime = $("#change_metter_time").psel().startTime + ":00";

    var check = /^\d+(\.{0,1}\d+){0,1}$/; //非负数正则

    // 获取数据
    for (var i = 0; i < changeMeter.functionList.length; i++) {
        var functionItem = changeMeter.functionList[i];
        var value = $('#_' + functionItem.functionId).pval();

        if (!check.test(value)) {
            changeMeter.isMeter = true;
            return;
        }
        functionItem.data = $('#_' + functionItem.functionId).pval();
    }
    // 换表时间
    changeMeter.changeTime = changeTime;

    pajax.get({
        url: "FNTMeterChangeWithFunctionService",
        data: changeMeter,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length > 0) {
                var detail = resJson[0];
                switch (detail.result) {
                    case 0:
                        $("#right_side_change_meter_window").phide();
                        common.msgSuccess("换表成功");
                        break;
                    case 1:
                        common.msgFailure("不能早于上次换表时间");
                        break;
                    case 2:
                        common.msgFailure("不能晚于当前时间");
                        break;
                    default:
                        common.msgFailure("换表失败");
                        break;
                }

            } else {
                common.msgFailure("换表失败");
            }
        },
        error: function () {
            common.msgFailure("换表失败");
        }
    });
}

//获取电表详情
tenantCtrl.getMeterDetail = function () {
    var model = tenantMngModel.instance();
    var meterDetail = model.meterDetail;
    var meterArr = meterDetail.meterArr;
    var meterId = meterArr[$("#meter_combox").psel().index].meterId;
    var timeObj = $("#meterDetailCalendar").psel();
    var tiemFrom = new Date(timeObj.startTime).format("y-M-d") + " 00:00:00";
    var timeTo = new Date(timeObj.realEndTime).format("y-M-d") + " 00:00:00";
    var density = ptool.formatGranularityToJava($('#meterDetailCalendar'));
    pajax.get({
        url: "FNTMeterEnergyDetailService",
        data: {
            "energyTypeId": meterDetail.energyTypeId, //能耗类型ID
            "meterId": meterId, //仪表id
            "timeFrom": tiemFrom,
            "timeTo": timeTo,
            "density": density, //0 15分钟 1时  2日   4月   5年
        },
        success: function (resJson) {
            var resJson = resJson || [];
            if (resJson.length > 0) {
                var rstData = resJson[0];
                meterDetail.dataUnit = rstData.unit;
                var functionList = rstData.functionList;
                meterDetail.chart = staticEvent.lineChart(rstData.unit);
                var had = false;
                var chartSymbols = ['circle', 'square', 'triangle', 'triangle-down'];
                var colors = ['#07ABD2', '#E16CB3', '#F79862', '#C1D013'];
                for (var j = 0; j < functionList.length; j++) {
                    var func_j = functionList[j];
                    var func_Name = func_j.functionName;
                    var dataArr = func_j.dataList;
                    for (var i = 0; i < dataArr.length; i++) {
                        var data_i = dataArr[i];
                        data_i.x = new Date((data_i.x + "").replace(/-/g, '/')).getTime();
                    }
                    meterDetail.chart.addSeries({
                        name: func_Name,
                        data: dataArr,
                        color: colors[j],
                        marker: {
                            symbol: chartSymbols[j]
                        }
                    }); //    [0].setData(dataArr);
                    had = true;
                }
                if (!had) {
                    meterDetail.chart.addSeries({
                        name: "",
                        data: []
                    });
                }
            } else {
                common.msgFailure("获取电表详情失败");
            }
        },
        error: function () {
            common.msgFailure("获取电表详情失败");
        }
    });
}

//租户搜索
tenantCtrl.searchTenant = function (putValue) {
    pajax.get({
        url: "FNTTenantLikeQueryService",
        data: {
            "like": putValue, //租户编码or租户名称
        },
        success: function (resJson) {
            resJson = resJson || [];
            tenantMngModel.instance().searchResultArr = resJson;
        },
        error: function () {
            common.msgFailure("搜索失败");
        }
    });
}

//激活租户
tenantCtrl.activeTenant = function (e) {
    var flag = $(e.target).attr('data-flag');
    var activeTime = flag == '0' ? $("#start_lessee_from_list_time").psel().startTime + " 00:00:00" : $("#start_lessee_time").psel().startTime + " 00:00:00";
    var selTenant = tenantMngModel.instance().selTenant;
    pajax.get({
        url: "FNTTenantActiveService",
        data: {
            "tenantId": selTenant.tenantId,
            "activeTime": activeTime,
        },
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length > 0) {
                var resultObj = resJson[0];
                if (resultObj.result == 0) {
                    selTenant.tenantStatus = 1;
                    tenantCtrl.getTenantDetail(selTenant); //获取租户详情
                    common.msgSuccess("激活成功");
                    staticEvent.startLesseeHide();
                    staticEvent.startLesseeFromListHide();
                    tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
                } else {
                    common.msgFailure("激活失败");
                }
            } else {
                common.msgFailure("激活失败");
            }
        },
        error: function () {
            common.msgFailure("激活失败");
        }
    });
}

//删除租户
tenantCtrl.deleteTenant = function (selTenant) {
    pajax.get({
        url: "FNTTenantRemoveService",
        data: {
            "tenantId": selTenant.tenantId,
        },
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length > 0) {
                var resultObj = resJson[0];
                if (resultObj.result == 0) {
                    tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
                    staticEvent.deleteLesseeBtnHide();
                    staticEvent.deleteLesseeFromListBtnHide();
                    common.msgSuccess("删除成功");
                    tenantMngModel.instance().currentPage = "tementListPage";
                } else {
                    common.msgFailure("删除失败");
                }
            } else {
                common.msgFailure("删除失败");
            }
        },
        error: function () {
            common.msgFailure("删除失败");
        }
    });
}

//单租户结算查询
tenantCtrl.tenantSettle_single = function () {
    var model = tenantMngModel.instance();
    var date = new Date();

    var selTenant = model.selTenant;
    var lastClearTime = model.settleLastClearingTime;
    var tenantIdList = [selTenant.tenantId];
    var selectTime = $("#settle_accounts_time").psel().startTime;
    var clearingTime = selectTime + " 00:00:00";
    var nowMonth = date.getMonth() + 1;
    var nowDate = date.getDate();
    var canClearFlag = true;

    nowMonth = nowMonth < 10 ? '0' + nowMonth : '' + nowMonth;
    nowDate = nowDate < 10 ? '0' + nowDate : '' + nowDate;
    var nowTime = date.getFullYear() + nowMonth + nowDate;

    if (lastClearTime && (selectTime.replace(/-/g, '') - lastClearTime.replace(/-|:|\s/g, '').substr(0, 8) <= 0)) {
        // 错误提示显示
        common.hidePartLoading();
        $('.s_a_choose_date_error_msg_lt').hide().prev().show();
        $('#settle_ok > div').pdisable(true);
        canClearFlag = false;
        // return;
    } else if (selectTime.replace(/-/g, '') - nowTime > 0) {
        // 错误提示显示
        common.hidePartLoading();
        $('.s_a_choose_date_error_msg_bf').hide().next().show();
        $('#settle_ok > div').pdisable(true);
        canClearFlag = false;
        // return;
    } else {
        // 错误提示隐藏
        $('.s_a_choose_date_error_msg_bf').hide();
        $('.s_a_choose_date_error_msg_lt').hide();
        $('#settle_ok > div').pdisable(false);
    }

    function success(resJson) {
        var canClearing = resJson.canClearing == 1;
        var settle = selTenant.settle;
        settle.energyUnit = resJson.energyUnit;
        settle.dataArr = resJson.tenantList;
        if (settle.dataArr != '') {
            var currentBillingEnergy = settle.dataArr[0].currentBillingEnergy;
            var currentBillingMoney = settle.dataArr[0].currentBillingMoney;
            settle.dataArr[0].currentBillingEnergy = tenantCtrl.numberFormat(currentBillingEnergy, tenantCtrl.fixType_dynamic, true);
            settle.dataArr[0].currentBillingMoney = tenantCtrl.numberFormat(currentBillingMoney, tenantCtrl.fixType_dynamic, true);
        }
        if (canClearing) { //可以结算
            if (canClearFlag) {
                $("#settle_ok_button").pdisable(false);
            } else {
                $("#settle_ok_button").pdisable(true);
            }
            settle.clearingTime = clearingTime;
        } else {
            $("#settle_ok_button").pdisable(true);
        }
        model.meterFaultList = resJson.meterFaultList;
        model.clearingTimeFaultList = resJson.clearingTimeFaultList;
    }
    tenantCtrl.tenantSettle(tenantIdList, selTenant.settle.energyTypeId, clearingTime, success);
}

//租户批量结算查询
tenantCtrl.tenantSettle_bat = function () {
    var model = tenantMngModel.instance();
    var date = new Date();
    var checkedTenantArr = model.checkedTenantArr;
    var tenantIdList = [];
    var t_map = {};
    for (var i = 0; i < checkedTenantArr.length; i++) {
        var t = checkedTenantArr[i];
        var tenantId = t.tenantId;
        tenantIdList.push(tenantId);
        t_map[tenantId] = t;
    }
    var selectTime = $("#formtime").psel().startTime;
    var lastClearTime = model.maxDateInSettlement;
    var clearingTime = selectTime + " 00:00:00";

    var nowMonth = date.getMonth() + 1;
    var nowDate = date.getDate();

    nowMonth = nowMonth < 10 ? '0' + nowMonth : '' + nowMonth;
    nowDate = nowDate < 10 ? '0' + nowDate : '' + nowDate;
    var nowTime = date.getFullYear() + nowMonth + nowDate;
    if (lastClearTime && (selectTime.replace(/-/g, '') - lastClearTime <= 0)) {
        // 错误提示显示
        common.hidePartLoading();
        $('.s_choose_date_error_msg_lt').hide().prev().show();
        $('#bat_settle_ok').pdisable(true);
        return;
    } else if (selectTime.replace(/-/g, '') - nowTime > 0) {
        // 错误提示显示
        common.hidePartLoading();
        $('.s_choose_date_error_msg_bf').hide().next().show();
        $('#bat_settle_ok').pdisable(true);
        return;
    } else {
        // 错误提示隐藏
        $('.s_choose_date_error_msg_bf').hide();
        $('.s_choose_date_error_msg_lt').hide();
        $('#bat_settle_ok').pdisable(false);
    }

    function success(resJson) {
        var canClearing = resJson.canClearing == 1;
        model.bat_energyUnit = resJson.energyUnit;
        if (canClearing) { //可以结算
            $("#bat_settle_ok").pdisable(false);
            for (var i = 0; i < resJson.tenantList.length; i++) {
                var item_i = resJson.tenantList[i];
                var id = item_i.tenantId;
                t_map[id].settle_bat = item_i;
            }
            model.selTenantFaultType = "portion";
        } else { //不可以结算
            $("#bat_settle_ok").pdisable(true);
            model.selTenantFaultType = "all";
        }
        model.meterFaultList = resJson.meterFaultList;
        model.clearingTimeFaultList = resJson.clearingTimeFaultList;

    }
    tenantCtrl.tenantSettle(tenantIdList, model.energyType, clearingTime, success);
}

//租户结算-查询
tenantCtrl.tenantSettle = function (tenantIdList, energyTypeId, clearingTime, success) {
    var param = {
        "energyTypeId": energyTypeId,
        "tenantList": tenantIdList,
        "clearingTime": clearingTime,
    };
    pajax.get({
        url: "FNTBatchPostPayBillingQueryService",
        data: param,
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length == 0) {
                $("#bat_settle_ok").pdisable(true);
                $("#settle_ok_button").pdisable(true);
                common.msgFailure("查询结算数据失败");
            } else {
                var rstData = resJson[0];
                var canClearing = rstData.canClearing == 1;
                if (canClearing) {
                    var tenantList = rstData.tenantList;
                    for (var i = 0; i < tenantList.length; i++) {
                        var t = tenantList[i];
                        t.currentClearingTime = clearingTime;
                        t.currentBillingEnergy = tenantCtrl.numberFormat(t.currentBillingEnergy, tenantCtrl.fixType_dynamic, true);
                        t.currentBillingMoney = tenantCtrl.numberFormat(t.currentBillingMoney, tenantCtrl.fixType_money, true);
                    }
                }
                success(rstData);
            }
        },
        error: function () {
            $("#bat_settle_ok").pdisable(true);
            $("#settle_ok_button").pdisable(true);
            common.msgFailure("查询结算数据失败");
        },
        complete: function () {
            common.hidePartLoading();
        }
    });
}

//单租户保存结算
tenantCtrl.saveSettle_single = function () {
    $("#re_settle_ok").show().siblings().hide();
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    var settle = selTenant.settle;
    var dataArr = settle.dataArr;

    function callback(state, resJson) {
        if (state == "success") {
            $("#settle_back").show().siblings().hide();
            var order = resJson[0];
            settle.orderId = order.orderId;
            tenantCtrl.getTenantDetail(selTenant);
            tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
        }
        staticEvent.settleState(state);
    }
    tenantCtrl.saveSettle(dataArr, settle.energyTypeId, callback);
}

//批量保存结算
tenantCtrl.saveSettle_bat = function () {
    $("#bat_re_settle_ok").show().siblings().hide();
    var model = tenantMngModel.instance();
    var checkedTenantArr = model.checkedTenantArr;
    var dataArr = [];
    for (var i = 0; i < checkedTenantArr.length; i++) {
        if (!checkedTenantArr[i].settle_bat.currentBillingEnergy) continue;
        dataArr.push(checkedTenantArr[i].settle_bat);
    }

    function callback(state, resJson) {
        staticEvent.bat_settleState(state);
        tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
    }
    tenantCtrl.saveSettle(dataArr, model.energyType, callback);
}

//保存结算
tenantCtrl.saveSettle = function (tenantList, energyTypeId, callback) {
    var param = {
        "energyTypeId": energyTypeId,
        "tenantList": tenantList,
    };
    pajax.get({
        url: "FNTBatchPostPayBillingSaveService",
        data: param,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 0) {
                callback("failure");
            } else {
                callback("success", resJson);
            }
        },
        error: function () {
            callback("failure");
            common.msgFailure("服务异常,结算失败");
        }
    });
}

//获取结算明细
tenantCtrl.getSettleDetail = function (settleDetail) {
    var param = {
        "energyTypeId": settleDetail.energyTypeId,
        "tenantId": settleDetail.tenantId,
        "timeFrom": settleDetail.timeFrom,
        "timeTo": settleDetail.timeTo,
    };
    pajax.get({
        url: "FNTBatchPostPayBillingDetailService",
        data: param,
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var detail = resJson[0];
                settleDetail.energyUnit = detail.energyUnit;
                settleDetail.dataList = detail.dataList
            } else {
                common.msgFailure("获取结算明细失败");
            }
        },
        error: function () {
            common.msgFailure("获取结算明细失败");
        },
        complete: function () {
            $('#bill_detail_loading').phide();
        }
    });
}

//充值前查询
tenantCtrl.beforeRecharge = function (energyTypeId, prePayType, meterId) {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    var energyTypeName = tenantCtrl.getEnergyTypeName(energyTypeId);
    var recharge = {
        energyTypeId: energyTypeId,
        energyTypeName: energyTypeName,
        meterId: meterId,
        billingType: "",
        remainData: "",
        dataUnit: "",
        refreshTime: "",
        typeName: energyTypeName + "-预付费-" + (prePayType == 1 ? "软件充表扣" : "软件充软件扣"),
        prePayType: prePayType,
        orgRemainData: "",
        rechargeValue: "",
        weiDaoZhangCount: ""
    }
    selTenant.recharge = recharge;
    pajax.get({
        url: "FNTPrePayBeforePayService",
        data: {
            "tenantId": selTenant.tenantId, //租户编码
            "meterId": meterId,
            "energyTypeId": energyTypeId,
            "prePayType": prePayType, // 1 软件充值仪表扣费 2 软件充值软件扣费
        },
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var data = resJson[0];
                recharge.billingType = data.billingType;
                recharge.remainData = data.remainData;
                recharge.dataUnit = data.billingTypeUnit;
                recharge.refreshTime = data.lastUpdateTime;
                recharge.isBeiDian = data.isBeiDian;
                recharge.returnData = data.returnData;
                recharge.saleCount = data.saleCount;
                recharge.weiDaoZhangCount = data.weiDaoZhangCount;
                model.beforeRefundCostData.isRefundCost = false;
            } else {
                common.msgFailure("充值前查询失败");
            }
        },
        error: function () {
            common.msgFailure("充值前查询失败");
        },
        complete: function () {
            $("#rechargeLoadingSoft").phide();
            $("#rechargeLoadingMeter").phide();
        }
    });
}

// 手动刷新loading
tenantCtrl.refreshIcon = function () {
    if (!event) return;
    var $el = event.currentTarget;
    tenantMngModel.data.currentTarget = $el;
    $($el).removeClass('refresh');
    $($el).addClass('per-loading-nomal_pic');

}
// 手动刷新loading停止
tenantCtrl.refreshIconStop = function () {
    var currentTarget = tenantMngModel.data.currentTarget;
    $(currentTarget).addClass('refresh');
    $(currentTarget).removeClass('per-loading-nomal_pic');
    tenantMngModel.data.currentTarget = null;
}

//充值前数据手动刷新
tenantCtrl.refreshRechargeData = function (flag) {

    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    var recharge = selTenant.recharge;
    var beforeRefundCostData = model.beforeRefundCostData;
    var params = {
        "tenantId": selTenant.tenantId, //租户编码
        "meterId": recharge.meterId || null,
        "energyTypeId": flag == 1 ? recharge.energyTypeId : beforeRefundCostData.energyTypeId,
        "prePayType": flag == 1 ? recharge.prePayType : flag, // 1 软件充值仪表扣费 2 软件充值软件扣费
    }
    tenantCtrl.refreshIcon();

    pajax.get({
        url: "FNTPrePayRefreshService",
        data: params,
        success: function (resJson) {
            resJson = resJson || [];
            if (resJson.length > 0) {
                var data = resJson[0];
                if (model.beforeRefundCostData.isRefundCost) {
                    model.beforeRefundCostData.lastUpdateTime = data.lastUpdateTime;
                    model.beforeRefundCostData.remainData = data.remainData;
                }
                recharge.refreshTime = data.lastUpdateTime;
                model.$set(recharge, 'remainData', data.remainData);
            } else {
                common.msgFailure("刷新失败");
            }
        },
        error: function () {
            common.msgFailure("刷新失败");
        },
        complete: function () {
            tenantCtrl.refreshIconStop();
            $("#rechargeLoadingMeterSuccess").phide();
            $("#refundLoadingMeterSuccess").phide();
        }
    });
}

//租户充值
tenantCtrl.tenantRecharge = function (value) {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant
    var recharge = selTenant.recharge;

    var params = {
        "remainData": recharge.remainData, // 剩余量
        "tenantId": selTenant.tenantId, //租户编码
        "meterId": recharge.meterId || null,
        "energyTypeId": recharge.energyTypeId,
        "prePayType": recharge.prePayType, // 1 软件充值仪表扣费 2 软件充值软件扣费
        "value": value,
        "billingType": recharge.billingType, // 0,//0
    };
    model.oldRemainData = recharge.remainData;
    pajax.post({
        url: "FNTPrePayPayService",
        data: params,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length != 0) {
                recharge.orgRemainData = recharge.remainData;
                tenantCtrl.refreshRechargeData(1);
                var data = resJson[0];
                recharge.isBeiDian = data.isBeiDian;
                recharge.realPayData = data.realPayData;
                recharge.deductData = data.deductData;
                recharge.rechargeValue = value;
                if (recharge.prePayType == 1) {
                    staticEvent.topUpRechargeMeterBackEvent();
                } else {
                    staticEvent.topUpRechargeSoftwareSuccessEvent();
                }
            } else {
                if (recharge.prePayType == 1) {
                    staticEvent.topUpRechargeMeterBackEvent();
                } else {
                    staticEvent.topUpRechargeSoftwareErrorEvent();
                }
            }
            setTimeout(function () {
                $("#recharge_details").pdisable(false);
            }, 0);
        },
        complete: function () {
            staticEvent.recharge_ing("hide");
        },
        error: function () {
            common.msgFailure("服务异常,充值失败");
        }
    });
}

//获取能耗类型名称
tenantCtrl.getEnergyTypeName = function (energyTypeId) {
    var model = tenantMngModel.instance();
    var energyArr = model.energyTypeArr;
    var energyTypeName = "";
    for (var i = 0; i < energyArr.length; i++) {
        if (energyArr[i].id == energyTypeId) {
            energyTypeName = energyArr[i].name;
            break;
        }
    }
    return energyTypeName
}

//缴费前
tenantCtrl.beforePayOrder = function (energyTypeId, orderArr, yingJiaoJinE) {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    var energyTypeName = tenantCtrl.getEnergyTypeName(energyTypeId);
    var totalMoney = 0;
    if (yingJiaoJinE === undefined) {
        for (var i = 0; i < orderArr.length; i++) {
            totalMoney += orderArr[i].money;
        }
    } else {
        totalMoney = yingJiaoJinE;
    }
    var payOrder = {
        energyTypeId: energyTypeId,
        energyTypeName: energyTypeName,
        totalMoney: totalMoney,
        dataArr: orderArr,
    }
    selTenant.payOrder = payOrder;
}

//租户缴费
tenantCtrl.payBill = function (selTenant, flag) {
    var model = tenantMngModel.instance();
    var orderIdList = [];
    var energyTypeId = "";
    if (flag == "bat_single") {
        energyTypeId = model.energyType;
        for (var i = 0; i < selTenant.noPayBillArr.length; i++) {
            orderIdList.push(selTenant.noPayBillArr[i].orderId);
        }
    } else {
        var payOrder = selTenant.payOrder;
        energyTypeId = payOrder.energyTypeId;
        for (var i = 0; i < payOrder.dataArr.length; i++) {
            orderIdList.push(payOrder.dataArr[i].orderId);
        }
    }
    var param = {
        "buildingId": selTenant.buildingId, //租户编码
        "tenantId": selTenant.tenantId, //租户编码
        "energyTypeId": energyTypeId,
        "orderList": orderIdList,
    };
    pajax.get({
        url: "FNTPostPayPayService",
        data: param,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 0) {
                model.isRefresh = true;
                if (flag == "bat_single") {
                    selTenant.noPayBill_Ready = true;
                    staticEvent.paySuccessShow();
                } else {
                    tenantCtrl.getTenantDetail(selTenant);
                    staticEvent.payTheFeesSuccessEvent();
                }
            } else {
                if (flag == "bat_single") {
                    staticEvent.payErrorShow();
                } else {
                    staticEvent.payTheFeesErrorEvent();
                }
            }
        },
        complete: function () {
            if (flag == "bat_single") {
                staticEvent.payInhide("hide");
            } else {
                staticEvent.pay_ing("hide");
            }
        },
        error: function () {
            common.msgFailure("服务异常,缴费失败");
            if (flag == "bat_single") {
                staticEvent.payErrorShow();
            } else {
                staticEvent.payTheFeesErrorEvent();
            }
        }
    });
}

//租户批量缴费
tenantCtrl.payBill_bat = function () {
    var model = tenantMngModel.instance();
    var energyTypeId = model.energyType;
    var tenantArr = model.checkedTenantArr.filter(function (x) {
        return !x.noPayBill_Ready;
    });
    var tenantList = [];
    for (var i = 0; i < tenantArr.length; i++) {
        var t = tenantArr[i];
        var orderList = [];
        for (var j = 0; j < t.noPayBillArr.length; j++) {
            orderList.push(t.noPayBillArr[j].orderId);
        }
        tenantList.push({
            "tenantId": t.tenantId, //租户编码
            "energyTypeId": energyTypeId,
            "orderList": orderList
        });
    }
    var param = {
        "tenantList": tenantList,
    }
    pajax.get({
        url: "FNTBatchPostPayPayService",
        data: param,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 0) {
                model.isRefresh = true;
                staticEvent.payBothSuccessShow();
            } else {
                setTimeout(function () {
                    staticEvent.payBothErrorShow();
                }, 500);
            }
        },
        complete: function () {
            staticEvent.payBothInhide("hide");
        },
        error: function () {
            setTimeout(function () {
                staticEvent.payBothErrorShow();
            }, 500);
        }
    });
}

//批量缴费成功后
tenantCtrl.payBill_bat_afterSuccessOk = function () {
    staticEvent.payBothSuccessHide();
    setTimeout(function () {
        var model = tenantMngModel.instance();
        var tenantArr = model.checkedTenantArr.filter(function (x) {
            return !x.noPayBill_Ready;
        });
        for (var i = 0; i < tenantArr.length; i++) {
            tenantArr[i].noPayBill_Ready = true;
        }
    }, 500)
}

//根据html内容下载文件
tenantCtrl.downFileByHtmlContent = function (htmlContent, fileName, fileType) {
    var type = fileType == "img" ? 1 : 0;
    pajax.downloadByParam("FNSPdfAndImgBuilderService", {
        htmlContent: htmlContent,
        type: type,
        fileName: fileName
    });
}

//获取缴费记录
tenantCtrl.getFeeRecord = function (isSingle, isDownload) {
    $('#fee_record_calendar ._combobox_bottom ').hide();
    var model = tenantMngModel.instance();
    var timeObj = $("#" + (isSingle ? "fee_record_calendar" : "fee_record_bat_time")).psel();
    var timeFrom = new Date(timeObj.startTime).format('y-M-d') + " 00:00:00";
    var timeTo = new Date(timeObj.realEndTime).format('y-M-d') + " 00:00:00";
    var energyTypeId = isSingle ? model.selTenant.feeRecordArr_energyTypeId : model.energyType;
    var tenantList = isSingle ? [model.selTenant] : model.checkedTenantArr;
    var t_map = {};
    var tenantIdList = [];
    for (var i = 0; i < tenantList.length; i++) {
        var t = tenantList[i];
        var tenantId = t.tenantId;
        tenantIdList.push(tenantId);
        if (!isDownload) {
            t.feeRecordArr = [];
            t_map[tenantId] = t;
        }
    }
    var param = {
        "timeType": ldp.downExcTimeType(timeObj.timeType),
        "energyTypeId": energyTypeId,
        "tenantList": tenantIdList,
        "timeFrom": timeFrom,
        "timeTo": timeTo,
        "isDownload": isDownload ? 1 : 0, //0 为查询记录 1 为下载  当为1 时
    }
    var fName = "FNTBatchPostPayBillGridService";
    if (isDownload) {
        pajax.downloadByParam(fName, param);
    } else {
        pajax.get({
            url: fName,
            data: param,
            success: function (resJson) {
                resJson = resJson || [];
                if (resJson.length > 0) {
                    var detail = resJson[0];
                    var tempList = detail.tenantList;
                    for (var i = 0; i < tempList.length; i++) {
                        var item_i = tempList[i];
                        var t_id = item_i.tenantId;
                        var orderList = item_i.orderList;
                        for (var j = 0; j < orderList.length; j++) {
                            var order_j = orderList[j];
                            order_j.money = tenantCtrl.numberFormat(order_j.money, tenantCtrl.fixType_money, true);
                        }
                        t_map[t_id].feeRecordArr = orderList;
                    }
                }
            },
            error: function () {
                common.msgFailure("获取缴费记录失败");
            },
            complete: function () {
                common.hidePartLoading();
            }
        });
    }
}

//租户欠费帐单查询_批量
tenantCtrl.getNoPayBillArr_bat = function (isDownload) {
    var model = tenantMngModel.instance();
    if (!isDownload) {
        model.bat_energyUnit = "";
    }
    var checkedTenantArr = model.checkedTenantArr;
    var t_map = {};
    var tenantIdList = [];
    for (var i = 0; i < checkedTenantArr.length; i++) {
        var t_i = checkedTenantArr[i];
        var tenantId = t_i.tenantId;
        tenantIdList.push(tenantId);
        if (!isDownload) {
            t_i.noPayBillArr = [];
            t_i.noPayBillsTotalMoney = "";
            t_map[tenantId] = t_i;
        }
    }
    var param = {
        "energyTypeId": model.energyType,
        "tenantList": tenantIdList,
        isDownload: isDownload ? 1 : 0
    };
    var fName = "FNTBatchPostPayArrearageGridService";
    if (isDownload) {
        pajax.downloadByParam(fName, param);
    } else {
        pajax.get({
            url: fName,
            data: param,
            success: function (resJson) {
                resJson = resJson || [];
                if (resJson.length > 0) {
                    var result = resJson[0];
                    model.bat_energyUnit = result.energyUnit;
                    var tempArr = result.tenantList;
                    for (var i = 0; i < tempArr.length; i++) {
                        var item_i = tempArr[i];
                        var item_id = item_i.tenantId;
                        t_map[item_id].noPayBillArr = item_i.orderList;
                        t_map[item_id].noPayBillsTotalMoney = item_i.totalMoney;
                    }
                } else {
                    common.msgFailure("获取租户欠费账单失败");
                }
            },
            error: function () {
                common.msgFailure("获取租户欠费账单失败");
            },
            complete: function () {
                common.hidePartLoading();
            }
        });
    }
}

//能耗充值记录（批量）
tenantCtrl.getRechargeRecord_bat = function (isDownload, timeController) {
    var model = tenantMngModel.instance();
    var fName = '';

    switch (model.selFeeType.id) {
        case 1:
            fName = "FNTBatchPrePay2GridService";
            model.currentPage = "prepaidRecordsSofterMeter";
            break;

        case 2:
            fName = "FNTBatchPrePay3GridService";
            model.currentPage = "recharge";
            break;
    }

    var timeObj = $("#" + timeController).psel();
    var timeFrom = new Date(timeObj.startTime).format("y-M-d h:m:s");
    var timeTo = new Date(timeObj.realEndTime).format("y-M-d h:m:s");
    var checkedTenantArr = model.checkedTenantArr;
    var t_map = {};
    var tenantIdList = [];
    for (var i = 0; i < checkedTenantArr.length; i++) {
        var t_i = checkedTenantArr[i];
        var tenantId = t_i.tenantId;
        tenantIdList.push(tenantId);
        if (!isDownload) {
            t_i.energyCostDataList = [];
            t_map[tenantId] = t_i;
        }
    }

    var param = {
        "timeType": ldp.downExcTimeType(timeObj.timeType),
        "energyTypeId": model.energyType,
        "timeFrom": timeFrom,
        "timeTo": timeTo,
        tenantList: tenantIdList,
        isDownload: isDownload ? 1 : 0
    }
    // console.log(param);
    common.showPartLoading();

    if (isDownload) {
        pajax.downloadByParam(fName, param);
        common.hidePartLoading();
    } else {
        pajax.get({
            url: fName,
            data: param,
            success: function (resJson) {
                resJson = resJson || [];
                if (resJson instanceof Array && resJson.length > 0) {
                    switch (model.selFeeType.id) {
                        case 1:
                            // console.log(resJson);
                            var prepaidRecordsSofterMeterData = model.prepaidRecordsSofterMeterData = resJson[0];
                            var tenantList = prepaidRecordsSofterMeterData.tenantList;

                            var numArrOne = [],
                                numArrTwo = [];

                            for (var i = 0; i < tenantList.length; i++) {
                                var roomList = tenantList[i].roomList;
                                numArrOne.push(roomList.length, 1);
                                for (var j = 0; j < roomList.length; j++) {
                                    var orderList = roomList[j].orderList || [];
                                    numArrTwo.push(orderList.length, 0);
                                }
                            }
                            numArrOne.push(1);
                            numArrTwo.push(0);
                            Vue.nextTick(function () {

                                for (var i = 1; i < 9; i++) {
                                    var count = 0;
                                    $('.p_r_s_m_box .room_code_' + i).each(function (idx) {
                                        var sum = 0;
                                        if (numArrOne[idx] == 0) {
                                            $(this).css('height', 36 + 'px');
                                        } else {
                                            for (var j = 0; j < numArrOne[idx]; j++) {
                                                sum += (numArrTwo[count] || 1);
                                                count++;
                                            }
                                            $(this).css('height', sum * 36 + 'px');
                                        }
                                    });
                                    $('.p_r_s_m_box .sort_code_' + i).each(function (idx) {
                                        $(this).css('height', (numArrTwo[idx] || 1) * 36 + 'px');
                                    });
                                }

                                if (tenantList.length != 0) {
                                    var heightArr = [];

                                    $('.p_r_s_m_gird_item_box').find('li').each(function () {
                                        heightArr.push($(this).outerHeight());
                                    })

                                    var boxHeight = heightArr.reduce(function (prev, cur, index, arr) {
                                        return prev + cur;
                                    });

                                    if (boxHeight > $('.p_r_s_m_gird').outerHeight() - $('.p_r_s_m_gird_tit').outerHeight()) {
                                        boxHeight = $('.p_r_s_m_gird').outerHeight() - $('.p_r_s_m_gird_tit').outerHeight();
                                    }
                                    $('.p_r_s_m_gird_body').outerHeight(boxHeight + 1);
                                } else {
                                    $('.p_r_s_m_box .per-grid-nodata').show();
                                }
                            });
                            break;

                        case 2:
                            var result = resJson[0];
                            var prepaidRecordsSofterSoftData = model.prepaidRecordsSofterSoftData = result;
                            var tenantList = prepaidRecordsSofterSoftData.tenantList;
                            for (var i = 0; i < checkedTenantArr.length; i++) {
                                checkedTenantArr[i].tenantAmountSum = result.tenantList[i].tenantAmountSum;
                                checkedTenantArr[i].tenantMoneySum = result.tenantList[i].tenantMoneySum;
                            }
                            model.bat_energyUnit = result.energyUnit;
                            var tempArr = result.tenantList;
                            for (var i = 0; i < tempArr.length; i++) {
                                var item_i = tempArr[i];
                                var item_id = item_i.tenantId;
                                t_map[item_id].rechargeRecordList_bat = item_i.orderList;
                            }
                            break;
                    }
                } else {
                    common.msgFailure("获取充值记录失败");
                }
            },
            error: function () {
                common.msgFailure("获取充值记录失败");
            },
            complete: function () {
                common.hidePartLoading();
            }
        });
    }
}

//软件充表扣保存充值记录
tenantCtrl.saveRecharge_soft_meter = function (selTenant) {
    var recharge = selTenant.recharge;
    var param = {
        "remainData": recharge.remainData, // 剩余量
        "tenantId": selTenant.tenantId, //租户编码
        "meterId": recharge.meterId,
        "energyTypeId": recharge.energyTypeId,
        "prePayType": 1, // 1 软件充值仪表扣费
        "value": recharge.rechargeValue,
        "billingType": recharge.billingType, //0
        "realPayData": recharge.realPayData, //实际充值金额
        "deductData": recharge.deductData, //扣除预退费金额
    }
    pajax.get({
        url: "FNTPrePayPaySaveService",
        data: param,
        success: function (resJson) {
            if ((resJson instanceof Array) && resJson.length == 0) {
                staticEvent.topUpRechargeMeterBackCloseEvent();
            } else {
                common.msgFailure("保存充值记录失败");
            }
        },
        error: function () {
            common.msgFailure("保存充值记录失败");
        }
    });
}

//界面事件----------↓↓↓↓↓---------------

//添加租户筛选房间
tenantCtrl.filterRoom = function () {
    var selBuilding = tenantMngModel.instance().addTenantSelBuilding;
    var floorFilter = $("#floor_filter").psel();
    var floorId = "";
    if (floorFilter) {
        floorId = selBuilding.floorArr[floorFilter.index].id;
    }
    var search = $("#room_search").pval().key.trim().toUpperCase();
    var showArr = selBuilding.roomArr.filter(function (item_i) {
        return (floorId == "_all" || item_i.floorId == floorId) && (search == "" || item_i.code.toUpperCase().indexOf(search) >= 0) && item_i.status == 0;
    });
    selBuilding.roomShowArr = showArr;
}

//切换租户状态
tenantCtrl.t_status_filter = function (obj) {
    tenantMngModel.data.selTenantStatus = obj;
    tenantMngModel.data.tenantFilterParam = [];
    tenantMngModel.data.selFeeType = new tenantFilterParam();
    $("#t_feetype_cbx_hou").precover("");
    $("#t_feetype_cbx").precover("选择扣费类型");
    $("#t_showdata_cbx").precover("选择显示项");
    var $t_energy_type = $("#t_energy_type");
    if (obj.id == 2) {
        $t_energy_type.pdisable(true);
    } else {
        $t_energy_type.pdisable(false);
    }
    tenantCtrl.getTenantFilterParam();
}

//切换能耗类型
tenantCtrl.t_energytype_filter = function (obj) {
    tenantMngModel.data.tenantArr_unit = "";
    tenantMngModel.data.tenantFilterParam = [];
    tenantMngModel.data.selFeeType = new tenantFilterParam();
    $("#t_feetype_cbx_hou").precover("");
    $("#t_feetype_cbx").precover("选择扣费类型");
    $("#t_showdata_cbx").precover("选择显示项");
    tenantMngModel.data.selEnergyTypePayType = obj;
    tenantCtrl.getTenantFilterParam();
}

//切换建筑
tenantCtrl.t_building_filter = function () {
    tenantCtrl.getTenantFilterParam();
}
//远程充值部分3.6版本 ++切换建筑
tenantCtrl.remotetopup_building_filter = function () {
    staticEvent.Remotetopupshow();
    tenantCtrl.getBuildingArr();
    tenantCtrl.addRemotetopup(); //还原表格并重新获取数据;
}
//自动发送短信部分3.6版本 ++切换建筑
tenantCtrl.addtenant_building_filter = function () {
    tenantCtrl.messageBlacklist();
    tenantCtrl.getBuildingArr();
    tenantCtrl.addtenantname(); //还原表格并重新获取数据;
}

//切换扣费类型
tenantCtrl.t_feetype_filter = function (obj) {
    tenantMngModel.data.selFeeType = obj;
    tenantMngModel.data.selLimitType = 0;
    tenantMngModel.data.selShowData.id = -1;
    tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
}

//切换显示数据
tenantCtrl.t_showdata_filter = function (obj) {
    tenantMngModel.data.selShowData = obj;
    if (obj.id == -1) {
        tenantMngModel.data.selLimitType = 0;
    }
    tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
}

//租户筛选限制条件
tenantCtrl.limit = function (flag) {
    tenantCtrl.tenantOperationHide();
    tenantMngModel.data.selLimitType = flag;
    tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
}

//换页
tenantCtrl.tenantPageChange = function () {
    tenantCtrl.getTenantArr();
}

//列头排序
tenantCtrl.tenantArrColumnSort = function (event) {
    var sortStatus = event.pEventAttr;
    var sortType = sortStatus.sortType == "asc" ? 1 : -1;
    var columnName = "";

    $('#grid1 .icon').addClass('sortGray');
    $(event.currentTarget).find('.icon').removeClass('sortGray');

    var model = tenantMngModel.instance();
    switch (sortStatus.columnIndex) {
        case 0: //租户编号
            columnName = "columnSort_tenantId";
            model.columnSort_field = "tenantId";
            break;
        case 10: //上次结算时间
            columnName = "columnSort_lastClearingTime";
            model.columnSort_field = "lastClearingTime";
            break;
        case 16: //剩余金额
            columnName = "columnSort_remainMoney";
            model.columnSort_field = "remainMoney";
            break;
        case 17: //剩余*量
            columnName = "columnSort_remainEnergy";
            model.columnSort_field = "remainEnergy";
            break;
        case 18: //剩余天数
            columnName = "columnSort_remainDays";
            model.columnSort_field = "remainDays";
            break;
    }
    if (columnName) {
        tenantMngModel.instance()[columnName] = sortType;
        model.columnSort_type = sortType;
    }
    //tenantCtrl.precoverGrid();//还原表格并重新获取数据;
    $("#grid1").precover();
    tenantCtrl.getTenantArr();
}

//选择平均电价类型
tenantCtrl.switchPJ = function () {
    tenantMngModel.instance().selPrice.type = 0;
}

//选择分时电价类型
tenantCtrl.switchFS = function () {
    tenantMngModel.instance().selPrice.type = 1;
}

//点击电价保存事件
tenantCtrl.priceSaveClick = function () {
    var model = tenantMngModel.instance();
    var type = model.selPrice.type;
    var content;
    if (!$("#price_name").pverifi()) {
        return;
    }
    if (type == 0) {
        if (!$("#pj_value").pverifi()) {
            return;
        }
        if (!tenantCtrl.updatePriceTexeBlur($("#pj_value").pval())) return;
        content = $("#pj_value").pval();
    } else {
        if (!$("#jf_value").pverifi()) {
            return;
        }
        if (!tenantCtrl.updatePriceTexeBlur($("#jf_value").pval())) return;
        if (!$("#gf_value").pverifi()) {
            return;
        }
        if (!tenantCtrl.updatePriceTexeBlur($("#gf_value").pval())) return;
        if (!$("#pd_value").pverifi()) {
            return;
        }
        if (!tenantCtrl.updatePriceTexeBlur($("#pd_value").pval())) return;
        if (!$("#dg_value").pverifi()) {
            return;
        }
        if (!tenantCtrl.updatePriceTexeBlur($("#dg_value").pval())) return;
        var jf = $("#jf_value").pval();
        var gf = $("#gf_value").pval();
        var pd = $("#pd_value").pval();
        var dg = $("#dg_value").pval();
        content = [{
                "type": "J",
                "value": jf
            },
            {
                "type": "F",
                "value": gf
            },
            {
                "type": "G",
                "value": dg
            },
            {
                "type": "P",
                "value": pd
            }
        ];
    }
    var name = $("#price_name").pval();
    if (model.priceOperateType == 2) {
        tenantCtrl.addPrice(model.selEnergyType, name, type, content);
    } else if (model.priceOperateType == 1) {
        tenantCtrl.updatePrice(model.selPrice.id, name, type, content);
    }
}

tenantCtrl.tenantOperationHide = function () {
    $('#opt_btns').pdisable(false);
    checkedTenantTypes.length = 0;
}

//进入租户详情
tenantCtrl.goTenantDetail = function (obj) {
    tenantMngModel.data.tenantDetailsItem = obj;
    var newObj = JSON.parse(JSON.stringify(obj));
    var oldTenantId = window.localStorage.getItem("newTenantId");
    window.localStorage.setItem("newTenantId", newObj.tenantId);
    var currTenantId = window.localStorage.getItem("newTenantId");
    var interval = tenantCtrl.interval;
        if(oldTenantId != currTenantId) {
        if(interval) {
            clearInterval(tenantCtrl.interval);
        }
        $("#recharge_details").text('确认充值');
        $("#recharge_details").pdisable(false);
    }
    tenantCtrl.getTenantDetail(newObj); //获取租户详情
    tenantCtrl.getWXPayState(newObj);
    var model = tenantMngModel.instance();
    model.selTenant = newObj;
    model.selTenantOrg = obj;
    model.customAlarmPanelIsShow = false;
    model.currentPage = "tenementDetails";

    
}

//切换付费类型
tenantCtrl.changePayType_yu = function (model, event) {
    var energyTypeId = model.id;
    $("#" + energyTypeId + "_fee_type").show();
    $("#" + energyTypeId + "_recharge_type").show();
}

//切换付费类型
tenantCtrl.changePayType_hou = function (model, event) {
    var energyTypeId = model.id;
    $("#" + energyTypeId + "_fee_type").hide();
    $("#" + energyTypeId + "_recharge_type").hide();
}

//进入退租页面
tenantCtrl.goLeaveTenantPage = function (num) {
    var model = tenantMngModel.instance();
    model.rentIdback = num;
    model.currentPage = "surrenderTenancy";
    var date = new Date();
    Vue.nextTick(function () {
        $("#surrender_tenancy_time").psel({
            y: date.getFullYear(),
            M: date.getMonth() + 1,
            d: date.getDate()
        }, false);
        tenantCtrl.getLeaveTenantDetail();
    });
}

//获取换表记录
tenantCtrl.goMeterChangeRecordPage = function () {
    common.showPartLoading();
    tenantCtrl.getMeterChangeRecordArr();
    tenantMngModel.instance().currentPage = "changeMeterRecords";
}

//显示价格方案详情
tenantCtrl.showDetailPriceContent = function (energyTypeId, priceId) {
    var model = tenantMngModel.instance()
    var prePayType = model.selTenant.energyList[0].prePayType;
    // if (prePayType == 1) {
    //     $('#editPriceBtn').pdisable(true)
    // } else {
    //     $('#editPriceBtn').pdisable(false)
    // }
    $('#pj_value').precover();
    $('#price_name').precover();
    $('#jf_value').precover();
    $('#gf_value').precover();
    $('#pd_value').precover();
    $('#dg_value').precover();
    var model = tenantMngModel.instance();
    model.priceOperateType = 0;
    var priceArr = [];
    for (var i = 0; i < model.energyTypeArr.length; i++) {
        var item_i = model.energyTypeArr[i];
        if (item_i.id == energyTypeId) {
            model.selEnergyType = item_i;
            priceArr = item_i.priceArr;
            break;
        }
    }
    for (var i = 0; i < priceArr.length; i++) {
        var priceObj = priceArr[i];
        if (priceObj.id == priceId) {
            model.selPrice = JSON.parse(JSON.stringify(priceObj));
            if (priceObj.type == 0) {
                $('#pjdjRadio').psel(true);
            } else {
                $('#fsdjRadio').psel(true);
            }
            $('#build_edit_pricePlan').pshow({
                title: '价格方案详情'
            });
            break;
        }
    }
}

//进入单租户能耗费用报告
tenantCtrl.goEnergyCostReport = function (num) {
    var model = tenantMngModel.instance();
    model.energyCosttempId = num;
    model.selTenant.energyCostReport = new energyCostReportVM();
    model.currentPage = "energyCostReport";
    model.pdfPage = "energyCosttemp";
    var tenantId = model.selTenant.tenantId;
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth();
    var startTime = new Date(y, m, 1, 0, 0, 0).getTime();
    var endTime = new Date(y, m, date.getMonthLength(), 0, 0, 0).getTime();
    Vue.nextTick(function () {
        $("#reportCalendar").psel({
            startTime: startTime,
            endTime: endTime
        }, false);
        tenantCtrl.getSelfEnergyTypeArr({
            tenantId: tenantId
        });
        $('#pdf_wrapper .noReport').show().siblings().hide();
    });
}



//查看历史账单
tenantCtrl.goHistoryBillPage = function (energyTypeId) {
    common.showPartLoading();
    var model = tenantMngModel.instance();
    model.energyCosttempId = 1;
    var energyCostReport = new energyCostReportVM();
    energyCostReport.energyTypeId = energyTypeId;
    model.selTenant.energyCostReport = energyCostReport;
    model.currentPage = "energyCostReport";
    model.pdfPage = "historybillingtemp";
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth();
    var startTime = new Date(y, m, 1, 0, 0, 0).getTime();
    var endTime = new Date(y, m, date.getMonthLength(), 0, 0, 0).getTime();
    Vue.nextTick(function () {
        $("#reportCalendar").psel({
            startTime: startTime,
            endTime: endTime
        }, false);
        //获取历史账单
        tenantCtrl.getHistoryBillArr();
    });
}

//查看充值记录
tenantCtrl.goRechargeRecordPage = function (energyTypeId, num) {
    common.showPartLoading();
    var model = tenantMngModel.instance();
    model.energyCosttempId = num;
    var energyCostReport = new energyCostReportVM();
    energyCostReport.energyTypeId = energyTypeId;
    model.selTenant.energyCostReport = energyCostReport;
    model.currentPage = "energyCostReport";
    model.pdfPage = "energyCostcharge";
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth();
    var startTime = new Date(y, m, 1, 0, 0, 0).getTime();
    var endTime = new Date(y, m, date.getMonthLength(), 0, 0, 0).getTime();
    Vue.nextTick(function () {
        $("#reportCalendar").psel({
            startTime: startTime,
            endTime: endTime
        }, false);
        model.selTenant.energyCostReport.timeFrom = new Date($('#reportCalendar').psel().startTime).format('y.M.d');
        model.selTenant.energyCostReport.timeTo = new Date($('#reportCalendar').psel().endTime).format('y.M.d');
        //获取充值记录
        tenantCtrl.getRechargeRecord();
    });
}

//查看欠费账单
tenantCtrl.goNoPayBillPage = function (num, energyTypeId, orderId) {
    var model = tenantMngModel.instance();
    model.energyCosttempId = num;
    var energyCostReport = new energyCostReportVM();
    energyCostReport.energyTypeId = energyTypeId;
    energyCostReport.isCurrentOrder = orderId ? true : false;
    model.selTenant.energyCostReport = energyCostReport;
    model.currentPage = "energyCostReport";
    model.pdfPage = "energyCostarrears";
    tenantCtrl.getNoPayBillArr(orderId);
}

//进入电表详情页面
tenantCtrl.goMeterDetailPage = function (energyTypeId, energyTypeName, meterId) {
    var model = tenantMngModel.instance()
    var selTenant = model.selTenant;
    if (selTenant.tenantStatus == 0 || selTenant.tenantStatus == 2) {
        return;
    }
    model.currentPage = "meterDetail";
    var roomArr = selTenant.roomList;
    var meterIdArr = [];
    for (var i = 0; i < roomArr.length; i++) {
        var energyArr = roomArr[i].energyList;
        for (var j = 0; j < energyArr.length; j++) {
            var item_j = energyArr[j];
            if (item_j.energyTypeId == energyTypeId) {
                meterIdArr = meterIdArr.concat(item_j.meterList);
                break;
            }
        }
    }
    var meterArr = [];
    for (var i = 0; i < meterIdArr.length; i++) {
        meterArr.push({
            meterName: energyTypeName + "表" + meterIdArr[i],
            meterId: meterIdArr[i]
        });
    }
    model.meterDetail = {
        tenantId: model.selTenant.tenantId,
        energyTypeId: energyTypeId,
        energyTypeName: energyTypeName,
        dataUnit: "",
        meterArr: meterArr,
    }
    var date = new Date();
    var startTime = date.format().substring(0, 10) + " 00:00:00";
    Vue.nextTick(function () {
        $("#meterDetailCalendar").psel({
            startTime: startTime,
            endTime: startTime,
        }, false);
        for (var i = 0; i < meterIdArr.length; i++) {
            if (meterIdArr[i] == meterId) {
                $("#meter_combox").psel(i, false);
                break;
            }
        }
        tenantCtrl.getMeterDetail();
    });
}

//单租户结算页面
tenantCtrl.goTenantSettlePage = function (lastClearingTime, energyTypeId, num) {
    common.showPartLoading();
    $("#settle_ok").show().siblings().hide();
    var model = tenantMngModel.instance();
    model.settleLastClearingTime = lastClearingTime;
    model.energyCosttempId = num;
    var energyArr = model.energyArr;
    var energyTypeName = tenantCtrl.getEnergyTypeName(energyTypeId);
    model.currentPage = "settleAccounts";
    model.selTenant.settle = {
        energyTypeId: energyTypeId,
        energyTypeName: energyTypeName,
        energyUnit: "",
        clearingTime: "",
        dataArr: [],
        orderId: ""
    }
    var date = new Date();
    Vue.nextTick(function () {
        $("#settle_accounts_time").psel({
            y: date.getFullYear(),
            M: date.getMonth() + 1,
            d: date.getDate()
        }, false);
        tenantCtrl.tenantSettle_single();
    });
}

//确定删除租户
tenantCtrl.confirmDeleteTenant = function () {
    tenantCtrl.deleteTenant(tenantMngModel.instance().selTenant)
}

//数据格式化
tenantCtrl.numberFormat = function (number, fixType, isStrict) {
    if (number === null) {
        return '--';
    }
    if (isNaN(number)) {
        return number;
    }
    var data = new Number(number);
    var fixCount = 0;
    switch (fixType) {
        case tenantCtrl.fixType_day:
            fixCount = 0;
            break;
        case tenantCtrl.fixType_money:
            fixCount = 2;
            break;
        case tenantCtrl.fixType_price:
            fixCount = 4;
            break;
        case tenantCtrl.fixType_percent:
            fixCount = 1;
        case tenantCtrl.fixType_meter:
            fixCount = 2;
            break;
        case tenantCtrl.fixType_dynamic:
            if (data >= 1) {
                fixCount = 1;
            } else {
                fixCount = 3;
            }
            break;
        default:
            return number;
            break;
    }
    var data = data.toFixed(fixCount);
    if (!isStrict) {
        data = parseFloat(data)
    }
    return data;
}
tenantCtrl.fixType_day = "day"; //天数
tenantCtrl.fixType_money = "money"; //金额2位
tenantCtrl.fixType_price = "price"; //金额4位
tenantCtrl.fixType_percent = "percent"; //百分数
tenantCtrl.fixType_dynamic = "dynamic"; //能耗
tenantCtrl.fixType_meter = "meter"; //表读数

//进入单租户缴费记录页面
tenantCtrl.goFeeRecordPage = function (energyTypeId, num) {
    common.showPartLoading();
    var model = tenantMngModel.instance();
    model.energyCosttempId = num;
    model.currentPage = 'paymentRecords';
    model.selTenant.feeRecordArr_energyTypeId = energyTypeId;
    var date = new Date();
    var startTime = new Date(date.getFullYear(), date.getMonth(), 1, 0, 0, 0).format();
    var endTime = new Date(date.getFullYear(), date.getMonth(), date.getMonthLength(), 0, 0, 0).format();
    Vue.nextTick(function () {
        $("#fee_record_calendar").psel({
            startTime: startTime,
            endTime: endTime
        }, false);
        tenantCtrl.getFeeRecord(true, false);
    });
}

//进入批量缴费记录页面
tenantCtrl.goFeeRecordPage_bat = function (num) {
    common.showPartLoading();
    var model = tenantMngModel.instance();
    model.energyCosttempId = num;
    model.currentPage = 'payRecord';
    var date = new Date();
    var startTime = new Date(date.getFullYear(), date.getMonth(), 1, 0, 0, 0).format();
    var endTime = new Date(date.getFullYear(), date.getMonth(), date.getMonthLength(), 0, 0, 0).format();
    Vue.nextTick(function () {
        $("#fee_record_bat_time").psel({
            startTime: startTime,
            endTime: endTime
        }, false);
        tenantCtrl.getFeeRecord(false, false);
    });
}

//单租户下载缴费记录
tenantCtrl.downFeeRecord = function () {
    tenantCtrl.getFeeRecord(true, true);
}

//下载缴费记录_批量
tenantCtrl.downFeeRecord_bat = function () {
    tenantCtrl.getFeeRecord(false, true);
}

//单租户缴费记录切换时间
tenantCtrl.feeRecordChangeTime = function () {
    tenantCtrl.getFeeRecord(true, false);

}

//缴费记录切换时间-批量
tenantCtrl.feeRecordChangeTime_bat = function () {
    tenantCtrl.getFeeRecord(false, false);
}

//搜索租户
tenantCtrl.searchTenantEvent = function (event) {
    tenantCtrl.tenantOperationHide();
    var putVal = event.pEventAttr.key;
    if (putVal != "") {
        tenantCtrl.searchTenant(putVal);
        $('.searchHint').fadeIn();
    } else {
        $('.searchHint').fadeOut();
    }
}

//搜索结果选中
tenantCtrl.searchSelect = function (item, buildingId) {
    // 清空页码输入框状态
    staticEvent.pageSizeDefault();

    $('.searchHint').fadeOut();
    var newItem = new tenantVM();
    newItem.tenantId = item.tenantId;
    newItem.buildingId = buildingId;
    tenantCtrl.goTenantDetail(newItem);

}
// 日志 搜索input
tenantCtrl.searchField = function(event){
    var model = tenantMngModel.instance();
    var putVal = event.pEventAttr.key;
    if (putVal != "") {
        model.queries = putVal
        tenantCtrl.viewLog(0,putVal);
    }
   
}

//批量下载欠费账单
tenantCtrl.goNoPayBillPage_bat = function () {

    var model = tenantMngModel.instance();
    model.currentPage = "arrearsBill";
    Vue.nextTick(function () {
        tenantCtrl.getNoPayBillArr_bat(false);
    });
}

//进入能耗费用报表_批量
tenantCtrl.goEnergyCostReport_bat = function () {
    common.showPartLoading();
    var model = tenantMngModel.instance();
    model.energyCostTimeList = [];
    model.currentPage = "energyCost";
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth();
    var startTime = new Date(y, m, 1, 0, 0, 0).getTime();
    var endTime = new Date(y, m, date.getMonthLength(), 0, 0, 0).getTime();
    Vue.nextTick(function () {
        $("#energy_cost_calendar").psel({
            startTime: startTime,
            endTime: endTime
        }, false);
        tenantCtrl.getTenantEnergyMoneyReport_bat(false);
    });
}

//进入能耗费用报表_批量切换时间
tenantCtrl.getTenantEnergyMoneyReport_bat_changeTime = function () {
    tenantCtrl.getTenantEnergyMoneyReport_bat(false);
}

//进入充值记录_批量
tenantCtrl.goRecharge_bat = function () {

    var model = tenantMngModel.instance();
    var timeController = '';
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth();
    var startTime = new Date(y, m, 1, 0, 0, 0).getTime();
    var endTime = new Date(y, m, date.getMonthLength(), 0, 0, 0).getTime();

    switch (model.selFeeType.id) {
        case 1:
            timeController = 'recharge_bat_calendar_software_meter';
            break;

        case 2:
            timeController = 'recharge_bat_calendar';
            break;
    }
    Vue.nextTick(function () {
        $("#" + timeController).psel({
            startTime: startTime,
            endTime: endTime
        }, false);
        tenantCtrl.getRechargeRecord_bat(false, timeController);
    });
}

//充值记录表_批量切换时间
tenantCtrl.getRechargeRecord_bat_changeTime = function () {
    var model = tenantMngModel.instance();
    switch (model.selFeeType.id) {
        case 1:
            timeController = 'recharge_bat_calendar_software_meter';
            break;

        case 2:
            timeController = 'recharge_bat_calendar';
            break;
    }
    tenantCtrl.getRechargeRecord_bat(false, timeController);
}

//下载本期账单（单租户）
tenantCtrl.downCurrentBill = function () {
    var model = tenantMngModel.instance();
    var settle = model.selTenant.settle;
    tenantCtrl.goNoPayBillPage(1, settle.energyTypeId, settle.orderId);
}


//批量结算
tenantCtrl.goSettlePage_bat = function () {

    common.showPartLoading();
    var model = tenantMngModel.instance();
    model.bat_energyUnit = '--';
    model.currentPage = "settlement";
    var date = new Date();
    Vue.nextTick(function () {
        $("#formtime").psel({
            y: date.getFullYear(),
            M: date.getMonth() + 1,
            d: date.getDate()
        }, false);
        tenantCtrl.tenantSettle_bat();
    });
}

//进入批量缴费页面
tenantCtrl.goPayBillPage_bat = function () {
    common.showPartLoading();
    var model = tenantMngModel.instance();
    var checkedTenantArr = model.checkedTenantArr;
    for (var i = 0; i < checkedTenantArr.length; i++) {
        checkedTenantArr[i].noPayBill_Ready = false;
    }
    tenantCtrl.getNoPayBillArr_bat(false);
    tenantMngModel.instance().currentPage = "pay";
}

//获取结算明细
tenantCtrl.goSettleDetail_bat = function (obj) {
    $('#bill_detail_loading').pshow();
    var model = tenantMngModel.instance();
    var settle_bat = obj.settle_bat;
    var energyTypeName
    model.settleDetail = {
        energyTypeId: model.energyType,
        energyTypeName: model.energyTypeName,
        timeFrom: settle_bat.lastClearingTime,
        timeTo: settle_bat.currentClearingTime,
        tenantId: obj.tenantId,
        tenantName: obj.tenantName,
        energyUnit: model.bat_energyUnit,
        dataList: []
    };
    staticEvent.billShow();
    tenantCtrl.getSettleDetail(model.settleDetail);
}

//获取结算明细
tenantCtrl.goSettleDetail_single = function () {
    $('#bill_detail_loading').pshow();
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    var settle = selTenant.settle;
    model.settleDetail = {
        energyTypeId: settle.energyTypeId,
        timeFrom: settle.dataArr[0].lastClearingTime,
        timeTo: settle.clearingTime,
        tenantId: selTenant.tenantId,
        tenantName: selTenant.tenantName,
        energyUnit: settle.energyUnit,
        dataList: []
    };
    staticEvent.billShow();
    tenantCtrl.getSettleDetail(model.settleDetail);
}
//跳转第一页
tenantCtrl.setPage = function(data) {
    var page_psel =  $('#page_simple').psel();
    var page_count = $('#page_simple').pcount();
    if(data === 0 && page_psel != 1) {
        $('#page_simple').psel(1);
    }else if(data === 1){
        var pageLen = page_count;
        $('#page_simple').psel(pageLen);
    }
}
// 确定每页行数
tenantCtrl.setPageSize = function () {
    staticEvent.comboboxhide();

    var model = tenantMngModel.instance();
    if (!$("#page_text").pverifi()) {
        return;
    }
    var pageText = $("#page_text").pval().trim();

    var pageSize = parseInt(pageText);
    if (pageSize == 0) {
        $("#page_text").pshowTextTip("请输入正整数");
        return;
    }
    if (pageSize > 5000) {
        $("#page_text").pshowTextTip("最大支持5000条");
        return;
    }
    tenantCtrl.pageSize = pageSize;
    window.localStorage.setItem("simple_pageCount", tenantCtrl.pageSize);
    tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
}
// 确定每页行数
tenantCtrl.setPageSize1 = function () {
    staticEvent.comboboxhide();
    var model = tenantMngModel.instance();
    if (!$("#page_text1").pverifi()) {
        return;
    }
    var pageText = $("#page_text1").pval().trim();
    var pageSize = parseInt(pageText);
    if (pageSize == 0) {
        $("#page_text1").pshowTextTip("请输入正整数");
        return;
    }
    if (pageSize > 5000) {
        $("#page_text1").pshowTextTip("最大支持5000条");
        return;
    }
    tenantCtrl.pageSize1 = pageSize;
    window.localStorage.setItem("simple_pageCount", tenantCtrl.pageSize1);
    tenantCtrl.addtenantname();
}

//进入仪表设置记录页面
tenantCtrl.goSetRecord = function () {
    tenantMngModel.instance().currentPage = 'setRecord';
    tenantMngModel.instance().sr_selTypeId = 0;
    Vue.nextTick(function () {
        $("#sr_type_sel").psel(0, false); //默认选择全部
        $("#sr_energy_sel").psel(0, false); //能耗类型默认选择全部
        tenantCtrl.getSetRecordArr(false);
    });
}

//仪表设置记录下载
tenantCtrl.downSetRecord = function () {
    tenantCtrl.getSetRecordArr(true);
}

//选择操作类型
tenantCtrl.sr_selType = function (dataSource, pEvent) {
    var model = tenantMngModel.instance();
    var index = pEvent.pEventAttr.index;
    model.sr_selTypeId = model.sr_typeArr[index].id;;
    tenantCtrl.getSetRecordArr(false);
}
//选择能源类型
tenantCtrl.sr_energyType = function () {
    tenantCtrl.getSetRecordArr(false);
}

//切换时间
tenantCtrl.setRecordChangeTime = function () {
    tenantCtrl.getSetRecordArr(false);
}

//设置每页条数
tenantCtrl.setPageSize_sr = function () {
    staticEvent.comboboxhide();
    var model = tenantMngModel.instance();
    if (!$("#sr_page_text").pverifi()) {
        return;
    }
    var pageText = $("#sr_page_text").pval().trim();
    var pageSize = parseInt(pageText);
    if (pageSize == 0) {
        $("#sr_page_text").pshowTextTip("请输入正整数");
        return;
    }
    if (pageSize > 5000) {
        $("#sr_page_text").pshowTextTip("最大支持5000条");
        return;
    }
    tenantCtrl.pageSize_sr = pageSize;
    window.localStorage.setItem("simple_pageCount_sr", tenantCtrl.pageSize_sr);
    tenantCtrl.getSetRecordArr(false);

}

// 下载批量修改电价日志
tenantCtrl.downloadLogs = function (){
    tenantCtrl.viewLog(1)
}

tenantCtrl.changePageSizeForMeterSetRecord = function () {
    tenantCtrl.getSetRecordArr(false);
}
// 分页
tenantCtrl.changePageSizeLogs = function(){
    tenantCtrl.viewLog(0)
}
//选择执行结果
tenantCtrl.sr_selType = function (pEvent) {
    var model = tenantMngModel.instance();
    var index = pEvent.pEventAttr.index;
    model.sr_selTypeId = model.sr_typeArr[index].id;;
    tenantCtrl.viewLog(0)
}
//仪表设置记录接口
tenantCtrl.getSetRecordArr = function (isDownload) {
    var model = tenantMngModel.instance();
    if (!isDownload) {
        common.showPartLoading();
        model.sr_dataArr = [];
    }
    var typeId = 0;
    var typeSel = $("#sr_type_sel").psel();
    if (typeSel) {
        typeId = model.sr_typeArr[typeSel.index].id;
    }
    var energyCode = null;
    var energySel = $("#sr_energy_sel").psel();
    if (energySel) {
        energyCode = model.sr_energyTypeArr[energySel.index].id;
    }
    var timeObj = $("#setRecordCalendar").psel();
    var timeFrom = new Date(timeObj.startTime).format("y-M-d h:m:s");
    var timeTo = new Date(timeObj.realEndTime).format("y-M-d h:m:s");
    var pageIndex = $("#page_simple_sr").psel() - 1;
    var param = {
        "tenantName": model.selTenant.tenantName,
        "tenantId": model.selTenant.tenantId,
        "energyTypeId": energyCode,
        "setType": typeId,
        "timeFrom": timeFrom,
        "timeTo": timeTo,
        "isDownload": isDownload ? 1 : 0,
        "pageIndex": pageIndex,
        "pageSize": tenantCtrl.pageSize_sr,
    }
    var fName = "FNTMeterSetRecordService";
    if (isDownload) {
        pajax.downloadByParam(fName, param);
    } else {
        pajax.post({
            url: fName,
            data: param,
            success: function (resJson) {
                if ((resJson instanceof Array) && resJson.length > 0) {
                    var result = resJson[0];
                    var count = result.count;
                    var pageCount = Math.ceil(count / tenantCtrl.pageSize_sr);
                    $("#page_simple_sr").pcount(Math.max(1, pageCount));
                    model.sr_dataArr = result.list;
                } else {
                    common.msgFailure("获取记录失败，服务返回错误");
                }
            },
            complete: function () {
                common.hidePartLoading();
            },
            error: function () {
                common.msgFailure("获取记录失败,服务异常");
            }
        });
    }
}

//设置短信自动黑名单
tenantCtrl.messageBlacklist = function () {
    var model = tenantMngModel.instance();
    var buildingId = null;
    var buildingSel = $("#t_building_cbx").psel();
    if (buildingSel) {
        buildingId = model.buildingArr[buildingSel.index].id;
    }
    buildingId = buildingId == "_all" ? null : buildingId;
    var param = {
        buildingId: buildingId
    };
    pajax.post({
        url: "FNTMessageBlacklistQueryService",
        data: param,
        success: function (resJson) {
            var data = resJson || [];
            var count = resJson[0].length;
            $("#messageBlacklist").pcount(count);
            if (data.length > 0) {
                tenantMngModel.data.messageBlacklist = data[0];
            } else {
                common.msgFailure("短信自动黑名单失败");
            }
        },
        error: function () {
            common.msgFailure("短信自动黑名单失败");
        }
    })
}

//设置短信自动黑名单
tenantCtrl.addtenantnameShow = function () {
    $("#addtenantnamemodalWindow").pshow();
    $('#ddSearch .per-searchbox-input input').val('');
    var model = tenantMngModel.instance();
    model.addtenantName1 = [];
    setTimeout(function () {
        $("#blackList").precover()
        model.addtenantName1 = model.addtenantName.slice(0, 30);
        var count = model.addtenantName.length;
        $("#blackList").pcount(count);
        var pageCount = Math.ceil(count / 30);
        $("#page_simple1").pcount(Math.max(1, pageCount));
    }, 10)
    model.reserveMessageBlacklist = [];
}
//设置第二层短信自动黑名单
tenantCtrl.addtenantname = function () {
    tenantMngModel.data.addtenantName1 = [];
    var model = tenantMngModel.instance();
    var buildingId = null;
    var buildingSel = $("#addtenant_building_cbx").psel();
    if (buildingSel) {
        buildingId = model.buildingArr[buildingSel.index].id;
    }
    buildingId = buildingId == "_all" ? null : buildingId;
    var param = {
        buildingId: buildingId,
        "like": ''
    };
    pajax.post({
        url: "FNTMessageBlacklistTenantListService",
        data: param,
        success: function (resJson) {
            data = resJson || [];
            if (data.length > 0) {
                $("#blackList").precover();
                tenantMngModel.data.addtenantName = data[0];
                tenantMngModel.data.addtenantNameCopy = data[0];
                tenantMngModel.data.addtenantName1 = data[0].slice(0, 30)
                var count = data[0].length;
                $("#blackList").pcount(count);
                var pageCount = Math.ceil(count / 30);
                $("#page_simple1").pcount(Math.max(1, pageCount));
            } else {
                common.msgFailure("短信自动黑名单失败");
            }
        },
        error: function () {
            common.msgFailure("短信自动黑名单失败");
        }
    })
}


tenantCtrl.addtenantnamehideSave = function (dataParameter) { //短信黑名单添加保存3.6版本
    pajax.post({
        url: 'FNTMessageBlacklistSaveService',
        data: dataParameter,
        success: function (result) {
            $("#message").pshow({
                text: "添加成功！",
                state: "success"
            });
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "添加失败",
                state: "failure"
            });
            console.log("添加数据错误");
        },
        complete: function () { //天机成功后获取最新数据
            tenantCtrl.messageBlacklist();
            tenantCtrl.addtenantname();   
        }
    });
};


//设置充值内部黑名单
tenantCtrl.addRemotetopupShow = function () {
    var model = tenantMngModel.instance();
    $("#RemotetopupaddforWindow").pshow();
    $('#reachageSearch .per-searchbox-input input').val('');
    $("#rechage").precover();
    var count = model.Remotetopup1.length;
    model.Remotetopup2 = model.Remotetopup1.slice(0, 30);
    $("#rechage").pcount(count);
    var pageCount = Math.ceil(count / 30);
    $("#page_simple2").pcount(Math.max(1, pageCount));
}


tenantCtrl.addRemotetopup = function () {
    tenantMngModel.data.Remotetopup2 = [];
    var model = tenantMngModel.instance();
    var buildingId = null;
    var buildingSel = $("#remotetopup_building_cbx").psel();
    if (buildingSel) {
        buildingId = model.buildingArr[buildingSel.index].id;
    }
    buildingId = buildingId == "_all" ? null : buildingId;
    var param = {
        buildingId: buildingId,
        like: ''
    };
    pajax.post({
        url: "FNTPrePayBlacklistTenantListService",
        data: param,
        success: function (resJson) {
            data = resJson || [];
            if (data.length > 0) {
                tenantMngModel.data.Remotetopup1 = data[0];
                tenantMngModel.data.Remotetopup2 = data[0].slice(0, 30);
                var count = data[0].length;
                $("#rechage").pcount(count);
                var pageCount = Math.ceil(count / 30);
                $("#page_simple2").pcount(Math.max(1, pageCount));
            } else {
                common.msgFailure("远程充值黑名单失败");
            }
        },
        error: function () {
            common.msgFailure("远程充值黑名单失败");
        }
    })
}

tenantCtrl.RemotetopuphideSave = function (dataParameter) { //充值黑名单添加保存3.6版本
    pajax.post({
        url: 'FNTPrePayBlacklistSaveService',
        data: dataParameter,
        success: function (result) {
            $("#message").pshow({
                text: "添加成功！",
                state: "success"
            });
        },
        error: function () {
            $("#gloadLoading").phide();
            $("#message").pshow({
                text: "添加失败",
                state: "failure"
            });
            console.log("添加数据错误");
        },
        complete: function () { //天机成功后获取最新数据
            /* staticEvent.Remotetopupshow();*/
        }
    });
};
// 查询异常账单列表
tenantCtrl.abnormalBillList = function (param) {
    $("#gloadLoading").pshow();
    pajax.post({
        url: 'FNTErrorOrderQueryService',
        data: param,
        success: function (result) {
            var bodyType = {
                0: '软充软扣',
                1: '软充表扣'
            };
            result[0].list.forEach(function (item) {
                item.moneyAmount = item.money ? item.money + '元' : item.amount + item.amountUnit;
                item.bodyTypeName = bodyType[item.bodyType];
            });
            tenantMngModel.data.abnormalBillList = result[0].list;
            $("#abnormalBillPage").pcount(Math.ceil(result[0].count / 15), false);
        },
        error: function () {
            tenantMngModel.data.abnormalBillList = [];
            $("#abnormalBillPage").pcount(1, false);
        },
        complete: function () { //天机成功后获取最新数据
            $("#gloadLoading").phide();
            if (!param.isPage) {
                $("#abnormalBillPage").psel(1, false);
            };
        }
    });
};
// 验证账号
tenantCtrl.userVerify = function (param, fn) {
    pajax.post({
        url: 'FNTCommonCheckPasswordService',
        data: param,
        success: function (result) {
            fn(result[0].status);
        },
        error: function () {},
        complete: function () {}
    });
};
// 处理异常账单
tenantCtrl.errOrderDispoes = function (param, fn, err) {
    pajax.post({
        url: 'FNTErrorOrderSetService',
        data: param,
        success: function (result) {
            staticEvent.getAbnormalBillList(false, {
                pEventAttr: {
                    pageIndex: $("#abnormalBillPage").psel() - 1
                }
            });
            fn();
        },
        error: function () {
            err();
        },
        complete: function () {}
    });
};