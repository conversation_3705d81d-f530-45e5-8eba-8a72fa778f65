/// <reference path="controller.js" />

function model() {}

function tenantMngModel() {};



//属性以“m_”开头，比如 m_name:"tom"（防止和 data.data 中命名冲突）
tenantMngModel.data = {
    isProtectionCircuit: true, // 是否保电
    isCloseBrake: true, // 是否合闸
    instrumentalData: {}, // 仪表设置前查询数据
    meterSetStep: 1, // 仪表设置步骤
    meterSetState: 1, // 保存仪表设置状态 0 失败 1 成功
    meterIdForMeterSet: '', // 当前设置仪表的仪表 ID
    roomCodeForMeterSet: '', // 当前设置仪表的房间编号
    energyTypeIdForMeterSet: '', // 当前设置仪表的能耗类型
    meterSetType: '', //仪表设置类型
    meterSetTypeName: '', //仪表设置类型名称
    instrumentalSupportFunctions: {}, // 仪表支持功能
    //-------------------------
    refundRentRecordsData: {}, // 批量查看退费记录数据
    //-------------------------
    pdfIpConfig: '', // pdf配置地址
    //-------------------------
    pageSizeDefault: null, // 分页器默认页数
    //-------------------------
    beforeRefundCostData: {}, // 退租前查询数据
    oldRemainData: '', // 软充表扣原始剩余金额
    //-------------------------
    settleTypeForPassword: '', // 结算类型
    prePayTypeForPassword: '', // 点击充值的充值类型
    isThroughVerification: 0, // 是否通过密码验证 1通过 0不通过
    // isTrueBtnForPassword: false, // 是否显示真实的发送按钮
    verifyPasswordValue: '', // 用户输入的密码
    verifyPasswordTip: '', // 用户输入的密码提示
    passwordIsEmpty: false, // 密码是否为空
    //-------------------------
    prepaidRecordsSofterMeterData: {}, // 单租户充值记录表充表扣数据
    prepaidRecordsSofterSoftData: {}, // 单租户充值记录软充软扣数据
    //-------------------------
    payPostMoneyEntryFlag: '', // 缴费入口标识
    //-------------------------
    gaugeRecordSingleTenantFromPage: '', //表底数记录的来源页面字符串
    nowCheckedEnergyIdSingle: null, //表底数当前选择能耗类型（单租户）
    nowCheckedHourSingle: '00:00:00', // 表底数当前选择小时数（单租户）
    beforeCheckBoxtHourSingle: '00:00:00', // 点击复选框以前的小时数（单租户）
    nowCheckedTimeSingle: '', // 表底数时间控件当前选择的时间（单租户）
    beforeCheckBoxtTimeSingle: '', // 点击复选框以前的时间（单租户）
    gaugeRecordSingleTenantArr: [], //表底数数据（单租户）
    //-------------------------
    nowCheckedEnergyId: null, //表底数当前选择能耗类型
    nowCheckedHour: '00:00:00', // 表底数当前选择小时数
    beforeCheckBoxtHour: '00:00:00', // 点击复选框以前的小时数
    nowCheckedTime: '', // 表底数时间控件当前选择的时间
    beforeCheckBoxtTime: '', // 点击复选框以前的时间
    hourArrForGauge: [], //表底数小时下拉框数据
    energyTypeArrForGauge: [], //表底数使用的能耗类型
    gaugeRecordArr: [], //表底数数据

    //-------------------------
    isDownload: 0, //是否下载
    alarmSearchData: {}, // 报警查询数据（与物业监控统一）
    customAlarmPanelIsShow: false, // 自定义报警设置面板是否显示
    remainingAmountReportList: [], // 剩余金额/量报表数据
    operationPermissions: {}, //权限信息
    pageSize: 100,
    //-------------------------
    isRefresh: false,
    currentPage: 'tementListPage',
    pdfPage: 'energyCosttemp',
    stepArr_2: ['基本信息', '选择能耗'],
    stepArr_3: ['基本信息', '选择房间', '选择能耗'],
    stepArr: [],
    subaddPage: "step1",
    step: 1,
    //-------------------------
    meterNum: 0, //退租页电表读数
    addOrEdit: 0, //0 添加租户，1编辑租户
    energyCosttempId: 0, //0首页跳入能耗费用报告  1 租户详情跳入 2 编辑租户时
    rentIdback: 0, //退租页面的返回  0 首页跳入1 租户详情跳入
    contacts: [{
        contactName: "",
        contactMobile: ""
    }], //联系人列表
    meterformulalist: [],
    buildingArr2: [], //不包含全部
    addTenantSelBuilding: new buildingVM(),
    energyTypeArr: [], //能耗类型
    selfenergyTypeArr: [], //租户自己的能耗类型
    selEnergyType: new energyTypeVM(), //编辑||添加租户管理电价进入的能耗类型
    selPriceId: "", //编辑||添加租户进入电价方案是所选择的电价id
    priceOperateType: 0, //电价操作0详情 1 编辑 2新建
    selPrice: new priceVM(), //被选中的方案类型
    confirmDelPrice: new priceVM(),
    searchResultArr: [], //租户搜索结果
    tenantStatusArr: [{
        name: '全部',
        id: -1
    }, {
        name: '已激活',
        id: 1
    }, {
        name: '未激活',
        id: 0
    }, {
        name: '已退租',
        id: 2
    }],
    // 修改电价日志
    resultArr:[ {
        name: '失败',
        id: 1
    }, {
        name: '成功',
        id: 0
    },],
    fromTime:'',
    toTime:'',
    addtenantName: [],
    addtenantNameCopy: [],
    addtenantName1: [],
    messageBlacklist: [],
    Remotetopup: [],
    remotetopup: [],
    remoteROTtopup: [],
    cutRemotetopup1: [],
    remoteTopupb: [],
    reserveMessageBlacklistb: [],
    reserveMessageBlacklistbb: [],
    reserveMessageBlacklist: [],
    xMessageBlacklist: [],
    xMessageBlackFlag: false,
    xMessageBlacklistSelect:[],
    xremotetopup: [],
    Remotetopup1: [],
    Remotetopup3: [],
    Remotetopup2: [],
    modelforlist: [],
    selTenantStatus: {
        name: '已激活',
        id: 1
    },
    buildingArr: [], //建筑列表包含全部
    energyTypePayTypeArr: [], //能耗和付费类型列表
    selEnergyTypePayType: new energyTypePayTypeVM(),
    roomArr: [], //房间列表
    tenantTypeArr: [], //业态
    floorArr: [], //楼层
    tenantFilterParam: [], //租户筛选条件
    selFeeType: new tenantFilterParam(), //当前选择的扣费类型
    selShowData: new tenantFilterParam(), //当前选择的显示类型
    selLimitType: 0,
    blackNamelistpage: 1, //3.6黑名单列表翻页
    blackNamelistpage2: 1, //3.6黑名单列表翻页
    tenantArr_unit: "", //列表能耗单位
    tenantArr: [], //租户列表
    tenantArr1: [],

    tenantArr_unit1: "", //列表能耗单位
    isGetTenantArrReady: false,
    selTenant: new tenantVM(), //选择的租户
    copySelTenant: {}, //编辑租户
    selTenantOrg: new tenantVM(), //选择的租户

    payBillBat_selTenant: new tenantVM(), //批量缴费中选中的租户
    columnSort_field: "tenantId",
    columnSort_type: 1,
    columnSort_tenantId: 1,
    columnSort_remainMoney: 1,
    columnSort_remainEnergy: 1,
    columnSort_remainDays: 1,
    columnSort_lastClearingTime: 1,
    samePriceId: "",
    samePriceName: "",
    alarmSetParam: {},
    changeMeter: {
        meterId: "",
        energyTypeId: "",
        energyTypeName: "",
        functionList: [],
        isMeter: false,
    },
    meterDetail: {
        tenantId: "",
        energyTypeId: "",
        energyTypeName: "",
        dataUnit: "",
        meterArr: [],
        chart: ""
    },
    searchResultArr: [],
    bat_energyUnit: "", //批量单位,
    energyCostTimeList: [],
    settleDetail: {
        energyTypeId: "",
        energyTypeName: "",
        timeFrom: "",
        timeTo: "",
        tenantId: "",
        tenantName: "",
        energyUnit: "",
        dataList: []
    }, //结算明细
    clearingTimeFaultList: [], // 未到结算时间租户
    meterFaultList: [], // 仪表暂无数据租户
    selTenantFaultType: "", // 结算类型（全部不能结算、部分可以结算）
    sr_typeArr: [{
        id: 0,
        name: "剩余量清零"
    }, {
        id: 1,
        name: "保电"
    }, {
        id: 3,
        name: "透支金额"
    }, {
        id: 2,
        name: "分合闸"
    }, {
        id: 4,
        name: "更新价格"
    }], //0 剩余量清零 1保电 2闸 3透支金额 4更新价格
    sr_selTypeId: 0,
    sr_energyTypeArr: [], //仪表设置记录，能源类型
    sr_dataArr: [],
    //自动发送短信
    autoMsg: {
        timeArr: [{
            name: "00时"
        }, {
            name: "01时"
        }, {
            name: "02时"
        }, {
            name: "03时"
        }, {
            name: "04时"
        }, {
            name: "05时"
        }, {
            name: "06时"
        }, {
            name: "07时"
        }, {
            name: "08时"
        }, {
            name: "09时"
        }, {
            name: "10时"
        }, {
            name: "11时"
        }, {
            name: "12时"
        }, {
            name: "13时"
        }, {
            name: "14时"
        }, {
            name: "15时"
        }, {
            name: "16时"
        }, {
            name: "17时"
        }, {
            name: "18时"
        }, {
            name: "19时"
        }, {
            name: "20时"
        }, {
            name: "21时"
        }, {
            name: "22时"
        }, {
            name: "23时"
        }],
        index1: 0,
        index2: 0,
        hasItem: "",
        item1: {
            name: 666
        },
        item2: {
            name: 888
        },
        sendStuats: "",
    },
    meterList: [],
    // 异常账单能源类型
    energyTypes: [{
            name: '全部',
            id: 'null'
        },
        {
            name: '电',
            id: 'Dian'
        },
        {
            name: '水',
            id: 'Shui'
        },
        {
            name: '热水',
            id: 'ReShui'
        },
        {
            name: '燃气',
            id: 'RanQi'
        }
    ],
    // 异常账单类型
    billTypes: [{
            name: '全部',
            id: "all"
        },
        {
            name: '未处理',
            id: '0'
        },
        {
            name: '已处理',
            id: '1'
        },
    ],
    abnormalBillList: [], // 异常账单列表
    abnormalBillInfo: {}, // 进行处理的异常账单信息
    checkedTenant: '', //复选框选中的租户
    priceType: [], //更新价格 下拉框价格数据
    tenantDetailsItem: [], //租户详情
    // meterType: [] //仪表设置数 
    BatchRecordArr:[] //批量修改价格方案日志列表
};

/**
 * 创建 Vue 实例方法
 */
tenantMngModel.createModel = function () {
    tenantMngModel.instance();
}

// 单例模式，每次创建返回同一个 Vue 实例
tenantMngModel.instance = function () {
    if (!tenantMngModel._instance) {
        tenantMngModel._instance = new Vue({
            el: '#tenantManage',
            // Vue 数据模型
            data: tenantMngModel.data,
            // Vue 绑定的事件方法
            methods: {
                // 保存仪表设置记录
                saveMeterSetRecordBtn: function () {
                    var params = {
                        meterId: this.meterIdForMeterSet,
                        roomCode: this.roomCodeForMeterSet,
                        energyTypeId: this.energyTypeIdForMeterSet,
                        setType: this.meterSetType,
                        value: this.instrumentalData.value,
                        price: this.instrumentalData.result
                    }
                    staticEvent.hideInstrumentSetWindow();
                    tenantCtrl.saveMeterSetRecord(params);
                },
                // 仪表设置确定进行密码验证
                meterSetGoPasswordVerification: function (flag) {
                    var pverifi = '';
                    var iptValue = '';
                    var pverifiArr = [];
                    var iptValueArr = [];
                    switch (this.meterSetType) {
                        case 0:
                            break;
                        case 1:
                            this.isProtectionCircuit = flag;
                            break;
                        case 2:
                            this.isCloseBrake = flag;
                            break;
                        case 3:
                            pverifi = $('#overdraft_amount').pverifi();
                            if (!pverifi) return;
                            iptValue = parseFloat($('#overdraft_amount').pval());
                            this.$set(this.instrumentalData, 'iptValue', iptValue);
                            break;
                        case 4:
                            var result = this.instrumentalData.result;
                            if (this.instrumentalSupportFunctions.meterType == 0) {
                                // 普通费率
                                pverifi = $('#electricity_price_ipt_L').pverifi();
                                if (!pverifi) return;
                                iptValue = parseFloat($('#electricity_price_ipt_L').pval());
                                if (!tenantCtrl.updatePriceTexeBlur(iptValue)) {
                                    return;
                                }
                                for (var i = 0; i < result.length; i++) {
                                    iptValueArr.push({
                                        value: iptValue,
                                        typeName: result[i].typeName,
                                        unit: result[i].unit,
                                        type: result[i].type
                                    });
                                }
                            } else {
                                // 多费率
                                for (var i = 0; i < result.length; i++) {
                                    pverifiArr.push($('#electricity_price_ipt_' + i).pverifi());
                                    if (!tenantCtrl.updatePriceTexeBlur($('#electricity_price_ipt_' + i).pval())) {
                                        return;
                                    }
                                    iptValueArr.push({
                                        value: $('#electricity_price_ipt_' + i).pval(),
                                        typeName: result[i].typeName,
                                        unit: result[i].unit,
                                        type: result[i].type
                                    });
                                }
                                pverifi = pverifiArr.every(function (val) {
                                    return val;
                                });
                                iptValue = iptValueArr.every(function (val) {
                                    return val.value != '';
                                });
                                if (!pverifi || !iptValue) return;
                            }
                            this.$set(this.instrumentalData, 'iptValueArr', iptValueArr);
                            break;
                    }
                    this.meterSetStep = 3;
                },
                // 刷新仪表设置数据
                refreshInstrumentalData: function () {
                    tenantCtrl.refreshIcon();
                    tenantCtrl.getInstrumentalData(this.meterIdForMeterSet, this.meterSetType);
                },
                // 仪表设置上一步事件
                meterSetPrevStepEvent: function () {
                    this.meterSetStep--;
                    if (this.meterSetStep == 1) {
                        this.meterSetTypeName = '仪表设置';
                        $('.window_instrument_set').find('.per-modal-custom_title').text('仪表设置');
                    }
                    if (this.meterSetStep == 2) {
                        staticEvent.reductionPasswordForMeterSet();
                        tenantCtrl.getInstrumentalData(this.meterIdForMeterSet, this.meterSetType);
                    }
                },
                // 仪表设置点击事件
                instrumentalBtnEvent: function (setType) {
                    this.meterSetType = setType;
                    this.meterSetStep = 2;
                    this.meterSetState = 1;
                    this.instrumentalData.value = '--';
                    var txt = '';
                    switch (setType) {
                        case 0:
                            txt = '仪表清零';
                            break;
                        case 1:
                            txt = '保电';
                            break;
                        case 2:
                            txt = '分合闸'
                            break;
                        case 3:
                            txt = '透支'
                            break;
                        case 4:
                            txt = '更新价格';
                            break;
                    }
                    this.meterSetTypeName = txt;
                    $('.window_instrument_set').find('.per-modal-custom_title').text(txt);
                    staticEvent.reductionPasswordForMeterSet();
                    tenantCtrl.getInstrumentalData(this.meterIdForMeterSet, setType);
                },
                // 租户详情--仪表设置
                instrumentSetEvent: function (roomCode, energyTypeName, meterId, energyTypeId) {
                    this.meterSetState = 1;
                    $('#instrument_partLoading').pshow();
                    tenantCtrl.getInstrumentalSupportFunctions(roomCode, energyTypeName, meterId, energyTypeId);
                },
                // 取消自定义报警
                closeCustomAlarmChecked: function () {
                    this.customAlarmPanelIsShow = !this.customAlarmPanelIsShow;
                    tenantCtrl.getGlobalAlarmArr('custom');
                },
                // 自定义报警事件
                customAlarmChecked: function (event) {
                    this.customAlarmPanelIsShow = true;
                    var flag = $(event.target).parent().attr('data-flag') || $(event.target).attr('data-flag');
                    if (this.alarmSearchData.setUp == 0) {
                        $('#custom_radio').psel(true, false);
                        $('#global_radio').psel(true, true);
                    } else if (this.alarmSearchData.setUp == 1) {
                        $('#global_radio').psel(true, false);
                        $('#custom_radio').psel(true, true);
                    } else {
                        tenantCtrl.getGlobalAlarmArr(flag);
                    }
                },
                // 租户列表中表格最后一tenant_operation栏操作
                // tenant_operation: function (event, model) {
                //     event.stopPropagation();
                //     // 清空页码输入框状态
                //     staticEvent.pageSizeDefault();
                //     for (var i = 0; i < $('.t_p_box .per-combobox-wrap').length; i++) {
                //         $('.t_p_box .per-combobox-wrap').eq(i).hide();
                //     }
                //     var gridHeight = $("#grid1 .per-grid-dynamic_con").outerHeight(); //表格高度
                //     var gridOffTop = $("#grid1 .per-grid-dynamic_con").offset().top; //表格距上方高度
                //     var optBtnParentHeight = $(event.currentTarget).parent().outerHeight(); //操作栏父级高度
                //     var optBtnParentOffTop = $(event.currentTarget).parent().offset().top; //操作栏父级距上方高度

                //     var optBtnHeight = $(event.currentTarget).next().outerHeight(); //操作栏高度
                //     var optBtnOffTop = $(event.currentTarget).next().offset().top + optBtnParentOffTop; //操作栏距上方高度
                //     var index = $(event.currentTarget).attr('data-idx');

                //     if ($(event.currentTarget).next().css('display') == 'none') {

                //         $('.opt_btns').stop().fadeOut();
                //         $(event.currentTarget).next().stop().fadeIn();
                //         // 操作栏高度大于表格高度
                //         if (gridHeight <= optBtnHeight) {

                //             $('#grid1 .per-grid-dynamic_con, #grid1  .per-scrollbar, #grid1  .per-scrollbar_wrap').addClass('overflowVisble');

                //         } else {

                //             $('#grid1 .per-grid-dynamic_con, #grid1  .per-scrollbar, #grid1  .per-scrollbar_wrap').removeClass('overflowVisble');

                //             // 判断上方位置
                //             if (optBtnOffTop <= gridOffTop) {

                //                 $(event.currentTarget).next().css('top', '8px');

                //             } // 判断下方位置
                //             else if (gridHeight - (optBtnOffTop - gridOffTop) <= optBtnHeight) {
                //                 if ((optBtnParentHeight - 2) * (parseInt(index) + 1) <= optBtnHeight) {
                //                     $(event.currentTarget).next().css('top', (optBtnParentHeight - optBtnHeight) / 2 + 'px');
                //                 } else {
                //                     $(event.currentTarget).next().css('top', optBtnParentHeight - optBtnHeight - 3 + 'px');
                //                 }
                //             }
                //         }
                // } else {
                //     $('.opt_btns').stop().hide();
                //     $('#grid1 .per-grid-dynamic_con, #grid1 .per-scrollbar, #grid1 .per-scrollbar_wrap').removeClass('overflowVisble');
                // }
                // },
                // // 租户列表中表格最后一栏操作
                // tenant_operation_detail: function (item, event, type) {
                //     event.stopPropagation();
                //     var newItem = JSON.parse(JSON.stringify(item));
                //     var prePayType = item.prePayType;
                //     this.selTenant = newItem
                //     var callback = undefined;
                //     switch (type) {
                //         case "down_energy_report": //下载能耗费用报告
                //             tenantCtrl.goEnergyCostReport(0);
                //             break;
                //         case "gauge_record_single": //表底数记录
                //             staticEvent.gaugeRecordSingleTenantBtn('tementListPage');
                //             break;
                //         case "active": //激活
                //             staticEvent.startLesseeFromListBtn();
                //             break;
                //         case "upt_price": //修改价格方案
                //             if(prePayType === 1) {
                //                 staticEvent.floatHide();
                //                 $("#message").pshow({
                //                     text: "修改价格时，租户充值类型不能选择软充表扣的租户",
                //                     state: "failure"
                //                 });
                //             }else {
                //                 tenantCtrl.getSamePriceBeforeUpdate([newItem.tenantId]);
                //                 $("#upt_price_bat_tip").css('display', 'none');
                //                 staticEvent.floatShow();
                //             }
                //             break;
                //         case "down_no_bill": //下载欠费账单
                //             tenantCtrl.goNoPayBillPage(0, this.energyType);
                //             break;
                //         case "fee": //缴费（走合并缴费）
                //             var energyTypeId = this.energyType;
                //             callback = function () {
                //                 var energyList = newItem.energyList;
                //                 for (var i = 0; i < energyList.length; i++) {
                //                     var energy = energyList[i];
                //                     if (energy.energyTypeId == energyTypeId) {
                //                         staticEvent.payTheFeesPostPaidEvent_combine(energyTypeId, energy.postPay.orderList, energy.postPay.yingJiaoJinE);
                //                         break;
                //                     }
                //                 }
                //             }
                //             break;
                //         case "send_fee_msg": //发送缴费提醒
                //             staticEvent.sendMassageFromListToMaster(this.energyType, this.payType);
                //             break;
                //         case "get_fee_records": //缴费记录
                //             tenantCtrl.goFeeRecordPage(this.energyType, 0);
                //             break;
                //         case "account": //结算
                //             tenantCtrl.goTenantSettlePage(newItem.lastClearingTime, this.energyType, 0);
                //             break;
                //         case "get_recharge_record": //充值记录
                //             tenantCtrl.goRechargeRecordPage(this.energyType, 0);
                //             break;
                //         case "get_return_premium_record": // 退费记录
                //             tenantCtrl.goRemainingAmountReportPage(this.energyType, 0);
                //             break;
                //         case "send_recharge_msg": //发送充值提醒
                //             staticEvent.sendMassageFromListToMaster(this.energyType, this.payType);
                //             break;
                //         case "recharge": //TODO 充值暂时不支持。
                //             break;
                //         case "delete": //删除
                //             staticEvent.deleteLesseeFromListBtn();
                //             break;
                //         case "leave": //退租
                //             tenantCtrl.goLeaveTenantPage(0);
                //             break;
                //     }
                //     tenantCtrl.getTenantDetail(newItem, callback);
                //     $(event.currentTarget).parent().fadeOut();
                // },
                // 租户详情--换表事件
                changeMeterEvent: function (energyTypeId, energyTypeName, meterId) {
                    this.changeMeter.meterId = meterId;
                    this.changeMeter.energyTypeId = energyTypeId;
                    this.changeMeter.energyTypeName = energyTypeName;

                    $("#right_side_change_meter_window").pshow({
                        title: '换表'
                    });
                    var date = new Date();
                    $("#change_metter_time").psel({
                        y: date.getFullYear(),
                        M: date.getMonth() + 1,
                        d: date.getDate(),
                        h: date.getHours(),
                        m: date.getMinutes() - 5
                    }, false);

                    for (var i = 0; i < this.changeMeter.functionList.length; i++) {
                        var functionItem = this.changeMeter.functionList[i];
                        $('#_' + functionItem.functionId).precover(false)
                    }
                    this.changeMeter.isMeter = false;
                    // 获取仪表功能号
                    tenantCtrl.getchangeMeter(meterId);
                },
                // 退租页--可编辑电表读数鼠标移入移出事件
                mouseOnEdit: function ($event) {
                    $($event.target).find('.edit').show().parent().css('backgroundColor', '#F8F8F8').parent().css('backgroundColor', '#F8F8F8');
                },
                mouseOutEdit: function ($event) {
                    $($event.target).find('.edit').hide().parent().css('backgroundColor', '#FFFFFF').parent().css('backgroundColor', '#FFFFFF');
                },
                // 退租页--编辑表格中的电表读数
                editMeterNum: function ($event) {
                    // 处理其他的输入框恢复默认状态
                    if ($($event.target).attr('data-flag')) {
                        $dom = $($event.target);
                    } else {
                        $dom = $($event.target).parent();
                    }
                    var otherIptVal = $('.more > div').find('.num').text();
                    $('.more > div').find('input').hide().removeClass('blue_border').val(otherIptVal).siblings().hide().parent().find('div:first-child').show();
                    var iptVal = $dom.find('.edit').prev().text();
                    $dom.find('.edit').hide().parent().hide().parent().find('input').show().addClass('blue_border').val('').focus().val(iptVal).siblings('i').show();

                    var model = tenantMngModel.instance();
                    model.meterNum = iptVal;
                },
                // 退租页--保存对表格中电表读数的修改
                saveMeterNum: function ($event, index_meter, index_fun, energy, type) {
                    var model = tenantMngModel.instance();
                    var meter = energy.postPay.meterList[index_meter];
                    var functionList = meter.functionList;
                    var func = functionList[index_fun];
                    var numId = $event.target.id;
                    // console.log($event.target.id);

                    iptVal = $($event.target).parent().find('input').val().trim();
                    if (iptVal == "") {
                        common.msgFailure("不能为空");
                        return;
                    }
                    if (isNaN(iptVal)) {
                        common.msgFailure("请输入数字");
                        return;
                    }
                    if (!iptVal.pisPositiveNumber()) {
                        common.msgFailure("请输入合法数字");
                        return;
                    }
                    if (numId == 'now_num' && func.lastBillingData > iptVal) {
                        common.msgFailure("本次读数不能小于上次读数");
                        return;
                    }

                    iptVal = parseFloat(iptVal);
                    $($event.target).hide().siblings().hide().parent().find('div:first-child').show();

                    if (type == "currentNum") { //本次读数
                        if (func.currentBillingData == iptVal) {
                            return;
                        }
                        func.currentBillingData = iptVal;
                        //计算差值
                        func.diffData = func.currentBillingData - func.lastBillingData; //不考虑换表
                    } else { //读数差值
                        if (func.diffData == iptVal) {
                            return;
                        }
                        func.diffData = iptVal;
                        //计算本次读数
                        func.currentBillingData = func.lastBillingData + func.diffData; //不考虑换表
                    }
                    //计算钱数
                    var priceType = energy.priceType;
                    var priceTemplateContent = energy.priceTemplateContent;
                    var func_type = func.type;
                    var priceContent = -1;
                    if (priceType == 1) { //分时电价
                        for (var i = 0; i < priceTemplateContent.length; i++) {
                            var price = priceTemplateContent[i];
                            if (func_type == price.type) {
                                priceContent = price.value;
                                break;
                            }
                        }
                    } else { //平均电价
                        priceContent = priceTemplateContent;
                    }
                    if (priceContent == -1) {
                        common.msgFailure("电价与表类型不匹配");
                        func.money = null;
                        meter.totalMoney = null;
                        return;
                    }
                    func.money = func.diffData * priceContent;
                    //计算表总额
                    var meterTotalMoney = 0;
                    for (var i = 0; i < functionList.length; i++) {
                        var func_k = functionList[i];
                        meterTotalMoney += func_k.money;
                    }
                    meter.totalMoney = meterTotalMoney;
                    //计算所有总额
                    var totalMoney = 0;
                    var dataArr_Hou = this.selTenant.leaveContent.dataArr_Hou;
                    for (var i = 0; i < dataArr_Hou.length; i++) {
                        var postPay = dataArr_Hou[i].postPay;
                        var orderList = postPay.orderList;
                        for (var j = 0; j < orderList.length; j++) {
                            totalMoney += orderList[j].money;
                        }
                        var meterList = postPay.meterList;
                        for (var j = 0; j < meterList.length; j++) {
                            totalMoney += meterList[j].totalMoney;
                        }
                    }
                    this.selTenant.leaveContent.totalMoney_Hou = totalMoney;
                    $($event.target).parent().find('.edit').show();
                },
                // 退租页--放弃对表格中电表读数的修改
                giveUpAmend: function ($event) {
                    iptVal = $($event.target).parent().find('.num').text();
                    $($event.target).hide().siblings().hide().parent().find('div:first-child').show();
                    $($event.target).parent().find('input').val(iptVal);
                    $($event.target).parent().find('.edit').show();
                },
                // 退租页--取消退租 返回各自跳转页
                cancelSurrenderTenancy: function () {
                    if (this.rentIdback == 0) {
                        this.currentPage = 'tementListPage';
                    } else if (this.rentIdback == 1) {
                        this.currentPage = 'tenementDetails';
                    }
                }
            },
            // 钩子函数：Vue 实例创建后执行
            created: function () {
                // 初始化页面的loding
                common.showPartLoading();
            },
            computed: {
                addChooseRoomArr: function () {
                    return this.addTenantSelBuilding.roomArr.filter(function (x) {
                        return x.isChecked;
                    });
                },
                isAllChooseRoomGetMeterReady: function () {
                    var isReady = true;
                    for (var i = 0; i < this.addChooseRoomArr.length; i++) {
                        if (!this.addChooseRoomArr[i].isGetMeterReady) {
                            isReady = false;
                            break;
                        }
                    }
                    var rst = isReady && (this.addChooseRoomArr.length > 0);
                    return rst
                },
                add_priceType: function () {
                    var priceType = {};
                    var energyTypeArr = this.energyTypeArr;
                    for (var i = 0; i < energyTypeArr.length; i++) {
                        var energyTypeId = energyTypeArr[i].id;
                        var hadPJ = false;
                        for (var j = 0; j < this.addChooseRoomArr.length; j++) {
                            var meterList = this.addChooseRoomArr[j][energyTypeId + "MeterArr"];
                            for (var k = 0; k < meterList.length; k++) {
                                if (meterList[k].meterType == 0) {
                                    hadPJ = true;
                                    break;
                                }
                            }
                            if (hadPJ) {
                                break;
                            }
                        }
                        priceType[energyTypeId] = hadPJ;
                    }
                    return priceType;
                },
                //添加租户时哪些能耗类型可用
                add_energyType: function () {
                    var energyType = {};
                    var energyTypeArr = this.energyTypeArr;
                    for (var i = 0; i < energyTypeArr.length; i++) {
                        var energyTypeId = energyTypeArr[i].id;
                        var hadMeter = false;
                        energyTypeArr[i].hadMeter = false;
                        for (var j = 0; j < this.addChooseRoomArr.length; j++) {
                            var meterList = this.addChooseRoomArr[j][energyTypeId + "MeterArr"];
                            if (meterList instanceof Array) {
                                hadMeter = meterList.length > 0;
                                energyTypeArr[i].hadMeter = meterList.length > 0;
                                if (hadMeter) {
                                    break;
                                }
                            }
                        }
                        energyType[energyTypeId] = hadMeter;
                    }
                    return energyType;
                },
                payType: function () {
                    var tempArr = this.selEnergyTypePayType.id.split("_");
                    if (tempArr.length == 2) {
                        return tempArr[1];
                    }
                    return "";
                },
                energyType: function () {
                    var tempArr = this.selEnergyTypePayType.id.split("_");
                    if (tempArr.length > 0) {
                        return tempArr[0];
                    }
                    return "";
                },
                energyTypeName: function () {
                    var energyTypeName = "";
                    var energyArr = this.energyTypeArr;
                    for (var i = 0; i < energyArr.length; i++) {
                        if (energyArr[i].id == this.energyType) {
                            energyTypeName = energyArr[i].name;
                            break;
                        }
                    }
                    return energyTypeName
                },
                bat_upt_priceArr: function () {
                    var priceArr = [];
                    var selType = this.energyType;
                    for (var i = 0; i < this.energyTypeArr.length; i++) {
                        var item_i = this.energyTypeArr[i];
                        if (item_i.id == selType) {
                            priceArr = item_i.priceArr;
                            break;
                        }
                    }
                    return priceArr;
                },
                // 批量分时电价列表
                dianPriceArr:function(){
                    var allList = [];
                    var priceArr = [];
                    var energyType = 'Dian';
                    for (var i = 0; i < this.energyTypeArr.length; i++) {
                        var item_i = this.energyTypeArr[i];
                        if (item_i.id == energyType) {
                            allList = item_i.priceArr;
                            priceArr = allList.filter(function(item){
                                    return item.type==1
                            })
                        }
                    }
                    return priceArr;
                },
                // 选中的租户模型
                checkedTenantArr: function () {
                    return this.tenantArr.filter(function (x) {
                        return x.isChecked;
                    });
                },
                // 选中的租户id集合
                checkedTenantIdArr: function () {
                    var tenantIdArr = [];
                    this.checkedTenantArr.forEach(function (val) {
                        tenantIdArr.push(val.tenantId);
                    });
                    return tenantIdArr
                },
                // 获取剩余量/天数/金额参数集合
                remainingAmountParams: function () {
                    return {
                        energyTypeId: this.energyType,
                        tenantList: this.checkedTenantIdArr,
                        remainType: this.selShowData.id,
                        isDownload: this.isDownload
                    }
                },
                // 表底数参数集合
                gaugeRecordParams: function () {
                    return {
                        isDownload: this.isDownload,
                        tenantList: this.checkedTenantIdArr,
                        energyTypeId: this.nowCheckedEnergyId,
                        time: (this.nowCheckedTime + ' ' + this.nowCheckedHour).trim()
                    }
                },
                // 表底数参数集合（单租户）
                gaugeRecordSingleTenantParams: function () {
                    return {
                        isDownload: this.isDownload,
                        tenantId: this.selTenant.tenantId,
                        energyTypeId: this.nowCheckedEnergyIdSingle,
                        time: (this.nowCheckedTimeSingle + ' ' + this.nowCheckedHourSingle).trim()
                    }
                },
                bat_total_noPayTenantCount: function () {
                    return this.checkedTenantArr.filter(function (x) {
                        return !x.noPayBill_Ready;
                    }).length;
                },
                maxDateInSettlement: function () {
                    var checkedTenantArr = this.checkedTenantArr;
                    var newCheckedTenantArr = [];
                    for (var i = 0; i < checkedTenantArr.length; i++) {
                        var checkedTenantArrItem = checkedTenantArr[i];
                        newCheckedTenantArr.push(checkedTenantArrItem.lastClearingTime.replace(/-|:|\s/g, '').substr(0, 8));
                    }
                    return Math.max.apply(Math, newCheckedTenantArr);
                },
                maxDateInSurrenderTenancy: function () {
                    var dataArr_Hou = this.selTenant.leaveContent.dataArr_Hou;
                    var lastClearTimeMax = null;
                    if (dataArr_Hou.length != 0) {
                        var maxDateArr = [];
                        for (var i = 0; i < dataArr_Hou.length; i++) {
                            var meterList = dataArr_Hou[i].postPay.meterList;
                            for (var j = 0; j < meterList.length; j++) {
                                if (!meterList[j].lastClearingTime) continue;
                                maxDateArr.push(meterList[j].lastClearingTime.replace(/-|:|\s/g, '').substr(0, 8));
                            }
                        }
                        lastClearTimeMax = maxDateArr.length == 0 ? null : Math.max.apply(Math, maxDateArr);
                    }
                    return lastClearTimeMax;
                },
                bat_total_noPayBillCount: function () {
                    var totalCount = 0;
                    for (var i = 0; i < this.checkedTenantArr.length; i++) {
                        var t = this.checkedTenantArr[i];
                        if (!t.noPayBill_Ready) {
                            totalCount += t.noPayBillArr.length;
                        }
                    }
                    return totalCount;
                },
                bat_total_noPayMoney: function () {
                    var totalMoney = 0;
                    for (var i = 0; i < this.checkedTenantArr.length; i++) {
                        var t = this.checkedTenantArr[i];
                        if (!t.noPayBill_Ready) {
                            totalMoney += t.noPayBillsTotalMoney;
                        }
                    }
                    return totalMoney;
                },
                checkedTenantContactArr: function () {
                    var c_Arr = [];
                    for (var i = 0; i < this.checkedTenantArr.length; i++) {
                        var t = this.checkedTenantArr[i];
                        c_Arr.push({
                            buildingId: t.buildingId,
                            tenantId: t.tenantId,
                            tenantName: t.tenantName,
                            contactName: t.contactName,
                            contactMobile: t.contactMobile
                        });
                    }
                    return c_Arr;
                },

            },
            watch: {
                tenantFilterParam: function (newValue) {
                    if (newValue.length > 0) {
                        this.$nextTick(function () {
                            $("#t_feetype_cbx_hou").psel(0, false);
                            $("#t_feetype_cbx").psel(0, false);
                        });
                    }
                },
                selFeeType: function (newValue) {
                    if (newValue.childArr.length > 0) {
                        this.$nextTick(function () {
                            $("#t_showdata_cbx").psel(0, false);
                        });
                    }
                },
                selPrice: function (newValue) {
                    if (newValue) {
                        if (newValue.type == 0) {
                            $('#pjdjRadio').psel(true);
                        } else if (newValue.type == 1) {
                            $('#fsdjRadio').psel(true);
                        } else {
                            $('#pjdjRadio').psel(false);
                            $('#fsdjRadio').psel(false);
                        }
                    }
                },
            },
            components: {
                'post-paid': {
                    template: '#post_paid_tpl',
                    props: {
                        energyTypeId: "",
                        status: 0,
                        operationPermissions: Object,
                        "priceTemplate": {
                            "id": "",
                            "name": "",
                            "type": "",
                        },
                        "payContent": {
                            "orderList": [],
                            "weiJieSuanJinE": "", //未结算金额
                            "yingJiaoJinE": "", //应缴金额
                            "lastClearingTime": "", //上次结算事件
                        }
                    }
                },
                'meter-meter': {
                    template: '#meter_meter_tpl',
                    props: {
                        energyTypeId: "",
                        status: 0,
                        operationPermissions: Object,
                        "priceTemplate": {
                            "id": "",
                            "name": "",
                            "type": "",
                        },
                        "payContent": {
                            "remainType": "剩余金额（元）",
                            "remainData": "", //剩余量
                            "remainDays": ""
                        }
                    }
                },
                'software-meter': {
                    template: '#software_meter_tpl',
                    props: {
                        energyTypeId: "",
                        status: 0,
                        operationPermissions: Object,
                        "priceTemplate": {
                            "id": "",
                            "name": "",
                            "type": "",
                        },
                        "payContent": {
                            "remainType": "剩余金额（元）",
                            "meterList": [],
                            //{
                            //        "meterId":"",//仪表编码
                            //        "remainData":12.5//剩余量
                            //      "remainDays":"1~3"
                            //    }
                        }
                    }
                },
                'software-software': {
                    template: '#software_software_tpl',
                    props: {
                        energyTypeId: "",
                        status: 0,
                        operationPermissions: Object,
                        "priceTemplate": {
                            "id": "",
                            "name": "",
                            "type": "",
                        },
                        "payContent": {
                            "remainType": "剩余金额（元）",
                            "remainData": 12.5, //剩余量
                            "remainDays": ""
                        }
                    },
                }
            }
        });
    }
    return tenantMngModel._instance; // 返回 Vue 实例
}

//租户模型
function tenantVM() {
    var self = this;
    self["tenantId"] = ""; //租户id
    self["tenantName"] = ""; //租户名称
    self["tenantStatus"] = ""; //租户状态，0，未激活，1，已激活，2，已退租
    self["prePayType"] = ""; //已激活预付费租户的充值扣费类型0//("预付费-表充表扣", 0),("预付费-软充表扣", 1),("预付费-软充软扣", 2);
    self["roomIds"] = ""; //字符串格式的房间id
    self["buildingId"] = ""; //所属建筑Id
    self["buildingName"] = ""; //所属建筑名称
    self["area"] = ""; //面积
    self["contactName"] = ""; //联系人
    self["contactMobile"] = ""; //联系电话
    self["activeTime"] = ""; //激活时间格式 yyyy-MM-dd hh:mm:ss
    self["leaveTime"] = ""; //退租时间
    self["monthEnergy"] = 0; //当月能耗
    self["remainMoney"] = 0; //剩余金额
    self["remainEnergy"] = 0; //剩余量
    self["remainDays"] = "--"; //剩余天数 n~n
    self["lastClearingTime"] = ""; //上次结算时间
    self["lastBillingTime"] = ""; //上次缴费时间
    self["noBillingEnergy"] = 0; //未结算能耗
    self["noBillingMoney"] = 0; //未结算金额
    self["orderSize"] = 0; //未缴费账单数
    self["billingEnergy"] = 0; //结算的能耗量
    self["billingMoney"] = 0; //结算的金额
    self["contactList"] = [], //其他联系人列表{contactName，contactMobile}
        self["tenantTypeId"] = ""; //业态Id
    self["tenantTypeName"] = ""; //业态名称
    self["remark"] = ""; //备注
    self["roomList"] = []; //房间列表
    self["update_energyList"] = []; //编辑租户时的能耗集合
    self["energyList"] = []; //能耗集合
    self["ishasPrepayment"] = true, // 是否含有预付费
        self["energySplitExpressionList"] = []; //拆分公式
    self["isChecked"] = false; //是否在租户列表选中
    self["meterChangeRecordArr"] = []; //租户的换表记录，某一能耗类型
    self["isAlarm"] = false; //租户的换表记录，某一能耗类型
    self.energyCostReport = new energyCostReportVM(); //pdf报告
    self.leaveContent = {
        dataArr_Hou: [], //所有后付费的能耗
        totalCount_Hou: 0,
        totalMoney_Hou: null,
        dataArr_Yu: [], //所有预付费的能耗
    }; //退租各个能耗详情
    self.settle = {
        energyTypeId: "",
        energyTypeName: "",
        energyUnit: "",
        clearingTime: "",
        dataArr: [],
        orderId: ""
    }; //结算
    self.recharge = {
        energyTypeId: "",
        energyTypeName: "",
        meterId: "",
        billingType: "",
        remainData: "",
        dataUnit: "",
        refreshTime: "",
        typeName: "",
        prePayType: "",
        orgRemainData: "",
        rechargeValue: ""
    }; //充值
    self.payOrder = {
        energyTypeId: "",
        energyTypeName: "",
        totalMoney: "",
        dataArr: [],
    }; //缴费单租户
    self.feeRecordArr = []; //缴费记录,单租户+批量
    self.feeRecordArr_energyTypeId = ""; //单租户缴费记录所选择的能耗类型id
    self.noPayBillArr = []; //欠费账单-批量
    self.noPayBillsTotalMoney = ""; //欠费账单总计
    self.noPayBill_Ready = false; //批量缴费中单个租户缴费是否已缴清
    self.energyCostDataList = []; //批量能耗报表
    self.rechargeRecordList_bat = []; // 批量充值记录
    self.settle_bat = {
        "currentClearingTime": null,
        "lastClearingTime": null,
        "currentBillingEnergy": null,
        "currentBillingMoney": null
    };
}
//租户信息
tenantVM.tran = function (org, newItem) {
    //var newItem = new tenantVM();
    newItem["tenantFlag"] = org["tenantFlag"];
    newItem["tenantId"] = org["tenantId"];
    newItem["tenantName"] = org["tenantName"];
    newItem["tenantStatus"] = org["tenantStatus"];
    newItem["prePayType"] = org["prePayType"];
    newItem["roomIds"] = org["roomIds"];
    newItem["buildingId"] = org["buildingId"];
    newItem["buildingName"] = org["buildingName"];
    newItem["ishasPrepayment"] = org["ishasPrepayment"];
    newItem["area"] = tenantCtrl.numberFormat(org["area"], tenantCtrl.fixType_money);
    newItem["contactName"] = org["contactName"];
    newItem["contactMobile"] = org["contactMobile"];
    newItem["activeTime"] = org["activeTime"];
    newItem["leaveTime"] = org["leaveTime"];
    newItem["monthEnergy"] = tenantCtrl.numberFormat(org["monthEnergy"], tenantCtrl.fixType_dynamic, true);
    newItem["remainMoney"] = tenantCtrl.numberFormat(org["remainMoney"], tenantCtrl.fixType_money, true);
    newItem["remainEnergy"] = tenantCtrl.numberFormat(org["remainEnergy"], tenantCtrl.fixType_dynamic, true);
    newItem["remainDays"] = org["remainDays"];
    newItem["lastBillingTime"] = org["lastBillingTime"];
    newItem["noBillingEnergy"] = tenantCtrl.numberFormat(org["noBillingEnergy"], tenantCtrl.fixType_dynamic, true);
    newItem["noBillingMoney"] = tenantCtrl.numberFormat(org["noBillingMoney"], tenantCtrl.fixType_money, true);
    newItem["orderSize"] = org["orderSize"];
    newItem["billingEnergy"] = tenantCtrl.numberFormat(org["billingEnergy"], tenantCtrl.fixType_dynamic, true);
    newItem["billingMoney"] = tenantCtrl.numberFormat(org["billingMoney"], tenantCtrl.fixType_money, true);
    newItem["contactList"] = org["contactList"] || [];
    newItem["remark"] = org["remark"];
    newItem["roomList"] = org["roomList"] || [];
    newItem["energyList"] = org["energyList"] || [];
    newItem["tenantTypeId"] = org["tenantTypeId"];
    newItem["tenantTypeName"] = org["tenantTypeName"];
    newItem["isAlarm"] = org["isAlarm"] == 1;
    if (newItem["tenantStatus"] == 0) {
        $("#switch1").pdisable(true);
    }
    return newItem;
}


//能耗类型
function energyTypeVM() {
    var self = this;
    self.id = "";
    self.name = "";
    self.code = "";
    self.priceArr = [];
    self.priceUnit = "";
}

//能耗及付费类型模型
function energyTypePayTypeVM() {
    var self = this;
    self["id"] = ""; //     : "Dian_0",
    self["name"] = ""; //     : "电"
}

//房间模型
function roomVM() {
    var self = this;
    self["id"] = ""; //  : "uuid",  //房间主键
    self["code"] = ""; //  : "R1001", //房间编码
    self["buildingId"] = ""; //  :“3101140001”，//建筑编码
    self["floorId"] = ""; //  :"1F",  //楼层编码
    self["area"] = 0; // :445.5,//面积
    self["energyTypeIds"] = ""; //  :"Dian,Shui",// 能耗类型编码列表
    self["status"] = 1; // :1 //0 未使用 1 使用中
    self["isChecked"] = false; // :1 //0 未使用 1 使用中
    self["allMeterList"] = []; //所有类型的表具
    self["DianMeterArr"] = [];
    self["ShuiMeterArr"] = [];
    self["ReShuiMeterArr"] = [];
    self["RanQiMeterArr"] = [];
    self["DianMeterIdStr"] = "";
    self["ShuiMeterIdStr"] = "";
    self["ReShuiMeterIdStr"] = "";
    self["RanQiMeterIdStr"] = "";
}

//电价
function priceVM() {
    var self = this;
    self["id"] = "";
    self["name"] = "";
    self["type"] = -1;
    self["unit"] = "";
    self["content"] = {};
}

//建筑
function buildingVM() {
    var self = this;
    self.id = "";
    self.name = "";
    self.floorArr = [];
    self.roomArr = [];
    self.roomShowArr = [];
}

//能耗费用报表
function energyCostReportVM() {
    var self = this;
    self.timeFrom = "";
    self.timeTo = "";
    self.timeShow = function () {
        var timeFrom = (self.timeFrom + "").substring(0, 10);
        var timeTo = (self.timeTo + "").substring(0, 10);
        var strTime = timeFrom + (timeFrom == timeTo ? "" : ('~' + timeTo));
        return strTime.replace(/-/g, '.')
    }
    self.energyTypeId = "";
    self.energyTypeName = ""; //能耗类型
    self.energyUnit = ""; //能耗单位
    self.prePayType = ""; //充值方式
    self.billingType = ""; //终止类型
    self.billingTypeUnit = ""; //充值单位
    self["totalEnergy"] = ""; //  :32323,
    self["totalMoney"] = ""; //  :23322.3
    self["remainMoney"] = "";
    self.priceTemplateId = ""; //充值单位
    self.priceTemplateName = ""; //充值单位
    self["dataList"] = [];
    self.isCurrentOrder = false; //是否为本期账单
}

//筛选参数
function tenantFilterParam() {
    var self = this;
    self.id = -1;
    self.name = "";
    self.childArr = [];
}

tenantFilterParam.tranFrom_YuFuFei = function (chargeType) {
    var self = new tenantFilterParam();
    self.id = chargeType.type;
    self.name = chargeType.name;
    var orgArr = chargeType.remainType || [];
    var childArr = [];
    for (var i = 0; i < orgArr.length; i++) {
        var item = orgArr[i];
        var newItem = new tenantFilterParam();
        newItem.id = item.type;
        newItem.name = item.name;
        childArr.push(newItem);
    }
    self.childArr = childArr;
    return self;
}

tenantFilterParam.tranFrom_HouFuFei = function (amountType) {
    var self = new tenantFilterParam();
    self.id = amountType.type;
    self.name = amountType.name;
    return self;
}