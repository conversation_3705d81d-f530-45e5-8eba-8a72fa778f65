/**
 * Created by lidongpeng on 2017/10/18.
 */

var ldp = {};
ldp.nextsteptime = 0; //添加租户下一步点击的个数
ldp.addRoomBtn = function (event) {
    event.stopPropagation();
    $('#chooseRoom').pshow();
    $('#floor_filter').precover('选择房间');
    $('#room_search').precover();
    $("#floor_filter").psel(0);
};
ldp.managePricePlan = function (obj) {
    //显示管理价格方案
    var model = tenantMngModel.instance();
    model.selEnergyType = obj;
    model.energyCosttempId = 2;
    var selObj = $("#" + obj.id + "_price_sel").psel();
    var priceId = "";
    if (selObj) {
        priceId = obj.priceArr[selObj.index].id;
    }
    model.selPriceId = priceId;
    model.currentPage = 'managePrivcePlanPage';
};
ldp.deletePricePlan = function (event, obj) {
    tenantMngModel.instance().confirmDelPrice = obj;
    //价格方案关联了租户,但没有相同类型的价格方案
    $('#deletePricePlan_noSamePlan').pshow({
        title: '确定要删除该价格方案吗？',
        subtitle: ''
    });
};

ldp.hideFloat = function () {
    $('#deletePricePlan_noSamePlan').phide();
};
ldp.deleteTement = function (event) {
    //删除成功
    $(event.currentTarget).parents('.per-modal-control').phide();
    // $('#message').pshow({text:'操作成功',state:'success'});
};
ldp.showPricePlan = function (event, obj) {
    event.stopPropagation();
    var type = $(event.currentTarget).attr('type');
    var model = tenantMngModel.instance();
    $('#pj_value').precover(false);
    $('#jf_value').precover(false);
    $('#gf_value').precover(false);
    $('#pd_value').precover(false);
    $('#dg_value').precover(false);
    switch (type) {
        case 'detail': //查看详情
            model.selEnergyType = obj;
            var selObj = $("#" + obj.id + "_price_sel").psel();
            var priceId = "";
            if (selObj) {
                priceId = obj.priceArr[selObj.index].id;
            }
            model.selPriceId = priceId
            var idx = $("#" + obj.id + "_price_sel").psel().index;
            var energyType;
            for (var i = 0; i < model.energyTypeArr.length; i++) {
                var item_i = model.energyTypeArr[i];
                if (item_i.id == obj.id) {
                    energyType = item_i;
                    break;
                }
            }
            var priceObj = energyType.priceArr[idx];
            model.selPrice = JSON.parse(JSON.stringify(priceObj));
            $('#build_edit_pricePlan').pshow({
                title: '价格方案详情'
            });
            model.priceOperateType = 0;
            if (model.selPrice.type == 0) {
                $('#pjdjRadio').psel(true);
            } else {
                $('#fsdjRadio').psel(true);
            }
            break;
        case 'edit': //编辑
            model.selPrice = JSON.parse(JSON.stringify(obj));
            $('#build_edit_pricePlan').pshow({
                title: '编辑价格方案'
            });
            model.priceOperateType = 1;
            $('#price_name').precover();

            break;
        default: //新建
            $('#build_edit_pricePlan').pshow({
                title: '新建价格方案'
            });
            $('#price_name').precover();
            model.priceOperateType = 2;
            var newPrice = new priceVM();
            newPrice.type = 0;
            newPrice.unit = model.selEnergyType.priceUnit;
            model.selPrice = newPrice;
    }
};
// ldp.editPriceBtn = function (event) {
//     var model = tenantMngModel.instance();
//     tenantMngModel.instance().priceOperateType = 1;
// };
ldp.edit_electricityprice = function (event) {
    event.preventDefault();
    if (!tenantMngModel.instance().priceOperateType || event.target.nodeName === 'INPUT') {
        return;
    } //详情状态时不可编辑
    var _this = $(event.currentTarget);
    _this.find('.textState').hide();
    _this.find('.inputState').show();
    _this.find('input').focus();
};

ldp.add_editNext = function () {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    if (typeof (selTenant.energyList.prePayType) == undefined) {
        if (selTenant.energyList[0].prePayType == 1) {
            $('#pricePlan').pdisable(true);
            $("#message").pshow({
                text: "租户充值类型包含软充表扣，不允许修改",
                state: "failure"
            });
        } else {
            $('#pricePlan').pdisable(false)
        }
    }
    //判断基本信息页
    if (model.subaddPage === 'step1') {
        var tementNoVal = $('#tementNo').pval();

        function valid() {
            if (model.addOrEdit == 0 && !$('#parentBuilding').psel()) {
                common.msgFailure("请选择建筑");
            }
            // 判断租户名称是否符合要求   
            if (!$('#tementName').pverifi()) {
                return;
            }
            // 判断租户编号是否符合要求   
            if (!$('#tementNo').pverifi()) {
                return;
            } else {
                let verifiReg = /^[A-Za-z0-9]{1,20}$/;
                let verifiName = $('#tementNo input').val();
                if (verifiReg.test(verifiName) == false) {
                    $("#tementNo .error-tip").css("display", "block").text("填入内容应为英文或数字！");
                    $('#tementNo input').addClass("input-error")
                    return;
                }
            }
            // 判断面积是否符合要求
            if (!$('#tementArea').pverifi()) {
                return;
            }
            //判断联系电话是否符合要求
            if (!!$('.contact_number input').val()) {
                let verifiReg = /^(13[0-9]|14[0-9]|15[0-9]|17[0-9]|18[0-9])\d{8}$/;
                let verifiName = $('.contact_number input').val();
                if (verifiReg.test(verifiName) == false) {
                    $(".contact_number .error-tip").css("display", "block").text("手机号格式不正确！");
                    $('.contact_number input').addClass("input-error");
                    return;
                }
            }
            // 备注错误
            if (!$('#tementRemarks').pverifi()) {
                return;
            }

            if (!$('#parentYetai').psel()) {
                common.msgFailure("请选择业态");
                return;
            }
            //面积
            var $area = $('#tementArea');
            var areaPutVal = $area.pval();
            if (areaPutVal != "") {
                if (!$area.pverifi()) {
                    return;
                }
                tenantMngModel.instance().selTenant.area = Number(areaPutVal).toFixed(2);
            }
            var contactlist = $('.input_comboxPartGroup>li .contact .per-input-basic');
            var tellist = $('.input_comboxPartGroup>li .contact_number .per-input-basic');
            //第一组验证
            if (!contactlist.eq(0).pverifi() || !tellist.eq(0).pverifi()) {
                return;
            }
            //自定义添加验证
            for (var i = 1; i < contactlist.length; i++) {
                var $eq_a = contactlist.eq(i);
                var $eq_b = tellist.eq(i);
                var val_a = $eq_a.pval();
                var val_b = $eq_b.pval();
                if (val_a != "" || val_b != "") {
                    if (!$eq_a.pverifi() || !$eq_b.pverifi()) {
                        return;
                    }
                }
            }
            if (model.addOrEdit == 1 && model.selTenant.tenantStatus == 1) {
                //编辑已激活租户
                model.subaddPage = 'step3';
                model.step = 2;
            }
            //验证通过，点击下一步生效
            if (model.stepArr.length > model.step) {
                //根据所选择的建筑获取楼层和房间
                var selBuilding = model.buildingArr2[$('#parentBuilding').psel().index];
                if (selBuilding.roomArr.length == 0) {
                    tenantCtrl.getRoomArr(selBuilding,callback);
                }else {
                    callback();
                }
                if (selBuilding.floorArr.length == 0) {
                    tenantCtrl.getFloorArr(selBuilding);
                }
                function callback() {
                    model.subaddPage = 'step' + (++model.step);
                }
                
                model.addTenantSelBuilding = selBuilding;
            }
        }
        if (model.addOrEdit == 0) {
            tenantCtrl.verifyTenantId(tementNoVal, function (result) {
                if (result) {
                    valid();
                } else {
                    $('#tementNo').pshowTextTip('该租户编号已存在');
                    return;
                }
            });
        } else {
            valid();
        }
    } else { //判断房间页
        if (model.addChooseRoomArr.length == 0) {
            common.msgFailure("请选择房间");
            return
        }
        var formulaMap = {};
        var expressionList = [];
        var formula_energy_typeArr = $("[name='formula_energy_type']");
        var formula_valArr = $("[name='formula_val']");
        for (var i = 0; i < formula_energy_typeArr.length; i++) {
            var $eq_a = formula_energy_typeArr.eq(i);
            var $eq_b = formula_valArr.eq(i);
            var typeSel = $eq_a.psel();
            var formulaVal = $eq_b.pval().trim();
            if (!typeSel && formulaVal == "") {
                continue;
            }
            if (!typeSel) {
                common.msgFailure("请选择拆分公式能耗类型");
                return;
            }
            if (!$eq_b.pverifi()) {
                return;
            }
            var energyTypeId = model.energyTypeArr[typeSel.index].id;
            if (energyTypeId in formulaMap) {
                common.msgFailure("同一个能耗类型不能有多个拆分公式");
                return;
            }
            formulaMap[energyTypeId] = $eq_b;
            expressionList.push({
                "energyTypeId": energyTypeId,
                "expression": formulaVal
            });
        }
        if (expressionList.length > 0) {
            tenantCtrl.verifyFormula(expressionList, function (resJson) {
                if (!(resJson instanceof Array) || resJson.length == 0) {
                    common.msgFailure("验证拆分公式失败");
                    return;
                }
                resJson = resJson[0].expressionList;
                if (!(resJson instanceof Array) || resJson.length == 0) {
                    common.msgFailure("验证拆分公式失败");
                    return;
                }
                var hadError = false;
                for (var i = 0; i < resJson.length; i++) {
                    var item_i = resJson[i];
                    var _energyTypeId = item_i.energyTypeId;
                    if (item_i.result == 1) {
                        hadError = true;
                        formulaMap[_energyTypeId].pshowTextTip("拆分公式不合法");
                    }
                }
                if (!hadError) {
                    validExpressionList();
                }
            });
        } else {
            validExpressionList();

            if (model.addOrEdit == 0) {
                ldp.nextsteptime++;
                if (ldp.nextsteptime != 1) {
                    return;
                }
                $('#Dian_0_payment_types').psel(true, true);
                $('#Dian_0_deduction_types').psel(true, true);
                $('#Dian_1_recharge_types').psel(true, true);
                $('#Shui_0_payment_types').psel(true, true);
                $('#Shui_0_deduction_types').psel(true, true);
                $('#Shui_1_recharge_types').psel(true, true);
                $('#ReShui_0_payment_types').psel(true, true);
                $('#ReShui_0_deduction_types').psel(true, true);
                $('#ReShui_1_recharge_types').psel(true, true);
                $('#RanQi_0_payment_types').psel(true, true);
                $('#RanQi_0_deduction_types').psel(true, true);
                $('#RanQi_1_recharge_types').psel(true, true);
            }
        }

        function validExpressionList() {
            //验证通过，点击下一步生效
            if (model.stepArr.length > model.step) {
                model.subaddPage = 'step' + (++model.step);
            }
        }
    }
};
ldp.add_editPrev = function () {
    if ($('#formula_text')[0]) {
        $('#formula_text').precover();
    }
    var model = tenantMngModel.instance();
    if (model.step > 1) {
        model.subaddPage = 'step' + (--model.step);
    }
};

//完成
ldp.add_editFinish = function () { //添加/编辑完成按钮
    var model = tenantMngModel.instance();
    //验证并取值
    var energyList = [];
    var hadEnergy = false;
    var energyTypeArr = model.energyTypeArr;
    for (var i = 0; i < energyTypeArr.length; i++) {
        var energyType_i = energyTypeArr[i];
        var energyTypeId = energyType_i.id;
        if (!$('#' + energyTypeId + '_system').parents('.system_list').attr('pdisabled')) {
            var isSystemChecked = $("#" + energyTypeId + "_system").psel();
        } else {
            var isSystemChecked = false;
        }
        if (!isSystemChecked) {
            continue;
        }
        hadEnergy = true;
        var ffType = -1;
        var yuStatus = $("#" + energyTypeId + "_0_payment_types").psel();
        if (yuStatus) {
            ffType = 0;
        } else {
            var houStatus = $("#" + energyTypeId + "_1_payment_types").psel();
            if (houStatus) {
                ffType = 1;
            }
        }
        if (ffType == -1) {
            common.msgFailure("请选择付费类型");
            return;
        }
        var kfType = -1;
        var czType = -1;
        if (ffType == 0) {
            var kfTypeStatus_0 = $("#" + energyTypeId + "_0_deduction_types").psel();
            if (kfTypeStatus_0) {
                kfType = 0;
            } else {
                var kfTypeStatus_1 = $("#" + energyTypeId + "_1_deduction_types").psel();
                if (kfTypeStatus_1) {
                    kfType = 1;
                } else {
                    var kfTypeStatus_2 = $("#" + energyTypeId + "_2_deduction_types").psel();
                    if (kfTypeStatus_2) {
                        kfType = 2;
                    }
                }
            }
            if (kfType == -1) {
                common.msgFailure("请选择扣费类型");
                return;
            }

            var czTypeStatus_0 = $("#" + energyTypeId + "_0_recharge_types").psel();
            if (czTypeStatus_0) {
                czType = 0;
            } else {
                var czTypeStatus_1 = $("#" + energyTypeId + "_1_recharge_types").psel();
                if (czTypeStatus_1) {
                    czType = 1;
                }
            }
            if (czType == -1) {
                common.msgFailure("请选择充值类型");
                return;
            }
        } else {
            kfType = null;
            czType = null;
        }
        var priceSel = $("#" + energyTypeId + "_price_sel").psel();
        if (!priceSel) {
            common.msgFailure("请选择价格方案");
            return;
        }
        var selPrice = energyType_i.priceArr[priceSel.index];
        if (model.add_priceType[energyTypeId]) {
            if (selPrice.type == 1) {
                common.msgFailure("所选房间中存在不支持分时电价的表具,故不能选择分时" + energyType_i.name + "价");
                return;
            }
        }
        var priceTemplateId = selPrice.id;
        energyList.push({
            "energyTypeId": energyTypeId,
            "ffType": ffType, //0 预付费 1 后付费
            "kfType": kfType, //0 表充表扣, 1 软件充值仪表扣费 2 软件充值软件扣费
            "czType": czType, //0 量 1 钱
            "priceTemplateId": priceTemplateId
        });
    }
    if (energyList.length == 0) {
        common.msgFailure("请至少选择一个能耗类型");
        return;
    }
    //第一步 基本页的各项值
    var tenantName = $('#tementName').pval(); //租户名称
    var tenantId = $('#tementNo').pval(); //租户编号
    var parentBuilding = $('#parentBuilding').psel(); //所属建筑
    var selBuilding = model.buildingArr2[parentBuilding.index];
    var buildingId = selBuilding.id;
    var parentYetai = $('#parentYetai').psel(); //所属业态
    var selTenantType = model.tenantTypeArr[parentYetai.index];
    var tenantTypeId = selTenantType.id;
    var areaPutVal = $('#tementArea').pval();
    var area = areaPutVal ? parseFloat(areaPutVal) : null;
    var contacts = model.contacts; //联系人组 (第一组必填)
    var firstContact = contacts[0];
    var contactName = firstContact.contactName;
    var contactMobile = firstContact.contactMobile;
    var contactList = [];
    for (var i = 1; i < contacts.length; i++) {
        var contact_i = contacts[i];
        var name = contact_i.contactName;
        var tel = contact_i.contactMobile;
        if (name == "" || tel == "") {
            continue;
        }
        contactList.push({
            "contactName": name,
            "contactMobile": tel,
        });
    }
    var remark = $('#tementRemarks').pval(); //备注(选填)

    //第二步
    var chooseRoomArr = model.addChooseRoomArr;
    var roomList = [];
    for (var i = 0; i < chooseRoomArr.length; i++) {
        var room_i = chooseRoomArr[i];
        roomList.push({
            "roomId": room_i.id,
            "roomCode": room_i.code
        });
    }
    var energySplitExpressionList = [];
    var formula_energy_typeArr = $("[name='formula_energy_type']");
    var formula_valArr = $("[name='formula_val']");
    for (var i = 0; i < formula_energy_typeArr.length; i++) {
        var $eq_a = formula_energy_typeArr.eq(i);
        var $eq_b = formula_valArr.eq(i);
        var typeSel = $eq_a.psel();
        var formulaVal = $eq_b.pval().trim();
        if (!typeSel || formulaVal == "") {
            continue;
        }
        var energyTypeId = model.energyTypeArr[typeSel.index].id;
        energySplitExpressionList.push({
            "energyTypeId": energyTypeId,
            "expression": formulaVal
        });
    }
    var param = {
        "tenantId": tenantId, //租户编码
        "tenantName": tenantName, //租户名称
        "buildingId": buildingId, //建筑id
        "tenantTypeId": tenantTypeId, //租户类型编码
        "area": area,
        "contactName": contactName,
        "contactMobile": contactMobile,
        "contactList": contactList,
        "remark": remark,
        "roomList": roomList,
        "energySplitExpressionList": energySplitExpressionList,
        "energyList": energyList
    };
    // console.log(param);
    if (model.addOrEdit == 0) {
        tenantCtrl.addTenant(param);
        $("#t_status_cbx").psel(2, true);
    } else {
        tenantCtrl.updateTenant(param);
    }
};

ldp.addContact = function (event) { //添加联系人
    var model = tenantMngModel.instance();
    if (model.contacts.length > 3) {
        return;
    }
    model.contacts.push({
        contactName: '',
        contactMobile: ''
    });
};
ldp.minuscontacts = function (event) { //删除联系人
    var model = tenantMngModel.instance();
    model.contacts.splice($(event.currentTarget).attr('index'), 1);
};

//选择房间状态改变
ldp.chooseRoom = function (obj, event) {
    event.stopPropagation();
    if (obj.isChecked) {
        if (obj.meterList.length == 0) {
            tenantCtrl.getRoomMeter(tenantMngModel.instance().addTenantSelBuilding.id, obj); // 获取房间的仪表
        }
    }
};

ldp.minusRoom = function (obj, e) {
    var rst = !obj.isChecked;
    $("#" + obj.id).psel(rst, false);
    obj.isChecked = rst;
};

ldp.sel_room_confirm = function () { //获取房间列表
    $('#chooseRoom').phide();
};

ldp.addMeterformula = function () {
    var model = tenantMngModel.instance();
    model.meterformulalist.push({
        "energyTypeId": "",
        "expression": ""
    });
};
ldp.minusMeterformula = function (event, a) {
    var model = tenantMngModel.instance();
    model.meterformulalist.splice($(event.currentTarget).attr('index'), 1);
};
ldp.checkchinese = function (event) {
    // 不允许中文输入，输不进去
    var val = event.target.value;
    for (var i = 0; i < val.length; i++) {
        var testStr = val[val.length - 1];
        if (/^[\u4e00-\u9fa5]+$/i.test(testStr)) {
            val = val.substring(0, val.length - 1);
        }
    }
    event.target.value = val;
};
//拆分公式获取焦点 选择相应位置后，自动又跳转到最后位置
ldp.formulaclick = function () {
    var val = $('#formula_text input').val();
    $('#formula_text input').val("").focus().val(val);
};

ldp.selectEnergyType = function (obj, event) {
    var state = event.pEventAttr.state,
        _this = $(event.currentTarget);
    if (state) {
        _this.parents('.system_list_head').siblings('.system_list_body').slideDown();
    } else {
        _this.parents('.system_list_head').siblings('.system_list_body').slideUp();
    }
};
ldp.toaddTement = function () {
    var model = tenantMngModel.instance();
    model.currentPage = 'addTementPage';
};
ldp.checkedPricePlan = function (obj, event) {
    var model = tenantMngModel.instance();
    var _this = $(event.currentTarget);
    _this.parents('.pricePlan_combox').siblings('.checkPriceDetail').pdisable(false);
};

$(function () {
    $('#tementNo input').on('blur', function () {
        var _value = $(this).val();
        if (_value) {
            //检验是否重复
            tenantCtrl.verifyTenantId(_value, function (result) {
                if (!result) {
                    $('#tementNo').pshowTextTip('该租户编号已存在');
                }
            });
        }
    });

    $(document).on('click', function (event) {
        var tg = $(event.target);
        if (tg.parents('#meterDetailCalendar')[0]) {
            return
        }
        $('#meterDetailCalendar ._combobox_bottom ').hide();
        if (tg.parents('#reportCalendar')[0]) {
            return
        }
        $('#reportCalendar ._combobox_bottom ').hide();
        if (tg.parents('#fee_record_calendar')[0]) {
            return
        }
        $('#fee_record_calendar ._combobox_bottom ').hide();
    });
});
ldp.toTementDetail = function () {
    var model = tenantMngModel.instance();
    model.currentPage = 'tenementDetails';
};
ldp.chooseMeterTime = function () {
    $('#meterDetailCalendar ._combobox_bottom ').hide();
    tenantCtrl.getMeterDetail();
};

//添加租户
ldp.addTement = function () {
    // 清空页码输入框状态
    staticEvent.pageSizeDefault();

    var model = tenantMngModel.instance();
    model.selTenant = new tenantVM();
    model.addOrEdit = 0;
    model.stepArr = model.stepArr_3;
    model.step = 1;
    model.subaddPage = 'step1';
    model.currentPage = 'addTementPage';
    model.contacts = [{
        contactName: "",
        contactMobile: ""
    }];
    model.meterformulalist = [];
    for (var i = 0; i < model.buildingArr2.length; i++) {
        var b_item = model.buildingArr2[i];
        b_item.roomArr = [];
        b_item.roomShowArr = [];
    }
    $("#parentBuilding").precover("请选择");
    $("#parentYetai").precover("请选择");
    $("#tementName").precover();
    $("#tementNo").precover();
    $("#tementName").precover();
    $("#contact").precover();
    $("#contact_tel").precover();
    $('#tementArea').precover();
    for (var i = 0; i < $('.contact .per-input-basic').length; i++) {
        $('.contact .per-input-basic').eq(i).precover();
    }
    for (var i = 0; i < $('.contact_number .per-input-basic').length; i++) {
        $('.contact_number .per-input-basic').eq(i).precover();
    }
    //kyan 修改 
    $('.input_comboxPart').find('.input_comboxPart').eq(0).pdisable(false);
    $('.input_comboxPart').find('.input_comboxPart').eq(1).pdisable(false);
    //清空选择能耗的已选项
    var allCombox = $('.system_list_body .per-combobox-basic');
    for (var i = 0; i < allCombox.length; i++) {
        var _list = allCombox.eq(i);
        _list.precover('请选择');
        $('.system_list_head .per-switch-checkbox').eq(i).psel(false, true)
    }
    $('.checkPriceDetail').pdisable(true);
};

//下载全码
ldp.allCode = function () {
    var fName = 'FNTTenantFlagDownloadService';
    pajax.downloadByParam(fName, '');
}

// 编辑租户 添加租户页面的返回
ldp.gotoTenementDetails = function () {
    var model = tenantMngModel.instance();
    //console.log(model.addOrEdit);
    if (model.addOrEdit == 0) {
        tenantMngModel.instance().currentPage = 'tementListPage';
        //还原编辑下一步点击次数
        ldp.nextsteptime = 0;
    } else if (model.addOrEdit == 1) {
        model.selTenant = JSON.parse(JSON.stringify(model.copySelTenant));
        tenantMngModel.instance().currentPage = 'tenementDetails';
    }
};

//编辑租户
ldp.editTementshow = function () {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    model.contacts = [{
        contactName: "",
        contactMobile: ""
    }];
    model.meterformulalist = [];
    for (var i = 0; i < model.buildingArr2.length; i++) {
        var b_item = model.buildingArr2[i];
        b_item.roomArr = [];
        b_item.roomShowArr = [];
    }
    $("#parentBuilding").precover("请选择");
    $("#parentYetai").precover("请选择");
    $("#tementName").precover();
    $("#tementNo").precover();
    $("#tementName").precover();
    $("#contact").precover();
    $("#contact_tel").precover();
    for (var i = 0; i < $('.contact .per-input-basic').length; i++) {
        $('.contact .per-input-basic').eq(i).precover();
    }
    for (var i = 0; i < $('.contact_number .per-input-basic').length; i++) {
        $('.contact_number .per-input-basic').eq(i).precover();
    }
    //清空选择能耗的已选项
    var allCombox = $('.system_list_body .per-combobox-basic');
    for (var i = 0; i < allCombox.length; i++) {
        var _list = allCombox.eq(i);
        _list.precover('请选择');
        $('.system_list_head .per-switch-checkbox').eq(i).psel(false, true)
    }
    $('.checkPriceDetail').pdisable(true);
    model.copySelTenant = JSON.parse(JSON.stringify(model.selTenant));
    //kyan + 租户编号、已激活租户不可更改租户编号，所属建筑
    $('.input_comboxPart').find('.input_comboxPart').eq(0).pdisable(true);
    if(selTenant.tenantStatus == 1) {
        $('.input_comboxPart').find('.input_comboxPart').eq(1).pdisable(true);
    }else {
        $('.input_comboxPart').find('.input_comboxPart').eq(1).pdisable(false);
    }
    model.step = 1;
    model.addOrEdit = 1;
    model.subaddPage = 'step1';
    //所属建筑
    var addTenantSelBuilding;
    for (var i = 0; i < model.buildingArr2.length; i++) {
        var selBuilding = model.buildingArr2[i];
        selBuilding.roomArr = [];
        selBuilding.roomShowArr = [];
        if (selTenant.buildingId == selBuilding.id) {
            addTenantSelBuilding = selBuilding;
            $("#parentBuilding").psel(i);
        }
    }
    model.addTenantSelBuilding = addTenantSelBuilding;
    if (selTenant.tenantStatus == 1) { //编辑已激活
        ldp.editTementshow.anyncTool = new AsynTool();
        ldp.editTementshow.anyncTool.all("tenantCtrl.getRoomArr_" + addTenantSelBuilding.id, "tenantCtrl.getUpdateTenantDetail_" + selTenant.tenantId, function () {
            tenantCtrl.dealTenantRoom();
        });
        tenantCtrl.getRoomArr(addTenantSelBuilding);
    }
    tenantCtrl.getUpdateTenantDetail(selTenant);
    model.stepArr = selTenant.tenantStatus == 0 ? model.stepArr_3 : model.stepArr_2;

    model.currentPage = 'addTementPage';
};
ldp.editTementshow.anyncTool;


//下载pdf
ldp.downPdf = function (event) {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant; //当前租户
    var pdfPage = model.pdfPage;
    var fileName = "";
    var _energyType = selTenant.energyCostReport.energyTypeName; // '电';
    var _pdfPageName = '';
    var _timeType = 'yyyy-MM-dd';
    switch ($('#reportCalendar').psel().timeType) {
        case 'M':
            _timeType = 'yyyy-MM';
            break;
        case 'y':
            _timeType = 'yyyy';
            break;
    }
    var pdfTime = ($('#reportCalendar')[0] ? new Date($('#reportCalendar').psel().startTime).format(_timeType) : new Date().format('yyyy-MM-dd')) + '~' + ($('#reportCalendar')[0] ? new Date($('#reportCalendar').psel().endTime).format(_timeType) : new Date().format('yyyy-MM-dd'));
    var tenantName = selTenant.tenantName;
    //合适的文件名字
    switch (pdfPage) {
        case "energyCosttemp": //下载能耗费用报告
            _pdfPageName = '能耗费用报告';
            break;
        case "historybillingtemp": //下载历史账单
            _pdfPageName = '历史账单';
            break;
        case "energyCostcharge": //下载充值记录
            _pdfPageName = '充值记录';
            break;
        case "energyCostarrears": //下载欠费账单
            _pdfPageName = '欠费账单';
            break;
        case "returnPremiumRecord": // 下载退费记录
            _pdfPageName = '退费记录';
            break;
    }
    fileName = tenantName + '-' + _energyType + '-' + _pdfPageName + '-' + pdfTime;

    // 有配置文件的时候读取配置文件地址，没有配置文件取地址栏中的地址
    var _localhost = model.pdfIpConfig || window.location.origin + "/";
    var links = '<link rel="stylesheet" href="' + _localhost + 'css/tement/meter_detail.css"><link rel="stylesheet" href="' + _localhost + 'css/tement/pdfReport.css"><link rel="stylesheet" href="' + _localhost + 'css/tement/pdfCharge.css"><link rel="stylesheet" href="' + _localhost + 'css/tement/pdfArrears.css"><link rel="stylesheet" href="' + _localhost + 'css/tement/pdfReturn.css">';
    var _headText = '<!DOCTYPE html><html><head><meta charset="UTF-8">' + links + '<style>.noReport {display:none!important;}html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, tt, var, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, i, b, button {margin: 0;padding: 0;border: 0;font-weight: inherit;font-style: inherit;font-size: 100%;vertical-align: baseline;list-style: none;}.isdownPdf {display: block;}</style></head><body><div class="report_wrapper">';
    var _footText = '</div></body></html>';
    var pdfStr = _headText + $('#pdf_wrapper').html() + _footText;
    //console.log(pdfStr);
    tenantCtrl.downFileByHtmlContent(pdfStr, fileName, "pdf");
};

//打印记录20180531wp     
ldp.printHtml = function (selecter, type) {
    var _localhost = model.pdfIpConfig || window.location.origin + "/";
    var relink = '<link rel="stylesheet" href="' + _localhost + 'css/tement/download.css">';
    var links = '<link rel="stylesheet" href="' + _localhost + 'css/tement/meter_detail.css"><link rel="stylesheet" href="' + _localhost + 'css/tement/pdfReport.css"><link rel="stylesheet" href="' + _localhost + 'css/tement/pdfCharge.css"><link rel="stylesheet" href="' + _localhost + 'css/tement/pdfArrears.css"><link rel="stylesheet" href="' + _localhost + 'css/tement/pdfReturn.css">';
    var elink = '<link rel="stylesheet" href="' + _localhost + 'css/tement/downloadExcel.css">';
    var slink = '<link rel="stylesheet" href="' + _localhost + 'css/tement/downloadExcelSoft.css">';
    var link;
    switch (type) {
        case 1:
            link = relink; //充值&退费样式
            break;
        case 2:
            link = elink; //充值批量打印excel软充表扣
            break;
        case 3:
            link = slink; //充值批量打印excel软充软扣
            break;
        default:
            link = links; //各种记录表格样式
            break;
    }
    var _headText = '<!DOCTYPE html><html><head><meta charset="UTF-8">' + link + '<style>.noReport {display:none!important;}html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, tt, var, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, i, b, button {margin: 0;padding: 0;border: 0;font-weight: inherit;font-style: inherit;font-size: 100%;vertical-align: baseline;list-style: none;}.isdownPdf {display: block;}</style></head><body><div class="report_wrapper" style="width:1070px;">';
    var _footText = '</div></body></html>';

    var printhtml = _headText + $(selecter).html() + _footText;
    var f = document.getElementById('printf');
    f.contentDocument.write(printhtml);
    f.contentDocument.close();
    $("#partLoadingCom").pshow();

    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1; //判断是否IE<11浏览器
    var isEdge = userAgent.indexOf("Edge") > -1 && !isIE; //判断是否IE的Edge浏览器
    var isIE11 = userAgent.indexOf('Trident') > -1 && userAgent.indexOf("rv:11.0") > -1;
    window.frames['printf'].focus();
    setTimeout(function () {
        if (isIE | isEdge | isIE11) {
            window.frames['printf'].focus(); //ie11
            print();
        } else {
            f.contentWindow.print(); //chrome
        }
        $("#partLoadingCom").phide();
    }, 1500);

}

//各种下载页面的返回
ldp.downPageGoback = function () {
    tenantMngModel.data.currentPage = 'tenementDetails';
};

//直接返回租户列表页面
ldp.backToTemenListPage = function () {
    var model = tenantMngModel.instance();
    /* 20181022 wp+ */
    var index = window.location.href.indexOf('&tid')
    if (index > 0) {
        var arr = window.location.href.split('&');
        var tenantId = decodeURI(arr[arr.length - 2].split('=')[1]);
        var buildingId = decodeURI(arr[arr.length - 1].split('=')[1]);
        var newItem = new tenantVM();
        newItem.tenantId = tenantId;
        newItem.buildingId = buildingId;
        window.location.href = window.location.href.replace(/Tenant/, "PropertyMonitor");
        return;
    }
    /* end */
    if (model.isRefresh) {
        tenantCtrl.precoverGrid(); //还原表格并重新获取数据;
        model.isRefresh = false;
    }
    model.currentPage = 'tementListPage';
}

// 能耗费用报表不同情况返回
ldp.energyCosttempback = function () {
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    if (model.energyCosttempId == 0) { //租户列表
        ldp.backToTemenListPage();
    } else if (model.energyCosttempId == 1) { //租户详情
        model.currentPage = 'tenementDetails';
    } else if (model.energyCosttempId == 2) { //添加租户
        model.currentPage = 'addTementPage';
    } else if (model.energyCosttempId == 3) { //缴费记录
        model.currentPage = 'pay';
    } else if (model.energyCosttempId == 4) { //结算
        // model.currentPage = 'settleAccounts';
    } else if (model.energyCosttempId == 5) {
        $('#instrument_set').css("display", "block");
        model.currentPage = 'tenementDetails';
    }else if(model.energyCosttempId == 6){
        model.currentPage = 'tementListPage';
    }
};

//时间更新表格
ldp.updataPdf = function () {
    var model = tenantMngModel.instance();
    switch (model.pdfPage) {
        case 'energyCostcharge': //充值记录
            tenantCtrl.getRechargeRecord();
            break;
        case "historybillingtemp": //历史账单
            tenantCtrl.getHistoryBillArr();
            break;
        case "energyCostarrears": //欠费账单
            tenantCtrl.getNoPayBillArr();
            break;
        case "returnPremiumRecord": // 退费记录
            tenantCtrl.getReturnPremiumRecord();
            break;
    }
    $('#reportCalendar ._combobox_bottom ').hide();
};

ldp.hideTimeCalendar = function () {
    $('#reportCalendar ._combobox_bottom ').hide();
};

ldp.downExcTimeType = function (str) {
    switch (str) {
        case 'd':
            return 2;
        case 'w':
            return 3;
        case 'M':
            return 4;
        case 'y':
            return 5;
    }
};

ldp.switchClick = function () {
    // $("#switch1").psel(true)
    var model = tenantMngModel.instance();
    var selTenant = model.selTenant;
    var state = $("#switch1").psel();
    pajax.post({
        url: "FNTRemoteRechargeStatusSetService",
        data: {
            buildingId: selTenant.buildingId,
            tenantId: selTenant.tenantId,
            remoteRechargeStatus: +state
        },
        success: function (res) {
            common.msgSuccess("设置状态成功");
        },
        error: function () {
            $("#switch1").psel(!state, false);
            common.msgFailure("设置状态失败");
        }
    })

}

//添加，编辑租户 面积保留两位小数
ldp.tementArea = function(){
    let area = $("#tementArea").pval();
    !!area?$("#tementArea").pval(Number(area).toFixed(2)):area;
}