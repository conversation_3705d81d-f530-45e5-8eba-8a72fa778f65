/*事件注册*/
$(function () {
    logger.init();

    $(document).on("click", function () {
        var tg = event.target;
        var index = -1;
        if (!$(tg).hasClass('shijiankongjian') && !$(tg).parents('.shijiankongjian').length && !$(tg).parents('#option0').length) {
            $('.shijiankongjian').hide();
            model.xuanzhongindex = -1;
        } else {
            index = 0;
        }
        if (!$(tg).hasClass('seachjianzhu') && !$(tg).parents('.seachjianzhu').length /* && !$(tg).parents('.seachjianzhu')[0]*/ && !$(tg).parents('#option1').length) {
            $('.seachjianzhu').hide();
            model.xuanzhongindex = -1;
        } else {
            index = 1;
        }
        if (!$(tg).hasClass('daixialasearch') && !$(tg).parents('.daixialasearch').length /* && !$(tg).parents('.daixialasearch')[0]*/ && !$(tg).parents('#option2').length) {
            $('.daixialasearch').hide();
            model.xuanzhongindex = -1;
        } else {
            index = 2;
        }
        if (!$(tg).hasClass('seachzhilu') && !$(tg).parents('.seachzhilu').length /* && !$(tg).parents('.seachzhilu')[0]*/ && !$(tg).parents('#option3').length) {
            $('.seachzhilu').hide();
            model.xuanzhongindex = -1;
        } else {
            index = 3;
        }
        if (index !== -1) model.xuanzhongindex = index;
        if (!$(tg).hasClass('templzhiyuanshu') && !$(tg).parents('.templzhiyuanshu').length) {
            bianjishanchuyc();
        }
    });
    // 点击sheet页添加对应状态
    $(".sheetPageUl li").on("click", function () {
        $(".sheetPageUl li").removeClass("show");
        $(this).addClass("show");
    })
    // 计算模板里的元素居中对齐
    var boxWidth = $(document.body).css("width");
    var tempNum = parseInt(parseInt(boxWidth) / 340);
    var templateWidth = (tempNum - 1) * 370;
    $("#templaterongqi_box").css("width", templateWidth + "px");

    //报表打印首页
    //点击模板管理跳转到模板管理页
    $(".reportlp2").click(function () {
        $("#report_printing").hide();
        $("#template_manage").show();
    });
    //点击报表时间选择项；
    $(".reportlcon").on("click", '.articdiv1acont', function (event) {
        event.stopPropagation();
        var srollTop = $(".srolltop").scrollTop();
        var id = $(this).attr('id');
        var index = $(this).index();
        if (id == 'option0') {
            if (model.startTime) $("#divaa").psetTime(model.startTime, model.endTime);
            if ($(".shijiankongjian").is(':visible')) {

            }
            $(".shijiankongjian").toggle();
            if ($(".shijiankongjian").is(':visible')) $('.calendar-list').hide();
            var artictop = $(this)[0].offsetTop + 50 - srollTop;
            $(".shijiankongjian").css("top", artictop);
            setCheckedOption(".shijiankongjian", index);
        };
        //点击建筑；
        if (id == 'option1') { //index === '1'
            $(".seachjianzhu").toggle();
            $('.seachjianzhu [p-type="tree-combobox"]').show();
            var artictop = $(this)[0].offsetTop + 50 - srollTop;
            var windowHeight = $(document.body).height();
            if (windowHeight > 720) {
                $(".seachjianzhu").css("top", artictop);
            } else {
                $(".seachjianzhu").css("top", "210px");
            }
            $('.seachjianzhu').pctlRecover();
            $('.seachjianzhu').psel(model.xuanzhongBUildid[0], false);
            setCheckedOption(".seachjianzhu", index);
        };
        // 点击分项；
        if (id == 'option2') {
            model.toSubitemTree = JSON.parse(JSON.stringify(model.curSubitemTree));
            model.toAddSubitemNames = JSON.parse(JSON.stringify(model.xuanzhongfenxuang));
            model.toAddSubitemNames1 = JSON.parse(JSON.stringify(model.xuanzhongfenxuang1));
            model.toAddSubitemIds = JSON.parse(JSON.stringify(model.xuanzhongfenxuangid));
            $('.xialasearch').pctlRecover(false);
            $('.xialasearch .float-button').show();
            $('#subitemCombo').psel(model.curSubitemTree.name, false);
            model.toSubitemTree = JSON.parse(JSON.stringify(model.curSubitemTree));
            if ($('.daixialasearch').is(':hidden')) {
                $('.xialaliebiao .combobox-con').hide();
                if (model.getItemList.length) {
                    controller.getItemTree(true);
                } else {
                    window['searchItemTree'] = [];
                    model.getItemTree = [];
                    model.toSubitemTree = {};
                    model.getItemTree = [];
                    toggelSelSubitem();
                }
            } else {
                $('.daixialasearch').hide();
                $('.xialaliebiao .combobox-con').hide();
            }
        };
        //点击支路
        if (id == 'option3') {
            model.toAddBranchNames = JSON.parse(JSON.stringify(model.xuanzhongzhilu));
            model.toAddBranchIds = JSON.parse(JSON.stringify(model.xuanzhongyibiaoid));
            model.toAddBranchIds2 = JSON.parse(JSON.stringify(model.branchid));

            $(".seachzhilu").toggle();
            var artictop = $(this)[0].offsetTop + 50 - srollTop;
            var windowHeight = $(document.body).height();
            var _boxTop = $("#option3").offset().top;
            if ((windowHeight - _boxTop) > 456) {
                $(".seachzhilu").css("top", artictop);
            } else {
                $('.seachzhilu').removeAttr("style");
                $(".seachzhilu").css("bottom", 0);
            }
            $('.seachzhilu').pctlRecover(false);
            $('.seachzhilu .float-button').show();
            model.getBranchTree = JSON.parse(JSON.stringify(model.getBranchTree));
            setTimeout(function () {
                for (var i = 0; i < model.branchid.length; i++) {
                    $('.seachzhilu').psel(model.branchid[i], false)
                }
            }, 0);
            setCheckedOption(".seachzhilu", index);
        };
    });
    //点击时间控件里的确定按钮选定时间且时间控件消失；
    $(".shijianquedingan").click(function (event) {
        event.stopPropagation();
        var timeObj = $('#divaa').pgetTime();
        model.startTime = timeObj.startStr.replace(/\//g, '-');
        model.endTime = timeObj.endStr.replace(/\//g, '-');
        model.realEndTime = timeObj.realEndStr.replace(/\//g, '-');
        model.xuanzhongshij = [];
        var startStr = $('#divaa').pgetTime().startStr.split(" ")[0].split("/");
        var startzhou = $("#divaa .day-box").eq(0).find("i").text();
        var endzhou = $("#divaa .day-box").eq(1).find("i").text();
        var endStr = $('#divaa').pgetTime().endStr.split(" ")[0].split("/");
        var timetype = $('#divaa').pgetTime().timeType;
        $(".articdiv1acont").eq(0).find(".shijianxzriqi").show();
        if (model.currSelTemplate.includeBuild === true) {} else {
            var include = model.currSelTemplate.include;
            if (include.indexOf("2") !== -1) {
                //$("#option3").removeClass("cdisable");
            }
        }
        $(".shijiankongjian").hide();
        switch (timetype) {
            case 'y':
                var stry = startStr[0] + '年';
                model.xuanzhongshij.push(stry);
                break;
            case 'yy':
                var stryy = startStr[0] + '年-' + endStr[0] + '年';
                model.xuanzhongshij.push(stryy);
                break;
            case 'M':
                var strM = startStr[0] + '年' + startStr[1] + '月';
                model.xuanzhongshij.push(strM);
                break;
            case 'MM':
                var strMM = startStr[0] + '年' + startStr[1] + '月-' + endStr[0] + '年' + endStr[1] + '月';
                model.xuanzhongshij.push(strMM);
                break;
            case 'w':
                var strw = startStr[0] + '年' + startStr[1] + '月' + startzhou + '周';
                model.xuanzhongshij.push(strw);
                break;
            case 'ww':
                var strww = startStr[0] + '年' + startStr[1] + '月' + startzhou + '周-' + endStr[0] + '年' + endStr[1] + '月' + endzhou + '周';
                model.xuanzhongshij.push(strww);
                break;
            case 'd':
                var strd = startStr[0] + '年' + startStr[1] + '月' + startStr[2] + '日';
                model.xuanzhongshij.push(strd);
                break;
            case 'dd':
                var strdd = startStr[0] + '年' + startStr[1] + '月' + startStr[2] + '日-' + endStr[0] + '年' + endStr[1] + '月' + endStr[2] + '日';
                model.xuanzhongshij.push(strdd);
                break;

        }
        model.xuanzhongindex = -1;
    });
    //点击选择建筑中树形菜单中的确认按钮时树形菜单消失；选中选中的建筑；
    $(".seachjianzhu").on("click", '[p-type="button-backBlueBorder"]', function (event) {
        event.stopPropagation();
        model.curSubitemTree = JSON.parse(JSON.stringify(model.toSubitemTree));
        model.xuanzhongBUild = [];
        model.xuanzhongBUildid = [];
        //$(".articdiv1acont").eq(2).removeClass("cdisable");
        var a = $(".seachjianzhu");
        var b = model.xuanzhongBUild;
        var c = model.xuanzhongBUildid
        querenaniu(a, b, c);
        var include = model.currSelTemplate.include;
        if (include.indexOf("1") !== -1) {
            var data = {
                buildingId: model.xuanzhongBUildid[0],
                tempId: model.currSelTemplate.id
            }
            controller.getItemList(data);
            //controller.getItemTree(data);
        } else {
            if (include.indexOf("2") !== -1) {
                //$("#option3").removeClass("cdisable");
            }
        }
        model.xuanzhongindex = -1;
    });

    //点击分项里的搜索确定按钮；分项的树形菜单消失；
    $(".daixialasearch").on("click", '[p-type="button-backBlueBorder"]', function (event) {
        event.stopPropagation();
        model.curSubitemTree = JSON.parse(JSON.stringify(model.toSubitemTree));
        $('.daixialasearch [p-type="tree-combobox"]').show();


        model.xuanzhongfenxuang = JSON.parse(JSON.stringify(model.toAddSubitemNames));
        model.xuanzhongfenxuang1 = JSON.parse(JSON.stringify(model.toAddSubitemNames1));
        model.xuanzhongfenxuangid = JSON.parse(JSON.stringify(model.toAddSubitemIds));
        var a = $(".xialasearch");
        if (a.parent().hasClass("daixialasearch")) {
            a.parent().hide();
        } else {
            a.hide();
        }
        model.xuanzhongindex = -1;
    });
    //点击支路里的确认按钮;
    $(".seachzhilu").on("click", '[p-type="button-backBlueBorder"]', function (event) {
        event.stopPropagation();
        $('.seachzhilu [p-type="tree-combobox"]').show();
        model.xuanzhongzhilu = JSON.parse(JSON.stringify(model.toAddBranchNames));
        model.xuanzhongyibiaoid = JSON.parse(JSON.stringify(model.toAddBranchIds));
        model.branchid = JSON.parse(JSON.stringify(model.toAddBranchIds2));
        var a = $(".seachzhilu");
        if (a.parent().hasClass("daixialasearch")) {
            a.parent().hide();
        } else {
            a.hide();
        }
        model.xuanzhongindex = -1;
    });
    //reportr
    //点击tab最右边的下拉出现内容；
    $(".xialatabanniu").click(function (event) {
        event.stopPropagation();
        if ($(".mobanxialakuang").is(":visible")) {
            $(".mobanxialakuang").hide()
        } else {
            $(".mobanxialakuang").show()
        }
    });
    //点击其他区域下拉框隐藏
    $(document).click(function (event) {
        event.stopPropagation();
        var tg = event.target;
        if (!$(tg).hasClass("mobanxialakuang") && !$(tg).hasClass("xialatabanniu")) {
            $(".mobanxialakuang").hide();
        }
    })
    //点击模板管理页的返回按钮返回到首页
    $(".returnreport").click(function () {
        controller.getTempList();
        var index = -1;
        for (var i = 0; i < model.getTempList.length; i++) {
            var item = model.getTempList[i];
            if (item.id === model.tempid) {
                index = i;
                model.currSelTemplate = item;
                model.temname = item.name;
            }
        }
        if (index !== -1) {
            $(".reportlul li").hide();
            $(".reportlul li").eq(index).show();
            $(".guanbi").css("display", "inline-block");
        } else { 
            //删除报表打印页选中的模板
            modelMethod.feihumoban();
        }
        $(".templzhiyuanshu").find(".rihuozhoup").removeClass("xianzhongtemp");
        bianjishanchuyc();
        $("#template_manage").hide();
        $("#report_printing").show();
    });

    //滑过模板的时候编辑删除按钮显示；
    $(".templaterongqi").on("mouseenter", ".templzhiyuanshu", function () {
        if ($(this).find(".tempbianjibc").is(":visible")) {
            return
        } else {
            $(".rihuozhoup").removeClass("xianzhongtemp");
            $(this).find(".rihuozhoup").addClass("xianzhongtemp");
            $(this).find(".tempbianji").show();
        }
    });
    //离开的时候编辑删除按钮影藏
    $(".templaterongqi").on("mouseleave", ".templzhiyuanshu", function () {
        $(this).find(".tempbianji").hide();
        $(".rihuozhoup").removeClass("xianzhongtemp");
    });
    //点击模板其他区域编辑删除隐藏
    //$(document).on("click",function(event){
    //    event.stopPropagation();
    //    var e = event.target;
    //    console.log($(e));
    //        bianjishanchuyc();
    //
    //})
    //点击模板里的编辑的时候；
    $(".templaterongqi").on("click", ".templbianji", function (event) {
        event.stopPropagation();
        var jqTempbaocun = $(this).parents('.templzhiyuanshu').find('.tempbaocun');
        if (jqTempbaocun.hasClass('disabled')) {
            jqTempbaocun.removeClass('disabled');
        }
        var bianjineirong = $(this).parent().parent().prev().find(".bianjineirong");
        model.editTempId = bianjineirong.attr('id');
        var texts = bianjineirong.val();
        var texts = bianjineirong.attr("texts", texts);
        $(".templzhiyuanshu").find(".rihuozhoup").removeClass("xianzhongtemp");
        $(this).parent().parent().parent().find(".rihuozhoup").addClass("xianzhongtemp");
        // bianjishanchuyc();
        $(this).parent().parent().prev().find(".bianjineirong").removeAttr("readonly");
        $(this).parent().parent().prev().find(".bianjineirong").css("border", "1px solid #e9eef0");
        $(this).parents('.templzhiyuanshu').find('.mingchengchuts').hide();
        $(this).parents('.templzhiyuanshu').find('.mingchengempty').hide();

        $(this).parent().parent().hide();
        $(this).parent().parent().next().show();
        // console.log($(this).parent().parent().parent())
        $(this).parent().parent().parent().find(".bianjineirong").css("border-color", "#02aad1");
        $(this).parent().parent().parent().find(".bianjineirong").css("height", "70px");
        $(this).parent().parent().parent().find(".tempbianjibc").css("border-top", "1px solid #fff");
    });
    //当编辑框里获取焦点后边框变蓝；
    // $(".templaterongqi").on("focus", ".bianjineirong", function () {
    //     $(this).css("border-color", "#02aad1");
    // });
    //阻止编辑框内的默认事件
    $(".templaterongqi").on("click", ".bianjineirong", function (event) {
        if (!$(this).attr("readonly")) {
            event.stopPropagation();
        }
    });
    //点保存的时候名称框变成不可编辑且判断是否重名；
    /*$(".templaterongqi").on("click", ".tempbaocun", function() {
     $(this).parent().parent().prev().find(".bianjineirong").attr("readonly", "true");
     $(this).parent().parent().prev().find(".bianjineirong").css("border", "none");
     $(".tempbianjip").show();
     $(".tempbaocunp").hide();
     })*/
    //点击删除按钮的时候出现删除弹窗；
    $(".templaterongqi").on("click", ".templshanchu", function (event) {
        event.stopPropagation();
        $(".templzhiyuanshu").find(".rihuozhoup").removeClass("xianzhongtemp");
        $(this).parent().parent().parent().find(".rihuozhoup").addClass("xianzhongtemp");
        bianjishanchuyc();
        $(this).parent().parent().next().next().show();
    });
    //点击删除弹窗的取消按钮弹窗消失；
    $(".templaterongqi").on("click", ".shamchudamcengqx", function () {
        $(this).parents(".shamchudamceng").hide();
    });
    //点击上传按钮出现上传弹窗；
    $(".scteplate").click(function () {
        $('.wangroutishi').hide();
        $('.mingchengtishi').hide();
        $('.nameEmpty').hide();
        $(".shangchuandccond2 [type='text']").css('border', '1px solid #cacaca');
        $(".shangchuandccond2 [type='text']").val('');
        $(".shangchuandccond1 p").text('选择文件');
        $('#mobanshangchuanfile').val('');
        $('.shangchuanqr').addClass('disabled');
        $(".shangchuandc").show();
    });
    //点击上传弹窗里的取消按钮上传弹窗消失；
    $(".shangcqx").click(function () {
        $(".shangchuandc").hide();
    });
    $(".shangchuandccond2 [type='text']").on('keyup', function () {
        var value = $(this).val();
        $(this).val(value.replace(/[\\\/\:\*\?\"\<\>\|\.\$]/ig, ''));
    });
    $(".shangchuandccond2 [type='text']").focus(function () {
        $('.mingchengtishi').hide();
        $('.nameEmpty').hide();
        $(this).css("border", "1px solid #02aad1");
    });
    $(".shangchuandccond2 [type='text']").blur(function () {
        $(this).css("border", "1px solid #cacaca");
    });
    //点击上传弹窗里的上传按钮。按钮出现上传中的状态；
    $(".shangchuanqr").click(function () {
        $('.wangroutishi').hide();
        $('.nameEmpty').hide();
        var filename = $(".shangchuandccond2 [type='text']").val();
        var fullFilename = $(".shangchuandccond1 p").text();
        if (!filename) {
            $('.nameEmpty').show();
            return;
        }
        var lens = model.getTempList.length;
        for (var x = 0; x < lens; x++) {
            var name1 = model.getTempList[x].name;
            if (filename === name1) {
                $('.mingchengtishi').show();
                $(".shangchuandccond2 [type='text']").css('border', '1px solid red');
                return;
            }
        }
        $(this).addClass("shangchuanzhong");
        controller.uploadTempExcel(filename,fullFilename);
    });

    $('.xialasearch input').on('keyup', function () {
        if ($(this).val()) {
            $('.xialasearch .float-button').hide();
        } else {
            $('.xialasearch .float-button').show();
        }
    });

    $('.seachzhilu input').on('keyup', function () {
        if ($(this).val()) {
            $('.seachzhilu .float-button').hide();
        } else {
            $('.seachzhilu .float-button').show();
        }
    });

});

function bianjishanchuyc() {
    var lengs = $(".templzhiyuanshu").length;
    for (var x = 0; x < lengs; x++) {
        var texts = $(".templzhiyuanshu").eq(x).find(".bianjineirong").attr("texts");
        if (texts) {
            model.getTempList[x].name = texts
        }
    }
    $(".templzhiyuanshu").find(".bianjineirong").attr("readonly", "true");
    $(".templzhiyuanshu").find(".bianjineirong").css("border", "none");
    $(".templzhiyuanshu").find(".tempbianjibc").hide();
    $(".templzhiyuanshu").find(".shamchudamceng").hide();
}


function adjust() {
    setTimeout(function () {
        var zw = $(document).width() - 40;
        var leng = $(".templzhiyuanshu").length;
        var aw = 0;
        for (var i = 0; i < leng; i++) {
            aw += $(".templzhiyuanshu").eq(i).width() + 29;
            if (aw > zw) {
                if (aw - zw < 28) {
                    var ww = aw - 28;
                    $(".templaterongqi").width(ww);
                    return
                } else {
                    ww = aw - $(".templzhiyuanshu").eq(i).width() - 29 - 28;
                    $(".templaterongqi").width(ww);
                    return
                }
            }
            if (i == leng - 1) {
                if (aw <= zw) {
                    $(".templaterongqi").width(aw);
                }
            }
        }
    }, 0)
};

//右边报表打印的tab；
function tanwidth() {
    var repotrwidth = $(".reportrtabd").width();
    var lilength = model.showReports.length;
    var strwidth = 0,
        k = 0;
    for (var x = 0; x < lilength; x++) {
        strwidth = 180 * (x + 1) + 2;
        if (strwidth > repotrwidth) {
            k = x;
            break;
        }
    }
    setTimeout(function () {
        if (k !== 0) {
            for (var x = 0; x < lilength; x++) {
                if (x < k) {
                    $(".reportrtab li").eq(x).show();
                } else {
                    $(".reportrtab li").eq(x).hide();
                }
            }
        } else {
            $(".reportrtab li").show();
        }
    }, 0)
};

function textchang(event) {
    $('.wangroutishi').hide();
    var _this = $(event.target);
    var value = _this.val();
    if (!value) return;
    var arrvaule = value.split("\\");
    var vaule = arrvaule[arrvaule.length - 1];
    var index = vaule.indexOf('.xls') !== -1 ? vaule.indexOf('.xls') : vaule.indexOf('.xlsx') !== -1 ? vaule.indexOf('.xlsx') : vaule.length;
    var vaule1 = vaule.slice(0, index);
    var files = event.target.files[0];
    $(".shangchuandccond2 [type='text']").val(vaule1);
    $(".shangchuandccond1 p").text(vaule);
    $(".shangchuanqr").data(files);
    model.file = files;
    $(".shangchuanqr").removeClass('disabled');
    $(".shangchuandccond1 p").attr("title", vaule);
};
//调用提示框方法
function showTip(status) {

};
//根据当前日期获取到现在时间为当月的第几周
function getMonthWeek(a) {
    /*
     a = d = 当前日期
     b = 6 - w = 当前周的还有几天过完(不算今天)
     a + b 的和在除以7 就是当天是当前月份的第几周
     */
    var date = new Date(a),
        w = date.getDay(),
        d = date.getDate();
    return Math.ceil((d + 6 - w) / 7);
};
//点击确认按钮的时候添加内容；
function querenaniu(a, b, c) {
    if (a.parent().hasClass("daixialasearch")) {
        a.parent().hide();
    } else {
        a.hide();
    }
    var len = a.find(".pitch").length;
    for (var x = 0; x < len; x++) {
        if (!a.find(".pitch").eq(x).parent().parent().parent().prev().hasClass("pitch")) {
            var text = a.find(".pitch").eq(x).find("b").text();
            var id = a.find(".pitch").eq(x).attr("nid");
            b.push(text);
            c.push(id)
        }

    }
};

//划过出现省略号
function ellipsis(e) {
    var target = e.target;
    var containerLength = $(target).width();
    var textLength = target.scrollWidth;
    var paddingL = parseInt($(target).css('padding-left'));
    var paddingR = parseInt($(target).css('padding-right'));
    var text = $(target).text().replace(/\s/g, ''); //ȥ�����пո�
    if (textLength > (containerLength + paddingL + paddingR)) {
        $(target).attr('title', text);
    }
};

$(function () {
    $("body").on("mouseenter", ".slh", ellipsis);
});

function selBuild(argument1, event) {
    event.stopPropagation();
    var item = arguments[0].model; //argument1.model
    setBuildAndGetSubitem(item, 'name');

};

function filterSelBuild(event) {
    event.stopPropagation();
    var item = event.pEventAttr.arr[event.pEventAttr.index];
    setBuildAndGetSubitem(item, 'name');
};

function setBuildAndGetSubitem(item, attrName) {
    $('.seachjianzhu').hide();
    if (item.id === model.xuanzhongBUildid[0]) return;
    model.xuanzhongBUild = [item[attrName]];
    model.xuanzhongBUildid = [item.id];
    var include = model.currSelTemplate.include;
    if (include.indexOf("1") !== -1) {
        controller.getItemList();
    }
    model.xuanzhongindex = -1;
}

function selSubitem(argument1, event) {
    event.stopPropagation();
    var item = arguments[0].model;
    var checked = $('div [nid=' + item.id + ']').hasClass("pitch");
    if (checked) {
        for (var i = 0; i < model.toAddSubitemIds.length; i++) {
            if (model.toAddSubitemIds[i] === item.id) {
                model.toAddSubitemIds.splice(i, 1);
                model.toAddSubitemNames.splice(i, 1);
                model.toAddSubitemNames1.splice(i, 1);
                break;
            }
        }
    } else {
        model.toAddSubitemIds.unshift(item.id);
        model.toAddSubitemNames.unshift(item.name);
        model.toAddSubitemNames1.unshift(model.toSubitemTree.name + '-' + item.name);
    }
};

function filterSelSubitem(event) {
    shuxingshousuo(event, 3);
    var item = event.pEventAttr.arr[event.pEventAttr.index];
    var check = false;
    for (var i = 0; i < model.xuanzhongfenxuangid.length; i++) {
        if (model.xuanzhongfenxuangid[i] === item.id) {
            check = true;
            break;
        }
    }
    if (!check) { //如果原item没有被选中，则进行选中处理
        model.xuanzhongfenxuangid.unshift(item.id);
        model.xuanzhongfenxuang.unshift(item.name);
        model.xuanzhongfenxuang1.unshift(model.toSubitemTree.name + '-' + item.name);
    }

    var a = $(".xialasearch");
    if (a.parent().hasClass("daixialasearch")) {
        a.parent().hide();
    } else {
        a.hide();
    }
    model.xuanzhongindex = -1;
};

function selBranch(argument1, event) {
    event.stopPropagation();
    var item = arguments[0].model;
    var checked = $('div [nid=' + item.id + ']').hasClass("pitch");
    if (checked) {
        for (var i = 0; i < model.toAddBranchIds2.length; i++) {
            if (model.toAddBranchIds2[i] === item.id) {
                model.toAddBranchIds.splice(i, 1);
                model.toAddBranchIds2.splice(i, 1);
                model.toAddBranchNames.splice(i, 1);
                break;
            }
        }
    } else {
        model.toAddBranchIds.unshift(item.meterId);
        model.toAddBranchIds2.unshift(item.id);
        model.toAddBranchNames.unshift(item.name);
    }
};

function filterSelBranch(event) {
    shuxingshousuo(event, 4);
    var item = event.pEventAttr.arr[event.pEventAttr.index];
    var check = false;
    for (var i = 0; i < model.branchid.length; i++) {
        if (model.branchid[i] === item.id) {
            check = true;
            break;
        }
    }
    if (!check) {
        model.xuanzhongyibiaoid.unshift(item.meterId);
        model.branchid.unshift(item.id);
        model.xuanzhongzhilu.unshift(item.name);
    }

    var a = $(".seachzhilu");
    if (a.parent().hasClass("daixialasearch")) {
        a.parent().hide();
    } else {
        a.hide();
    }
    model.xuanzhongindex = -1;
};

function addyibiaoid(item, event) {
    shuxingjieguoqx(event);
};

function seladdyibiao(event) {
    shuxingshousuo(event, 5);
    var a = $(".seachyibiao");
    model.xuanzhongyibiao = [];
    model.xuanzhongyibiaoid = [];
    var b = model.xuanzhongyibiao;
    var c = model.xuanzhongyibiaoid;
    querenaniu(a, b, c);
};

//树形结构的全选
function shuxingjieguoqx(event) {
    var siblings = $(event.currentTarget).parent().parent().siblings();
    var lengs = siblings.length;
    if (!$(event.currentTarget).hasClass('pitch')) {
        $(event.currentTarget).next().find(".temp-title").addClass("pitch");
        for (var x = 0; x < lengs; x++) {
            if (!siblings.eq(x).find(".temp-title").hasClass("pitch")) {
                break;
            }
            if (x == lengs - 1) {
                $(event.currentTarget).parent().parent().parent().prev().addClass("pitch");
            }
        }
    } else {
        $(event.currentTarget).next().find(".temp-title").removeClass("pitch");
    }
    //取消全选
    if ($(event.currentTarget).hasClass('pitch')) {
        $(event.currentTarget).parents(".temp-con").prev().removeClass("pitch");
    }
};

//树形结构搜索点击事件
function shuxingshousuo(event, n) {
    if (n == 2) {
        demos = $(".seachjianzhu .temp-title");
    }
    if (n == 3) {
        demos = $(".xialasearch .temp-title");
    }
    if (n == 4) {
        demos = $(".seachzhilu .temp-title");
    }
    var index = event.pEventAttr.index;
    var attrName = n != 4 ? 'id' : /*'meterId'*/ 'id';
    var id = event.pEventAttr.arr[index][attrName];
    var lenths = demos.length;
    for (var x = 0; x < lenths; x++) {
        var ids = demos.eq(x).attr("nid");
        if (ids == id) {
            demos.eq(x).addClass("pitch");
            demos.eq(x).next().find(".temp-title").addClass("pitch");
            if (n < 5) {
                //$(".articdiv1acont").eq(n).removeClass("cdisable");
            }

            return
        }
    }
};

//点击报表的选择项的时候有内容的时候对应的右边勾选
function duiyingguoxuan(n) {
    if (n == 2) {
        narr = model.xuanzhongBUildid;
        demos = $(".seachjianzhu .temp-title");
    }
    if (n == 3) {
        narr = model.xuanzhongfenxuangid;
        demos = $(".xialasearch .temp-title");
    }
    if (n == 4) {
        //narr = model.xuanzhongzhiluid;
        narr = model.xuanzhongyibiaoid;
        demos = $(".seachzhilu .temp-title");
    }
    /*
     if (n == 5) {
     narr = model.xuanzhongyibiaoid;
     demos = $(".seachyibiao .temp-title");
     }
     */
    var lengs = narr.length;
    var lengs1 = demos.length;
    for (var x = 0; x < lengs; x++) {
        var id = narr[x];
        for (var y = 0; y < lengs1; y++) {
            var ids = demos.eq(y).attr("nid");
            if (ids == id) {
                demos.eq(y).addClass("pitch");
                demos.eq(y).next().find(".temp-title").addClass("pitch");
            }
        }
    }
}

function stopProp(event) {
    event.stopPropagation();
}

function setFullName(builds, parentName) {
    for (var i = 0; i < builds.length; i++) {
        var item = builds[i];
        item.name0 = item.name;
        if (item.parentId !== '-1') {
            item.name += '-' + parentName;
        }
        if (item.child.length) {
            setFullName(item.child, item.name);
        }
    }
}

function deleteOption(event) {
    event.stopPropagation();
    $('.danchukuang').hide();
    var _this = event.currentTarget;
    var parents = $(_this).parents(".articdiv1acont");
    var parindex = parents.index();
    var index = $(_this).parent().parent().index();
    if (model.duoxiangxianzhe.indexOf(1) === -1 && model.duoxiangxianzhe.indexOf(2) !== -1 && parindex === 2) {
        parindex = 3;
    }
    switch (parindex) {
        case 0:
            model.xuanzhongshij.splice(index, 1);
            break;
        case 1:
            model.xuanzhongBUild.splice(index, 1);
            model.xuanzhongBUildid.splice(index, 1);
            model.xuanzhongfenxuang = [];
            model.xuanzhongfenxuang1 = [];
            model.xuanzhongfenxuangid = [];
            break;
        case 2:
            model.xuanzhongfenxuang.splice(index, 1);
            model.xuanzhongfenxuang1.splice(index, 1);
            model.xuanzhongfenxuangid.splice(index, 1);
            break;
        case 3:
            model.xuanzhongzhilu.splice(index, 1);
            model.xuanzhongyibiaoid.splice(index, 1);
            model.branchid.splice(index, 1);
            break;
    }
}

function setCheckedOption(className, index) {
    model.xuanzhongindex = $(className).is(':visible') ? index : -1;
    var classNames = ['.shijiankongjian', '.seachjianzhu', '.daixialasearch', '.seachzhilu'];
    for (var i = 0; i < classNames.length; i++) {
        if (className !== classNames[i]) {
            $(classNames[i]).hide();
        }
    }
}

function toggelSelSubitem() {
    setTimeout(function () {
        for (var i = 0; i < model.xuanzhongfenxuangid.length; i++) {
            $('.xialasearch').psel(model.xuanzhongfenxuangid[i], false)
        }
        var srollTop = $(".srolltop").scrollTop();
        $(".daixialasearch").toggle();
        var artictop = $('#option2')[0].offsetTop + 50 - srollTop;
        var bodyHeight = $(document.body).height();
        var leftBarHeight = $(".reportlcon").height() + 100;
        if (bodyHeight - 530 > leftBarHeight) {
            $(".daixialasearch").css("top", leftBarHeight - 100);
        } else {
            var _height = bodyHeight - 50 - 530;
            $(".daixialasearch").css("top", _height);
        }
        $(".daixialasearch").css("top", _height);
        setCheckedOption(".daixialasearch", 2);
    }, 0);
}

function guid() {
    function S4() {
        return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }

    return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
}

function cancelSelSheets() {
    for (var i = 0; i < model.curReport.sheets.length; i++) {
        model.curReport.sheets[i].selected = false;
    }
}