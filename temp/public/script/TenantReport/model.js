/*页面model*/
var model = {
    currSelTemplate: {}, //当前选择的模板
    getTempList: [], //模板数据列表
    getBuildList: [], //建筑列表数据
    temname: '', //模板名称
    tempid: '', //判断选择的是那个模板
    format: '', //时间类型
    mobanshifouxian: 0, //判断是否选了模板；
    xuanzhongindex: -1, //选择的是模板下的哪一个
    xuanzhongshij: [], //选择的时间
    xuanzhongBUild: [], //选中的建筑
    xuanzhongBUildid: [], //选中的建筑id
    xuanzhongfenxuang: [], //选择的分项，每项为:'排污泵'
    xuanzhongfenxuang1: [], //选择的分项，每项为:'分项树A-排污泵'
    xuanzhongfenxuangid: [], //选择分项id
    xuanzhongzhilu: [], //选择的支路
    xuanzhongzhiluid: [], //选中的支路id
    branchid: [], //
    xuanzhongyibiao: [], //选择的仪表
    xuanzhongyibiaoid: [], //选择的仪表id
    getItemList: [], //分项树列表
    getItemTree: [], //分项树菜单
    getBranchTree: [], //支路树菜单
    getMeterTree: [], //仪表树菜单
    shijianway: '', //时间选择方式；
    duoxiangxianzhe: '', //判断分项仪表支路那个显示；
    baobiaoxianshi: 0, //生成报表；
    tabbeixuanzhong: 0, //判断哪个tab被选中；
    tabarr: [],
    maptabarr: [],
    baobiaoneirong: 0,
    showReports: [], //页面展示的报表列表
    allReports: [], //页面生成的所有报表
    curReport: {}, //选中的报表
    toSubitemTree: {},
    curSubitemTree: {},
    startTime: '', //时间控件选择的开始时间
    endTime: '', //时间控件选择的结束时间
    realEndTime: '',
    toAddSubitemIds: [],
    toAddSubitemNames: [],
    toAddSubitemNames1: [],
    toAddBranchIds: [], //实际是仪表id
    toAddBranchNames: [],
    toAddBranchIds2: [], //支路id
    curSheets: [], //当前报表所有的sheet 项
    showSheets: [], //当前报表显示的sheet tab项
    //maxSheetCount: 0,       //当前页面最多可容纳的sheet tab个数
    selectedSheet: {
        html: ''
    }, //选中的sheet
    nameCount: {}, //报表名称（模板+时间）个数
    isIntCount: false, //sheet根据屏幕是否刚好显示整数个数
    isInit: false, //是否是默认状态
    deletedCreatingReports: [], //删除的生成中报表
    buttonShow: 0, //'模板管理'按钮显示
    tempnameCopy:'',//存取模板及选中时间字符
    buildId :'',//存取选中建筑id
};

var modelMethod = {
    selTemp: function (item, index) {
        if (item.id === this.tempid) return;
        model.startTime = null;
        model.currSelTemplate = item;
        this.temname = item.name;
        this.tempid = item.id;
        this.format = item.type;
        var data = {
            tempId: item.id
        }
        $(".reportlul li").hide();
        $(".reportlul li").eq(index).show();
        $(".guanbi").css("display", "inline-block");
        this.mobanshifouxian = 1;
        if (item.type == 'ss') {
            var now = new Date;
            var y = now.getFullYear();
            var m = now.getMonth();
            var d = now.getDate();
            now = y + '年' + (m + 1) + '月' + d + '日';
            this.xuanzhongshij = [now];
            this.startTime = y + '-' + (m + 1) + '-' + d;
        }
        this.duoxiangxianzhe = item.include;
        $("#divaa").pinit('time', 'dchart', {
            attr: {
                format: item.type,
                show: 'v' /*, lock: false*/
            }
        })

        if (model.currSelTemplate.includeBuild === true) {
            controller.getBuildList(data);
        }
        var include = model.currSelTemplate.include;
        if (include.indexOf("2") !== -1) {
            controller.getBranchTree(data);
        }
    },

    switchTemp: function (item, event) {
        event.stopPropagation();
        var targe = $(event.target);
        var id = item.id;
        var status;
        if (targe.attr("checked")) {
            targe.removeAttr('checked');
            status = 0;
        } else {
            targe.attr("checked", "checked");
            status = 1;
        }
        controller.forswitchTemp(id, status);
    },

    editTemp: function (item, event) {
        event.stopPropagation();
        var targe = $(event.target);
        if (!item.name) {
            targe.parents('.templzhiyuanshu').find('.mingchengempty').show();
            targe.addClass('disabled');
            targe.parent().parent().parent().find(".bianjineirong").css("border", "1px solid #ff7b7b");
            return;
        }
        var texts = targe.parent().parent().parent().find(".bianjineirong").attr("texts");
        var olditem = item;
        var item = JSON.parse(JSON.stringify(item));
        var name = item.name;
        var index = this.getTempList.indexOf(olditem);
        var lens = this.getTempList.length;
        for (var x = 0; x < lens; x++) {
            var name1 = this.getTempList[x].name;
            if (x !== index && name === name1) {
                targe.next().show();
                targe.addClass('disabled');
                targe.parent().parent().parent().find(".bianjineirong").css("border", "1px solid #ff7b7b");
                return;
            }
        }
        controller.updateTemp(item, targe, function () {
            targe.parent().parent().parent().find(".bianjineirong").attr("readonly", "true");
            targe.parent().parent().parent().find(".bianjineirong").css("border", "none");
            targe.next().hide();
            targe.parent().parent().parent().find(".bianjineirong").css("border", "rgb(233, 238, 240)");
            targe.parent().parent().parent().find(".bianjineirong").attr("texts", name);
            targe.parent().parent().hide();
        });
    },
    delTemp: function (item, index) {
        var item = JSON.parse(JSON.stringify(item));
        var _this = this;
        controller.delTemp(item, function () {
            _this.getTempList.splice(index, 1);
        });
    },
    feihumoban: function (event) {
        if (event) event.stopPropagation();
        $(".reportlul li").show();
        $(".guanbi").css("display", "none");
        $(".danchukuang").hide();
        model.temname = '';
        model.tempid = '';
        model.format = '';
        model.xuanzhongindex = '';
        model.duoxiangxianzhe = '';
        model.xuanzhongshij = [];
        model.xuanzhongBUild = [];
        model.xuanzhongBUildid = [];
        model.xuanzhongfenxuang = [];
        model.xuanzhongfenxuang1 = [];
        model.xuanzhongfenxuangid = [];
        model.xuanzhongzhilu = [];
        model.xuanzhongzhiluid = [];
        model.branchid = [];
        model.xuanzhongyibiao = [];
        model.xuanzhongyibiaoid = [];
        model.mobanshifouxian = 0;
    },
    createReport: function () {
        var tempname = this.temname + this.xuanzhongshij[0];
        var buildId = this.xuanzhongBUildid[0];
        if(tempname!==this.tempnameCopy || buildId !== this.buildId) {
            this.buildId = buildId;
            this.tempnameCopy = tempname;
            this.baobiaoxianshi = 1;
            var timeName;
            switch (this.format) {
                case 'y':
                case 'yy':
                    timeName = this.xuanzhongshij[0];
                    break;
                case 'M':
                case 'MM':
                    timeName = this.xuanzhongshij[0].replace(/[年]/g, '.');
                    timeName = timeName.replace(/[月]/g, '');
                    break;
                case 'w':
                case 'ww':
                    timeName = this.xuanzhongshij[0];
                    break;
                case 'd':
                case 'dd':
                case 'ss':
                    timeName = this.xuanzhongshij[0].replace(/[年月]/g, '.');
                    timeName = timeName.replace(/[日]/g, '');
                    break;
                default:
                    break;
            }
            var timeTempName = this.currSelTemplate.name + ' ' + timeName;
            var reportName = timeTempName;
            var arr = [];
            for (var i = 0; i < this.allReports.length; i++) {
                var item = this.allReports[i];
                if (item.timeTempName === timeTempName) {
                    arr.push(item);
                }
            }
            if (arr.length) {
                reportName += '(' + arr.length + ')';
            }
            /*
            var count = this.nameCount[timeTempName];
            count = count ? ++count : 1;
            this.nameCount[timeTempName] = count;
            if (count > 1) {
            reportName += '(' + (count - 1) + ')';
            }
            */
            var uuid = guid();
            this.allReports.unshift({
                tempId: this.tempid,
                reportName: reportName,
                load: true,
                timeTempName: timeTempName,
                uuid: uuid
            });
            this.showReports.unshift({
                tempId: this.tempid,
                reportName: reportName,
                load: true,
                timeTempName: timeTempName,
                uuid: uuid
            });
            this.tabarr.unshift(reportName);
            this.maptabarr.unshift(reportName);
            tanwidth();
            this.tabbeixuanzhong = 0;
            this.baobiaoneirong = 0;
            this.curReport = model.showReports[0];
            $('#reportContent').html(this.curReport.html);
            var data = {
                name: reportName,
                tempId: this.tempid,
                timetype: this.format,
                time: this.xuanzhongshij[0],
                build: this.xuanzhongBUildid,
                build: this.xuanzhongBUildid,
                item: this.xuanzhongfenxuangid,
                branch: this.xuanzhongzhiluid,
                meter: this.xuanzhongyibiaoid
            }
            this.curReport = {};
            this.selectedSheet = {html: ''};
            $('#reportContent').html(this.selectedSheet.html);
            this.curSheets = [];
            this.showSheets = [];
            this.selectedSheet = {};
            controller.getReport(data, uuid);
            $(".reportrtabd").css("border", "1px solid #fff")
        }else {
            $("#notice2-con").pshow('failure', '请勿重复点击');
        }
        
    },
    selReportCombo: function (item, index) {
        var index = index;
        var maxnum = parseInt($(".reportrtabd").width() / 182) - 1;
        var indexnum;
        if (index > maxnum) {
            if (this.maptabarr.indexOf(this.tabarr[index]) === -1 || this.maptabarr.length === this.tabarr.length) {
                indexnum = index - maxnum;
                this.maptabarr = this.tabarr.slice(indexnum, indexnum + maxnum + 1);
                this.showReports = this.allReports.slice(indexnum, indexnum + maxnum + 1);
                this.tabbeixuanzhong = maxnum;
                this.baobiaoneirong = index;
            } else {
                this.baobiaoneirong = index;
                var nindex = this.maptabarr.indexOf(this.tabarr[index]);
                this.tabbeixuanzhong = nindex;
            }
        } else {
            if (this.maptabarr.indexOf(this.tabarr[index]) === -1) {
                this.maptabarr = this.tabarr.slice(index, index + maxnum + 1);
                this.showReports = this.allReports.slice(index, index + maxnum + 1);
                this.tabbeixuanzhong = 0;
                this.baobiaoneirong = index;
            } else {
                var nnindex = this.maptabarr.indexOf(this.tabarr[index]);
                this.tabbeixuanzhong = nnindex;
                this.baobiaoneirong = index;
            }
        }
        this.curReport = this.allReports[index];
        if (this.curReport.sheets && this.curReport.sheets.length) {
            cancelSelSheets();
            this.curReport.sheets[0].selected = true;
            this.selectedSheet = this.curReport.sheets[0];
        } else {
            this.selectedSheet = {
                html: ''
            };
        }
        $('#reportContent').html(this.selectedSheet.html);
        if (this.curReport.sheets) {
            this.curSheets = JSON.parse(JSON.stringify(this.curReport.sheets));
            this.showSheets = JSON.parse(JSON.stringify(this.curReport.sheets));
        } else {
            this.curSheets = {};
            this.showSheets = {};
        }

        setTimeout(function () {
            var sheetUlWidth = $(".sheetPageUl").width();
            var maxCount = Math.ceil(sheetUlWidth / 130);
            var count = model.curSheets.length;
            if (count > maxCount) {
                model.showSheets = model.curSheets.slice(0, maxCount);
            }
        }, 0);

        $(".mobanxialakuang").hide();
        tanwidth();
    },
    deletetab: function (reportName, index, item) {
        if (item.load) {
            item.deleted = true;
            model.deletedCreatingReports.push(item);
        }
        var deleteCurReport = reportName === this.curReport.reportName;
        this.showReports.splice(index, 1);
        this.maptabarr.splice(index, 1);
        var _index = this.tabarr.indexOf(reportName);
        this.tabarr.splice(_index, 1);
        this.allReports.splice(_index, 1);
        var maxCount = parseInt($(".reportrtabd").width() / 182);
        var report;
        if (this.showReports.length < maxCount && this.showReports.length < this.allReports.length || this.showReports.length === this.allReports.length && this.showReports.length > maxCount) {
            //左侧放置一个到showReports中的首位
            var startIndex = this.showReports[0].index;
            var startIndex1 = this.allReports[0].index;
            if (startIndex > startIndex1) {
                var index0 = this.tabarr.indexOf(this.maptabarr[0]);
                var prevIndex = index0 - 1;
                report = JSON.parse(JSON.stringify(this.allReports[prevIndex]));
                this.showReports.unshift(report);
                this.maptabarr.unshift(this.tabarr[prevIndex]);
            }

            //右侧推入一个到showReports中
            var endIndex = this.showReports[this.showReports.length - 1].index;
            var endIndex1 = this.allReports[this.allReports.length - 1].index;
            if (endIndex < endIndex1) {
                report = JSON.parse(JSON.stringify(this.allReports[endIndex + 1]));
                this.showReports.push(report);
                this.maptabarr.push(this.tabarr[endIndex + 1]);
            }
        }
        tanwidth();
        if (this.showReports.length) {
            if (deleteCurReport) {
                this.tabbeixuanzhong = 0;
                //this.baobiaoneirong = 0;
                this.curReport = model.showReports[0];
                if (this.curReport.sheets.length) {
                    cancelSelSheets();
                    this.curReport.sheets[0].selected = true;
                    this.selectedSheet = this.curReport.sheets[0];
                } else {
                    this.selectedSheet = {
                        html: ''
                    };
                }
                $('#reportContent').html(this.selectedSheet.html);
                this.curSheets = JSON.parse(JSON.stringify(this.curReport.sheets));
                this.showSheets = JSON.parse(JSON.stringify(this.curReport.sheets));
            }
        } else {
            this.curReport = {};
            this.selectedSheet = {};
            $('#reportContent').html('');
            this.curSheets = [];
            this.showSheets = [];
        }
        if (!this.showReports.length) {
            $(".reportrtabd").css("border", "1px solid #d9e2e8");
            $(".reportrtabd").css("border-bottom", "none");
        }
    },
    //下载报表
    xiazaibaobiao: function () {
        controller.downloadReport();
    },
    //生成的报表滚动；
    yidong: function () {
        var leftr1 = $(".baobiaoshujucon").scrollLeft();
        $(".baobiaoshujunav>ul").css("left", -leftr1);
    },
    //点击tab下面的报表内容对应
    selReportTab: function (item) {
        var index = this.tabarr.indexOf(item);
        this.baobiaoneirong = index;
        this.curReport = this.allReports[index];
        if (this.curReport.sheets && this.curReport.sheets.length) {
            cancelSelSheets();
            this.curReport.sheets[0].selected = true;
            this.selectedSheet = this.curReport.sheets[0];
        } else {
            this.selectedSheet = {
                html: ''
            };
        }
        $('#reportContent').html(this.selectedSheet.html);
        if (this.curReport.sheets) {
            this.curSheets = JSON.parse(JSON.stringify(this.curReport.sheets));
            this.showSheets = JSON.parse(JSON.stringify(this.curReport.sheets));
        } else {
            this.curSheets = {};
            this.showSheets = {};
        }
    },
    //选择分项树
    selSubitemTree: function () {
        var item = arguments[0].model;
        model.toSubitemTree = item;
        controller.getItemTree();
    },
    //选择表单
    selSheet: function (item, index) {
        for (var i = 0; i < this.showSheets.length; i++) {
            if (i !== index) {
                this.showSheets[i].selected = false;
            } else {
                this.showSheets[i].selected = true;
            }
        }
        for (var i = 0; i < this.curSheets.length; i++) {
            if (this.curSheets[i].index !== this.showSheets[index].index) {
                this.curSheets[i].selected = false;
            } else {
                this.curSheets[i].selected = true;
            }
        }
        this.showSheets = JSON.parse(JSON.stringify(this.showSheets));
        this.curSheets = JSON.parse(JSON.stringify(this.curSheets));
        item.selected = true;
        this.selectedSheet = item;
        $('#reportContent').html(item.html);
    },
    //点击表单向左箭头
    sheetToLeft: function () {
        var startIndex = this.showSheets[0].index;
        this.showSheets.pop();
        var sheet = JSON.parse(JSON.stringify(this.curSheets[startIndex - 1])); //
        this.showSheets.unshift(sheet);
    },
    //点击表单向右箭头
    sheetToRight: function () {
        var endIndex = this.showSheets[this.showSheets.length - 1].index;
        this.showSheets.shift();
        var sheet = JSON.parse(JSON.stringify(this.curSheets[endIndex + 1])); //
        this.showSheets.push(sheet);
    },
    //点击模板区域
    clickTemplate: function (item, event) {
        var target = $(event.target);
        if (item.id !== model.editTempId) {
            //恢复样式
            bianjishanchuyc();
        }
    },
    //编辑模板名称聚焦恢复样式
    recoveryStyle: function (event) {
        var _this = event.currentTarget;
        if (!$(_this).attr("readonly")) {
            var jqTempbaocun = $(_this).parents('.templzhiyuanshu').find('.tempbaocun');
            if (jqTempbaocun.hasClass('disabled')) {
                jqTempbaocun.removeClass('disabled');
            }
            $(_this).css("border", "1px solid #02aad1");
            $(_this).parents('.templzhiyuanshu').find('.mingchengchuts').hide();
            $(_this).parents('.templzhiyuanshu').find('.mingchengempty').hide();
        }
    },
    //编辑模板名称限制输入
    limitInput: function (event) {
        var _this = event.currentTarget;
        if (!$(_this).attr("readonly")) {
            var value = $(_this).val();
            $(_this).val(value.replace(/[\\\/\:\*\?\"\<\>\|\.\$]/ig, ''));
        }
    },
};

var computedMethod = {
    //判断生成报表是否可以点击
    baobiaoshifoukeyisc: function () {
        var arr = this.duoxiangxianzhe.split(",")
        var len = arr.length - 1;
        var text = arr[len];
        if (text == '1') {
            if (this.xuanzhongfenxuang.length !== 0) {
                return 1;
            }
        }
        if (text == '2') {
            if (this.xuanzhongzhilu.length !== 0) {
                return 1;
            }
        }
        if (text == '3') {
            if (this.xuanzhongyibiao.length !== 0) {
                return 1;
            }
        }
    }
};
var logger = {
    init: function () {
        var demo = new Vue({
            el: 'body',
            data: model,
            computed: computedMethod,
            methods: modelMethod,
        });
        controller.getTempList();
        controller.getFNTCommonUserPermissionService();

        return demo;
    }
};