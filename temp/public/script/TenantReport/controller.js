/*数据请求*/
var controller = {
    //获取模板列表
    getTempList: function () {
        $('#yemianqiehuanloading').show();
        pajax.post({
            url: 'FNGGridTempListService',
            data: {},
            success: function (result) {
                var result = result || [];
                var tempType;
                var obj = {
                    'd': '日',
                    'w': '周',
                    'm': '月',
                    'y': '年',
                }
                //正则对数据进行format
                result.forEach(function (value, index, array) {
                    var type = value.type;
                    for (var k in obj) {
                        if (new RegExp('(' + k + ')').test(type)) {
                            value.formatType = type.replace(eval('/' + k + '/g'), obj[k]);
                            if (value.type.length === 2) {
                                value.formatType = value.formatType.split('').join('-');
                            }
                        }
                    }
                    value.type == 'ss' ? value.formatType = '实 时' : '';
                });

                for (var i = 0; i < result.length; i++) {
                    result[i].count = 0;
                }
                result = result.filter(item => item.valid == 1)
                model.getTempList = result;
            },
            error: function (err) {},
            complete: function () {
                $('#yemianqiehuanloading').hide();
            }

        });
    },

    //更新开关
    forswitchTemp: function (id, status) {
        var data = {
            id: id,
            status: status
        }
        pajax.update({
            // url: 'forswitchTemp',
            url: 'FNGGridTempUpdateStatusService',
            data: data,
            success: function (result) {
                // console.log(result)
            },
            error: function (error) {
                console.log(error)
            }

        });
    },

    //上传模板文件
    uploadTempExcel: function (filename,fullFilename) {
        var endStr = fullFilename.slice(fullFilename.length - 5);
        if(endStr.indexOf('.xls')!=-1 || endStr.indexOf('.xlsx')!=-1){
            pajax.updateBeforeWithFile({
                // url: 'uploadTempExcel',
                url: 'FNGGridTempSaveService',
                data: {
                    templateName: filename,
                    attachments: {
                        file: /*$('#mobanshangchuanfile')[0].files[0]*/ model.file,
                        subdirectory: tool.fileManagerDirectory,
                        fileName: filename,
                        toPro: 'resource'
                    }
                },
                success: function (result) {
                    $("#notice-con").pshow("success", '上传成功');
                    controller.getTempList();
                    $(".shangchuandc").hide();
                    $('.shangchuanqr').addClass('disabled');
                    $('.shangchuanqr').removeClass('shangchuanzhong');
                },
                error: function (err) {
                    $("#notice2-con").pshow('failure', '上传失败');
                    $('.wangroutishi').show();
                    $('.shangchuanqr').removeClass('shangchuanzhong');
                },
                complete: function () {}
            });
        }else {
            $("#notice2-con").pshow('failure', '上传失败,请选择以.xls或.xlsx结尾的文件');
            $('.shangchuanqr').removeClass('shangchuanzhong');
        }
        
    },
    //修改模板
    updateTemp: function (data, targe, call) {
        $('#yemianqiehuanloading').show();
        pajax.update({
            // url: 'updateTemp',
            url: 'FNGGridTempUpdateService',
            data: data,
            success: function (result) {
                if (typeof call == 'function') call();
                $("#notice-con").pshow("success", '名称修改成功');
            },
            error: function (err) {
                $("#notice2-con").pshow('failure', '名称未修改');
            },
            complete: function () {
                $('#yemianqiehuanloading').hide();
            }
        });
    },
    //删除模板
    delTemp: function (data, call) {
        $('#yemianqiehuanloading').show();
        pajax.update({
            // url: 'delTemp',
            url: 'FNGGridTempDeleteService',
            data: data,
            success: function (result) {
                $("#notice-con").pshow("success", '删除成功');
            },
            error: function (error) {
                $("#notice2-con").pshow('failure', '删除失败');
            },
            complete: function () {
                if (typeof call == 'function') call();
                $('#yemianqiehuanloading').hide();
            }
        });
    },
    //生成报表
    getReport: function (data, uuid) {
        data.startTime = model.startTime;
        data.endTime = model.realEndTime;

        //data.build = ['4101020001']; //一会注释这个 wp

        var build = model.currSelTemplate.buildIdList || [];
        if (build.length) data.build = null;
        var pureMeters = [];
        for (var i = 0; i < data.meter.length; i++) {
            var meterId = data.meter[i];
            if (pureMeters.indexOf(meterId) == -1) {
                pureMeters.push(meterId);
            }
        }
        data.meter = JSON.parse(JSON.stringify(pureMeters));
        pajax.post({
            // url: 'getReport',
            url: 'FNGGridGenerateService',
            data: data,
            success: function (result) {
                for (var i = 0; i < model.deletedCreatingReports.length; i++) {
                    if (uuid === model.deletedCreatingReports[i].uuid) return;
                }
                model.baobiaoxianshi = 2;
                //wp
                if (!result[0].fileHtml) result[0].fileHtml = [];
                var sheets = result[0].fileHtml;
                for (var i = 0; i < sheets.length; i++) {
                    sheets[i].index = i;
                }
                var sheet = sheets.length ? sheets[0] : {};
                sheet.selected = true;
                $('#reportContent').html(sheet.html);
                for (var i = 0; i < model.showReports.length; i++) {
                    if (model.showReports[i].reportName === result[0].name) {
                        model.showReports[i].load = false;
                        model.showReports[i].resource = result[0].id;
                        model.showReports[i].sheets = sheets;
                        model.showReports[i].html = sheet.html;
                    }
                    model.showReports[i].index = i;
                }
                for (var i = 0; i < model.allReports.length; i++) {
                    if (model.allReports[i].reportName === result[0].name) {
                        model.allReports[i].load = false;
                        model.allReports[i].resource = result[0].id;
                        model.allReports[i].sheets = sheets;
                        model.allReports[i].html = sheet.html;
                        model.curReport = model.allReports[i];
                    }
                    model.allReports[i].index = i;
                }

                var curSheets = [],
                    showSheets = [];
                model.curSheets = JSON.parse(JSON.stringify(sheets));
                model.showSheets = JSON.parse(JSON.stringify(sheets));
                model.selectedSheet = sheet;

                setTimeout(function () {
                    var unitSheetWidth = $(".sheetPageUl").width() / 130;
                    model.isIntCount = unitSheetWidth % 1 === 0;
                    var maxCount = parseInt(unitSheetWidth);
                    var count = model.curSheets.length;
                    if (count > maxCount) {
                        model.showSheets = model.curSheets.slice(0, maxCount);
                    }
                }, 0);

                $("#notice2-con").pshow('success', '生成报表成功');
            },
            error: function (error) {
                for (var i = 0; i < model.showReports.length; i++) {
                    if (model.showReports[i].reportName === data.name) {
                        model.showReports[i].load = false;
                        model.showReports[i].hideDownload = true;
                    }
                }
                for (var i = 0; i < model.allReports.length; i++) {
                    if (model.allReports[i].reportName === data.name) {
                        model.allReports[i].load = false;
                        model.allReports[i].hideDownload = true;
                        model.curReport = model.allReports[i];
                    }
                }
                $("#notice2-con").pshow('failure', '生成报表失败');
            },
            complete: function () {}
        })
    },
    //下载报表
    downloadReport: function () {
        pajax.download(model.curReport.resource);
    },
    //获取建筑列表
    getBuildList: function (data) {
        pajax.post({
            url: 'getBuildList',
            data: data,
            success: function (result) {
                model.getBuildList = result;
                //wp
                for (var i = 0; i < result.length; i++) {
                    var cur = result[i];
                    cur.level = 0;
                }
                window['searchBildList'] = JSON.parse(JSON.stringify(result));
            },
            error: function (error) {},
            complete: function () {}
        });
    },
    //获取分项支路下拉列表
    getItemList: function () {
        var data = {
            buildingId: model.xuanzhongBUildid[0],
            tempId: model.currSelTemplate.id
        }
        pajax.post({
            url: 'getItemList',
            data: data,
            success: function (result) {
                model.xuanzhongfenxuang = [];
                model.xuanzhongfenxuang1 = [];
                model.xuanzhongfenxuangid = [];
                model.getItemList = result;
                if (result.length) {
                    model.toSubitemTree = result[0];
                    model.curSubitemTree = JSON.parse(JSON.stringify(result[0]));
                    setTimeout(function () {
                        $('#subitemCombo').psel(0, false);
                    }, 0);
                    controller.getItemTree();
                } else {
                    window['searchItemTree'] = [];
                    model.getItemTree = [];
                    model.toSubitemTree = {};
                    model.getItemTree = [];
                }
            },
            error: function (error) {},
            complete: function () {}
        });
    },
    //获取分项树形菜单
    getItemTree: function (toggleSelSubitem) {
        pajax.post({
            url: 'getItemTree',
            data: {
                itemId: model.toSubitemTree.id
            },
            success: function (result) {
                window['searchItemTree'] = result;
                model.getItemTree = result;
                if (toggleSelSubitem) {
                    toggelSelSubitem();
                } else {
                    setTimeout(function () {
                        for (var i = 0; i < model.xuanzhongfenxuangid.length; i++) {
                            $('.xialasearch').psel(model.xuanzhongfenxuangid[i], false);
                        }
                        $('.xialasearch input').focus();
                        $('.xialasearch input').blur();
                    }, 0);
                }
            },
            error: function (error) {},
            complete: function () {}
        });
    },
    //获取支路树形菜单
    getBranchTree: function (data) {
        pajax.post({
            url: 'getBranchTree',
            data: data,
            success: function (result) {
                window['searchBranchTree'] = result;
                model.getBranchTree = result;
            },
            error: function (error) {},
            complete: function () {}
        });
    },
    //获取'模板管理'显示权限
    getFNTCommonUserPermissionService: function () {
        pajax.post({
            url: 'FNTCommonUserPermissionService',
            data: {
                permissionId: 'GridManager'
            },
            success: function (result) {
                if (result && result.length > 0) {
                    model.buttonShow = result[0].isHave;
                }
            },
            error: function (error) {},
            complete: function () {}
        });
    }

};