<!-- 组织结构树 -->
<script type="text/html" id="scrStruct">
    <div class="tree-temp">
        <div class="temp-tit" data-bind="click: lineSel">
            <span data-bind="style: { visibility: childs.length ? 'visible' : 'hidden' }, click: clickSpan">r</span>
            <b data-bind="text: name">工程经理工程</b>
        </div>
        <div class="temp-con" data-bind="template: { name: 'scrStruct', foreach: childs }"></div>
    </div>
</script>

<!-- 功能设置 -->
<script type="text/html" id="functionSetTemp">
    <div class="functionSet-f-wrap" id="functionSet">
        <div class="f-f-w-temp">
            <label><em>*</em><b>产品线：</b></label>
            <div class="f-f-w-input">
                <div p-create="combobox-form" id="cpcode" p-bind="attr:{placeholder:'请选择所属产品线',items:fGroups,text:name,cpcode:code}" p-rely="true"></div>
                <div class="error-tip"><span><i>!</i><em>请选择所属产品线</em></span></div>
            </div>
        </div>
        <div class="f-f-w-temp">
            <label><em>*</em><b>功能名称：</b></label>
            <div class="f-f-w-input" id="featureName" p-create="text-text" p-bind="attr:{placeholder:'请输入功能名称',spaceerrtext:'功能名称不可为空'}"></div>
        </div>
        <div class="f-f-w-temp">
            <label><em>*</em><b>功能编码：</b></label>
            <div class="f-f-w-input" id="featureCode" p-create="text-text" p-bind="attr:{placeholder:'请输入功能编码',spaceerrtext:'功能编码不可为空'}"></div>
        </div>
        <div class="f-f-w-temp" style="display: none;">
            <label><em>*</em><b>标志：</b></label>
            <div class="f-f-w-input">
                <label for="buildIcon" class="buildIcon-but" id="lblFIcon">点击上传标志</label>
                <input type="file" id="buildIcon" accept="image/jpeg, image/x-png" style="display: none;" onchange="fIconUpload(this)">
                <div class="img" id="fIcon" path=""></div>
                <div class="img-x" onclick="buildIconRemove(this)" style="display: none;" id="delFIcon">x</div>
                <div class="error-tip"><span><i>!</i><em>请上传标志</em></span></div>

            </div>
        </div>
        <div class="f-f-w-temp">
            <label><em>*</em><b>功能URL：</b></label>
            <div class="f-f-w-input" id="featureUrl" p-create="text-text" p-bind="attr:{placeholder:'请输入功能URL',spaceerrtext:'功能URL不可为空'}"></div>
        </div>
        <div class="functionSet-f-button">
            <div p-create="button-backBlueBorder" p-bind="attr:{text:'保存'}" onclick="functionSetFloatSaveHide()"></div>
            <!--<div p-create="button-grayBorder" p-bind="attr:{text:'取消'}" onclick="functionSetFloatHide()"></div>-->
        </div>
    </div>
</script>

<!--组织结构管理中的结构树-->
<script type="text/html" id="scrStrManTree">
    <div class="tree-con-temp">
        <div class="tree-con-wrap">
            <div class="temp-chunk" data-bind="css: { noshuxian: childs.length == 0 }">
                <input readonly data-bind="visible: !$root.isEditStruct(), value: name, attr: { title: name }" />
                <input data-bind="visible: $root.isEditStruct(), value: name, event: { focus: focusH }" placeholder="点击填写组织名称" />
                <div class="error-tips">*名字不能为空</div>
                <div class="add-remove">
                    <span data-bind="click: add">J</span>
                    <span data-bind="click: remove">-</span>
                </div>
            </div>
        </div>
        <div class="tree-con-con">
            <!--ko template:{name:'scrStrManTree',foreach:childs}-->
            <!--/ko-->
        </div>
    </div>
</script>

<!-- 发布公告 moadl temp-->
<script type="text/html" id="v6swAnnouncementwrapCustom">
    <div class="announcement-wrap">
        <div class="announcement-tit">发布公告</div>
        <div class="announcement-textarea">
            <div class="t">公告内容</div>
            <div class="textarea" p-create="text-textarea" p-bind="attr:{id:'txtNoticeCon',placeholder:'公告内容不可超过200字',spaceerrtext:'公告内容不可为空',islentip:true,length:200,lengtherrtext:'输入不能超过200个字符！'}"></div>
        </div>
        <div class="subfieldSet-button">
            <div p-create="button-backBlueBorder" p-bind="attr:{text:'发布'}" onclick="saveNotice()"></div>
            <div p-create="button-grayBorder" p-bind="attr:{text:'取消'}" onclick="announcementModalHide()"></div>
        </div>
    </div>
</script>

<!--组件内分项树-->
<script type="text/html" id="srtmodelitem">
    <div class="m-pop-temp">
        <div class="m-pop-temp-tit" data-bind="style: { 'padding-left': level * 25 + 'px' }, css: { pitch: isSel }, click: moduleSetController.multiSel, attr: { tid: id, pid: typeof groupId != 'undefined' ? groupId : '' }">
            <div class="arrows" onclick="moduleSetTreeShow(event,this)" p-create="button-blueIconNoBorder" p-bind="attr:{icon:'r'}" data-bind="style: { visibility: child.length > 0 ? 'visible' : 'hidden' }"></div>
            <b data-bind="text: name,attr:{title:name}">冷站1</b>
            <em>Z</em>
        </div>
        <div class="m-pop-temp-con" data-bind="template: { name: 'srtmodelitem', foreach: child }">
        </div>
    </div>
</script>
<!--报警方案内的组织结构及其用户树-->
<script type="text/html" id="scrdepartusers">
    <div class="tree-temp">
        <div class="tree-title" data-bind="click: alarmController.departUserSelEvent, attr: { aaii: id, dep: depart },style: { 'padding-left':( level-1) * 25 + 'px' }">
            <span class="arrows" onclick="addTreeTemp(this,event)" data-bind="style: { visibility: typeof child != 'undefined' &&  child != null && child.length > 0 ? 'visible' : 'hidden' }">
                <div p-create="button-blueIconNoBorder" p-bind="attr:{icon:'r'}"></div>
            </span>
            <b data-bind="text: name,style:{color:depart?'':'#637e99'}" ></b>
        </div>
        <div class="tree-con">
            <!--ko template:{name:'scrdepartusers',foreach:child}-->
            <!--/ko-->
        </div>
    </div>
</script>
