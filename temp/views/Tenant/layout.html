﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>租户管理</title>
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <script type="text/javascript" src="/scripts/lib/vue-2.2.0.min.js"></script>
    <link rel="stylesheet" href="../../pcontrol/css/flatBlueSeries_min_2.0.css" />
    <link rel="stylesheet" href="../../css/tement/tenantManage.css">
    <link rel="stylesheet" href="../../css/tement/energyCost.css">
    <link rel="stylesheet" href="../../css/tement/recharge.css">
    <link rel="stylesheet" href="../../css/tement/prepaidRecordsSofterMeter.css">
    <link rel="stylesheet" href="../../css/tement/refundRentRecord.css">
    <link rel="stylesheet" href="../../css/tement/pay.css">
    <link rel="stylesheet" href="../../css/tement/settlement.css">
    <link rel="stylesheet" href="../../css/tement/arrearsBill.css">
    <link rel="stylesheet" href="../../css/tement/payRecord.css">   
    <link rel="stylesheet" href="../../css/tement/add_editTement.css">
    <link rel="stylesheet" href="../../css/tement/component.css">
    <link rel="stylesheet" href="../../css/tement/remainingAmountReport.css">
    <link rel="stylesheet" href="../../css/tement/gaugeRecord.css">
    <link rel="stylesheet" href="../../css/tement/gaugeRecordSingleTenant.css">
    <link rel="stylesheet" href="../../css/tement/paymentRecords.css">
    <link rel="stylesheet" href="../../css/tement/changeMeterRecords.css">
    <link rel="stylesheet" href="../../css/tement/settleAccounts.css">
    <link rel="stylesheet" href="../../css/tement/surrenderTenancy.css">
    <link rel="stylesheet" href="../../css/tement/tenementDetails.css">
    <link rel="stylesheet" href="../../css/tement/meter_detail.css">
    <link rel="stylesheet" href="../../css/tement/pdfReport.css">
    <link rel="stylesheet" href="../../css/tement/pdfCharge.css">
    <link rel="stylesheet" href="../../css/tement/pdfReturn.css">
    <link rel="stylesheet" href="../../css/tement/pdfArrears.css">
    <link rel="stylesheet" href="../../css/tement/globalWindow.css">
    <link rel="stylesheet" href="../../css/tement/setRecord.css">
    <link rel="stylesheet" href="../../css/tement/abnormalBill.css">
    <script type="text/javascript" src="../../scripts/lib/jquery-2.0.0.min.js"></script>
    <script type="text/javascript" src="../../scripts/lib/highcharts-4.2.5.js"></script>
    <script type="text/javascript" src="../../pcontrol/pchart.js"></script>
    <script type="text/javascript" src="../../scripts/lib/vue-2.2.0.min.js"></script>
    <script type="text/javascript" src="../../scripts/tool/ptool.js"></script>
    <script type="text/javascript" src="../../scripts/tool/asynTool.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pconst.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pajax.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pautoComplete.js"></script>
    <script type="text/javascript" src="../../scripts/tool/psecret.js"></script>
    <script type="text/javascript" src="../../scripts/extend/Date.js"></script>
    <script type="text/javascript" src="../../scripts/extend/Math.js"></script>
    <script type="text/javascript" src="../../scripts/extend/String.js"></script>
    <script type="text/javascript" src="../../scripts/extend/jQueryDom.js"></script>
    <script type="text/javascript" src="../../scripts/extend/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../pcontrol/flatBlueSeries_src_2.0.js"></script>
    <script type="text/javascript" src="/script/tement/tenantMng/controller.js"></script>
    <script type="text/javascript" src="/script/tement/tenantMng/event/addtement_event.js"></script>
    <script type="text/javascript" src="/script/tement/tenantMng/event.js"></script>
    <script type="text/javascript" src="/script/tement/common/common.js"></script>
    <script type="text/javascript" src="/script/tement/tenantMng/model.js"></script>
    <script type="text/javascript" src="/script/tement/tenantMng/component.js"></script>
    <script type="text/javascript" src="/script/tement/searchComplete.js"></script>
</head>
<body>
    <div id="tenantManage">
        <pnotice-message id="message"></pnotice-message>
        <div class="mainBody mainBodyShow" :style="{display: tenantArr.length != 0 || isGetTenantArrReady ? 'block' : 'none'}">
            <!-- 租户列表  -->
            <%include tementOtherPage/tementListPage.html%>
            <!-- 添加租户-->
            <%include tementOtherPage/addTementPage.html%>
            <!-- 缴费记录-->
            <%include tementOtherPage/payRecord.html%>
            <!-- 欠费账单-->
            <%include tementOtherPage/arrearsBill.html%>
            <!-- 结算失败-->
            <%include tementOtherPage/settlementErr.html%>
            <!-- 结算成功-->
            <%include tementOtherPage/settlementSucc.html%>
            <!-- 结算-->
            <%include tementOtherPage/settlement.html%>
            <!-- 缴费电 -->
            <%include tementOtherPage/pay.html%>
            <!-- 充值记录电软件充软件扣  -->
            <%include tementOtherPage/recharge.html%>
            <!-- 充值记录电软件充表扣  -->
            <%include tementOtherPage/prepaidRecordsSofterMeter.html%>
            <!-- 退费记录 -->
            <%include tementOtherPage/refundRentRecord.html%>
            <!-- 能耗费用报表电 -->
            <%include tementOtherPage/energyCost.html%>
            <!-- 剩余金额/量/天数报表 -->
            <%include tementOtherPage/remainingAmountReport.html%>
            <!-- 表底数记录 -->
            <%include tementOtherPage/gaugeRecord.html%>
            <!-- 表底数记录（单租户） -->
            <%include tementOtherPage/gaugeRecordSingleTenant.html%>
            <!-- 缴费记录-->
            <%include tementOtherPage/paymentRecords.html%>
            <!-- 换表记录-->
            <%include tementOtherPage/changeMeterRecords.html%>
            <!-- 结算页 -->
            <%include tementOtherPage/settleAccounts.html%>
            <!-- 退租页 -->
            <%include tementOtherPage/surrenderTenancy.html%>
            <!-- 租户详情 -->
            <%include tementOtherPage/tenementDetails.html%>
            <!-- 管理价格方案-->
            <%include tementOtherPage/managePrivcePlanPage.html%>
            <!-- 电表详情-->
            <%include tementOtherPage/meterDetail.html%>
            <!-- 能耗费用报告-->
            <%include tementOtherPage/energyCostReport.html%>
            <!-- 设置记录 -->
            <%include tementOtherPage/setRecord.html%>
            <!-- 异常账单列表 -->
            <%include tementOtherPage/abnormalBill.html%>
            <!-- 日志 -->
            <%include tementOtherPage/logs.html%>
        </div>
        <!--设置报警门限  -->
        <pwindow-modal id="policeWindow" title="全局报警设置" templateid="police"></pwindow-modal>

        <!--设置自动发送短信 20180606wp++  -->
        <pwindow-modal id="autoMsgWindow" title="自动发送短信设置" templateid="autoMsg"></pwindow-modal>
        <!--设置自动发送黑名单短信 20181025++  -->
        <pwindow-modal id="addtenantnamemodalWindow" title="选择租户" templateid="addtenantnamemodal"></pwindow-modal>

        <!--远程充值设置 3.6++  -->
        <pwindow-modal id="RemotetopupWindow" title="微信充值设置" templateid="Remotetopup"></pwindow-modal>
        <pwindow-modal id="RemotetopupaddforWindow" title="选择租户" templateid="Remotetopupaddfor"></pwindow-modal>
        <!--发送充值提醒  -->
        <pwindow-modal bind="true" id="'modal2Window'" title="'发送'+(payType==0?'充值':'缴费')+'提醒'" templateid="reminderWindow"></pwindow-modal>

        <!--缴费电 缴费首页 -->
        <pwindow-modal id="payWindow" title="缴费" templateid="pay"></pwindow-modal>

        <!--缴费电 缴费中 -->
        <pwindow-modal id="payInWindow" title="缴费中" templateid="payIn"></pwindow-modal>

        <!--缴费电 缴费成功  -->
        <pwindow-modal id="paySuccessWindow" title="<i class='per-notice_successicon'></i>缴费成功" templateid="paySuccess"></pwindow-modal>

        <!--缴费电 缴费失败  -->
        <pwindow-modal id="payErrorWindow" title="<i class='per-notice_failureicon'></i>缴费失败,请重新缴费" templateid="payError"></pwindow-modal>

        <!--缴费电 全部缴费 -->
        <pwindow-modal id="payBothWindow" title="全部缴费" templateid="payBoth"></pwindow-modal>

        <!--缴费电 全部缴费中 -->
        <pwindow-modal id="payBothInWindow" title="缴费中" templateid="payBothIn"></pwindow-modal>

        <!--缴费电 全部缴费成功-->
        <pwindow-modal id="payBothSuccWindow" title="<i class='per-notice_successicon'></i>缴费成功" templateid="payBothSucc"></pwindow-modal>

        <!--缴费电 全部缴费失败 -->
        <pwindow-modal id="payBothErrWindow" title="全部缴费" templateid="payBothErr"></pwindow-modal>

        <!--结算 账单明细  -->
        <pwindow-float id="billWindow" isshade="true" title="账单明细" templateid="bill_detail">
            <animate maxpx="0" minpx="-830"  orientation="right"> </animate>
        </pwindow-float>

        <!-- 缴费 -->
        <!-- 后付费缴费 -->
        <div class="pay_the_fees_post_paid_box">
            <pwindow-modal id="pay_the_fees_post_paid" title="缴费" templateid="pay_the_fees_post_paid_tpl"></pwindow-modal>
        </div>
        <!-- 后付费缴费中 -->
        <div class="pay_the_fees_loading_box">
            <pwindow-modal id="pay_the_fees_loading" title="缴费中" templateid="pay_the_fees_loading_tpl"></pwindow-modal>
        </div>
        <!-- 后付费缴费成功 -->
        <div class="pay_the_fees_success_box">
            <pwindow-modal id="pay_the_fees_success" title="<i class='per-notice_successicon'></i>缴费成功" templateid="pay_the_fees_success_tpl"></pwindow-modal>
        </div>
        <!-- 后付费缴费失败 -->
        <div class="pay_the_fees_error_box">
            <pwindow-modal id="pay_the_fees_error" title="<i class='per-notice_failureicon'></i>缴费失败" templateid="pay_the_fees_error_tpl"></pwindow-modal>
        </div>
        <!--控件模板-->
        <%include tementOtherPage/template/addtement.html%>
        <%include tementOtherPage/template/globalWindow.html%>
    </div>

    <!-- 租户详情点击缴费充值弹窗模板 -->
    <!-- 后付费缴费 -->
    <script type="text/html" id="pay_the_fees_post_paid_tpl">
        <div class="pay_the_fees_post_paid font_color_dark_gray">
            <div>
                <div>
                    <h3>应缴总计：</h3>
                    <p class="font_size_big font_color_blue">
                        {{ Number(selTenant.payOrder.totalMoney).toFixed(2) }}
                        <span>元</span>
                    </p>
                </div>
                <div>
                    <h3>租户编号：</h3>
                    <p class="font_color_black">{{selTenant.tenantId}}</p>
                </div>
                <div>
                    <h3>租户名称：</h3>
                    <p class="font_color_black">{{selTenant.tenantName}}</p>
                </div>
                <div class="prev_bills">
                    <h3>能耗类型：</h3>
                    <p class="font_color_black">{{selTenant.payOrder.energyTypeName}}</p>
                </div>
                <div class="bills">
                    <div v-for="(model,index) in selTenant.payOrder.dataArr">
                        <h3>账单
                            <em v-text="index+1"></em>：</h3>
                        <p class="font_color_black">{{(model.orderTime+"").replace(/-/g,'.')}}</p>
                        <h3>应缴费用：</h3>
                        <p class="font_color_black">{{model.money}}元</p>
                    </div>
                </div>
            </div>
            <div class="btns">
                <div class="clearfix">
                    <div>
                        <pbutton-blue text="缴费" click="staticEvent.payTheFeesLodingEvent('normal')"></pbutton-blue>
                    </div>
                    <div>
                        <pbutton-white text="取消" click="staticEvent.payTheFeesPostPaidCloseEvent"></pbutton-white>
                    </div>
                </div>
            </div>
        </div>
    </script>
    <!-- 后付费缴费中 -->
    <script type="text/html" id="pay_the_fees_loading_tpl">
        <div class="pay_the_fees_loading">
            <ploading-part id="partLoading_in_pay_the_fees" text="缴费中..."></ploading-part>
            <p class="font_color_dark_gray" v-if="true">请勿关闭浏览器，稍后请仔细核对</p>
        </div>
    </script>
    <!-- 后付费缴费成功 -->
    <script type="text/html" id="pay_the_fees_success_tpl">
        <div class="pay_the_fees_success font_color_dark_gray">
            <div>
                <div>
                    <h3>应缴总计：</h3>
                    <p class="font_size_big font_color_green">
                        {{selTenant.payOrder.totalMoney}}
                        <span>元</span>
                    </p>
                </div>
                <div>
                    <h3>租户编号：</h3>
                    <p class="font_color_black">{{selTenant.tenantId}}</p>
                </div>
                <div>
                    <h3>租户名称：</h3>
                    <p class="font_color_black">{{selTenant.tenantName}}</p>
                </div>
                <div>
                    <h3>能耗类型：</h3>
                    <p class="font_color_black">{{selTenant.payOrder.energyTypeName}}</p>
                </div>
                <div class="bills">
                    <div v-for="(model,index) in selTenant.payOrder.dataArr">
                        <h3>账单
                            <em v-text="index+1"></em>：</h3>
                        <p class="font_color_black">{{(model.orderTime+"").replace(/-/g,'.')}}</p>
                        <h3>应缴费用：</h3>
                        <p class="font_color_black">{{model.money}}元</p>
                    </div>
                </div>
            </div>
            <div class="btns btn_1">
                <div class="clearfix">
                    <div>
                        <pbutton-blue text="关闭" click="staticEvent.payTheFeesSuccessCloseEvent"></pbutton-blue>
                    </div>
                </div>
            </div>
        </div>
    </script>
    <!-- 后付费缴费失败 -->
    <script type="text/html" id="pay_the_fees_error_tpl">
        <div class="pay_the_fees_error font_color_dark_gray">
            <div>
                <div>
                    <h3>应缴总计：</h3>
                    <p class="font_size_big font_color_red">
                        {{selTenant.payOrder.totalMoney}}
                        <span>元</span>
                    </p>
                </div>
                <div>
                    <h3>租户编号：</h3>
                    <p class="font_color_black">{{selTenant.tenantId}}</p>
                </div>
                <div>
                    <h3>租户名称：</h3>
                    <p class="font_color_black">{{selTenant.tenantName}}</p>
                </div>
                <div>
                    <h3>能耗类型：</h3>
                    <p class="font_color_black">{{selTenant.payOrder.energyTypeName}}</p>
                </div>

                <div class="bills">
                    <div v-for="(model,index) in selTenant.payOrder.dataArr">
                        <h3>账单
                            <em v-text="index+1"></em>：</h3>
                        <p class="font_color_black">{{(model.orderTime+"").replace(/-/g,'.')}}</p>
                        <h3>应缴费用：</h3>
                        <p class="font_color_black">{{model.money}}元</p>
                    </div>
                </div>
            </div>
            <div class="btns">
                <div class="clearfix">
                    <div>
                        <pbutton-blue text="重新缴费" click="staticEvent.payTheFeesLodingEvent"></pbutton-blue>
                    </div>
                    <div>
                        <pbutton-white text="取消" click="staticEvent.payTheFeesErrorCloseEvent"></pbutton-white>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <!--能耗费用报表模板  -->
    <script type="text/html" id="eneryCostScroll">
        <div>
            <ul>
                <li v-for="tenant in checkedTenantArr">
                    <div class="tenentNo">{{tenant.tenantId}}</div>
                    <div class="tenentBodyName">{{tenant.tenantName}}</div>
                    <div class="inline energyCost_table_body_bottom">
                        <template v-for="item in tenant.energyCostDataList">
                            <div class="slh"><span>{{tenantCtrl.numberFormat(item.energy,tenantCtrl.fixType_dynamic,true)}}</span></div>
                            <div class="slh"><span>{{tenantCtrl.numberFormat(item.money,tenantCtrl.fixType_money,true)}}</span></div>
                        </template>
                    </div>
                </li>
            </ul>
        </div>
    </script>

    <!--设置报警门限模板  -->
    <script type="text/html" id="police">
        <div class="police">
            <div class="plice_sel">
                <!-- 20180606wp++ -->
                <div v-for="(model, index) in alarmSearchData.typeList" :key="index" v-if="model.typeId != 'ZHBJ_16'">
                    <div class="police_tit">
                        <span v-if="model.type=='Dian'">电费用不足</span>
                        <span v-if="model.type=='Shui'">水费用不足</span>
                        <span v-if="model.type=='ReShui'">热水费用不足</span>
                        <span v-if="model.type=='RanQi'">燃气费用不足</span>
                        <span v-if="model.type=='fuHeLv'">负荷率</span>
                    </div>
                    <div v-for="(model,index1) in model.alarmList">
                        <div class="police_con clearfix">
                            <div style="min-width: 125px">{{model.typeName}}</div>
                            <div class="police_con_mid">
                                <ptext-text id="model.typeId" bind="true" disabled="!model.valid" value="model.limit" blur="staticEvent.validationAlarmValue">
                                    <verify errtip="报警门限值不能为空" verifytype="space"></verify>
                                    <verify errtip="格式不正确，请填写正整数" verifytype="int"></verify>
                                    <verify errtip="不能超过3位数，请重新填写" verifytype="length" length="3"></verify>
                                </ptext-text>
                            </div>
                            <div>{{model.unit}}报警</div>
                            <div class="btn">
                                <pswitch-slide id="'s'+index+index1"   state="model.valid" bind="true"></pswitch-slide>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="plice_ope clearfix">
                <pbutton-blue text="保存" click="staticEvent.policeDetermine" id="determine_global" data-flag="global"></pbutton-blue>
                <pbutton-white text="取消" click="staticEvent.policehide" id="cancel_global"></pbutton-white>
            </div>
        </div>
    </script>
    
    <!--自动发送短信模板 20180606wp++ -->
    <script type="text/html" id="autoMsg">
        <div class="autoMsgBox">
            <div class="autoMsg">
                <div class="autoMsgCol">
                    是否自动发送短信
                    <div class="btnS">
                        <pswitch-slide id="'autoMsgBtn'" change="staticEvent.switchClick" state="autoMsg.sendStuats" bind="true"></pswitch-slide>
                    </div>
                </div>
                <div class="autoMsgCol">
                    <span>设置自动短信时间(最多可设置两次)</span>
                    <div class="btnS">
                        <pbutton-white icon="J" shape="ellipse" click="staticEvent.addTimer"></pbutton-white>
                    </div>
                </div>
                <div class="autoMsgCol" v-show="autoMsg.sendStuats==1&&typeof(autoMsg.index1)=='number'">
                    <pcombobox-normal id="'autoTime1'" align="left" orientation="down" bind="true">
                        <item datasource="autoMsg.timeArr" text="name" ></item>
                    </pcombobox-normal>
                    <div class="btnS btnLeft">
                    <pbutton-white icon="p" shape="ellipse" click="staticEvent.delTimer(1)"></pbutton-white>
                </div>
                </div>
                <div class="autoMsgCol" v-show="autoMsg.sendStuats==1&&typeof(autoMsg.index2)=='number'">
                    <pcombobox-normal id="'autoTime2'" align="left" orientation="down" bind="true">
                        <item datasource="autoMsg.timeArr" text="name" ></item>
                    </pcombobox-normal>
                    <div class="btnS btnLeft">
                    <pbutton-white icon="p" shape="ellipse" click="staticEvent.delTimer(2)"></pbutton-white>
                    </div>
                </div>


                <div class="autoMsgCol">
                    <span>不发送短信租户列表</span>
                    <div class="btnS">
                       <pbutton-blue text="添加" isborder="false" click="tenantCtrl.addtenantnameShow()" ></pbutton-blue>
                    </div>
                </div>

              <div  style="width: 600px; height: 300px;"  >
                  <pgrid-multifunction style="width: 600px; height: 300px;"  id="'messageBlacklist'">
                      <panel datasource="messageBlacklist" sel="tenantCtrl.tenantPageChange"  templateid="mainGridTempfor" operation="true"
                             change="tenantCtrl.tenantCheck"  sortevent="tenantCtrl.tenantArrColumnSort" ></panel>
                      <!--表格内容  -->
                      <header>
                          <column name="租户编号" ></column>
                          <column name="租户名称"></column>
                          <column name="所属建筑"></column>
                      </header>
                  </pgrid-multifunction>
                </div>
                <div class="plice_ope clearfix">
                    <pbutton-blue text="保存" click="staticEvent.saveTime" id="determine_global" data-flag="global"></pbutton-blue>
                    <pbutton-white text="取消" click="staticEvent.autoMsghide" id="cancel_global"></pbutton-white>
                </div>
            </div>
        </div>
    </script>

    <!-- 添加黑名单模态框 -->
    <script type='text/html' id="mainGridTempfor">
        <div v-text='model.tenantId' :title="model.tenantId">-</div>
        <div v-text='model.tenantName' :title="model.tenantName">-</div>
        <div v-text='model.buildingName' :title="model.buildingName">-</div>
        <div class="operationCon">
            <i class="operationButton" :id="model.tenantId" @click="staticEvent.deletenantnamemoda()" style="color:#6985a1;margin-left: 30%">删除</i>
        </div>
    </script>
    <script type="text/html" id="addtenantnamemodal">
        <div style="width: 650px; height: 410px;"  id="blackListCount" >
            <pgrid-multifunction style="width: 650px; height: 350px;" id="'blackList'">
                <panel datasource="addtenantName1"  pagesize="5" checkbox="true" sel="tenantCtrl.tenantPageing" change="staticEvent._console" ></panel>
                <header >
                    <column name="租户编号" source="tenantId" click=""></column>
                    <column name="租户名称" source="tenantName" click=""></column>
                    <column name="建筑名称" source="buildingName"></column>
                </header>
                <page >
                    <ppage-simple orientation="up" id="page_simple1"  ></ppage-simple>
                </page>
                <button>
                    <pcombobox-normal sel="tenantCtrl.addtenant_building_filter" bind="true" id="'addtenant_building_cbx'">
                        <header placeholder="'选择建筑'" prefix="建筑：" click="tenantCtrl.tenantOperationHide"></header>
                        <item datasource="buildingArr" text="name"></item>
                    </pcombobox-normal>
                    <psearch-promptly  change="staticEvent.eventCall(event)"  placeholder="输入租户编号或名称" style="width:200px;" id="ddSearch">
                        <tip ></tip>
                    </psearch-promptly>

                </button>
            </pgrid-multifunction>

            <div class="plice_ope clearfix">
                 <pbutton-blue text="确定" click="staticEvent.addtenantnamehideSave" id="sure_global"></pbutton-blue>
                 <pbutton-white text=" 返回" click="staticEvent.addtenantnamehide()"  id="return_global"></pbutton-white>
            </div>
        </div>
    </script>


   <!-- 远程充值 3.6版本-->
    <script type="text/html"  id="Remotetopup">
        <!--远程充值租户列表 3.6++ -->
        <div class="autoMsgBox">
            <div class="autoMsg">
                <div class="autoMsgCol">
                    <span>微信充值黑名单</span>
                    <div class="btnS">
                        <pbutton-blue text="添加" isborder="false" click="tenantCtrl.addRemotetopupShow()" ></pbutton-blue>
                    </div>
                </div>
                <div  style="width:600px;height:350px;"  >
                    <pgrid-multifunction style="width:600px;height: 350px;"  id="'RemotetopupCount'">
                    <panel datasource="Remotetopup"  templateid="Remotetopupfor" operation="true" ></panel>
                    <header>
                        <column name="租户编号" ></column>
                        <column name="租户名称"></column>
                        <column name="所属建筑"></column>
                    </header>
                </pgrid-multifunction>
                </div>
                <div class="plice_ope clearfix">
                    <pbutton-blue text="保存" click="staticEvent.Remotetopupsave" id="determine_global" data-flag="global"></pbutton-blue>
                    <pbutton-white text="取消" click="staticEvent.Remotetopuphide" id="cancel_global"></pbutton-white>
                </div>
            </div>
        </div>
    </script>
    <script type="text/html" id="Remotetopupfor">
        <div v-text='model.tenantId' :title="model.tenantId">-</div>
        <div v-text='model.tenantName' :title="model.tenantName">-</div>
        <div v-text='model.buildingName' :title="model.buildingName">-</div>
        <div class="operationCon">
            <i class="operationButton" :id="model.tenantId" @click="staticEvent.deleteRemotetopupfor()" style="color:#6985a1;margin-left: 30%">删除</i>
        </div>
    </script>

    <script type="text/html" id="Remotetopupaddfor">
        <div style="width: 650px; height: 410px;"  id="rechageCount" >
            <pgrid-multifunction style="width:650px; height: 350px;" id="'rechage'">
                <panel datasource="Remotetopup2" checkbox="true"  sel="tenantCtrl.rechagePageing"  change="staticEvent.remotetListclick" ></panel>
                <header >
                    <column name="租户编号" source="tenantId" click=""></column>
                    <column name="租户名称" source="tenantName" click=""></column>
                    <column name="建筑名称" source="buildingName"></column>
                </header>
                 <page >
                      <ppage-simple orientation="up" id="page_simple2"  ></ppage-simple>
                  </page>
                <button >
                    <pcombobox-normal sel="tenantCtrl.remotetopup_building_filter" bind="true" id="'remotetopup_building_cbx'">
                        <header placeholder="'选择建筑'" prefix="建筑：" click="tenantCtrl.tenantOperationHide"></header>
                        <item datasource="buildingArr" text="name"></item>
                    </pcombobox-normal>
                    <psearch-promptly  change="staticEvent.searchCall(event)"  placeholder="输入租户编号或名称" style="width:200px;" id="reachageSearch">
                        <tip ></tip>
                    </psearch-promptly>
                </button>

            </pgrid-multifunction>

            <div class="plice_ope clearfix">
                <pbutton-blue text="确定" click="staticEvent.remotetopupHideSave" id="sure_global"></pbutton-blue>
                <pbutton-white text=" 返回" click="staticEvent.remotetopupSaveHide()"  id="return_global"></pbutton-white>
            </div>
        </div>
    </script>


    <script type="text/html" id="reminderWindow">
        <!--发送充值提醒模板  -->
        <div class="modalwindow">
            <p>{{payType=='0'?'充值':'缴费'}}提醒将发送给：</p>
            <div class="modalwindow_table">
                <pgrid-multifunction id="'reminder'">
                    <panel datasource="checkedTenantContactArr" checkbox="false" ></panel>
                    <header>
                        <column name="租户名称" source="tenantName"></column>
                        <column name="联系人" source="contactName"></column>
                        <column name="手机号" source="contactMobile"></column>
                    </header>


                </pgrid-multifunction>
            </div>
            <div class="modalwindow_button">
                <pbutton-blue text=" 确定发送" click="tenantCtrl.sendMsgBat" id="send_msg_bat_ok"></pbutton-blue>
                <pbutton-white text=" 取消"  click="staticEvent.modal2hide" id="send_msg_bat_cancel"></pbutton-white>
            </div>
        </div>
    </script>

        <!--缴费电 缴费首页 -->
    <script type="text/html" id="pay">
        <div class="pay_the_fees_post_paid font_color_dark_gray">
            <div>
                <div>
                    <h3>应缴总计：</h3>
                    <p class="font_size_big font_color_blue">
                        {{tenantCtrl.numberFormat(payBillBat_selTenant.noPayBillsTotalMoney,tenantCtrl.fixType_money,true)}}
                        <span>元</span>
                    </p>
                </div>
                <div>
                    <h3>租户编号：</h3>
                    <p class="font_color_black">{{payBillBat_selTenant.tenantId}}</p>
                </div>
                <div>
                    <h3>租户名称：</h3>
                    <p class="font_color_black">{{payBillBat_selTenant.tenantName}}</p>
                </div>
                <div class="prev_bills">
                    <h3>能耗类型：</h3>
                    <p class="font_color_black">{{energyTypeName}}</p>
                </div>
                <div class="bills">
                    <div  v-for="(bill,index) in payBillBat_selTenant.noPayBillArr">
                        <h3>账单
                            <em v-text="index+1"></em>：</h3>
                        <p class="font_color_black">{{bill.orderTime}}</p>
                        <h3>应缴费用：</h3>
                        <p class="font_color_black">{{tenantCtrl.numberFormat(bill.money,tenantCtrl.fixType_money,true)}}元</p>
                    </div>
                </div>
            </div>
            <div class="btns">
                <div class="clearfix">
                    <div>
                        <pbutton-blue text="缴费" click="staticEvent.payTheFeesLodingEvent('bat_single')"></pbutton-blue>
                    </div>
                    <div>
                        <pbutton-white text="取消" click="staticEvent.payhide"></pbutton-white>
                    </div>
                </div>
            </div>
        </div>
    </script>
    
    <!--缴费电 缴费中  -->
    <script type="text/html" id="payIn">
        <div style="width: 460px; height: 567px;">
            <ploading-part id="partLoading" text="缴费中..."></ploading-part>
            <p class="loading_title" style="color: #02a9d1;">请勿关闭浏览器，稍后请仔细核对</p>
        </div>
    </script>

    <!--缴费电 缴费成功 -->
    <script type="text/html" id="paySuccess">
         <div class="pay_the_fees_success font_color_dark_gray">
            <div>
                <div>
                    <h3>应缴总计：</h3>
                    <p class="font_size_big font_color_green">
                        {{tenantCtrl.numberFormat(payBillBat_selTenant.noPayBillsTotalMoney,tenantCtrl.fixType_money,true)}}
                        <span>元</span>
                    </p>
                </div>
                <div>
                    <h3>租户编号：</h3>
                    <p class="font_color_black">{{payBillBat_selTenant.tenantId}}</p>
                </div>
                <div>
                    <h3>租户名称：</h3>
                    <p class="font_color_black">{{payBillBat_selTenant.tenantName}}</p>
                </div>
                <div>
                    <h3>能耗类型：</h3>
                    <p class="font_color_black">{{energyTypeName}}</p>
                </div>
                <div class="bills">
                    <div v-for="(bill,index) in payBillBat_selTenant.noPayBillArr">
                        <h3>账单
                            <em v-text="index+1"></em>：</h3>
                        <p class="font_color_black">{{bill.orderTime}}</p>
                        <h3>应缴费用：</h3>
                        <p class="font_color_black">{{tenantCtrl.numberFormat(bill.money,tenantCtrl.fixType_money,true)}}元</p>
                    </div>
                </div>
            </div>
            <div class="btns btn_1">
                <div class="clearfix">
                    <div>
                        <pbutton-blue text="关闭" click="staticEvent.paySuccessHide"></pbutton-blue>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <!--缴费电 缴费失败 -->
    <script type="text/html" id="payError">
         <div class="pay_the_fees_error font_color_dark_gray">
            <div>
                <div>
                    <h3>应缴总计：</h3>
                    <p class="font_size_big font_color_red">
                        {{tenantCtrl.numberFormat(payBillBat_selTenant.noPayBillsTotalMoney,tenantCtrl.fixType_money,true)}}
                        <span>元</span>
                    </p>
                </div>
                <div>
                    <h3>租户编号：</h3>
                    <p class="font_color_black">{{payBillBat_selTenant.tenantId}}</p>
                </div>
                <div>
                    <h3>租户名称：</h3>
                    <p class="font_color_black">{{payBillBat_selTenant.tenantName}}</p>
                </div>
                <div>
                    <h3>能耗类型：</h3>
                    <p class="font_color_black">{{energyTypeName}}</p>
                </div>

                <div class="bills">
                    <div v-for="(bill,index) in payBillBat_selTenant.noPayBillArr">
                        <h3>账单
                            <em v-text="index+1"></em>：</h3>
                        <p class="font_color_black">{{bill.orderTime}}</p>
                        <h3>应缴费用：</h3>
                        <p class="font_color_black">{{tenantCtrl.numberFormat(bill.money,tenantCtrl.fixType_money,true)}}元</p>
                    </div>
                </div>
            </div>
            <div class="btns">
                <div class="clearfix">
                    <div>
                        <pbutton-blue text="重新缴费" click="staticEvent.payInshow"></pbutton-blue>
                    </div>
                    <div>
                        <pbutton-white text="取消" click="staticEvent.payErrorHide"></pbutton-white>
                    </div>
                </div>
            </div>
        </div>

    </script>

    <!--缴费电 全部缴费 -->
    <script type="text/html" id="payBoth">
        <div style="width: 520px; height: 260px;">
            <div class="payBoth">
                <div class="payBoth_top">
                    <div>
                        <p>应缴总计：</p>
                        <p id="colorBlue"><span class="textlarge">{{tenantCtrl.numberFormat(bat_total_noPayMoney,tenantCtrl.fixType_money,true)}}</span>元</p>
                    </div>
                </div>
                <div class="payBoth_bottom">
                    <div>
                        租户数：{{bat_total_noPayTenantCount}}
                    </div>
                    <div>
                        账单数：{{bat_total_noPayBillCount}}
                    </div>
                </div>
            </div>
            <div class="payBoth_button">
                <pbutton-blue text="缴费" id="payBoth_button1" click="staticEvent.payBothOk"></pbutton-blue>
                <pbutton-white text="取消" id="payBoth_button2" click="staticEvent.payBothHide">
                </pbutton-white>
            </div>
        </div>
    </script>

        <!--缴费电 全部缴费中 -->
    <script type="text/html" id="payBothIn">
        <div style="width: 520px; height: 300px;">
            <ploading-part id="partLoading" text="缴费中..."></ploading-part>
            <p class="loading_title" style="color: #02a9d1;">请勿关闭浏览器，稍后请仔细核对</p>
        </div>
    </script>

    <!--缴费电 全部缴费 成功 -->
    <script type="text/html" id="payBothSucc">
        <div style="width: 520px; height: 260px;">
            <div class="payBoth">
                <div class="payBoth_top">
                    <div>
                        <p>应缴总计：</p>
                        <p id="colorGreen"><span class="textlarge">{{tenantCtrl.numberFormat(bat_total_noPayMoney,tenantCtrl.fixType_money,true)}}</span>元</p>
                    </div>
                </div>
                <div class="payBoth_bottom">
                    <div>
                        租户数：{{bat_total_noPayTenantCount}}
                    </div>
                    <div>
                        账单数：{{bat_total_noPayBillCount}}
                    </div>
                </div>
            </div>
            <div class="payBoth_button">
                <pbutton-blue text="关闭" id="payBothSucc_button1" click="tenantCtrl.payBill_bat_afterSuccessOk"></pbutton-blue>
            </div>
        </div>
    </script>

    <!--缴费电 全部缴费 失败 -->
    <script type="text/html" id="payBothErr">
        <div style="width: 520px; height: 260px;">
            <div class="payBoth">
                <div class="payBoth_top">
                    <div>
                        <p>应缴总计：</p>
                        <p id="colorRed"><span class="textlarge">{{tenantCtrl.numberFormat(bat_total_noPayMoney,tenantCtrl.fixType_money,true)}}</span>元</p>
                    </div>
                </div>
                <div class="payBoth_bottom">
                    <div>
                        租户数：{{bat_total_noPayTenantCount}}
                    </div>
                    <div>
                        账单数：{{bat_total_noPayBillCount}}
                    </div>
                </div>
                <div class="payErr_text">
                    <i class="per-notice_failureicon"></i>缴费失败，请重新缴费
                </div>
            </div>

            <div class="payBoth_button">
                <pbutton-blue text="重新缴费" id="payBothErr_button1" click="staticEvent.payBothOk"></pbutton-blue>
                &nbsp;&nbsp;&nbsp;&nbsp;
                <pbutton-white text="取消" id="payBothErr_button2" click="staticEvent.payBothErrorHide">
                </pbutton-white>
            </div>
        </div>
    </script>

    <!--结算  账单明细  -->
    <script type="text/html" id="bill_detail">
        <div class="billCon">
            <div class="billCon_title">
                <div>
                    <p class="colorGray">租户编号：</p>
                    <p>{{settleDetail.tenantId}}</p>
                </div>
                <div>
                    <p class="colorGray">租户名称：</p>
                    <p>{{settleDetail.tenantName}}</p>
                </div>
            </div>
            <div class="bill">
                <div class="bill_top">
                    <ul>
                        <li class="inline">
                            <div style="width: 118px;">日期</div>
                            <div style="width: 154px;">当日耗{{settleDetail.energyTypeName}}量({{settleDetail.energyUnit}})</div>
                            <div style="width: 140px;">累计耗能({{settleDetail.energyUnit}})</div>
                            <div style="width: 112px;">累计金额(元)</div>
                        </li>
                    </ul>
                </div>
                <div class="bill_con">
                    <ul>
                        <li class="inline" v-for="detail in settleDetail.dataList">
                            <div style="width: 118px;">{{(detail.time+"").substring(0,10)}}</div>
                            <div style="width: 154px;">{{tenantCtrl.numberFormat(detail.dayEnergy,tenantCtrl.fixType_dynamic,true)}}</div>
                            <div style="width: 140px;">{{tenantCtrl.numberFormat(detail.totalEnergy,tenantCtrl.fixType_dynamic,true)}}</div>
                            <div style="width: 111px;">{{tenantCtrl.numberFormat(detail.totalMoney,tenantCtrl.fixType_money,true)}}</div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="bill_detail_loading_box">
            <ploading-part id="bill_detail_loading" text="加载中，请稍后..."></ploading-part>
        </div>
        <div v-show="settleDetail.dataList.length == 0">
            <pnotice-nodata text="暂无数据" subtitle="请重新加载"></pnotice-nodata>
        </div>
    </script>

    <!-- vue组件详情页右侧 -->
    <!-- 后付费 -->
    <script type="text/html" id="post_paid_tpl">
        <div class="activate_later">
            <!-- 账单信息容器 -->
            <div class="bills_girds">
                <!-- 账单信息 -->
                <div class="meter_meter_bills_girds_item">
                    <div class="bills_girds_item" v-for="order in payContent.orderList">
                        <div class="bills_girds_title clearfix">
                            <p>账单：{{(order.orderTime+"").replace(/-/g,'.')}}</p>
                            <p>账单编号：{{order.orderId}}</p>
                        </div>
                        <div class="meter_meter_bills_girds_content">
                            <div class="meter_meter_bills_girds_content_title">
                                <div class="meter_meter_energy_this_time">
                                    本期结算能耗（{{order.amountUnit}}）
                                </div>
                                <div class="meter_meter_operation">
                                    本期结算金额（元）
                                </div>
                                <div class="meter_meter_energy_this_time" v-if="operationPermissions.PostPay">
                                    操作
                                </div>
                            </div>
                            <div>
                                <div class="meter_meter_bills_girds_content_con">
                                    <div class="meter_meter_energy_this_time_con">
                                        {{tenantCtrl.numberFormat(order.amount,tenantCtrl.fixType_dynamic,true)}}
                                    </div>
                                    <div class="meter_meter_operation_con font_color_red">
                                        {{tenantCtrl.numberFormat(order.money,tenantCtrl.fixType_money,true)}}
                                    </div>
                                    <div class="meter_meter_energy_this_time_con font_color_dark_blue" v-if="operationPermissions.PostPay">
                                        <span @click="staticEvent.payTheFeesPostPaidEvent(energyTypeId,order)">缴费</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- 下载和查看欠费账单 -->
            <div class="download_and_checked_bills clearfix">
                <p class="download_bills" v-show="payContent.orderList.length>0" @click="tenantCtrl.goNoPayBillPage(1, energyTypeId)"><span>欠费账单</span></p>
                <p class="checked_bills" @click="tenantCtrl.goHistoryBillPage(energyTypeId)"><span>查看历史账单</span></p>
            </div>

            <!-- 当前价格方案和未结算金额 -->
            <div class="price_and_money clearfix">
                <div class="price">当前价格方案：<span @click.stop="tenantCtrl.showDetailPriceContent(energyTypeId,priceTemplate.id)">{{priceTemplate.name}}</span><span>（{{priceTemplate.type==0?'平均':'分时'}}）</span></div>
                <div class="money">未结算金额：{{tenantCtrl.numberFormat(payContent.weiJieSuanJinE,tenantCtrl.fixType_money,true)}}元</div>
            </div>

            <!-- 结算按钮 -->
            <div class="close_account" v-if="operationPermissions.PostPayBilling">
                <pbutton-white text="结算" @click="tenantCtrl.goTenantSettlePage(payContent.lastClearingTime,energyTypeId,1)"></pbutton-white>
            </div>

            <!-- 应缴总计金额 -->
            <div class="pay_all_money" v-show="payContent.orderList.length>0">应缴总计：<span class="font_color_red">{{tenantCtrl.numberFormat(payContent.yingJiaoJinE,tenantCtrl.fixType_money,true)}}</span> 元</div>

            <!-- 缴费操作按钮 -->
            <div class="pay_btns clearfix">
                <div class="merge_pay" v-show="payContent.orderList.length>0" @click="staticEvent.payTheFeesPostPaidEvent_combine(energyTypeId,payContent.orderList,payContent.yingJiaoJinE)" v-if="operationPermissions.PostPay">
                    <pbutton-blue text="合并缴费"></pbutton-blue>
                </div>
                <div class="send_pay_tip" v-show="payContent.orderList.length>0" v-if="operationPermissions.SendMessage">
                    <pbutton-white text="发送缴费提醒" @click="staticEvent.sendMassageToMaster(energyTypeId,1)"></pbutton-white>
                </div>
                <div class="check_record">
                    <div @click="tenantCtrl.goFeeRecordPage(energyTypeId, 1)"><span>缴费记录</span></div>
                </div>
            </div>
        </div>
    </script>
    
    <!-- 预付费表充表扣 -->
    <script type="text/html" id="meter_meter_tpl">
        <div class="activate_later">
            <!-- 账单信息容器 -->
            <div class="bills_girds">
                <!-- 账单信息 -->
                <div class="meter_meter_bills_girds_item">
                    <div class="meter_meter_bills_girds_content">
                        <div class="meter_meter_bills_girds_content_title">
                            <div class="meter_meter_energy_this_time">
                                仪表ID
                            </div>
                            <div class="meter_meter_operation">
                                {{payContent.remainType}}
                            </div>
                            <div class="meter_meter_energy_this_time bor_l_none">
                                剩余时间（天）
                            </div>
                        </div>
                        <div>
                            <div class="meter_meter_bills_girds_content_con" v-for="meter in payContent.meterList">
                                <div class="meter_meter_energy_this_time_con">
                                    {{meter.meterId}}
                                </div>
                                <div class="meter_meter_operation_con" :class="{'font_color_red':meter.isAlarm}">
                                    {{ tenantCtrl.numberFormat(meter.remainData,payContent.remainType=='剩余金额（元）'?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                                </div>
                                <div class="meter_meter_energy_this_time_con" :class="{'font_color_red':meter.isAlarm}">
                                    {{meter.remainDays ? meter.remainDays : '--'}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 当前价格方案和未结算金额 -->
            <div class="price_and_money clearfix">
                <div class="price">当前价格方案：<span @click.stop="tenantCtrl.showDetailPriceContent(energyTypeId,priceTemplate.id)">{{priceTemplate.name}}</span><span>（{{priceTemplate.type==0?'平均':'分时'}} ）</span></div>
            </div>

            <!-- 缴费操作按钮 -->
            <div class="pay_btns clearfix">
                <div class="send_pay_tip">
                    <pbutton-white text="发送充值提醒" @click="staticEvent.sendMassageToMaster(energyTypeId,0)"></pbutton-white>
                </div>
            </div>
        </div>
    </script>
    <!-- 预付费软件冲表扣 -->
    <script type="text/html" id="software_meter_tpl">
        <div class="activate_later">
            <!-- 账单信息容器 -->
            <div class="bills_girds">
                <!-- 账单信息 -->
                <div class="meter_meter_bills_girds_item">
                    <div class="meter_meter_bills_girds_content">
                        <div class="meter_meter_bills_girds_content_title">
                            <div class="meter_meter_energy_this_time">
                                仪表ID
                            </div>
                            <div class="meter_meter_operation">
                                {{payContent.remainType}}
                            </div>
                            <div class="meter_meter_energy_this_time bor_l_none">
                                剩余时间（天）
                            </div>
                            <div class="meter_meter_operation bor_l_none"  v-if="operationPermissions.PrePay||operationPermissions.Return">
                                操作
                            </div>
                        </div>
                        <div>
                            <div class="meter_meter_bills_girds_content_con" v-for="meter in payContent.meterList">
                                <div class="meter_meter_energy_this_time_con">
                                    {{meter.meterId}}
                                </div>
                                <div class="meter_meter_operation_con" :class="{'font_color_red':meter.isAlarm}">
                                    {{tenantCtrl.numberFormat(meter.remainData,payContent.remainType=='剩余金额（元）'?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                                </div>
                                <div class="meter_meter_energy_this_time_con" :class="{'font_color_red':meter.isAlarm}">
                                    {{meter.remainDays ? meter.remainDays : '--'}}
                                </div>
                                <div class="meter_meter_operation_con" v-if="operationPermissions.PrePay||operationPermissions.Return">
                                    <span class="font_color_dark_blue" @click="staticEvent.topUpRechargeMeterEvent(energyTypeId,1,meter.meterId)" v-if="operationPermissions.PrePay">充值</span>
                                    <span class="font_color_dark_blue" @click="staticEvent.showRefundCostMeterEvent(energyTypeId,1,meter.meterId)" v-if="operationPermissions.Return">退费</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 当前价格方案和未结算金额 -->
            <div class="price_and_money clearfix">
                <div class="price">当前价格方案：<span @click.stop="tenantCtrl.showDetailPriceContent(energyTypeId,priceTemplate.id)">{{priceTemplate.name}}</span><span>（{{priceTemplate.type==0?'平均':'分时'}}）</span></div>
            </div>

            <!-- 缴费操作按钮 -->
            <div class="pay_btns clearfix">
                <div class="send_pay_tip" v-if="operationPermissions.SendMessage">
                    <pbutton-white text="发送充值提醒" @click="staticEvent.sendMassageToMaster(energyTypeId,0)"></pbutton-white>
                </div>
                <!-- ++ -->
                <div class="refund_record">
                    <div @click="tenantCtrl.goRemainingAmountReportPage(energyTypeId, 1,1)"><span>退费记录</span></div>
                </div>
                <div class="check_record">
                    <div @click="tenantCtrl.goRechargeRecordPage(energyTypeId,1)"><span>充值记录</span></div>
                </div>
            </div>
        </div>
    </script>

    <!-- 预付费软件冲软件扣 -->
    <script type="text/html" id="software_software_tpl">
        <div class="activate_later">
            <!-- 账单信息容器 -->
            <div class="bills_girds">
                <!-- 账单信息 -->
                <div class="meter_meter_bills_girds_item">
                    <div class="meter_meter_bills_girds_content">
                        <div class="meter_meter_bills_girds_content_title">
                            <div class="meter_meter_energy_this_time">
                                {{payContent.remainType}}
                            </div>
                            <div class="meter_meter_operation">
                                剩余天数（天）
                            </div>
                            <div class="meter_meter_energy_this_time bor_l_none" v-if="operationPermissions.PrePay||operationPermissions.Return">
                                操作
                            </div>
                        </div>
                        <div>
                            <div class="meter_meter_bills_girds_content_con">
                                <div class="meter_meter_energy_this_time_con" :class="{'font_color_red':status==2}">
                                    {{tenantCtrl.numberFormat(payContent.remainData,payContent.remainType=='剩余金额（元）'?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                                </div>
                                <div class="meter_meter_operation_con" :class="{'font_color_red':status==2}">
                                    {{payContent.remainDays ? payContent.remainDays : '--'}}
                                </div>
                                <div class="meter_meter_energy_this_time_con font_color_dark_blue" v-if="operationPermissions.PrePay||operationPermissions.Return">
                                    <span @click="staticEvent.topUpRechargeSoftwareEvent(energyTypeId, 2)" v-if="operationPermissions.PrePay">充值</span>
                                    <span @click="staticEvent.showRefundCostSoftwareEvent(energyTypeId,2)" v-if="operationPermissions.Return">退费</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 当前价格方案和未结算金额 -->
            <div class="price_and_money clearfix">
                <div class="price">当前价格方案：<span @click.stop="tenantCtrl.showDetailPriceContent(energyTypeId,priceTemplate.id)">{{priceTemplate.name}}</span><span>（{{priceTemplate.type==0?'平均':'分时'}}）</span></div>
            </div>

            <!-- 缴费操作按钮 -->
            <div class="pay_btns clearfix">
                <div class="send_pay_tip" v-if="operationPermissions.SendMessage">
                    <pbutton-white text="发送充值提醒" @click="staticEvent.sendMassageToMaster(energyTypeId,0)"></pbutton-white>
                </div>
                <div class="refund_record">
                    <div @click="tenantCtrl.goRemainingAmountReportPage(energyTypeId, 1,2)"><span>退费记录</span></div>
                </div>
                <div class="check_record">
                    <div @click="tenantCtrl.goRechargeRecordPage(energyTypeId,1)"><span>充值记录</span></div>
                </div>
            </div>
        </div>
    </script>

    <script type="text/html" id="componentstep">
        <div class="step">
            <ul :class="'activestep'+stepArr.length+'_'+step">
                <li v-for="(item,index) in stepArr">
                    <div :style="{'fontFamily':step>(index+1)?'perficon':''}" v-text="step>(index+1)?'Z':(index+1)"></div>
                    <h2 :class="step===(index+1)?'active':''">{{item}}</h2>
                </li>
            </ul>
        </div>
    </script>

    <!-- 全局加载 $('#gloadLoading').pshow(); $('#gloadLoading').phide();  随用随调-->
    <ploading-global id="gloadLoading"></ploading-global>
    <!-- 局部加载 $('#partLoading').pshow(); $('#partLoading').phide();  随用随调-->
    <ploading-part id="partLoadingCom" text="加载中，请稍后..."></ploading-part>

<iframe id="printf" src="" width="0" height="0" frameborder="0"></iframe>
</body>
</html>
