<!-- 缴费电 -->
<div style="height: 100%;" v-if="currentPage=='pay'">
    <!--头部  -->
    <div class="pay_title">
        <div>
            <pbutton-white text="返回" id="pay_title_button1" click="ldp.backToTemenListPage"></pbutton-white>
        </div>
        <div id="pay_title_text">
            缴费-{{energyTypeName}}
        </div>
        <div>
            <pbutton-white text="缴费记录" id="pay_title_button2" onclick="tenantCtrl.goFeeRecordPage_bat(3)"></pbutton-white>
        </div>
    </div>
    <!--表格  -->
    <div class="pay_table">
        <!--表头  -->
        <div class="pay_table_top">
            <ul>
                <li class="inline">
                    <div class="payname slh">租户名称</div>
                    <div class="paynum slh">租户编号</div>
                    <div class="paynum slh">房间编号</div>
                    <div class="inline pay_table_con">
                        <div class="slh">账单</div>
                        <div class="slh">账单号</div>
                        <div class="slh">结算金额(元)</div>
                        <div class="slh">结算能耗({{bat_energyUnit}})</div>
                        <div class="slh">操作人</div>
                    </div>
                    <div class="payoper slh">未缴金额总计(元)</div>
                    <div class="payoper slh">操作</div>
                </li>
            </ul>
        </div>
        <!--表格内容  -->
        <div class="pay_table_body">
            <ul>
                <li class="inline" v-for="model in checkedTenantArr">
                    <div class="payname slh">{{model.tenantName}}</div>
                    <div class="paynum slh">{{model.tenantId}}</div>
                    <div class="paynum slh">{{model.roomIds}}</div>
                    <div class="pay_table_body_right">
                        <div class="inline pay_table_con" v-for="bill in model.noPayBillArr">
                            <div class="slh">{{bill.orderTime}}</div>
                            <div class="slh">{{bill.orderId}}</div>
                            <div class="slh">{{tenantCtrl.numberFormat(bill.money,tenantCtrl.fixType_money,true)}}</div>
                            <div class="slh">{{tenantCtrl.numberFormat(bill.amount,tenantCtrl.fixType_dynamic,true)}}</div>
                            <div class="slh">{{bill.userName}}</div>
                        </div>
                    </div>
                    <div class="payoper slh">{{tenantCtrl.numberFormat(model.noPayBillsTotalMoney,tenantCtrl.fixType_money,true)}}</div>
                    <div class="payoper slh" style="cursor:pointer;" :pdisabled="(model.noPayBill_Ready||model.noPayBillArr.length==0)">
                        <!--中弹出缴费-->
                        <pbutton-blue text="(model.noPayBill_Ready||model.noPayBillArr.length==0)?'已缴清':'缴费'" bind="true" isborder="false" click="staticEvent.payshow"></pbutton-blue>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <!--尾部  -->
    <div class="pay_floot">
        <pbutton-blue text="完成" click="ldp.backToTemenListPage" v-show="bat_total_noPayBillCount==0"></pbutton-blue>
        <pbutton-blue text="全部缴费" click="staticEvent.payBothShow" v-show="bat_total_noPayBillCount>0"></pbutton-blue>
    </div>
</div>
