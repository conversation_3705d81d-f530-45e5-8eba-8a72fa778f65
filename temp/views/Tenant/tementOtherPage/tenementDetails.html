<div class="t_d_box" v-show="currentPage == 'tenementDetails'">
    <!-- 操作栏 -->
    <div class="t_d_header clearfix">
        <!-- 返回按钮 -->
        <div class="t_d_go_back">
            <pbutton-white text="返回" click="ldp.backToTemenListPage"></pbutton-white>
        </div>
        <!-- 退租按钮 -->
        <div class="t_d_surrender" v-show="selTenant.tenantStatus==1" v-if="operationPermissions.LeaveTenant">
            <pbutton-borderred text="退租" click="tenantCtrl.goLeaveTenantPage(1)"></pbutton-borderred>
        </div>
        <!-- 删除按钮 -->
        <div class="t_d_delete" v-show="selTenant.tenantStatus==0">
            <pbutton-borderred text="删除" click="staticEvent.deleteLesseeBtn"></pbutton-borderred>
        </div>
        <!-- 激活按钮 -->
        <div class="t_d_activate" v-show="selTenant.tenantStatus==0" v-if="operationPermissions.ActiveTenant">
            <pbutton-blue text="激活" click="staticEvent.startLesseeBtn"></pbutton-blue>
        </div>
        <!-- 标题 -->
        <h1>{{selTenant.tenantName}}</h1>
    </div>
    <!-- 租户详情 -->
    <div class="t_d_content">
        <!-- 左侧盒模型 -->
        <div class="t_d_content_info">
            <!-- 租户信息标题及状态 -->
            <div class="t_d_content_info_title clearfix">
                <h2>租户信息</h2>
                <div v-if="selTenant.tenantStatus!=2 && operationPermissions.EditTenant"
                    onclick="ldp.editTementshow(event)">
                    <span class="edit_icon"><i>`</i>编辑租户</span>
                </div>
                <span class="status"
                    :class="{'no_activate':selTenant.tenantStatus==0,'activate':selTenant.tenantStatus==1,'surrender':selTenant.tenantStatus==2}">{{selTenant.tenantStatus==0?'未激活':(selTenant.tenantStatus==1?'已激活':'已退租')}}</span>
            </div>
            <!-- 租户详细信息容器 -->
            <div class="t_d_content_info_box">
                <!-- 租户详细信息 -->
                <div class="t_d_content_info_box_details" style="position: relative">
                    <pswitch-slide id="switch1" style="position: absolute;top:40px;left: 150px;" click="ldp.switchClick"
                        v-if="selTenant.tenantStatus!=2"></pswitch-slide>
                    <p>租户全码：{{selTenant.tenantFlag}}</p>
                    <p v-if="selTenant.tenantStatus!=2">是否启用微信充值：</p>
                    <p>租户编号：{{selTenant.tenantId}}</p>
                    <p>租户名称：{{selTenant.tenantName}}</p>
                    <p>所属建筑：{{selTenant.buildingName}}</p>
                    <p>租户面积：{{selTenant.area}}m²</p>
                    <p>所属业态：{{selTenant.tenantTypeName||"--"}}</p>
                    <p v-if="selTenant.tenantStatus==1">
                        激活时间：{{(selTenant.activeTime||"--").substring(0,10).replace(/-/g,'.')}}</p>
                    <p v-if="selTenant.tenantStatus==2">
                        退租时间：{{(selTenant.leaveTime||"--").substring(0,10).replace(/-/g,'.')}}</p>
                </div>

                <!-- 租户联系方式 -->
                <div class="t_d_content_info_box_person">
                    <div>
                        <p>联系人：{{selTenant.contactName}}</p>
                        <p>联系电话：{{selTenant.contactMobile}}</p>
                    </div>
                    <div v-for="contact in selTenant.contactList">
                        <p>联系人：{{contact.contactName}}</p>
                        <p>联系电话：{{contact.contactMobile}}</p>
                    </div>
                </div>

                <!-- 房间及表具信息表格 -->
                <div class="t_d_content_info_box_gird">
                    <!-- 表格标题 -->
                    <div class="t_d_content_info_box_gird_title clearfix">
                        <h3>房间及表具信息</h3>
                        <div v-show="selTenant.tenantStatus!=2" onclick="tenantCtrl.goMeterChangeRecordPage()">
                            <span>换表记录</span>
                        </div>
                        <div v-if="selTenant.tenantStatus!=2 && operationPermissions.MeterSet"
                            onclick="tenantCtrl.goSetRecord()">
                            <span>仪表设置记录</span>
                        </div>
                        <div v-show="selTenant.tenantStatus!=0&&selTenant.tenantStatus!=2"
                            onclick="staticEvent.gaugeRecordSingleTenantBtn('tenementDetails')">
                            <span>表底数记录</span>
                        </div>
                    </div>
                    <div class="t_d_content_info_box_gird_all">
                        <!-- 表头 -->
                        <div class="t_d_content_info_box_gird_head">
                            <div>
                                <div>房间</div>
                                <div>仪表能耗类型</div>
                                <div>表具</div>
                            </div>
                        </div>

                        <!-- 表格内容 -->
                        <div class="t_d_content_info_box_gird_body">
                            <!-- 无表具信息时显示 -->
                            <div class="t_d_content_info_box_gird_body_item" v-if="selTenant.roomList.length == 0">
                                <div class="room">--</div>
                                <div class="energy_and_meter_box">
                                    <div class="energy_and_meter">
                                        <div class="energy">--</div>
                                        <div class="meter">
                                            <div class="meter_item"
                                                :class="{'meter_other':selTenant.tenantStatus != 0}">
                                                <p :pdisabled="selTenant.tenantStatus == 0">--</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 每个房间的表具信息 -->
                            <div class="t_d_content_info_box_gird_body_item" v-for="room in selTenant.roomList">
                                <div class="room" :title="room.roomCode.length>8?room.roomCode:''"
                                    v-text="room.roomCode.length>8?((room.roomCode+'').substr(0,6)+'...'):room.roomCode">
                                </div>
                                <div class="energy_and_meter_box">
                                    <div class="energy_and_meter" v-for="energy in room.energyList">
                                        <div class="energy">{{energy.energyTypeName}}</div>
                                        <div class="meter">
                                            <div class="meter_item" :class="{'meter_other':selTenant.tenantStatus != 0}"
                                                v-for="meterId in energy.meterList">
                                                <p :pdisabled="selTenant.tenantStatus == 0"
                                                    @click="tenantCtrl.goMeterDetailPage(energy.energyTypeId,energy.energyTypeName,meterId)">
                                                    {{meterId}}</p>
                                                <span class="set_meter"
                                                    v-if="selTenant.tenantStatus!=2 && operationPermissions.MeterSet"
                                                    @click.stop="instrumentSetEvent(room.roomCode,energy.energyTypeName,meterId,energy.energyTypeId)">设置</span>
                                                <span class="change_meter"
                                                    v-if="selTenant.tenantStatus!=2&&operationPermissions.MeterChange"
                                                    @click.stop="changeMeterEvent(energy.energyTypeId,energy.energyTypeName,meterId)">换表</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧盒模型 -->
        <div class="t_d_content_energy">
            <!-- 能耗使用信息及状态 -->
            <div class="t_d_content_energy_title clearfix">
                <h2>能耗使用信息</h2>
                <div v-if="selTenant.tenantStatus!=0" @click="tenantCtrl.goEnergyCostReport(1)"><span>能耗费用报表 ></span>
                </div>
                <div v-if="selTenant.tenantStatus!=0&&selTenant.tenantStatus!=2&&selTenant.ishasPrepayment&&operationPermissions.AlarmSet"
                    @click="customAlarmChecked" data-flag="all"><span>自定义报警设置</span></div>
            </div>
            <div class="t_d_content_energy_box">
                <!-- 右侧每个能耗类型的信息 v-for -->
                <div class="t_d_content_energy_item" v-for="model in selTenant.energyList">
                    <!-- 缴费状态图标 -->
                    <i v-if="selTenant.tenantStatus==1"
                        :class="[model.status==0?'no_icon':(model.status==1?'wait_pay_icon':(model.status == 2 && ((model.prePayType == 0 && model.prePay_0.remainType == '剩余金额（元）') || (model.prePayType == 1 && model.prePay_1.remainType == '剩余金额（元）') || (model.prePayType == 2 && model.prePay_2.remainType == '剩余金额（元）')) ? 'not_sufficient_icon_money' : 'not_sufficient_icon_account'))]"></i>
                    <div class="t_d_content_energy_item_title clearfix">
                        <h3>{{model.typeName}}</h3>
                        <!-- 已激活状态下存在 -->
                        <p v-if="selTenant.tenantStatus==1&&model.energySplitExpression">
                            拆分公式：{{model.energySplitExpression}}</p>
                    </div>
                    <div class="t_d_content_energy_item_box">
                        <!-- 未激活内容 -->
                        <div class="before_no_activate" v-if="selTenant.tenantStatus==0">
                            <!-- 未激活提示 -->
                            <div class="no_activate_tip">租户还未激活，暂无能耗使用信息</div>
                            <!-- 当前价格方案 -->
                            <div class="price_and_money clearfix">
                                <div class="price">
                                    当前价格方案：
                                    <span
                                        @click.stop="tenantCtrl.showDetailPriceContent(model.energyTypeId,model.priceTemplate.id)">{{model.priceTemplate.name}}</span>
                                    <span>（{{model.priceTemplate.type==0?'平均':'分时'}}）</span>
                                </div>
                            </div>
                        </div>
                        <!-- 已激活内容 -->
                        <component v-if="selTenant.tenantStatus==1"
                            :is="model.payType==1?'post-paid':(model.prePayType==0?'meter-meter':(model.prePayType==1?'software-meter':'software-software'))"
                            :price-template="model.priceTemplate"
                            :pay-content="model.payType==1?model.postPay:(model.prePayType==0?model.prePay_0:(model.prePayType==1?model.prePay_1:model.prePay_2))"
                            :status="model.status" :energy-type-id="model.energyTypeId"
                            :operation-permissions="operationPermissions"></component>
                        <!-- 后付费账单 -->
                        <!-- 退租后内容 -->
                        <div class="surrender_later" v-if="selTenant.tenantStatus==2">
                            <!-- 累计消耗 -->
                            <div class="add_up clearfix">
                                <p class="add_up_consumption">
                                    累计消耗量：{{tenantCtrl.numberFormat(model.totalEnergy,tenantCtrl.fixType_dynamic,true)}}{{model.energyUnit}}
                                </p>
                                <p class="add_up_money">
                                    累计金额：{{tenantCtrl.numberFormat(model.totalMoney,tenantCtrl.fixType_money,true)}}元
                                </p>
                            </div>
                            <!-- 查看缴费记录 -->
                            <div class="check_payment_records" v-if="model.payType==0"
                                @click="tenantCtrl.goRechargeRecordPage(model.energyTypeId,1)">
                                <span>充值记录</span>
                            </div>
                            <div class="check_payment_records" v-if="model.payType!=0"
                                @click="tenantCtrl.goFeeRecordPage(model.energyTypeId,1)">
                                <span>缴费记录</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 自定义报警门限 -->
        <div class="police_box_all_ele per-modal-mask" v-show="customAlarmPanelIsShow">
            <div class="police_box">
                <div class="police">
                    <div class="plice_radio clearfix">
                        <div>
                            <pswitch-radio id="custom_radio" text="使用自定义报警" name="mask"
                                change="staticEvent.customAlarmTypeEvent" data-flag="custom"></pswitch-radio>
                        </div>
                        <div>
                            <pswitch-radio id="global_radio" text="使用全局报警" name="mask"
                                change="staticEvent.globalAlarmTypeEvent" data-flag="global"></pswitch-radio>
                        </div>
                    </div>
                    <div>
                        <div class="plice_sel">
                            <!-- 20180606wp++ -->
                            <div v-for="(model, index) in alarmSearchData.typeList" :key="index"
                                v-if="model.typeId != 'ZHBJ_16'" style="position: relative;">
                                <div class="police_tit">
                                    <span v-if="model.type=='Dian'">电费用不足</span>
                                    <span v-if="model.type=='Shui'">水费用不足</span>
                                    <span v-if="model.type=='ReShui'">热水费用不足</span>
                                    <span v-if="model.type=='RanQi'">燃气费用不足</span>
                                    <span v-if="model.type=='fuHeLv'">负荷率</span>
                                </div>
                                <div v-for="(model,index) in model.alarmList">
                                    <div class="police_con clearfix">
                                        <div style="min-width: 125px">{{model.typeName}}</div>
                                        <div class="police_con_mid">
                                            <ptext-text id="model.typeId" bind="true" disabled="!model.valid"
                                                value="model.limit" blur="staticEvent.validationAlarmValue">
                                                <verify errtip="报警门限值不能为空" verifytype="space"></verify>
                                                <verify errtip="格式不正确，请填写正整数" verifytype="int"></verify>
                                                <verify errtip="不能超过3位数，请重新填写" verifytype="length" length="3"></verify>
                                            </ptext-text>
                                        </div>
                                        <div>{{model.unit}}报警</div>
                                        <div class="btn">
                                            <pswitch-slide change="" state="model.valid" bind="true"></pswitch-slide>
                                        </div>
                                    </div>
                                </div>
                                <div class="cover" style="position: absolute;top:0;left:0;width: 100%;height:100%"
                                    v-show="alarmSearchData.isRealGlobal"></div>
                            </div>
                        </div>
                    </div>
                    <div class="plice_ope clearfix">
                        <pbutton-blue text="保存" click="staticEvent.policeDetermine" id="determine_custom"
                            data-flag="custom"></pbutton-blue>
                        <pbutton-white text="取消" id="cancel_custom" @click="closeCustomAlarmChecked"></pbutton-white>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 控件库标签 -->
    <div class="control_tag">
        <!-- 激活租户按钮弹出框 -->
        <div class="start_lessee_btn_box">
            <pwindow-modal id="start_lessee_btn" title="激活" templateid="start_lessee_tpl"></pwindow-modal>
        </div>

        <!-- 删除租户按钮弹出框 -->
        <pwindow-confirm id="delete_lessee_btn">
            <button>
                <pbutton-backred text="保存" click="tenantCtrl.confirmDeleteTenant"></pbutton-backred>
                <pbutton-white text="取消" click="staticEvent.deleteLesseeBtnHide"></pbutton-white>
            </button>
        </pwindow-confirm>

        <!-- 发送缴费提醒弹出框 -->
        <pwindow-modal id="send_massage_btn" title="发送缴费提醒" templateid="send_massage_tpl"></pwindow-modal>

        <!-- 充值 -->
        <!-- 预付费软件充表扣 -->
        <div class="top_up_recharge_meter_box">
            <pwindow-modal id="top_up_recharge_meter" title="充值" templateid="top_up_recharge_meter_tpl"></pwindow-modal>
        </div>

        <!-- 预付费软件充表扣返回界面 -->
        <div class="top_up_recharge_meter_back_box">
            <pwindow-modal id="top_up_recharge_meter_back" title="充值" templateid="top_up_recharge_meter_back_tpl">
            </pwindow-modal>
        </div>

        <!-- 预付费软件充软件扣 -->
        <div class="top_up_recharge_software_box">
            <pwindow-modal id="top_up_recharge_software" title="充值" templateid="top_up_recharge_software_tpl">
            </pwindow-modal>
        </div>
        <!-- 预付费软件充软件扣充值成功 -->
        <div class="top_up_recharge_software_success_box">
            <pwindow-modal id="top_up_recharge_software_success" title="充值成功"
                templateid="top_up_recharge_software_success_tpl"></pwindow-modal>
        </div>
        <!-- 预付费软件充软件扣充值失败 -->
        <div class="top_up_recharge_software_error_box">
            <pwindow-modal id="top_up_recharge_software_error" title="充值失败，请重新充值"
                templateid="top_up_recharge_software_error_tpl"></pwindow-modal>
        </div>

        <!-- 预付费充值中 -->
        <div class="top_up_recharge_loading_box">
            <pwindow-modal id="top_up_recharge_loading" title="充值中" templateid="top_up_recharge_loading_tpl">
            </pwindow-modal>
        </div>



        <!-- 预付费软件充软件扣退费 -->
        <div class="refund_cost_box">
            <pwindow-modal id="refund_cost_software" title="退费" templateid="refund_cost_tpl"></pwindow-modal>
        </div>

        <!-- 预付费软件充表扣退费++ -->
        <div class="refund_cost_meter_box">
            <pwindow-modal id="refund_cost_meter" title="退费" templateid="refund_cost_meter_tpl"></pwindow-modal>
        </div>

        <!-- 预付费软件充软件扣退费成功 -->
        <div class="refund_cost_success_box">
            <pwindow-modal id="refund_cost_success" title="退费成功" templateid="refund_cost_success_tpl"></pwindow-modal>
        </div>
        <!-- 预付费软件充仪表扣退费成功++ -->
        <div class="refund_cost_meter_success_box">
            <pwindow-modal id="refund_cost_meter_success" title="退费成功" templateid="refund_cost_meter_success_tpl">
            </pwindow-modal>
        </div>
        <!-- 预付费软件充软件扣退费失败 -->
        <div class="refund_cost_error_box">
            <pwindow-modal id="refund_cost_error" title="退费失败，请重新退费" templateid="refund_cost_error_tpl"></pwindow-modal>
        </div>
        <!-- 预付费软件充仪表扣退费失败++ -->
        <div class="refund_cost_meter_error_box">
            <pwindow-modal id="refund_cost_meter_error" title="退费失败，请重新退费" templateid="refund_cost_meter_error_tpl">
            </pwindow-modal>
        </div>

        <!-- 预付费退费中 -->
        <div class="refund_cost_loading_box">
            <pwindow-modal id="refund_cost_loading" title="退费中" templateid="refund_cost_loading_tpl"></pwindow-modal>
        </div>

        <!-- 点击换表侧弹窗 -->
        <pwindow-float id="right_side_change_meter_window" isshade="true" title="这是标题"
            templateid="right_side_window_tpl">
            <animate maxpx="0" minpx="-830" orientation="right"></animate>
        </pwindow-float>

    </div>

    <div class="control_tpl">
        <!-- 模板 -->
        <!-- 租户详情点击发送缴费信息提醒弹窗模板 -->
        <script type="text/html" id="send_massage_tpl">
            <div class="send_message_confirm">
                <p v-if="meterList.length>0" style="color:#333">
                    选择表：
                </p>
                <ul id = "allMeter">
                    <li  v-for="model in meterList" style="display:inline-block;margin:0 15px;">
                        <pswitch-radio class="chechMeterId" text="model.meterId" name="'mask'" change="staticEvent.selMeterId" bind='true'></pswitch-radio>
                    </li>
                </ul>
                <!-- <span v-for="model in meterList">{{model?model.meterId:''}}</span> -->

                <p>
                    <em id="pay_type_name">缴费</em>提醒短信将发送给：
                </p>
                <p>{{selTenant.tenantName}}的联系人{{selTenant.contactName}}（{{selTenant.contactMobile}}）</p>
                <div class="clearfix">
                    <div class="go_send">
                        <pbutton-blue text="确定发送" click="staticEvent.sendMassageYes"></pbutton-blue>
                    </div>
                    <div class="cancel_send">
                        <pbutton-white text="取消" click="staticEvent.sendMassageHide"></pbutton-white>
                    </div>
                </div>
            </div>
        </script>

        <!-- 租户详情点击激活按钮弹窗模板 -->
        <script type="text/html" id="start_lessee_tpl">
            <div class="start_lessee_confirm">
                <div class="start_lessee_text">
                    <p>
                        激活时间：
                        <span>（该租户将于当日00:00被激活）</span>
                    </p>
                    <div class="choose_date_controller">
                        <ptime-form id="start_lessee_time" sel="staticEvent.proofStartLesseeTime">
                            <panel timetype="yMd" startyear="2017"></panel>
                        </ptime-form>
                    </div>
                    <div class="t_d_choose_date_error_msg">
                        <p class="t_d_choose_date_error_msg_lt">
                            <i></i>
                            选择的时间必须晚于今天
                        </p>
                    </div>
                </div>
                <div class="start_lessee_btns clearfix">
                    <div class="go_start">
                        <pbutton-blue text="确定激活" click="staticEvent.startLesseeYes" data-flag="1"></pbutton-blue>
                    </div>
                    <div class="cancel_start" onclick="staticEvent.startLesseeHide()">
                        <pbutton-white text="取消"></pbutton-white>
                    </div>
                </div>
            </div>
        </script>


        <!-- 租户详情点击换表按钮弹窗模板 -->
        <script type="text/html" id="right_side_window_tpl">
            <div class="right_side_change_meter_window">
                <div class="right_side_change_meter_window_box">
                    <div class="meter_number">
                        <h3>表具编号：</h3>
                        <p>{{changeMeter.meterId}}</p>
                    </div>
                    <div class="energy_types">
                        <h3>能源类型：</h3>
                        <p>{{changeMeter.energyTypeName}}</p>
                    </div>
                    <div class="change_metter_time">
                        <h3>换表时间：</h3>
                        <div class="choose_date_controller">
                            <ptime-form id="change_metter_time">
                                <panel timetype="yMdhm" startyear="2012"></panel>
                            </ptime-form>
                        </div>
                    </div>
                    <div>
                        <div class="change_metter_text">
                            <h3>仪表读数：</h3>
                            <!--电表读数  -->
                            <div>
                                <ul class="change_meter_number">
                                    <li v-for="model in changeMeter.functionList">
                                        <div>{{ model.functionName }}</div>
                                        <div>
                                            <div >
                                                <ptext-text placeholder="" text="model.unit" bind="true" id="'_' + model.functionId" blur="staticEvent.isMeterOk(model.functionId)">
                                                    <!-- <verify errtip="不可为空"  verifytype="space"></verify>  -->
                                                    <!-- <verify errtip="请输入正数" verifytype="positivenumber"></verify> -->
                                                </ptext-text>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <!--错误信息提示  -->
                            <div class="change_metter_prompt">
                                <p class="prompt_con" v-show="changeMeter.isMeter">
                                    <i class="prompt_icon">i</i>
                                    <span>
                                        仪表读数不能为空,且输入必须为正数
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="change_meter_btn">
                    <div class="change_meter_btn_box">
                        <pbutton-blue text="确定" click="tenantCtrl.changeMeter"></pbutton-blue>
                    </div>
                </div>
            </div>
        </script>


        <!-- 预付费充值 -->
        <!-- 预付费软件充表扣 -->
        <script type="text/html" id="top_up_recharge_meter_tpl">
            <div class="top_up_recharge font_color_dark_gray" style="position:relative">
                <div>
                    <div style="display:flex;">
                        <h3>仪表ID：</h3>
                        <p class="font_color_black">{{selTenant.recharge.meterId}}</p>
                    </div>
                    <div>
                        <h3 class="refresh_title">
                            剩余{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：
                            <i class="font_color_dark_blue refresh" @click="tenantCtrl.refreshRechargeData(1)"></i>
                            <span class="font_color_gray">（{{selTenant.recharge.refreshTime}} 刷新）</span>
                        </h3>
                        <p style="margin-bottom:0">提醒：请认真查看并记录本数据，用于下一步校对</p>
                        <p class="font_size_big font_color_blue">
                            {{tenantCtrl.numberFormat(selTenant.recharge.remainData,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{selTenant.recharge.dataUnit}}</span>
                        </p>
                    </div>
                    <div>
                        <h3>充值{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：</h3>
                        <div class="ipt" style="margin-bottom:20px">
                            <ptext-text bind="true" placeholder="'请输入'+(selTenant.recharge.billingType == 1?'金额':(selTenant.recharge.energyTypeName+'量'))" text="selTenant.recharge.dataUnit"
                                        id="'charge_soft_meter'" blur="staticEvent.textBlurEvent(model, event, 'recharge_soft_meter_btn')">
                                <verify errtip="不可为空" verifytype="space"></verify>
                                <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                            </ptext-text>
                        </div>
                        <div v-if="selTenant.recharge.isBeiDian">
                            <!-- wp++ -->
                            <p>当前累计退费金额为<span style="color:red;margin-bottom:0">
                                {{tenantCtrl.numberFormat(selTenant.recharge.returnData,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}</span>元</p>
                            <p>实际充值金额=充值金额-退费金额</p>
                            <p>购电次数：<span style="color:red;margin-bottom:0">{{selTenant.recharge.saleCount}}</span></p>
                        </div>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>能耗类型：</h3>
                        <p class="font_color_black">{{selTenant.recharge.typeName}}</p>
                    </div>
                </div>
                <div class="btns">
                    <div class="clearfix">
                        <div>
                            <pbutton-blue id="recharge_soft_meter_btn" text="充值" click="staticEvent.recharge_soft_meter('charge_soft_meter')"></pbutton-blue>
                        </div>
                        <div>
                            <pbutton-white text="取消" click="staticEvent.topUpRechargeMeterCloseEvent"></pbutton-white>
                        </div>
                    </div>
                </div>
                <!-- 局部loading -->
                <ploading-part id="rechargeLoadingMeter" text="加载中，请稍后..."></ploading-part>
            </div>
        </script>

        <!-- 预付费软件充表扣返回界面 -->
        <script type="text/html" id="top_up_recharge_meter_back_tpl">
            <div class="top_up_recharge font_color_dark_gray" style="position:relative">
                <div class="top_up_recharge_has_tip">
                    <div>
                        <h3>仪表ID：</h3>
                        <p class="font_color_black">{{selTenant.recharge.meterId}}</p>
                    </div>
                    <div>
                        <h3>原始剩余{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：</h3>
                        <p>
                            {{tenantCtrl.numberFormat(oldRemainData,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{selTenant.recharge.dataUnit}}</span>
                        </p>
                    </div>
                    <div v-if="selTenant.recharge.isBeiDian">
                        <h3>实际充值{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：</h3>
                        <p>
                            {{tenantCtrl.numberFormat(selTenant.recharge.realPayData,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{selTenant.recharge.dataUnit}}</span>
                        </p>
                    </div>
                    <div v-else>
                        <h3>充值{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：</h3>
                        <p>
                            {{tenantCtrl.numberFormat(selTenant.recharge.rechargeValue,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{selTenant.recharge.dataUnit}}</span>
                        </p>
                    </div>
                    <div v-if="selTenant.recharge.isBeiDian">
                        <p>
                            本次充值退费金额为<span style="color:red;margin-bottom:0">{{tenantCtrl.numberFormat(selTenant.recharge.deductData,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}</span>
                            <span>{{selTenant.recharge.dataUnit}}</span>
                        </p>
                        <p>实际充值金额=充值金额-退费金额</p>
                    </div>
                    <div>
                        <h3 class="refresh_title">
                            当前剩余{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：
                            <i class="font_color_dark_blue refresh" @click="tenantCtrl.refreshRechargeData(1)"></i>
                            <span class="font_color_gray">（{{selTenant.recharge.refreshTime}} 刷新）</span>
                        </h3>
                        <p class="font_size_big font_color_blue">
                            {{tenantCtrl.numberFormat(selTenant.recharge.remainData,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{selTenant.recharge.dataUnit}}</span>
                        </p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>能耗类型：</h3>
                        <p class="font_color_black">{{selTenant.recharge.typeName}}</p>
                    </div>
                </div>
                <div class="btns" style="position:relative">
                    <div class="clearfix">
                        <div>
                            <pbutton-blue text="充值成功" @click="tenantCtrl.saveRecharge_soft_meter(selTenant)"></pbutton-blue>
                        </div>
                        <div class="print">
                            <pbutton-white icon="W" text="打印充值凭证" @click="ldp.printHtml('#top_up_recharge_meter_back',1)"></pbutton-white>
                        </div>
                        <div>
                            <pbutton-white text="充值失败" click="staticEvent.topUpRechargeMeterBackCloseEvent"></pbutton-white>
                        </div>

                    </div>
                </div>
                <div class="top_up_recharge_tip font_color_orange">
                    <p>点击充值失败将不会生成充值记录</p>
                </div>
                <!-- 局部loading -->
                <ploading-part id="rechargeLoadingMeterSuccess" text="更新数据中，请稍后..."></ploading-part>
            </div>
        </script>

        <!-- 预付费软件充软件扣 -->
        <script type="text/html" id="top_up_recharge_software_tpl">
            <div class="top_up_recharge font_color_dark_gray" style="position:relative">
                <div>
                    <div>
                        <h3 class="refresh_title">
                            剩余{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：
                            <i class="font_color_dark_blue refresh" @click="tenantCtrl.refreshRechargeData(1)"></i>
                            <span class="font_color_gray">（{{selTenant.recharge.refreshTime}} 刷新）</span>
                        </h3>
                        <p class="font_size_big font_color_blue">
                            {{tenantCtrl.numberFormat(selTenant.recharge.remainData,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{selTenant.recharge.dataUnit}}</span>
                        </p>
                        <p v-show="selTenant.recharge.weiDaoZhangCount != 0" class="special_tips font_color_blue">您有{{selTenant.recharge.weiDaoZhangCount}}个充值请求正在处理中，请在15分钟后查询剩余{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}</p>
                    </div>
                    <div>
                        <h3>充值{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：</h3>
                        <div class="ipt">
                            <ptext-text bind="true" placeholder="'请输入'+(selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量'))" text="selTenant.recharge.dataUnit" id="'charge_soft_soft'" blur="staticEvent.textBlurEvent(model, event, 'recharge_soft_soft_btn')">
                                <verify errtip="不可为空" verifytype="space"></verify>
                                <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                            </ptext-text>
                        </div>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div  style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                    <div  style="display:flex;">
                        <h3>能耗类型：</h3>
                        <p class="font_color_black">{{selTenant.recharge.typeName}}</p>
                    </div>
                </div>
                <div class="btns">
                    <div class="clearfix">
                        <div>
                            <pbutton-blue id="recharge_soft_soft_btn" text="充值" click="staticEvent.recharge_soft_soft('charge_soft_soft')"></pbutton-blue>
                        </div>
                        <div>
                            <pbutton-white text="取消" click="staticEvent.topUpRechargeSoftwareCloseEvent"></pbutton-white>
                        </div>
                    </div>
                </div>
                <!-- 局部loading -->
                <ploading-part id="rechargeLoadingSoft" text="加载中，请稍后..."></ploading-part>
            </div>
        </script>

        <!-- 预付费软件充软件扣充值成功 -->
        <script type="text/html" id="top_up_recharge_software_success_tpl">
            <div class="top_up_recharge_success font_color_dark_gray">
                <div>
                    <div>
                        <h3>原始剩余{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：</h3>
                        <p>
                            {{tenantCtrl.numberFormat(oldRemainData,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{selTenant.recharge.dataUnit}}</span>
                        </p>
                    </div>
                    <div>
                        <h3>本次充值{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：</h3>
                        <p class="font_size_big font_color_green">
                            {{tenantCtrl.numberFormat(selTenant.recharge.rechargeValue,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>
                                <span>{{selTenant.recharge.dataUnit}}</span>
                            </span>
                        </p>
                        <p class="font_color_gray ptext">下次扣费时将会更新表内剩余{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}，您可以在15分钟后刷新租户详情页面获取新的剩余{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>能耗类型：</h3>
                        <p class="font_color_black">{{selTenant.recharge.typeName}}</p>
                    </div>
                </div>
                <div class="btns btn_1" style="position:relative">
                    <div class="clearfix">
                        <div class="print">
                            <pbutton-white icon="W" text="打印充值凭证" @click="ldp.printHtml('#top_up_recharge_software_success .per-modal-custom',1)"></pbutton-white>
                        </div>
                        <div>
                            <pbutton-blue text="关闭" click="staticEvent.topUpRechargeSoftwareSuccessCloseEvent"></pbutton-blue>
                        </div>
                    </div>
                </div>
            </div>
        </script>

        <!-- 预付费软件充软件扣充值失败-->
        <script type="text/html" id="top_up_recharge_software_error_tpl">
            <div class="top_up_recharge_error font_color_dark_gray">
                <div>
                    <div>
                        <h3>原始剩余{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：</h3>
                        <p>
                            {{tenantCtrl.numberFormat(oldRemainData,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{selTenant.recharge.dataUnit}}
                        </p>
                    </div>
                    <div>
                        <h3>本次充值{{selTenant.recharge.billingType==1?'金额':(selTenant.recharge.energyTypeName+'量')}}：</h3>
                        <p class="font_size_big font_color_red">
                            0.00
                            <span>{{selTenant.recharge.dataUnit}}</span>
                        </p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>能耗类型：</h3>
                        <p class="font_color_black">{{selTenant.recharge.typeName}}</p>
                    </div>
                </div>
                <div class="btns">
                    <div class="clearfix">
                        <div>
                            <pbutton-blue text="重新充值" click="staticEvent.retryRecharge_soft_soft"></pbutton-blue>
                        </div>
                        <div>
                            <pbutton-white text="取消" click="staticEvent.topUpRechargeSoftwareErrorCloseEvent"></pbutton-white>
                        </div>
                    </div>
                </div>
            </div>
        </script>

        <!-- 充值中 -->
        <script type="text/html" id="top_up_recharge_loading_tpl">
            <div class="top_up_recharge_loading">
                <ploading-part id="partLoading_in_top_up_recharge" text="充值中..."></ploading-part>
                <p class="font_color_dark_gray">请勿关闭浏览器，否则将没有充值记录稍后请仔细核对表内余额</p>
            </div>
        </script>

        <!-- 退费 -->
        <!-- 预付费软件充软件扣 -->
        <script type="text/html" id="refund_cost_tpl">
            <div class="top_up_recharge font_color_dark_gray" style="positon:relative">
                <div>
                    <div>
                        <h3 class="refresh_title">
                            剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName||'--'+'量')}}：
                            <i class="font_color_dark_blue refresh" @click="tenantCtrl.refreshRechargeData(2)"></i>
                            <span class="font_color_gray">（{{beforeRefundCostData.lastUpdateTime}} 刷新）</span>
                        </h3>
                        <p class="font_size_big font_color_blue">
                            {{tenantCtrl.numberFormat(beforeRefundCostData.remainData,beforeRefundCostData.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                        <p v-show="beforeRefundCostData.weiDaoZhangCount != 0" class="special_tips font_color_blue">您有{{beforeRefundCostData.weiDaoZhangCount}}个退费请求正在处理中，请在15分钟后查询剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName||'--'+'量')}}</p>
                    </div>
                    <div>
                        <h3>退费{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName||'--'+'量')}}：</h3>
                        <div class="ipt">
                            <ptext-text bind="true" placeholder="'请输入'+(beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName||'--'+'量'))" text="beforeRefundCostData.billingTypeUnit"
                                        id="'refund_soft_soft'" blur="staticEvent.textBlurEvent(model, event, 'refund_cost_soft_btn')"  class="refundAmount">
                                <verify errtip="不可为空" verifytype="space"></verify>
                                <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                            </ptext-text>
                            <p class="allAmount" @click="staticEvent.allAmount">全部金额</p>
                        </div>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>能耗类型：</h3>
                        <p class="font_color_black">{{beforeRefundCostData.typeName}}</p>
                    </div>
                </div>
                <div class="btns">
                    <div class="clearfix">
                        <div>
                            <pbutton-blue id="refund_cost_soft_btn" text="退费" click="staticEvent.refund_cost_soft('refund_soft_soft')"></pbutton-blue>
                        </div>
                        <div>
                            <pbutton-white text="取消" click="staticEvent.hideRefundCostSoftwareEvent"></pbutton-white>
                        </div>
                    </div>
                </div>
                <!-- 局部loading -->
                <ploading-part id="partLoadingSoft" text="加载中，请稍后..."></ploading-part>
            </div>
        </script>

        <!-- 预付费软件充仪表扣 -->
        <script type="text/html" id="refund_cost_meter_tpl">
            <div class="top_up_recharge font_color_dark_gray" style="positon:relative">
                <div>
                    <div style="display:flex;">
                        <h3>仪表ID：</h3>
                        <p class="font_color_black">{{beforeRefundCostData.meterId}}</p>
                    </div>
                    <div>
                        <h3 class="refresh_title">
                            剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName||'--'+'量')}}：
                            <i class="font_color_dark_blue refresh" @click="tenantCtrl.refreshRechargeData(1,beforeRefundCostData.meterId)"></i>
                            <span class="font_color_gray">（{{beforeRefundCostData.lastUpdateTime}} 刷新）</span>
                        </h3>
                        <p class="font_size_big font_color_blue">
                            {{tenantCtrl.numberFormat(beforeRefundCostData.remainData,beforeRefundCostData.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                    </div>
                    <!-- wp++ -->
                    <div v-if="beforeRefundCostData.isBeiDian">
                        <p>当前累计退费金额为<span style="color:red">{{tenantCtrl.numberFormat(beforeRefundCostData.returnData,beforeRefundCostData.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}</span>元</p>
                    </div>
                    <div>
                        <h3>退费{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName||'--'+'量')}}：</h3>
                        <div class="ipt">
                            <ptext-text bind="true" placeholder="'请输入'+(beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName||'--'+'量'))" text="beforeRefundCostData.billingTypeUnit"
                                        id="'refund_meter_meter'" blur="staticEvent.textBlurEvent(model, event, 'refund_cost_soft_btn')" class="refundAmount">
                                <verify errtip="不可为空" verifytype="space"></verify>
                                <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                            </ptext-text>
                            <p class="allAmount"  @click="staticEvent.allAmount">全部金额</p>
                        </div>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                </div>
                <div class="btns">
                    <div class="clearfix">
                        <div  id="refundButton">
                            <pbutton-blue id="refund_cost_soft_btn" text="退费" click="staticEvent.refund_cost_meter('refund_meter_meter')"></pbutton-blue>
                        </div>
                        <div>
                            <pbutton-white text="取消" click="staticEvent.hideRefundCostMeterEvent"></pbutton-white>
                        </div>
                    </div>
                </div>
                <!-- 局部loading -->
                <ploading-part id="partLoadingMeter" text="加载中，请稍后..."></ploading-part>
            </div>

        </script>

        <!-- 预付费软件充软件扣退费成功 -->
        <script type="text/html" id="refund_cost_success_tpl">
            <div class="top_up_recharge_success font_color_dark_gray">
                <div>
                    <div>
                        <h3>原始剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}：</h3>
                        <p>
                            {{tenantCtrl.numberFormat(beforeRefundCostData.oldValue,selTenant.recharge.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                    </div>
                    <div>
                        <h3>本次退费{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}：</h3>
                        <p class="font_size_big font_color_green">
                            {{beforeRefundCostData.refundValue}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                        <p class="font_color_gray ptext">下次扣费时将会更新表内剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}，您可以在15分钟后刷新租户详情页面获取新的剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>能耗类型：</h3>
                        <p class="font_color_black">{{beforeRefundCostData.typeName}}</p>
                    </div>
                </div>
                <div class="btns btn_1" style="position:relative">
                    <div class="clearfix">
                        <div class="print">
                            <pbutton-white icon="W" text="打印退费凭证" @click="ldp.printHtml('#refund_cost_success',1)"></pbutton-white>
                        </div>
                        <div>
                            <pbutton-blue text="关闭" click="staticEvent.hideRefundCostSuccessEvent"></pbutton-blue>
                        </div>
                    </div>
                </div>
            </div>
        </script>
        <!-- 预付费软件充仪表扣退费成功++ -->
        <script type="text/html" id="refund_cost_meter_success_tpl">
            <div class="top_up_recharge_success font_color_dark_gray" style="positon:relative">
                <div>
                    <div>
                        <h3>仪表ID：</h3>
                        <!-- <p class="font_color_black">{{beforeRefundCostData.meterId}}</p> -->
                        <p class="font_color_black">{{selTenant.recharge.meterId}}</p>
                    </div>
                    <div>
                        <h3>原始剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}：</h3>
                        <p>
                            {{tenantCtrl.numberFormat(beforeRefundCostData.oldValue,beforeRefundCostData.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                    </div>
                    <div>
                        <h3>本次退费{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}：</h3>
                        <p class="font_size_big font_color_green">
                            {{beforeRefundCostData.refundValue}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                        <h3 class="refresh_title">
                            剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName||'--'+'量')}}：
                            <i class="font_color_dark_blue refresh" @click="tenantCtrl.refreshRechargeData(1,beforeRefundCostData.meterId)"></i>
                            <span class="font_color_gray">（{{beforeRefundCostData.lastUpdateTime}} 刷新）</span>
                        </h3>
                        <p>
                            {{tenantCtrl.numberFormat(beforeRefundCostData.remainData,beforeRefundCostData.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                </div>

                <div class="btns" style="position:relative">
                    <div class="clearfix">
                        <div>
                            <pbutton-blue id="return_success" text="退费成功" @click="staticEvent.meterReturnPaySaveService()"></pbutton-blue>
                        </div>
                        <!-- 20180601wp -->
                        <div class="print">
                            <pbutton-white icon="W" text="打印退费凭证" @click="ldp.printHtml('#refund_cost_meter_success',1)"></pbutton-white>
                        </div>
                        <div>
                            <pbutton-white text="退费失败" @click="staticEvent.hideRefundCostMeterSuccessEvent"></pbutton-white>
                        </div>

                    </div>
                </div>
                <!-- 局部loading -->
                <ploading-part id="refundLoadingMeterSuccess" text="加载中，请稍后..."></ploading-part>
            </div>
        </script>

        <!-- 预付费软件充软件扣退费失败-->
        <script type="text/html" id="refund_cost_error_tpl">
            <div class="top_up_recharge_error font_color_dark_gray">
                <div>
                    <div>
                        <h3>原始剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}：</h3>
                        <p>
                            {{tenantCtrl.numberFormat(beforeRefundCostData.remainData,beforeRefundCostData.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                    </div>
                    <div>
                        <h3>本次退费{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}：</h3>
                        <p class="font_size_big font_color_red">
                            {{beforeRefundCostData.refundValue}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>能耗类型：</h3>
                        <p class="font_color_black">{{beforeRefundCostData.typeName}}</p>
                    </div>
                </div>
                <div class="btns">
                    <div class="clearfix">
                        <div>
                            <pbutton-blue text="重新退费" click="staticEvent.refund_cost_soft('refund_soft_soft')"></pbutton-blue>
                        </div>
                        <div>
                            <pbutton-white text="取消" click="staticEvent.hideRefundCostErrorEvent"></pbutton-white>
                        </div>
                    </div>
                </div>
            </div>
        </script>

        <!-- 预付费软件充仪表扣退费失败++-->
        <script type="text/html" id="refund_cost_meter_error_tpl">
            <div class="top_up_recharge_error font_color_dark_gray">
                <div>
                    <div>
                        <h3>原始剩余{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}：</h3>
                        <p>
                            {{tenantCtrl.numberFormat(beforeRefundCostData.remainData,beforeRefundCostData.billingType==1?tenantCtrl.fixType_money:tenantCtrl.fixType_dynamic,true)}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                    </div>
                    <div>
                        <h3>本次退费{{beforeRefundCostData.billingType==1?'金额':(beforeRefundCostData.energyTypeName+'量')}}：</h3>
                        <p class="font_size_big font_color_red">
                            {{beforeRefundCostData.refundValue}}
                            <span>{{beforeRefundCostData.billingTypeUnit}}</span>
                        </p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户编号：</h3>
                        <p class="font_color_black">{{selTenant.tenantId}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>租户名称：</h3>
                        <p class="font_color_black">{{selTenant.tenantName}}</p>
                    </div>
                    <div style="display:flex;">
                        <h3>能耗类型：</h3>
                        <p class="font_color_black">{{beforeRefundCostData.typeName}}</p>
                    </div>
                </div>
                <div class="btns">
                    <div class="clearfix">
                        <div>
                            <pbutton-blue text="重新退费" click="staticEvent.refund_cost_meter('refund_meter_meter')"></pbutton-blue>
                        </div>
                        <div>
                            <pbutton-white text="取消" click="staticEvent.hideRefundCostErrorEvent"></pbutton-white>
                        </div>
                    </div>
                </div>
            </div>
        </script>

        <!-- 退费中 -->
        <script type="text/html" id="refund_cost_loading_tpl">
            <div class="top_up_recharge_loading">
                <ploading-part id="partLoading_in_refund_cost" text="退费中..."></ploading-part>
                <p class="font_color_dark_gray">请勿关闭浏览器，否则将没有退费记录稍后请仔细核对表内余额</p>
            </div>
        </script>

        <!-- 查询中 暂时不用-->
        <!-- <script type="text/html" id="search_cost_loading_tpl">
            <div class="top_up_recharge_loading">
                <ploading-part id="partLoading_in_refund_cost" text="查询中..."></ploading-part>
            </div>
        </script> -->
    </div>

</div>