<!--充值记录电软件充软件和 -->
<div class="recharge" style="height: 100%" v-show="currentPage=='recharge'">
    <!--头部  -->
    <div class="recharge_title">
        <div>
            <pbutton-white text="返回" id="recharge_title_button1" click="ldp.backToTemenListPage"></pbutton-white>
        </div>
        <div class="recharge_title_title">
            充值记录-{{energyTypeName}}-软充软扣
        </div>
        <div>
            <pbutton-white text="下载充值记录" icon="S" id="recharge_title_button2"
                click="tenantCtrl.getRechargeRecord_bat(true, 'recharge_bat_calendar')"></pbutton-white>
            <pbutton-white text="'打印充值记录'" icon="'S'" click="ldp.printHtml('.recharge_table',3)" bind="true">
            </pbutton-white>
        </div>
    </div>

    <!--时间栏  -->
    <div class="recharge_time">
        <ptime-calendar id="recharge_bat_calendar" sel="tenantCtrl.getRechargeRecord_bat_changeTime">
            <panel timetype="dwMy" startyear="2015" align="left"></panel>
        </ptime-calendar>
    </div>

    <!--表格部分  -->
    <div class="recharge_table">
        <!--表头  -->
        <div class="recharge_table_top">
            <ul>
                <li class="inline">
                    <div class="recharge_table_name">租户名称</div>
                    <div class="recharge_table_num">租户编号</div>
                    <div class="recharge_table_home">房间编号</div>
                    <div class="inline recharge_table_con">
                        <div class="recharge_table_con_time">充值时间</div>
                        <div class="recharge_table_con_num">充值单号</div>
                        <div class="recharge_table_con_money">充值金额(元)</div>
                        <div class="recharge_table_con_money">充值{{energyTypeName}}量({{bat_energyUnit}})</div>
                        <div class="recharge_table_con_money">操作人</div>
                    </div>
                </li>
            </ul>
        </div>
        <!--表格内容部分  -->
        <div class="recharge_table_body">
            <ul>
                <template v-for="tenant in prepaidRecordsSofterSoftData.tenantList">
                    <li class="inline">
                        <div class="recharge_table_name">{{tenant.tenantName}}</div>
                        <div class="recharge_table_num">{{tenant.tenantId}}</div>
                        <div class="recharge_table_home">{{tenant.roomIds}}</div>
                        <div class="recharge_table_body_right">
                            <div class="inline recharge_table_con" v-show="tenant.orderList == 0">
                                <div class="recharge_table_con_time">--</div>
                                <div class="recharge_table_con_num">--</div>
                                <div class="recharge_table_con_money">--</div>
                                <div class="recharge_table_con_money">--</div>
                                <div class="recharge_table_con_money">--</div>
                            </div>
                            <div class="inline recharge_table_con two" v-for="record in tenant.orderList"
                                v-hide="tenant.rechargeRecordList_bat == 0">
                                <div class="recharge_table_con_time">{{record.payTime}}</div>
                                <div class="recharge_table_con_num">{{record.orderId}}</div>
                                <div class="recharge_table_con_money">
                                    {{tenantCtrl.numberFormat(record.money,tenantCtrl.fixType_money,true)}}</div>
                                <div class="recharge_table_con_money">
                                    {{tenantCtrl.numberFormat(record.amount,tenantCtrl.fixType_dynamic,true)}}</div>
                                <div class="recharge_table_con_money">{{record.userName}}</div>
                            </div>
                        </div>
                    </li>
                    <li class="inline">
                        <div class="recharge_table_name">{{tenant.tenantName}}合计充值</div>
                        <div class="recharge_table_num"></div>
                        <div class="recharge_table_home"></div>
                        <div class="recharge_table_body_right">
                            <div class="inline recharge_table_con">
                                <div class="recharge_table_con_time"></div>
                                <div class="recharge_table_con_num"></div>
                                <div class="recharge_table_con_money">
                                    {{tenantCtrl.numberFormat(tenant.tenantMoneySum,tenantCtrl.fixType_dynamic,true)}}
                                </div>
                                <div class="recharge_table_con_money">
                                    {{tenantCtrl.numberFormat(tenant.tenantAmountSum,tenantCtrl.fixType_dynamic,true)}}
                                </div>
                                <div class="recharge_table_con_money"></div>
                            </div>
                        </div>
                    </li>
                </template>
                <li class="inline">
                    <div class="recharge_table_name">所选租户合计充值</div>
                    <div class="recharge_table_num"></div>
                    <div class="recharge_table_home"></div>
                    <div class="recharge_table_body_right">
                        <div class="inline recharge_table_con">
                            <div class="recharge_table_con_time"></div>
                            <div class="recharge_table_con_num"></div>
                            <div class="recharge_table_con_money">
                                {{tenantCtrl.numberFormat(prepaidRecordsSofterSoftData.moneySum,tenantCtrl.fixType_dynamic,true)}}
                            </div>
                            <div class="recharge_table_con_money">
                                {{tenantCtrl.numberFormat(prepaidRecordsSofterSoftData.amountSum,tenantCtrl.fixType_dynamic,true)}}
                            </div>
                            <div class="recharge_table_con_money"></div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>