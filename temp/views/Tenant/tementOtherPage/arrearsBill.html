<!--欠费账单  -->
<div style="height: 100%" v-show="currentPage=='arrearsBill'">
    <!--头部  -->
    <div class="arrearsBill_title">
        <div>
            <pbutton-white text="返回" id="arrearsBill_title_button1" click="ldp.backToTemenListPage"></pbutton-white>
        </div>
        <div class="arrearsBill_title_title">
            欠费账单-{{energyTypeName}}
        </div>
        <div @click="tenantCtrl.getNoPayBillArr_bat(true)">
            <pbutton-white text="下载欠费账单" icon="D" id="arrearsBill_title_button2"></pbutton-white>
        </div>
    </div>
    <!--时间栏  -->
    <div class="arrearsBill_time" style="display: none;">
        <ptime-calendar timetype="yMd" id="arrears_bill_calendar"></ptime-calendar>
    </div>

    <!--表格部分  -->
    <div class="arrearsBill_grid">
        <!--表头  -->
        <div class="arrearsBill_grid_top">
            <ul>
                <li class="inline">
                    <div class="slh arrearsBillName">租户名称</div>
                    <div class="slh arrearsBillName">租户编号</div>
                    <div class="slh arrearsBillName">房间编号</div>
                    <div class="inline arrearsBill_grid_con">
                        <div class="slh">账单</div>
                        <div class="slh">账单号</div>
                        <div class="slh">结算能耗({{bat_energyUnit}})</div>
                        <div class="slh">结算金额(元)</div>
                        <div class="slh">操作人</div>
                    </div>
                    <div class="slh arrearsBillName">未缴金额总计(元)</div>
                </li>
            </ul>
        </div>
        <!--表格内容  -->
        <div class="arrearsBill_grid_main">
            <ul>
                <li class="inline" v-for="model in checkedTenantArr">
                    <div class="slh arrearsBillName">{{model.tenantName}}</div>
                    <div class="slh arrearsBillName">{{model.tenantId}}</div>
                    <div class="slh arrearsBillName">{{model.roomIds}}</div>
                    <div class="arrearsBill_grid_main_right">
                        <div class="inline arrearsBill_grid_con" v-for="bill in model.noPayBillArr">
                            <div class="slh">{{bill.orderTime}}</div>
                            <div class="slh">{{bill.orderId}}</div>
                            <div class="slh">{{tenantCtrl.numberFormat(bill.amount,tenantCtrl.fixType_dynamic,true)}}</div>
                            <div class="slh">{{tenantCtrl.numberFormat(bill.money,tenantCtrl.fixType_money,true)}}</div>
                            <div class="slh">{{bill.userName}}</div>
                        </div>
                    </div>
                    <div class="slh arrearsBillName">{{tenantCtrl.numberFormat(model.noPayBillsTotalMoney,tenantCtrl.fixType_money,true)}}</div>
                </li>
            </ul>
        </div>
    </div>
</div>
