<div class="s_t_box" v-show="currentPage == 'surrenderTenancy'">
    <!-- 操作栏 -->
    <div class="s_t_header clearfix">
        <!-- 取消按钮 -->
        <div class="s_t_cancel">
            <pbutton-white text="取消" @click="cancelSurrenderTenancy"></pbutton-white>
        </div>
        <!-- 标题 -->
        <h1>退租-{{selTenant.tenantName}}</h1>
    </div>
    <!-- 退租页内容     -->
    <div class="s_t_content">
        <!-- 退租中间内容 -->
        <div class="s_t_content_mid">
            <!-- 退租时间选择 -->
            <div class="s_t_choose_date">
                <h2 class="s_t_date_title">退租时间：<span class="s_t_date_tip">（系统将为您自动结算截至到该日期00:00的所有待缴账单）</span></h2>
                <div class="s_t_date_date_controller">
                    <ptime-form id="surrender_tenancy_time" sel="tenantCtrl.getLeaveTenantDetail">
                        <panel timetype="yMd" startyear="2013"></panel>
                    </ptime-form>
                    <div class="s_t_choose_date_error_msg">
                        <p class="s_t_choose_date_error_msg_bf"><i></i>选择的时间不得早于上一次结算的时间</p>
                        <p class="s_t_choose_date_error_msg_lt"><i></i>选择的时间不得晚于今天</p>
                    </div>
                </div>
            </div>

            <!-- 退租未缴费账单后付费 -->
            <div class="s_t_not_pay_list" v-if="selTenant.leaveContent.dataArr_Hou.length>0&&selTenant.leaveContent.totalCount_Hou>0">
                <h2 class="s_t_not_pay_list_title">未缴费账单：</h2>
                <div class="s_t_not_pay_list_content_box">
                    <div class="s_t_not_pay_list_content" v-for="energy in selTenant.leaveContent.dataArr_Hou" v-if="energy.meterCount>0||energy.orderCount>0">
                        <!-- 能耗类型和付费类型 -->
                        <h3 class="s_t_not_pay_list_content_title">{{energy.energyTypeName+'-后付费'}}</h3>
                        <div>
                            <!-- 未结算 -->
                            <div class="unbalanced_account" v-if="energy.meterCount>0">
                                <!-- 此处颜色可改 -->
                                <h4 class="font_color_orange">未结算</h4>
                                <div class="s_t_not_pay_list_content_sub">
                                    <div class="s_t_not_pay_list_content_sub_box" v-for="(meter,index_meter) in energy.postPay.meterList">
                                        <!-- 账单及账单编号 -->
                                        <div class="s_t_not_pay_list_content_info clearfix">
                                            <p>账单：{{meter.lastClearingTime}}</p>
                                        </div>
                                        <!-- 账单付费表格 -->
                                        <div class="s_t_not_pay_list_content_gird">
                                            <!-- 表头 -->
                                            <div class="s_t_not_pay_list_content_gird_title">
                                                <div>仪表ID</div>
                                                <div>上次读数</div>
                                                <div>本次读数</div>
                                                <div>读数差值</div>
                                                <div>应缴费用（元）</div>
                                            </div>
                                            <!-- 表格内容 -->
                                            <div class="s_t_not_pay_list_content_gird_box">
                                                <div>
                                                    <div>{{meter.meterId}}</div>
                                                </div>
                                                <div class="more more_no_edit">
                                                    <div v-for="func in meter.functionList"><em v-if="func.type!='L'">{{func.typeName}}：</em>{{tenantCtrl.numberFormat(func.lastBillingData,tenantCtrl.fixType_meter,false)}}</div>
                                                </div>
                                                <div class="more">
                                                    <div @mouseenter="mouseOnEdit($event)" @mouseleave="mouseOutEdit($event)" v-for="(func,index_fun) in meter.functionList">
                                                        <div data-flag="fa" @click="editMeterNum($event)">
                                                            <span v-if="func.type!='L'">{{func.typeName}}：</span>
                                                            <span class="num">{{tenantCtrl.numberFormat(func.currentBillingData,tenantCtrl.fixType_meter,false)}}</span>
                                                            <i class="edit">`</i>
                                                        </div>
                                                        <input type="text">
                                                        <i class="check" id="now_num" @click="saveMeterNum($event,index_meter,index_fun,energy,'currentNum')">Z</i>
                                                        <i class="delete" @click="giveUpAmend($event)">x</i>
                                                    </div>
                                                </div>
                                                <div class="more">
                                                    <div @mouseenter="mouseOnEdit($event)" @mouseleave="mouseOutEdit($event)" v-for="(func,index_fun) in meter.functionList">
                                                        <div data-flag="fa" @click="editMeterNum($event)">
                                                            <span v-if="func.type!='L'">{{func.typeName}}：</span>
                                                            <span class="num">{{tenantCtrl.numberFormat(func.diffData,tenantCtrl.fixType_meter,false)}}</span>
                                                            <i class="edit">`</i>
                                                        </div>
                                                        <input type="text">
                                                        <i class="check" id="difference_num" @click="saveMeterNum($event,index_meter,index_fun,energy,'diffData')">Z</i>
                                                        <i class="delete" @click="giveUpAmend($event)">x</i>
                                                    </div>
                                                </div>
                                                <div class="font_color_red">
                                                    <div>{{tenantCtrl.numberFormat(meter.totalMoney,tenantCtrl.fixType_money,true)}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 已结算 -->
                            <div class="balanced_account" v-if="energy.orderCount>0">
                                <!-- 此处颜色可改 -->
                                <h4 class="font_color_green">已结算</h4>
                                <div class="s_t_not_pay_list_content_sub">
                                    <!-- v-for -->
                                    <div class="s_t_not_pay_list_content_sub_box" v-for="order in energy.postPay.orderList">
                                        <!-- 账单及账单编号 -->
                                        <div class="s_t_not_pay_list_content_info clearfix">
                                            <p>账单：{{(order.orderTime+"").replace(/-/g,'.')}}</p>
                                            <p>账单编号：{{order.orderId}}</p>
                                        </div>
                                        <!-- 账单付费表格 -->
                                        <div class="s_t_not_pay_list_content_gird">
                                            <!-- 表头 -->
                                            <div class="s_t_not_pay_list_content_gird_title">
                                                <div>本期结算能耗</div>
                                                <div>本期结算金额（元）</div>
                                            </div>
                                            <!-- 表格内容 -->
                                            <div class="s_t_not_pay_list_content_gird_box">
                                                <div class="balanced_account_type">
                                                    <div>{{tenantCtrl.numberFormat(order.amount,tenantCtrl.fixType_dynamic,true)}}</div>
                                                </div>
                                                <div>
                                                    <div>{{tenantCtrl.numberFormat(order.money,tenantCtrl.fixType_money,true)}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 价格方案 -->
                        <div class="price">当前价格方案：<span @click.stop="tenantCtrl.showDetailPriceContent(energy.energyTypeId,energy.priceTemplateId)">{{energy.priceTemplateName}}</span><span>（{{energy.priceType==0?'平均':'分时'}}）</span></div>
                    </div>
                </div>
                <div class="s_t_not_pay_list_money">
                    <p>应缴总计：<span class="font_color_red">{{tenantCtrl.numberFormat(selTenant.leaveContent.totalMoney_Hou,tenantCtrl.fixType_money,true)}}</span> 元</p>
                </div>
            </div>

            <!-- 退租未缴费账单预付费 -->
            <div class="s_t_not_pay_list" v-if="selTenant.leaveContent.dataArr_Yu.length>0">
                <h2 class="s_t_not_pay_list_title">预付费剩余：</h2>
                <!-- 能耗类型和付费类型 -->
                <div class="s_t_not_pay_list_content_box" v-for="energy in selTenant.leaveContent.dataArr_Yu">
                    <div class="s_t_not_pay_list_content">
                        <h3 class="s_t_not_pay_list_content_title">{{energy.energyTypeName+'-预付费-'+(energy.prePay.prePayType==0?'表充表扣':(energy.prePay.prePayType==1?'软件充表扣':'软件充软件扣'))}}</h3>
                        <div class="s_t_not_pay_list_content_sub">
                            <div class="s_t_not_pay_list_content_sub_box">
                                <!-- 账单付费表格 表充表扣-->
                                <div class="s_t_not_pay_list_content_gird" v-if="energy.prePay.prePayType==0">
                                    <!-- 表头 -->
                                    <div class="s_t_not_pay_list_content_gird_title">
                                        <div>仪表ID</div>
                                        <div>剩余{{energy.prePay.prepayChargeType==0?(energy.energyTypeName+'量'):'金额'}}（{{energy.prePay.unit}}）</div>
                                    </div>
                                    <!-- 表格内容 -->
                                    <div class="s_t_not_pay_list_content_gird_box" v-for="meter in energy.prePay.meterList  ">
                                        <div class="balanced_account_type">
                                            <div>{{meter.meterId}}</div>
                                        </div>
                                        <div>
                                            <div class="font_color_green">{{tenantCtrl.numberFormat(meter.remainData,energy.prePay.prepayChargeType==0?tenantCtrl.fixType_dynamic:tenantCtrl.fixType_money,true)}}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 账单付费表格 软件充表扣-->
                                <div class="s_t_not_pay_list_content_gird" v-if="energy.prePay.prePayType==1">
                                    <!-- 表头 -->
                                    <div class="s_t_not_pay_list_content_gird_title">
                                        <div>仪表ID</div>
                                        <div>剩余{{energy.prePay.prepayChargeType==0?(energy.energyTypeName+'量'):'金额'}}（{{energy.prePay.unit}}）</div>
                                    </div>
                                    <!-- 表格内容 -->
                                    <div class="s_t_not_pay_list_content_gird_box" v-for="meter in energy.prePay.meterList  ">
                                        <div class="balanced_account_type">
                                            <div>{{meter.meterId}}</div>
                                        </div>
                                        <div>
                                            <div class="font_color_green">{{tenantCtrl.numberFormat(meter.remainData,energy.prePay.prepayChargeType==0?tenantCtrl.fixType_dynamic:tenantCtrl.fixType_money,true)}}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 账单付费表格 软件充软件扣 -->
                                <div class="s_t_not_pay_list_content_gird" v-if="energy.prePay.prePayType==2">
                                    <!-- 表头 -->
                                    <div class="s_t_not_pay_list_content_gird_title">
                                        <div>剩余{{energy.prePay.prepayChargeType==0?(energy.energyTypeName+'量'):'金额'}}（{{energy.prePay.unit}}）</div>
                                    </div>
                                    <!-- 表格内容 -->
                                    <div class="s_t_not_pay_list_content_gird_box">
                                        <div class="balanced_account_type border_none">
                                            <div class="font_color_green">{{tenantCtrl.numberFormat(energy.prePay.tenantRemainData,energy.prePay.prepayChargeType==0?tenantCtrl.fixType_dynamic:tenantCtrl.fixType_money,true)}}</div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="price">当前价格方案：<span @click.stop="tenantCtrl.showDetailPriceContent(energy.energyTypeId,energy.priceTemplateId)">{{energy.priceTemplateName}}</span><span>（{{energy.priceType==0?'平均':'分时'}}）</span></div>
                    </div>

                    <div class="s_t_not_pay_list_money" style="display: none;">
                        <p class="residue">剩余金额：<span class="font_color_green">560</span> 元</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 结算页底部 -->
    <div class="s_t_footer">
        <!-- 结算按钮 -->
        <div class="s_t_settle_btn">
            <div class="s_t_settle_btn_box">
                <pbutton-blue id="leave_btn" text="清算并退租" click="staticEvent.confirmSurrenderTenancy"></pbutton-blue>
            </div>
        </div>
    </div>

    <!-- 弹窗 -->
    <pwindow-confirm id="confirm_urrender_enancy">
        <button>
            <pbutton-backred text="确定" click="staticEvent.confirmSurrenderTenancySend"></pbutton-backred>
            <pbutton-white text="取消" click="staticEvent.confirmSurrenderTenancyHide"></pbutton-white>
        </button>
    </pwindow-confirm>
</div>
