<!-- 日志 -->
<div v-show="currentPage==='logs'" class="g_r_box" >
    <!-- 操作栏 -->
    <div class="g_r_header clearfix">
        <!-- 返回按钮 -->
        <div class="g_r_go_back">
            <pbutton-white text="返回" click="ldp.backToTemenListPage"></pbutton-white>
        </div>

        <!-- 标题 -->
        <h1>日志</h1>

    </div>
    <!-- 表底数记录表格 -->
    <div class="g_r_gird_box setRecord_con" style="max-height: calc(100% - 100px)">
        <div class="flexbox spaceBetween g_r_gird_operation">
            <div class="flexbox left searchBox">
                <section class="searchText" style="width:240px;">
                    <psearch-promptly id="searchText" placeholder="租户编号、名称 仪表id" change="tenantCtrl.searchField" focus="tenantCtrl.searchField"></psearch-promptly>
                </section>
                <!-- 能耗类型下拉框 -->
                <section>
                    <pcombobox-normal sel="tenantCtrl.result" bind="true" align="right" orientation="down"
                        id="'results_type'">
                        <header placeholder="'执行结果'" prefix="执行结果：" click=""></header>
                        <item datasource="resultArr" text="name"></item>
                    </pcombobox-normal>
                </section>

                <!-- 时间选择控件 -->
                <section>
                    <ptime-calendar id="datePicker" orientation="down" sel="staticEvent.chooseDateRecord">
                        <panel timetype="Md"  align="left" double="true" commontime="['d','pd']"></panel>
                    </ptime-calendar>
                </section>
            </div>

            <div class="right">
                <!-- 下载按钮 -->
                <div class="g_r_download">
                    <pbutton-white text="'下载日志'" icon="'D'" click="tenantCtrl.downloadLogs" bind="true">
                    </pbutton-white>
                </div>
            </div>
        </div>

        <div class="g_r_gird">
            <div class="g_r_gird_tit">
                <div>租户编号</div>
                <div>租户名称</div>
                <div>仪表ID</div>
                <div>修改时间</div>
                <div>修改结果</div>
                <div style="flex:3">设置内容</div>
            </div>
            <div class="g_r_gird_body" >
                <pscroll-small id="GRScroll" templateid="batchRecords"></pscroll-small>
            </div>
        </div>
        <div class="page">
            <ppage-simple orientation="up" id="page_logs" sel="tenantCtrl.changePageSizeLogs"></ppage-simple>
            <section>
                <!-- <div class="page_con"> -->
                <p>每页显示</p>
                <ptext-text placeholder="" id="'sr_page_text'" click="staticEvent.comboboxhide" blur="staticEvent.verifyPageSizeEvent_sr" bind="true">
                    <verify errtip="请输入正整数" verifytype="positiveint"></verify>
                </ptext-text>
                <i>条</i>
                <pbutton-white text="确定" id="sr_page_determine" click="tenantCtrl.setPageSize_sr"></pbutton-white>
                <!-- </div> -->
            </section>
        </div>
    </div>
</div>
<script type="text/html" id="batchRecords">
    <ul class="g_r_gird_item_box">
        <li class="g_r_gird_item" v-for="(model, index) in BatchRecordArr" :key="index" style="height:36px">
            <div class="g_r_gird_item_0"><span>{{model.tenantId}}</span></div>
            <div class="g_r_gird_item_0"><span>{{model.tenantName}}</span></div>
            <div class="g_r_gird_item_0"><span>{{model.meterId}}</span></div>
            <div class="g_r_gird_item_0"><span>{{model.updateTime}}</span></div>
            <div> <span class="success_green" v-if="model.result==0">成功</span> <span class="error_red" v-if="model.result==1">失败</span></div>
            <div style="flex: 3"> <span>{{model.setContent}}</span> </div>
        </li>
    </ul>
</script>