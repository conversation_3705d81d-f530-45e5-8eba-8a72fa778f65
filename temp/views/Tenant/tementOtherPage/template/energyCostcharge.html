<!--充值记录-->
<div v-if="currentPage=='energyCostReport'&&pdfPage=='energyCostcharge'">
    <!--充值记录  头部  -->
    <div class="charge_head">
        <div class="charge_head_left">
            <h2><em><u>{{(selTenant.tenantName+"").substring(0,1)}}</u>{{(selTenant.tenantName+"").substring(1)}}</em>充值记录</h2>
            <span>{{selTenant.energyCostReport.timeShow()}}</span>
        </div>
        <div class="charge_head_right">{{selTenant.buildingName}}</div>
    </div>
    <!--充值记录  内容部分  -->
    <div class="charge_body">
        <!--内容部分头部  -->
        <div class="charge_body_title ">
            <div class="charge_body_title_left">
                <h2>租户编号：<em>{{selTenant.tenantId}}</em></h2>
                <h2>能耗类型：<em>{{selTenant.energyCostReport.energyTypeName}}</em></h2>
            </div>
        </div>
        <!--内容部分表格  -->
        <div class="charge_body_grid">
            <div class="charge_body_grid_head">
                <div class="title_item clearfix" style="display: flex;justify-content: space-around;">
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_1' : 'colspan_copy_1'" style="padding-right: 20px;">充值单号</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_2' : 'colspan_copy_2'">操作时间</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_3' : 'colspan_copy_3'">操作人</div>
                    <div class="colspan_4" v-if="selTenant.energyCostReport.prePayType == 1">仪表ID</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_6' : 'colspan_copy_6'" v-if="selTenant.energyCostReport.prePayType == 1" style="width:12%;">充值类型</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_5' : 'colspan_copy_4'"  style="padding-left: 20px;">充值{{selTenant.energyCostReport.billingType==0?'量':'金额'}}（{{selTenant.energyCostReport.billingTypeUnit}}）</div>
                    <div class="colspan_6" v-if="selTenant.energyCostReport.prePayType == 1">操作后剩余{{selTenant.energyCostReport.billingType==0?'量':'金额'}}（{{selTenant.energyCostReport.billingTypeUnit}}）</div>
                </div>
            </div>
            <div class="charge_body_grid_body">
                <div class="charge_body_grid_body_list clearfix" v-if="selTenant.energyCostReport.dataList == 0">
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_1' : 'colspan_copy_1'">--</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_2' : 'colspan_copy_2'">--</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_3' : 'colspan_copy_3'">--</div>
                    <div class="colspan_4" v-if="selTenant.energyCostReport.prePayType == 1">--</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_6' : 'colspan_copy_6'" v-if="selTenant.energyCostReport.prePayType == 1" style="width:12%;">--</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_5' : 'colspan_copy_4'">--</div>
                    <div class="colspan_6" v-if="selTenant.energyCostReport.prePayType == 1">--</div>
                </div>
                <div v-for="data in selTenant.energyCostReport.dataList" class="charge_body_grid_body_list clearfix" v-show="selTenant.energyCostReport.dataList != 0">
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_1' : 'colspan_copy_1'"  style=" overflow: hidden;white-space: nowrap;text-overflow: ellipsis;cursor:pointer;"  :title="data.orderId">{{data.orderId}}</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_2' : 'colspan_copy_2'">{{data.operateTime}}</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_3' : 'colspan_copy_3'">{{data.userName}}</div>
                    <div class="colspan_4" v-if="selTenant.energyCostReport.prePayType == 1">{{data.meterId}}</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_6' : 'colspan_copy_6'" v-if="selTenant.energyCostReport.prePayType == 1" style="width:12%;">{{data.channelType}}</div>
                    <div :class="selTenant.energyCostReport.prePayType == 1 ? 'colspan_5' : 'colspan_copy_4'">{{tenantCtrl.numberFormat(data.money,selTenant.energyCostReport.billingType==0?tenantCtrl.fixType_dynamic:tenantCtrl.fixType_money,true)}}</div>
                    <div class="colspan_6" v-if="selTenant.energyCostReport.prePayType == 1">{{data.remainData}}</div>
                </div>
            </div>
        </div>
    </div>
</div>



