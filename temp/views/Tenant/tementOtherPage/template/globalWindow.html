<!-- 所有公共弹框 -->
<div class="p_u_b-global">
    <!-- 退费 - 输入密码 -->
    <div class="window_refund_cost">
        <pwindow-modal id="refund_cost" title="退费" templateid="refund_cost_model"></pwindow-modal>
    </div>

    <!-- 结算 - 输入密码 -->
    <div class="window_settlement_remaining">
        <pwindow-modal id="settlement_remaining" title="结算" templateid="settlement_remaining_model"></pwindow-modal>
    </div>

    <!-- 充值 - 输入密码 -->
    <div class="window_top_up">
        <pwindow-modal id="top_up" title="充值" templateid="top_up_model"></pwindow-modal>
    </div>

    <!-- 缴费 - 输入密码 -->
    <div class="window_pay_the_fees">
        <pwindow-modal id="pay_the_fees" title="缴费" templateid="pay_the_fees_model"></pwindow-modal>
    </div>

    <!-- 全部缴费-输入密码 -->
    <div class="window_pay_the_fees_all">
        <pwindow-modal id="pay_the_fees_all" title="全部缴费" templateid="pay_the_fees_model_all"></pwindow-modal>
    </div>

    <!-- 仪表设置 -->
    <div class="window_instrument_set">
        <!-- 关闭图标 -->
        <i id="close_meter_setting" @click="staticEvent.hideInstrumentSetWindow">x</i>
        <pwindow-modal id="instrument_set" title="仪表设置" templateid="instrument_set_model"></pwindow-modal>
    </div>
</div>

<!-- 退费 -->
<script type="text/html" id="refund_cost_model">
    <div class="pay_the_fees_model_block">
        <div class="pay_the_fees_model_tit">
            您确定要退费{{beforeRefundCostData.refundValue}}{{beforeRefundCostData.billingTypeUnit}}吗？
        </div>
        <div class="pay_the_fees_model_tip">
            确定后该操作无法撤回
        </div>
        <div class="pay_the_fees_password_tit">
            操作密码：
        </div>
        <div class="pay_the_fees_password_input">
            <input :class="{red: (!isThroughVerification&&verifyPasswordTip != '') || passwordIsEmpty}" type="password" placeholder="请输入操作密码" v-model="verifyPasswordValue">
            <section v-show="!isThroughVerification&&verifyPasswordTip != ''">密码错误</section>
            <section v-show="passwordIsEmpty">不能为空</section>
        </div>
        <div class="pay_the_fees_operat">
            <pbutton-blue id="refund_cost_details" text="确认退费" pdisabled="false" click="tenantCtrl.verifyPasswordEvent('refund_cost_details')"></pbutton-blue>
            <pbutton-white text="取消" click="staticEvent.hidePassWordForRefundCost"></pbutton-white>
        </div>
    </div>
</script>

<!-- 结算 -->
<script type="text/html" id="settlement_remaining_model">
    <div class="pay_the_fees_model_block">
        <div class="pay_the_fees_model_tit">
            您确定要结算吗？
        </div>
        <div class="pay_the_fees_model_tip">
            确定后该操作无法撤回
        </div>
        <div class="pay_the_fees_password_tit">
            操作密码：
        </div>
        <div class="pay_the_fees_password_input">
            <input :class="{red: (!isThroughVerification&&verifyPasswordTip != '') || passwordIsEmpty}" type="password" placeholder="请输入操作密码" v-model="verifyPasswordValue">
            <section v-show="!isThroughVerification&&verifyPasswordTip != ''">密码错误</section>
            <section v-show="passwordIsEmpty">不能为空</section>
        </div>
        <div class="pay_the_fees_operat">
            <pbutton-blue id="settlement_remaining_btn" text="确认结算" pdisabled="false" click="tenantCtrl.verifyPasswordEvent('settlement_remaining_btn')"></pbutton-blue>
            <pbutton-white text="取消" click="staticEvent.hidePassWordForSettlement"></pbutton-white>
        </div>
    </div>
</script>

<!-- 充值 -->
<script type="text/html" id="top_up_model">
    <div class="pay_the_fees_model_block">
        <div class="pay_the_fees_model_tit">
            您确定要充值{{selTenant.recharge.rechargeValue}}{{selTenant.recharge.dataUnit}}吗？
        </div>
        <div class="pay_the_fees_model_tip">
            确定后该操作无法撤回
        </div>
        <div class="pay_the_fees_password_tit">
            操作密码：
        </div>
        <div class="pay_the_fees_password_input">
            <input :class="{red: (!isThroughVerification&&verifyPasswordTip != '') || passwordIsEmpty}" type="password" placeholder="请输入操作密码" v-model="verifyPasswordValue"  @focus="tenantCtrl.inputFocus">
            <section v-show="!isThroughVerification&&verifyPasswordTip != ''">密码错误</section>
            <section v-show="passwordIsEmpty">不能为空</section>
        </div>
        <div class="pay_the_fees_operat">
            <pbutton-blue id="recharge_details" text="确认充值" pdisabled="false" click="tenantCtrl.verifyPasswordEvent('recharge_details')"></pbutton-blue>
            <pbutton-white text="取消" click="staticEvent.hidePassWordForTopUp"></pbutton-white>
        </div>
    </div>
</script>

<!-- 缴费 -->
<script type="text/html" id="pay_the_fees_model">
    <div class="pay_the_fees_model_block">
        <div class="pay_the_fees_model_tit">
            您确定要缴费{{Number(selTenant.payOrder.totalMoney).toFixed(2)}}元吗？
        </div>
        <div class="pay_the_fees_model_tip">
            确定后该操作无法撤回
        </div>
        <div class="pay_the_fees_password_tit">
            操作密码：
        </div>
        <div class="pay_the_fees_password_input">
            <input :class="{red: (!isThroughVerification&&verifyPasswordTip != '') || passwordIsEmpty}" type="password" placeholder="请输入操作密码" v-model="verifyPasswordValue">
            <section v-show="!isThroughVerification&&verifyPasswordTip != ''">密码错误</section>
            <section v-show="passwordIsEmpty">不能为空</section>
        </div>
        <div class="pay_the_fees_operat">
            <pbutton-blue id="confirm_payment_list" text="确认缴费" pdisabled="false" click="tenantCtrl.verifyPasswordEvent('confirm_payment_list')"></pbutton-blue>
            <pbutton-white text="取消" click="staticEvent.hidePassWordForPay"></pbutton-white>
        </div>
    </div>

</script>

<!-- 全部缴费 -->
<script type="text/html" id="pay_the_fees_model_all">
    <div class="pay_the_fees_model_all_block">
        <div class="pay_the_fees_model_all_tit">
            您确定要缴费{{tenantCtrl.numberFormat(bat_total_noPayMoney,tenantCtrl.fixType_money,true)}}元吗？
        </div>
        <div class="pay_the_fees_model_all_tip">
            确定后该操作无法撤回
        </div>
        <div class="pay_the_fees_model_all_password_tit">
            操作密码：
        </div>
        <div class="pay_the_fees_model_all_password_input">
            <input :class="{red: (!isThroughVerification&&verifyPasswordTip != '') || passwordIsEmpty}" type="password" placeholder="请输入操作密码" v-model="verifyPasswordValue">
            <section v-show="!isThroughVerification&&verifyPasswordTip != ''">密码错误</section>
            <section v-show="passwordIsEmpty">不能为空</section>
        </div>
        <div class="pay_the_fees_model_all_operat">
            <pbutton-blue id="confirm_payment_all" text="确认缴费" pdisabled="false" click="tenantCtrl.verifyPasswordEvent('confirm_payment_all')"></pbutton-blue>
            <pbutton-white text="取消" click="staticEvent.hidePassWordForAllPay"></pbutton-white>
        </div>
    </div>

</script>

<!-- 仪表设置 -->
<script type="text/html" id="instrument_set_model">
    <div class="instrument_set_model_block" >
        <!-- 顶部列表 -->
        <div class="instrument_set_model_topList" v-show="meterSetState">
            <ul>
                <li>
                    <h4>仪表编号</h4>
                    <p :title="instrumentalSupportFunctions.meterId">{{instrumentalSupportFunctions.meterId}}</p>
                </li>
                <li>
                    <h4>所属租户</h4>
                    <p :title="selTenant.tenantName">{{selTenant.tenantName}}</p>
                </li>
            </ul>
            <ul>
                <li>
                    <h4>仪表能耗类型</h4>
                    <p :title="instrumentalSupportFunctions.energyTypeName">{{instrumentalSupportFunctions.energyTypeName}}</p>
                </li>
                <li>
                    <h4>所属房间</h4>
                    <p :title="instrumentalSupportFunctions.roomCode">{{instrumentalSupportFunctions.roomCode}}</p>
                </li>
            </ul>
        </div>

        <!-- 第一步，显示设置功能 -->
        <div class="instrument_set_main" v-show="meterSetStep == 1">
            <div class="instrument_set_model_title">
                <span>设置</span>
            </div>
            <div class="instrument_set_model_item">
                <ul>
                    <li :title="instrumentalSupportFunctions.remainClear == 0 ? '抱歉，该仪表不支持此功能' : ''">
                        <div :pdisabled="instrumentalSupportFunctions.remainClear == 0" @click="instrumentalBtnEvent(0)">仪表清零</div>
                    </li>
                    <li :title="instrumentalSupportFunctions.paulEle == 0 ? '抱歉，该仪表不支持此功能' : ''">
                        <div :pdisabled="instrumentalSupportFunctions.paulEle == 0" @click="instrumentalBtnEvent(1)">保电</div>
                    </li>
                    <li :title="instrumentalSupportFunctions.gate == 0 ? '抱歉，该仪表不支持此功能' : ''">
                        <div :pdisabled="instrumentalSupportFunctions.gate == 0" @click="instrumentalBtnEvent(2)">分合闸</div>
                    </li>
                    <li :title="instrumentalSupportFunctions.overdraft == 0 ? '抱歉，该仪表不支持此功能' : ''">
                        <div :pdisabled="instrumentalSupportFunctions.overdraft == 0" @click="instrumentalBtnEvent(3)">透支</div>
                    </li>
                    <li :title="instrumentalSupportFunctions.updatePrice == 0 ? '抱歉，该仪表不支持此功能' : ''">
                        <div :pdisabled="instrumentalSupportFunctions.updatePrice == 0" @click="instrumentalBtnEvent(4)">更新价格</div>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 第二步，当前状态查询 -->
        <div class="instrument_reset_block_Make_overdraft_updata" v-show="meterSetStep == 2">
            <div class="dashed"></div>
            <div class="top">
                <h4>
                    <span>当前</span>
                    <span v-if="meterSetType == 0">剩余量</span>
                    <span v-if="meterSetType == 1 || meterSetType == 2">状态</span>
                    <span v-if="meterSetType == 3">透支金额</span>
                    <span v-if="meterSetType == 4">价格</span>
                    <span>：</span>
                    <i class="refresh" @click="refreshInstrumentalData"></i>
                    <span>（{{instrumentalData.lastUpdateTime}} 刷新）</span>
                </h4>
            </div>
            <div class="number clearfix">
                <div v-show="meterSetType != 4">
                    <i>{{tenantCtrl.numberFormat(instrumentalData.value, tenantCtrl.fixType_price, true)}}</i><span v-show="meterSetType != 1 && meterSetType != 2">{{instrumentalData.unit || ''}}</span>
                </div>
                <div v-show="meterSetType == 4" v-for="(model, index) in instrumentalData.result" :key="index">
                    <span class="font_color_black">{{model.typeName}}</span>
                    <span class="font_color_black" v-if="instrumentalSupportFunctions.meterType == 1">：</span>
                    <i>{{tenantCtrl.numberFormat(model.value, tenantCtrl.fixType_price, true)}}</i>
                    <span>{{model.unit || ''}}</span>
                </div>
            </div>
            <div class="change" v-show="meterSetType == 3">
                <h4>修改透支金额</h4>
                <ptext-text id="'overdraft_amount'" placeholder="'请输入金额'" text="instrumentalData.unit" bind="true">
                    <verify errtip="金额不能为空" verifytype="space"></verify>
                    <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                </ptext-text>
            </div>
            <!-- 更新价格 —— 普通 -->
            <div class="change" v-show="meterSetType == 4 && instrumentalSupportFunctions.meterType == 0" v-for="(item, index) in instrumentalData.result" :key="index" id="toManagementPricePlan">
                <section>更新后该仪表价格将按此价格计费，不受价格方案影响</section>
                    <ptext-text id="'electricity_price_ipt_L'" placeholder="'请输入价格'" text="item.unit" bind="true" >
                        <verify errtip="价格不能为空" verifytype="space"></verify>
                        <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                    </ptext-text>
            </div>
            <!-- 更新价格 —— 多费率 -->
            <div class="changeMore" v-show="meterSetType == 4 && instrumentalSupportFunctions.meterType == 1">
                <section>更新后该仪表价格将按此价格计费，不受价格方案影响</section>
                <div>
                    <ul>
                        <li v-for="(model, index) in instrumentalData.result" :key="index">
                            <h4>{{model.typeName}}</h4>
                            <ptext-text id="'electricity_price_ipt_' + index" text="model.unit" placeholder="'请输入' + model.typeName + '价格'" bind="true" >
                                <verify errtip="价格不能为空" verifytype="space"></verify>
                                <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                            </ptext-text>
                        </li>
                    </ul>
                </div>
            </div>
           
            
            <!-- 操作按钮 -->
            <div class="operat" v-show="meterSetType == 0 || meterSetType == 3 || meterSetType == 4">
                <pbutton-blue text="meterSetType == 0?'清零':'确定'" bind="true" @click="meterSetGoPasswordVerification"></pbutton-blue>
                <pbutton-white text="上一步" @click="meterSetPrevStepEvent"></pbutton-white>
            </div>
            <div class="operat" v-show="meterSetType == 1 || meterSetType == 2">
                <pbutton-blue text="meterSetType == 1?'保电':'合闸'" bind="true" @click="meterSetGoPasswordVerification(true)"></pbutton-blue>
                <pbutton-blue text="meterSetType == 1?'解除保电':'分闸'" bind="true" @click="meterSetGoPasswordVerification(false)"></pbutton-blue>
                <pbutton-white text="上一步" @click="meterSetPrevStepEvent"></pbutton-white>
            </div>
        </div>

        <!-- 第三步，密码二次验证 -->
        <div class="instrument_reset_block_paulElectric_input" v-show="meterSetStep == 3">
            <div class="dashed"></div>
            <div class="title">
                <span>您确定要</span>
                <span v-show="meterSetType == 0">清零剩余量</span>
                <span v-show="meterSetType == 1">{{isProtectionCircuit?'保电':'解除保电'}}</span>
                <span v-show="meterSetType == 2">{{isCloseBrake?'合闸':'分闸'}}</span>
                <span v-show="meterSetType == 3">将透支金额修改为{{instrumentalData.iptValue}}{{instrumentalData.unit}}</span>
                <span v-show="meterSetType == 4">
                    <span>将价格更新为</span>
                    <span v-for="(model, index) in instrumentalData.iptValueArr" :key="index">
                        <span>{{model.typeName}}</span>
                        <span v-if="instrumentalSupportFunctions.meterType == 1">：</span>
                        <span>{{model.value}}</span>
                        <span>{{model.unit}}</span>
                        <span v-if="index != instrumentalData.result.length - 1">，</span>
                    </span>
                </span>
                <span>吗？</span>
            </div>
            <div class="password">
                操作密码：
            </div>
            <div class="content">
                <input :class="{red: (!isThroughVerification&&verifyPasswordTip != '') || passwordIsEmpty}" type="password" placeholder="请输入操作密码" v-model="verifyPasswordValue">
                <section v-show="!isThroughVerification&&verifyPasswordTip != ''">密码错误</section>
                <section v-show="passwordIsEmpty">不能为空</section>
            </div>
            <div class="operat">
                <pbutton-blue id="confirm_meter_set_change" text="确定修改" pdisabled="false" click="tenantCtrl.verifyPasswordEvent('confirm_meter_set_change')"></pbutton-blue>
                <pbutton-white text="上一步" @click="meterSetPrevStepEvent"></pbutton-white>
            </div>
        </div>

        <!-- 第四步，保存操作后的 loading -->
        <div class="instrument_reset_block_loading" v-show="meterSetStep == 4">
            <ploading-part id="instrument_partLoading" text="处理中..."></ploading-part>
            <p class="loading_title">请勿关闭浏览器</p>
        </div>

        <!-- 第五步，操作成功弹窗 -->
        <div class="instrument_reset_block_success"  v-show="meterSetStep == 5">
            <div class="dashed"></div>
            <div class="top">
                <h4>
                    <span>当前</span>
                    <span v-if="meterSetType == 0">剩余量</span>
                    <span v-if="meterSetType == 1 || meterSetType == 2">状态</span>
                    <span v-if="meterSetType == 3">透支金额</span>
                    <span v-if="meterSetType == 4">价格</span>
                    <span>：</span>
                    <i class="refresh" @click="refreshInstrumentalData"></i>
                    <span>（{{instrumentalData.lastUpdateTime}} 刷新）</span>
                </h4>
            </div>
            <div class="number clearfix">
                <div v-show="meterSetType != 4">
                    <i>{{tenantCtrl.numberFormat(instrumentalData.value, tenantCtrl.fixType_price, true)}}</i><span v-show="meterSetType != 1 && meterSetType != 2">{{instrumentalData.unit || ''}}</span>
                </div>
                <div v-show="meterSetType == 4" v-for="(model, index) in instrumentalData.result" :key="index">
                    <span class="font_color_black">{{model.typeName}}</span>
                    <span class="font_color_black" v-if="instrumentalSupportFunctions.meterType == 1">：</span>
                    <i>{{tenantCtrl.numberFormat(model.value, tenantCtrl.fixType_price, true)}}</i>
                    <span>{{model.unit || ''}}</span>
                </div>
            </div>
            <div class="operat">
                <pbutton-blue text="操作成功" @click="saveMeterSetRecordBtn"></pbutton-blue>
                <i class="instrument_icon" title="点击操作失败将不会生成操作记录">!</i>
                <pbutton-white text="操作失败" click="staticEvent.hideInstrumentSetWindow"></pbutton-white>
            </div>
        </div>

        <!-- 第六步，操作失败弹窗 -->
        <div class="instrument_reset_block_notResponse" v-show="meterSetStep == 6">
            <div class="notice">
                <pnotice-nodata icon="../../../../img/tement/noResponse.png" text="仪表长时间未响应" subtitle="请重新操作或稍后重试"></pnotice-nodata>
            </div>
            <div class="button">
                <pbutton-white text="重新操作" @click="instrumentalBtnEvent(meterSetType)"></pbutton-white>
            </div>
        </div>
    </div>
</script>