<!--结算-->
<div class="settlement" style="height: 100%" v-if="currentPage=='settlement'">
    <!-- 头部  -->
    <div class="settlement_title">
        <div>
            <pbutton-white text="取消" id="settlement_title_button" click="ldp.backToTemenListPage"></pbutton-white>
        </div>
        <div class="settlement_title_con">结算</div>
        <div></div>
    </div>

    <!--结算中央内容部分  -->
    <div class="settlement_content" id="bat_settle_content">
        <!--内容 标题 -->
        <div class="settlement_content_title">
            <div>
                <div>本次结算损耗：</div>
                <div>{{energyTypeName}}</div>
            </div>
            <div>
                <div class=date_title>请选择结算日期：</div>
                <div>
                    <ptime-form id="formtime" sel="tenantCtrl.tenantSettle_bat">
                        <panel timetype="yMd" startyear="2014"></panel>
                    </ptime-form>
                </div>
                <div class="s_choose_date_error_msg">
                    <p class="s_choose_date_error_msg_bf"><i></i>选择的时间必须晚于上次结算时间或激活时间</p>
                    <p class="s_choose_date_error_msg_lt"><i></i>选择的时间不得晚于今天</p>
                </div>
            </div>
        </div>
        <!--中央表格  -->
        <div class="settlement_content_table">
            <!--表头  -->
            <div class="settlement_content_table_top">
                <ul>
                    <li class="inline">
                        <div class="settlement_num slh">租户编号</div>
                        <div class="settlement_name slh">租户名称</div>
                        <div class="settlement_home slh">所在建筑</div>
                        <div class="settlement_homeNum slh">房间编号</div>
                        <div class="settlement_num slh">上次结算日期</div>
                        <div class="settlement_name slh">本期结算能耗({{bat_energyUnit}})</div>
                        <div class="settlement_money slh">本期结算金额(元)</div>
                    </li>
                </ul>
            </div>
            <!--表格内容  -->
            <div class="settlement_content_table_content">
                <ul>
                    <li class="inline" v-for="model in checkedTenantArr"
                        @click.stop="tenantCtrl.goSettleDetail_bat(model)">
                        <div class="settlement_num slh">{{model.tenantId}}</div>
                        <div class="settlement_name slh">{{model.tenantName}}</div>
                        <div class="settlement_home slh">{{model.buildingName}}</div>
                        <div class="settlement_homeNum slh">{{model.roomIds}}</div>
                        <div class="settlement_num slh">
                            {{(model.lastClearingTime+"").substring(0,10).replace(/-/g,'.')}}</div>
                        <div class="settlement_name slh">
                            {{tenantCtrl.numberFormat(model.settle_bat.currentBillingEnergy,tenantCtrl.fixType_dynamic,true)}}
                        </div>
                        <div class="settlement_money slh">
                            {{tenantCtrl.numberFormat(model.settle_bat.currentBillingMoney,tenantCtrl.fixType_money,true)}}
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div v-if="meterFaultList.length != 0 || clearingTimeFaultList.length != 0" class="prompt_settlement">
            <p class="prompt_con">
                <i class="prompt_icon">i</i>
                <span v-for="(item, index) in meterFaultList" :key="index">
                    {{item}}
                    <span v-if="index != meterFaultList.length - 1">、</span>
                </span>
                <span v-if="meterFaultList.length != 0">仪表暂无数据<span
                        v-if="clearingTimeFaultList.length != 0">；</span></span>
                <span v-for="(item, index) in clearingTimeFaultList" :key="index">
                    {{item}}
                    <span v-if="index != clearingTimeFaultList.length - 1">、</span>
                </span>
                <span v-if="clearingTimeFaultList.length != 0">未到结算时间</span>
                <span>。</span>
            </p>
            <p class="prompt_con prompt_foot"
                v-if="selTenantFaultType == 'portion' && (meterFaultList.length != 0 || clearingTimeFaultList.length != 0)">
                以上租户本次无法结算，将为您正常结算所选的其他租户。</p>
        </div>
    </div>
    <!--尾部  -->
    <div class="settlement_foot">
        <pbutton-blue id="bat_settle_ok" text="确定并结算" click="staticEvent.showPassWordForSettlement"></pbutton-blue>
        <pbutton-blue id="bat_re_settle_ok" text="重新结算" click="staticEvent.showPassWordForSettlement"></pbutton-blue>
        <!-- <pbutton-blue id="bat_settle_back" text="完成" click="ldp.backToTemenListPage"></pbutton-blue> -->
    </div>
</div>