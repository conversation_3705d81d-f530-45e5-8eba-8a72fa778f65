<div class="g_r_box" v-show="currentPage == 'gaugeRecord'">
    <!-- 操作栏 -->
    <div class="g_r_header clearfix">
        <!-- 返回按钮 -->
        <div class="g_r_go_back">
            <pbutton-white text="返回" click="ldp.backToTemenListPage"></pbutton-white>
        </div>

        <!-- 标题 -->
        <h1>表底数记录</h1>

    </div>

    <!-- 表底数记录表格 -->
    <div class="g_r_gird_box">
        <div class="g_r_gird_operation clearfix">
            <div class="g_r_gird_operation_left clearfix">
                <!-- 能耗类型下拉框 -->
                <div class="energy_type_box">
                    <pcombobox-normal sel="staticEvent.selectEnergyTypeGaugeRecord" bind="true" align="right" orientation="down" id="'energy_type'">
                        <header placeholder="'请选择能耗类型'" prefix="能源类型：" click=""></header>
                        <item datasource="energyTypeArrForGauge" text="name"></item>
                    </pcombobox-normal>
                </div>

                <!-- 时间选择控件 -->
                <div class="time_controller_box">
                    <ptime-calendar id="time_controller" orientation="down" sel="staticEvent.chooseTimeRangeGaugeRecord">
                        <panel timetype="d" startyear="2012" align="left" double="false" commontime="['d','pd']"></panel>
                    </ptime-calendar>
                </div>

                <!-- 小时选择下拉框 -->
                <div class="time_hour_box">
                    <pcombobox-normal sel="staticEvent.chooseHourGaugeRecord" bind="true" align="right" orientation="down" id="'time_hour'">
                        <header placeholder="'选择小时'" click=""></header>
                        <item datasource="hourArrForGauge" text="hour"></item>
                    </pcombobox-normal>
                </div>

                <div class="check_activate_time">
                    <pswitch-checkbox id="check_activate_time" text="查看激活时间表底数" change="staticEvent.checkActivateTime"></pswitch-checkbox>
                </div>
            </div>

            <div class="g_r_gird_operation_right">
                <!-- 下载按钮 -->
                <div class="g_r_download">
                    <pbutton-white text="'下载表底数记录'" icon="'D'" click="staticEvent.downloadGaugeRecordReport" bind="true"></pbutton-white>
                </div>
            </div>
        </div>

        <div class="g_r_gird">
            <div class="g_r_gird_tit">
                <div>租户编号</div>
                <div>租户名称</div>
                <div>租户激活时间</div>
                <div>房间编号</div>
                <div>仪表ID</div>
                <div>仪表能源类型</div>
                <div>{{!gaugeRecordParams.energyTypeId ? 'CT/倍率' : (gaugeRecordParams.energyTypeId == 'Dian' ? 'CT' : '倍率')}}</div>
                <div style="flex: 2">所选时间的仪表读数</div>
                <div>是否乘以{{!gaugeRecordParams.energyTypeId ? 'CT/倍率' : (gaugeRecordParams.energyTypeId == 'Dian' ? 'CT' : '倍率')}}</div>
            </div>
            <div class="g_r_gird_body">
                <pscroll-small id="GRScroll" templateid="GRScrollBox"></pscroll-small>
            </div>
        </div>
        <div ndm="" class="per-grid-nodata">
            <div pc="" _pt="pnotice-nodata" _id="bfbfheiegahhgdbfafh" class="per-prompt-abnormalmess">
                <span class="per-prompt_icon"></span>
                <span class="per-prompt_title">暂无数据</span>
                <span class="per-prompt_subtitle">请检查网络是否通畅</span>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="GRScrollBox">
    <ul class="g_r_gird_item_box">
        <li class="g_r_gird_item" v-for="(model, index) in gaugeRecordArr" :key="index">
            <div class="g_r_gird_item_0"><span>{{model.tenantId}}</span></div>
            <div class="g_r_gird_item_0"><span>{{model.tenantName}}</span></div>
            <div class="g_r_gird_item_0"><span>{{model.time}}</span></div>
            <div class="g_r_gird_item_0">
                <div class="g_r_gird_item_1 room_code_1" v-for="(model, index) in model.roomList" :key="index"><span>{{model.roomCode}}</span></div>
            </div>
            <div>
                <div class="g_r_gird_item_1 room_code_2" v-for="(model, index) in model.roomList" :key="index">
                    <div class="g_r_gird_item_2 sort_code_1" v-for="(model, index) in model.meterList" :key="index"><span>{{model.meterId}}</span></div>
                </div>
            </div>
            <div>
                <div class="g_r_gird_item_1 room_code_3" v-for="(model, index) in model.roomList" :key="index">
                    <div class="g_r_gird_item_2 sort_code_2" v-for="(model, index) in model.meterList" :key="index"><span>{{model.energyTypeName}}</span></div>
                </div>
            </div>
            <div>
                <div class="g_r_gird_item_1 room_code_4" v-for="(model, index) in model.roomList" :key="index">
                    <div class="g_r_gird_item_2 sort_code_3" v-for="(model, index) in model.meterList" :key="index"><span>{{model.ct || '--'}}</span></div>
                </div>
            </div>
            <div style="flex: 2">
                <div class="g_r_gird_item_1 room_code_5" v-for="(model, index) in model.roomList" :key="index">
                    <div class="g_r_gird_item_2 sort_code_4" v-for="(model, index) in model.meterList" :key="index">
                        <div class="g_r_gird_item_3" v-for="(model, index) in model.list" :key="index">
                            {{model.meterType == 0 ? '' : model.name + '：'}}
                            <span>{{tenantCtrl.numberFormat(model.value, tenantCtrl.fixType_meter, true)}}</span>
                            <span v-show="model.value">（{{model.energyUnit}}）</span>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <div class="g_r_gird_item_1 room_code_6" v-for="(model, index) in model.roomList">
                    <div class="g_r_gird_item_2 sort_code_5" v-for="(model, index) in model.meterList" :key="index">
                        <div class="g_r_gird_item_3" v-for="(model, index) in model.list" :key="index">{{model.isCt == 1 ? '是' : (model.isCt == 0 ? '否' : '--')}}</div>
                    </div>
                </div>
            </div>
        </li>
    </ul>
</script>