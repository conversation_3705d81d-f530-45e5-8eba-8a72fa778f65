<div class="meter_detail" v-if="currentPage==='meterDetail'">
    <!--电表详情-->
    <div class="header">
        <div class="cancelBtn">
            <pbutton-white text="返回" click="ldp.toTementDetail"></pbutton-white>
        </div>
        <div class="title">
            <pcombobox-normal bind="true" sel="tenantCtrl.getMeterDetail" isborder="false" id="'meter_combox'">
                <header placeholder="'选择表具'"></header>
                <item datasource="meterDetail.meterArr" text="meterName"></item>
            </pcombobox-normal>
        </div>
    </div>
    <div class="body">
        <div class="time_wrapper">
            <ptime-calendar id="meterDetailCalendar" align="left" orientation="down" sel="ldp.chooseMeterTime">
                <panel timetype="dMyw" startyear="2015" commontime="['d','M','y','w']"></panel>
            </ptime-calendar>
        </div>
        <div class="chart_wrapper" id="elecMeterdetailChart">
        </div>
    </div>
</div>
