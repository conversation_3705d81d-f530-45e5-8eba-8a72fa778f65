<!-- 报警管理 -->
<div id="alarmLists" v-show="currentPage === 'alarmLists'">
    <div class="alarm-con-con">
        <!-- 报警消息左侧导航 -->
        <div class="alarm-con-nav">
            <div class="alarm-nav-temp">
                <div class="nav-title">
                    <em class="state"></em><b>消息状态</b>
                </div>
                <ul class="alarm-nav-ul">
                    <li class="pitch allMessage" @click="alarmTypeSel($event,null)"><b>全部</b></li>
                    <li class="red" @click="alarmTypeSel($event,0)"><b>未恢复</b></li>
                    <li class="green" @click="alarmTypeSel($event,1)"><b>已恢复</b></li>
                    <li class="default" @click="alarmTypeSel($event,2)"><b>已过期</b></li>
                </ul>
            </div>
            <div class="alarm-nav-temp">
                <div class="nav-title"><em class="alarm"></em><b>报警类型</b></div>
                <div class="alarm-type-tree" id="divAlarmType">
                    <div class="tree-temp" v-for="item in alarmTypes">
                        <div class="temp-title show" :class="{ pitch: item.id == currAlarmType }"
                             @click="alarmTypeSelEvent($event,item)" :ttid="item.id">
                                    <span class="arrows" onclick="treeTempArrows(this,event)"
                                          v-show="item.list && item.list.length>0">
                                        <div class="icon">r</div>
                                    </span>
                            <b v-text="item.name" :title="item.name">全部</b>
                        </div>
                        <ul>
                            <li @click="alarmTypeSelEvent($event,cur)" v-for="cur in item.list"><b v-text="cur.name" :title="cur.name">租户管理费用不足</b>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- 报警消息右侧内容 -->
        <div class="alarm-con-wrap">
            <div class="alarm-c-w-left">
                <div class="c-left-title">
                    <div class="l-t-right">
                        <ptime-calendar id="tenantCalendar" orientation="down" sel="timeSelect(true,'tenantCalendar')">
                            <panel timetype="Myd" align="left" double="true" startyear="2003" endyear="2034"
                                   iscommontime="false"></panel>
                        </ptime-calendar>
                    </div>
                </div>
                <div class="c-left-con">
                    <!-- 无结果时 -->
                    <div class="nodata" v-show="alarms.length==0">
                        <pnotice-nodata text="暂无数据" subtitle="当前分类无结果"></pnotice-nodata>
                    </div>
                    <!-- 有结果时，原因加粗bold，li有选中样式active-->
                    <div class="c-left-con-wrap" v-show="alarms && alarms.length>0">
                        <ul id="ulalarmsgrid">
                            <li v-for="item in alarms" :id="item.id" @click="alarmSelEvent($event,item)">
                                <div>
                                    <div class="div-top">
                                        <span class="alarm-state"
                                              v-text="item.status==0?'未恢复':item.status==1?'已恢复':'已过期'"
                                              :class="{red: item.status == 0, green: item.status == 1 }"
                                              v-show="item.status>=0">未恢复</span>
                                        <span class="alarm-name">
                                                    <em v-text="item.alarmReason">预付费用不足</em>
                                            <!--<em class="silence-icon" data-bind="visible: isOffSound">q</em>-->
                                                </span>
                                    </div>
                                    <div class="div-bottom">
                                        <span v-text="item.alarmPositionName"
                                              :title="item.alarmPositionName">新华创新大厦001层</span>
                                        <span>
                                               <em class="name" v-text="item.alarmParentType">租赁空间管理与付费系统</em>
                                               <em class="time" v-text="item.alarmTime">2016.01.20   12:12</em>
                                        </span>
                                    </div>
                                </div>
                            </li>

                        </ul>
                        <div class="paga-wrap">
                            <ppage-simple number="50" orientation="up" id="alarmNewsPage"
                                          sel="alarmPageSel(true)"></ppage-simple>
                        </div>
                    </div>
                </div>
            </div>
            <div class="alarm-c-w-right">
                <!-- 无结果时 -->
                <div class="nodata" v-show="!alarmDetail.alarmId">
                    <pnotice-nodata text="暂无数据" subtitle="请选取需要查看的报警消息"></pnotice-nodata>
                </div>
                <!-- 有结果时 -->
                <div class="alarm-c-w-right-con" v-show="alarmDetail.alarmId">
                    <div class="alarm-c-w-right-con-wrap">
                        <div class="alarm-details-name">
                            <ul>
                                <li class="li1">
                                    <span v-text="alarmDetail.alarmParentType">报警类型</span>
                                </li>
                            </ul>
                        </div>
                        <!-- 报警详情 -->
                        <div class="alarm-details-type">
                            <ul>
                                <li class="tips" style="display: none;">提示：剩余电流过大存在电气火灾风险！</li>
                                <li><em>报警原因：</em><em v-text="alarmDetail.alarmReason">预付费电表剩余量不足</em></li>
                                <li><em>报警位置：</em><em v-text="alarmDetail.alarmPositionName">001号租户-001号电表</em></li>
                                <li><em>报警时间：</em><em v-text="alarmDetail.alarmTime">2016.01.20 12：12</em></li>
                                <li v-show="alarmDetail.isLimit == 0"><em >中断时间：</em><em v-text="alarmDetail.interruptTime">2016.01.20 12：12</em></li>
                                <li><em>当前状态：</em>
                                    <em v-text="alarmDetail.status == 0? '未恢复':alarmDetail.status == 1? '已恢复':'已过期'">已恢复</em>
                                    <em class="gray12 mar10" v-show="alarmDetail.status == 0">(恢复后状态将自动更新)</em>
                                </li>
                                <li class="gray12 mar10" v-show="alarmDetail.status == 1">
                                    <em>恢复时间：</em>
                                    <em v-text="alarmDetail.finishTime">2018-09-03 17:14:40</em>
                                    <em class="mar10"></em>
                                </li>

                            </ul>
                        </div>
                        <!-- 报警chart -->
                        <div class="alarm-details-con" v-show="alarmDetail.isLimit == 1">
                            <div class="alarm-d-title" >
                                        <span>
                                            <em>剩余({{alarmDetail.unit}})</em>  <em class="val  red" v-text="alarmDetail.currentValue">3-5</em>
                                        </span>
                                        <span>
                                            <em>门限值 ({{alarmDetail.unit}})</em>  <em class="val" v-text="alarmDetail.limitValue">123</em>
                                        </span>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>