<div class="dpRight_body">
    <div class="chartPart" v-show="showChart">
        <div class="chartFoot">
            <!--耗x量-->
            <div v-if="(''+selecteddataCpmparaType.name).indexOf('耗')===0" class="chartFoot_main">
                <div class="items">
                    <div>
                        <p class="title"><span>{{timeMap}}</span></p>
                        <p class="cont">
                            <span><em>{{energyInfo.data||'--'}}</em><i v-show="energyInfo.data!=null">{{energyInfo.dataUnit}}</i></span></p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>同比历史</p>
                        <p>
                            <span :class="energyInfo.tongbiRatio==null?'':energyInfo.tongbiRatio>0?'isredColor':'isgreenColor'"><em>{{(energyInfo.tongbiRatio>0?'+'+energyInfo.tongbiRatio:energyInfo.tongbiRatio)||'--'}}</em><em><i v-show="energyInfo.tongbiRatio!=null">%</i></em></span>
                        </p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>历史{{densityMap}}平均值</p>
                        <p><span><em>{{energyInfo.historyAvg ||'--'}}</em> <i v-show="energyInfo.historyAvg!=null">{{energyInfo.dataUnit}}</i></span>
                        </p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>{{densityMap}}单位平米值</p>
                        <p><span><em>{{energyInfo.areaAvg||'--'}}</em>  <i v-show="energyInfo.areaAvg!=null">{{energyInfo.areaAvgUnit}}</i></span>
                        </p>
                    </div>
                </div>
            </div>
            <!--剩余量-->
            <div v-if="(selecteddataCpmparaType.name+'')==='剩余量'" class="chartFoot_main">
                <div class="items">
                    <div>
                        <p><span>平均{{densityMap}}<em>{{energyInfo.type==0?'能耗':'费用'}}</em></span></p>
                        <p><span><em>{{energyInfo.avgData ||'--'}}</em>  <i v-show="energyInfo.avgData!=null">{{energyInfo.dataUnit}}</i></span>
                        </p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>最大{{densityMap}}<em>{{energyInfo.type==0?'能耗':'费用'}}</em></p>
                        <p><span><em>{{energyInfo.maxData ||'--'}}</em>  <i v-show="energyInfo.maxData!=null">{{energyInfo.dataUnit}}</i></span>
                        </p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p><em>{{new Date().format('yyyy年MM月dd日')}}</em>剩余量</p>
                        <p>
                            <span><em>{{energyInfo.remainData ||'--'}}</em>  <i v-show="energyInfo.remainData!=null">{{energyInfo.dataUnit}}</i></span>
                        </p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>剩余使用天数</p>
                        <p><span
                                :class="energyInfo.isAlarm==0?'':'isredColor'"><em>{{energyInfo.remainDays||'--'}}</em> <i v-show="energyInfo.remainDays!=null">天</i></span>
                        </p>
                    </div>
                </div>
            </div>
            <!--费用-->
            <div v-if="(selecteddataCpmparaType.name+'')==='费用'" class="chartFoot_main">
                <div class="items">
                    <div>
                        <p><span>{{timeMap}}</span></p>
                        <p><span><em>{{energyInfo.data||'--'}}</em> <i v-show="energyInfo.data!=null">元</i></span></p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>同比历史</p>
                        <p>
                            <span :class="energyInfo.tongbiRatio==null?'':energyInfo.tongbiRatio>0?'isredColor':'isgreenColor'"><em>{{(energyInfo.tongbiRatio>0?'+'+energyInfo.tongbiRatio:energyInfo.tongbiRatio)||'--'}}</em><em><i v-show="energyInfo.tongbiRatio!=null">%</i></em></span>
                        </p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>历史{{densityMap}}平均值</p>
                        <p><span><em>{{energyInfo.historyAvg ||'--'}}</em> <i v-show="energyInfo.historyAvg!=null">元</i></span></p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>{{densityMap}}单位平米值</p>
                        <p><span><em>{{energyInfo.areaAvg||'--'}}</em><i v-show="energyInfo.areaAvg!=null">元/㎡</i></span></p>
                    </div>
                </div>
            </div>
            <!--电功率-->
            <div v-if="(selecteddataCpmparaType.name+'')==='电功率'" class="chartFoot_main">
                <div class="items">
                    <div>
                        <p><span>当前负荷率</span></p>
                        <p><span><em>{{energyInfo.load||'--'}}</em> <i v-show="energyInfo.load!=null">%</i></span></p>
                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>历史功率最大值</p>
                        <p><span><em style="font-size: 50px">{{energyInfo.historyMaxLoad||'--'}}</em><i v-show="energyInfo.historyMaxLoad!=null">kW</i></span></p>
                        <p><span>{{energyInfo.historyMaxLoadTime||'--'}} </span></p>

                    </div>
                </div>
                <div class="items">
                    <div>
                        <p>空开容量（A）</p>
                        <p><span><em>{{energyInfo.emptyValue||'--'}}</em></span></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="dpRight_body_title clearFloat">
            <span class="dName">{{selectedchunk.tenementName + '-' + detailTitleTime + '-' + selecteddataCpmparaType.name}}</span>
            <div class="meterWraper"
                 v-show="(selecteddataCpmparaType.name+'')==='剩余量'||(selecteddataCpmparaType.name+'')==='电功率'">
                <div class="meterWrapername">切换{{treeTypemap}}表</div>
                <pcombobox-normal sel="monitorModel.instance().chooseMeter" id="'meterWraperCombo'" bind="true" align="right" orientation="down">
                    <header  placeholder="'头部提示文本'"></header>
                    <item datasource="meterArr" text="meterId" ></item>
                </pcombobox-normal>
            </div>
        </div>
        <div class="footwrap">
            <div class="chartMain">
                <ploading-part id="updatachartLoading" text="加载中，请稍候..."></ploading-part>
                <div id="chart_expend"></div>
            </div>
            <div class="monitorNodata" v-show="chartData.length===0">
                <pnotice-nodata text="暂无数据"></pnotice-nodata>
            </div>
        </div>
    </div>
    <%include alarmRecord.html%>
</div>