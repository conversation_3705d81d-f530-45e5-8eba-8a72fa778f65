<!-- 巡检查看 -->
<div id="roundsPage" class="clearFloat" v-show="currentPage === 'roundsPage'">
    <div class="rpRightp">
        <!--右侧租户块 -->
        <div class="rpRightp_title">
            <div id="typeTab">
                <div class="tab-buttondiy">
                    <div class="tab-tit">
                        <ul>
                            <li @click="selEnergyTab($event,index)" :class="index===0?'cur':''"  v-for="(model,index) in energysType" ><em>{{model.typeName}}</em><i v-show="model.isAlarm"></i></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="toggleFood-format">
            <pcombobox-normal sel="tabLesseePattern" id="floorandcommercial">
                <header click="restoreSearch"  placeholder="查看楼层"></header>
                <item datasource="tenementEvent.pollingbuttonArr" text="name" ></item>
            </pcombobox-normal>
        </div>
        <div class="rpRightp_body scroll_big">

            <!--/*-->
            <!--报警状态class .tentAlarmState-->
            <!--报警字体class  .roundsAlarmColor-->
            <!--*/-->
            <ul id="rPtent">
                <li v-for="(model,index) in pollingExamineData">
                    <div class="rpR_foolNo">
                        <span :title="model.floorTip">{{model.floorName||model.typeName}}</span>
                        <div class="alarmPoint" v-if="model.isAlarm"></div>
                    </div>
                    <div class="tentCellwrap" v-for="(model,index1) in model.tenantList">
                        <div class="tentCell" @click.stop="goToAlarmDetail(model, index,index1,'XJ')">
                            <div class="tentCell_name">
                                <h5 class="name slh">{{model.tenantName}}</h5>
                                <h5 class="floor">
                                    <div class="slh">
                                        <span v-for="( model,index) in model.roomList" :style="{'margin-left':index>0?'10px':''}">
                                            <span>{{model.roomId}}</span>
                                        </span>
                                    </div>
                                </h5>
                            </div>
                            <div class="tentCell_detail">
                                <div class="detail_left">
                                    <h5 class="name">今日{{model.energyTypeName}}</h5>
                                    <h5 class="unit" ><em class="slh">{{model.data||'--'}}<em v-show="model.data!=null">{{model.unit}}</em></em></h5>
                                </div>
                                <div class="detail_right" v-if="model.type == 1">
                                    <h5 class="name">产生费用</h5>
                                    <h5 class="unit"><em class="slh">{{model.cost||'--'}}<em v-show="model.cost!=null">元</em></em></h5>
                                </div>
                                <div class="detail_right" v-if="model.type == 0">
                                    <h5 class="name">剩余</h5>
                                    <h5 class="unit" :class="{detail_right_alarm: model.isAlarm}">
                                        <em class="slh"> {{model.remainDays||'--'}}<em v-show="model.remainDays!=null">天</em></em></h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
            <!-- 无数据 -->
            <div class="monitorNodata" v-show="pollingExamineData.length===0">
                <pnotice-nodata text="暂无数据"></pnotice-nodata>
            </div>
        </div>
    </div>
</div>