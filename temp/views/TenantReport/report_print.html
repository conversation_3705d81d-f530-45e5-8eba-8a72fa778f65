<div id="report_printing">
    <section class="reportl">
        <header>
            <p v-show="buttonShow">创建新报表</p>
            <div class="reportlp2" v-show="buttonShow" p-create="button-backBlueBorder" p-bind="attr:{text:'模板管理'}"></div>
        </header>
        <article class="srolltop">
            <p>选择模板</p>
            <aside>
                <div class="reportlcon">
                    <ul class="reportlul">
                        <li v-for="item in getTempList" @click="selTemp(item,$index)"><!--v-if="item.valid==1"-->
                            <b><em v-text="item.formatType" :class="item.type.length>1?  'scale': ''" >日</em></b>
                            <b :title="item.name" v-text="item.name">啥都撒接口的缴费</b>
                            <i class="guanbi" @click="feihumoban"></i>
                        </li>
                    </ul>
                    <div class="articdiv1" v-show="mobanshifouxian!==0">
                        <div class="articdiv1acont" id="option0" style="position: relative;">
                            <aside class="articdiv1a1" :class="{mobanxuanzhong:xuanzhongindex==0,'xuanzhewanchgeng':xuanzhongshij.length!==0}">
                                <i class="articdiv1ai1">1</i>
                                <i>选择时间</i>
                                <i class="xiaojiantou" :class="xuanzhongindex==0 ? 'normal' : ''"></i>
                            </aside>
                            <em onclick="stopProp(event)" class="shijianxzriqi" shijian="xs" v-show="xuanzhongshij.length" ;>
                                <strong v-for="item in xuanzhongshij" track-by="$index" >
                                    <b>{{item}}<i class="shijianshanchu" onclick="deleteOption(event)" v-show="currSelTemplate.type!='ss'">x</i></b>
                                </strong>
                            </em>
                            <div class="cover" onclick="stopProp(event)" style="width: 100%;height: 100%;position: absolute;top: 0;left: 0;z-index: 1;" v-show="currSelTemplate.type=='ss'"></div>
                        </div>
                        <div class="articdiv1acont" id="option1" v-if="currSelTemplate.includeBuild">
                            <aside class="articdiv1a1 articdiv1a2" :class="{mobanxuanzhong:xuanzhongindex==1,'xuanzhewanchgeng':xuanzhongBUild.length!==0}">
                                <i class="articdiv1ai1">2</i>
                                <i>选择建筑</i>
                                <i class="xiaojiantou" :class="xuanzhongindex==1 ? 'normal' : ''"></i>
                            </aside>
                            <em onclick="stopProp(event)" class="shijianxzriqi" jianzhu="xs" v-show="xuanzhongBUild.length!==0">
                                <strong v-for="item in xuanzhongBUild">
                                    <b jianzhu='text'>
                                    <!-- {{item}} -->
                                        <span class="xuanzhongTxt">{{item}}</span>
                                        <i class="shijianshanchu" onclick="deleteOption(event)">
                                            x
                                        </i>
                                    </b>
                                </strong>
                            </em>
                        </div>
                        <div class="articdiv1acont duoxiangxianzhe" v-if="duoxiangxianzhe.indexOf(1)!==-1" id="option2" :class="{cdisable:!xuanzhongBUild.length}">
                            <aside class="articdiv1a1 articdiv1a3" :class="{mobanxuanzhong:xuanzhongindex==2,'xuanzhewanchgeng':xuanzhongfenxuang.length!==0}">
                                <i class="articdiv1ai1">3</i>
                                <i>选择分项</i>
                                <i class="xiaojiantou" :class="xuanzhongindex==2 ? 'normal' : ''"></i>
                            </aside>
                            <em onclick="stopProp(event)" class="shijianxzriqi" v-show="xuanzhongfenxuang.length!==0">
                                <strong v-for="item in xuanzhongfenxuang1">
                                    <b><span class="xuanzhongTxt" :title="item">{{item}}</span><i class="shijianshanchu" onclick="deleteOption(event)">x</i></b>
                                </strong>
                            </em>
                        </div>
                        <div class="articdiv1acont" v-if="duoxiangxianzhe.indexOf(2)!==-1" id="option3">
                            <aside class="articdiv1a1" :class="{mobanxuanzhong:duoxiangxianzhe.indexOf(1)===-1 && xuanzhongindex==2 || xuanzhongindex==3,'xuanzhewanchgeng':xuanzhongzhilu.length!==0}">
                                <i class="articdiv1ai1" v-text="duoxiangxianzhe.indexOf(1)===-1 ? '3' : '4'">3</i>
                                <i>选择支路</i>
                                <i class="xiaojiantou" :class="xuanzhongindex==3 ? 'normal' : ''"></i>
                            </aside>
                            <em onclick="stopProp(event)" class="shijianxzriqi" v-show="xuanzhongzhilu.length!==0">
                                <strong v-for="item in xuanzhongzhilu">
                                    <b>{{item}}<i class="shijianshanchu" onclick="deleteOption(event)">x</i></b>
                                </strong>
                            </em>
                        </div>
                    </div>
                </div>
            </aside>
        </article>
        <p class="shengchengbaobiao"><i :class="xuanzhongshij.length && (!currSelTemplate.includeBuild || xuanzhongBUild.length) && (duoxiangxianzhe.indexOf(1) === -1 || xuanzhongfenxuang.length) && (duoxiangxianzhe.indexOf(2) === -1 || xuanzhongzhilu.length) ? '' : 'cdisable'" @click="createReport">生成报表</i></p>
        <div class="shijiankongjian danchukuang" style="display: none;">
            <div id="divaa"></div>
            <div class="shijiankongdiv2">
                <div class="shijianquedingan" p-create="button-backBlueBorder" p-bind="attr:{text:'确定'}"></div>
            </div>
        </div>
        <div class="seachjianzhu danchukuang" style="display: none;">
            <div p-create="tree-normal" p-bind="attr:{items:getBuildList,protext:name,prochild:child,ritems:'searchBildList',nodeid:id},event:{click:selBuild,sel:'filterSelBuild'}" p-rely="true"></div>
        </div>
        <div class="daixialasearch danchukuang" style="display: none;">
            <div class="xiala">
                <div class="xialaliebiao" id="subitemCombo" p-create="combobox-region" p-bind="attr:{items:getItemList,text:name,hsource:toSubitemTree.name},event:{sel:selSubitemTree}" p-rely="true">
                </div>
            </div>
            <div class="xialasearch">
                <div p-create="tree-combobox" p-bind="attr:{items:getItemTree,protext:name,prochild:child,ritems:'searchItemTree',nodeid:id},event:{click:selSubitem,sel:'filterSelSubitem'}" p-rely="true"></div>
            </div>
        </div>
        <!--支路-->
        <div class="seachzhilu danchukuang" style="display: none;">
            <div p-create="tree-combobox" p-bind="attr:{items:getBranchTree,protext:name,prochild:child,ritems:'searchBranchTree',nodeid:id},event:{click:selBranch,sel:'filterSelBranch'}" p-rely="true"></div>
        </div>
    </section>
    <section class="reportr">
        <header>
            <div class="reportrtabd">
                <ul class="reportrtab">
                    <li :class="{tabbeixuanzhong:$index==tabbeixuanzhong}" v-for="item in showReports" @click='selReportTab(item.reportName),tabbeixuanzhong=$index' track-by="$index" v-bind:title="item.reportName">
                        <i v-show="item.load"></i>
                        <i>{{item.reportName}}</i>
                        <i @click="deletetab(item.reportName,$index,item)"></i>
                    </li>
                </ul>
            </div>
            <div class="xialatabanniu" :class="{cdisable:tabarr.length==0}"><i></i></div>
            <div class="mobanxialakuang" style="display: none;">
                <ul>
                    <li v-for="item in tabarr" :class="item === $root.curReport.reportName ? 'show' : ''" @click="selReportCombo(item,$index)" track-by="$index" v-bind:title="item">
                        <span>{{item}}</span>
                    </li>
                </ul>
            </div>
        </header>
        <article>
            <img src="/img/TenantReport/wumoban.png" v-show="!allReports.length" />
            <img src="/img/TenantReport/baobiaoload.png" v-show="showReports[tabbeixuanzhong].load" />
            <img src="/img/TenantReport/report_error.png" v-show="curReport.hideDownload" />
            <div class="reportrcont scroll_big" v-show="allReports.length && !showReports[tabbeixuanzhong].load && !curReport.hideDownload" id="reportContent">
            </div>
        </article>
        <div class="sheetPageBox" v-show="showReports.length && !showReports[tabbeixuanzhong].load && !showReports[tabbeixuanzhong].hideDownload">
            <div class="optionBox">
                <span class="arrowLeft" :class="showSheets.length && showSheets[0].index > curSheets[0].index ? 'show' : 'forbidClick'" @click="sheetToLeft"></span>
                <span class="arrowRight" :class="showSheets.length && showSheets[showSheets.length - 1].index < curSheets[curSheets.length - 1].index ? 'show' : 'forbidClick'" @click="sheetToRight"></span>
            </div>
            <span class="dianLeft" :class="showSheets.length && showSheets[0].index > curSheets[0].index ? 'show' : ''">...</span>
            <ul class="sheetPageUl">
                <li :class="item.selected ? 'show' : ''" v-for="item in showSheets" v-text="item.sheetName" :title="item.sheetName" @click="selSheet(item, $index)">sheet0
                </li>
            </ul>
            <span class="dianRight" :class="showSheets.length && showSheets[showSheets.length - 1].index < curSheets[curSheets.length - 1].index ? 'show' : ''">...</span>
        </div>
        <div class="reportrfoot" v-show="!showReports[tabbeixuanzhong].hideDownload && !showReports[tabbeixuanzhong].load">
            <p @click="xiazaibaobiao()">下载</p>
        </div>
    </section>
</div>
<subitemTree>
    <div>
        <subitemTree></subitemTree>
    </div>
</subitemTree>
