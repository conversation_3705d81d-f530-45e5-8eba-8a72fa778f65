﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8"/>
    <title>报表打印</title>
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <script type="text/javascript" src="/scripts/lib/jquery-2.0.0.min.js"></script>
    <script type="text/javascript" src="/scripts/lib/vue-1.0.24.min.js"></script>
    <script type="text/javascript" src="/scripts/tool/pconst.js"></script>
    <script type="text/javascript" src="/scripts/tool/pajax.js"></script>
    <script type="text/javascript" src="/scripts/extend/Date.js"></script>
    <script type="text/javascript" src="/scripts/extend/Math.js"></script>
    <script type="text/javascript" src="/scripts/tool/psecret.js"></script>
    <script type="text/javascript" src="/pcontrol/flatBlueSeries_src.js"></script>
    <script type="text/javascript" src="/scripts/tool/pautoComplete.js"></script>
    <script type="text/javascript" src="/script/TenantReport/controller.js"></script>
    <script type="text/javascript" src="/script/TenantReport/event.js"></script>
    <script type="text/javascript" src="/script/TenantReport/model.js"></script>
    <script type="text/javascript" src="/script/TenantReport/tool.js"></script>
    <link rel="stylesheet" href="/css/TenantReport/reset.css"/>
    <link rel="stylesheet" href="/css/TenantReport/common.css"/>
    <link rel="stylesheet" href="/css/TenantReport/report_printing.css"/>
    <link rel="stylesheet" href="/css/TenantReport/template_manageme.css"/>
    <link rel="stylesheet" href="/pcontrol/css/flatBlueSeries_min.css"/>
    <link rel="stylesheet" href="/pcontrol/css/flatBlueSeries_min_2.0.css"/>

</head>
<body>
<div class="report_level1">
<div class="report_level2">
    <%
        var hasPower = true;
        if (user.name === 'persagyAdmin') {
            hasPower = true;
        } else {
            var roles = user.role || [];
            for (var i = 0; i < roles.length; i++) {
                if (roles[i].id === 'PC-Admin') {
                    hasPower = true;
                    break;
                }
            }
        }
    %>

    <% include report_print.html %>
    <% include template_management.html %>
    <% include tishiye.html %>
</div>
</div>
</body>

</html>