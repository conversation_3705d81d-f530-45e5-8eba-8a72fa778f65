<div id="operating_record">

    <!-- 头部操作栏 -->
    <div class="operating_record_header clearfix">

        <!-- 左侧下拉框及搜索框 -->
        <div class="operating_record_header_left _left clearfix">

            <!-- 操作类型下拉框 -->
            <div class="record_type_box">
                <pcombobox-normal sel="staticEvent.selectRecordTypeItemEvent" bind="true" align="right"
                    orientation="down" id="'record_type'">
                    <header placeholder="'请选择记录类型'" click="dataModel.initRecordParams"></header>
                    <item datasource="recordTypeArr" text="recordName"></item>
                </pcombobox-normal>
            </div>

            <!-- 能耗类型下拉框 -->
            <div class="energy_type_box" v-show="m_recordEnergyTypeFlag">
                <pcombobox-normal sel="staticEvent.selectRecordEnergyItemEvent" bind="true" align="right"
                    orientation="down" id="'energy_type'">
                    <header placeholder="'请选择能耗类型'" click="dataModel.initRecordParams"></header>
                    <item datasource="energyTypeArr" text="name"></item>
                </pcombobox-normal>
            </div>
            <!-- 仪表设置类型下拉框 -->
            <div class="energy_type_box" v-show="m_setTypeFlag">
                <pcombobox-normal sel="staticEvent.selectRecordsetItemEvent" bind="true" align="right"
                    orientation="down" id="'set_type'">
                    <header placeholder="'剩余量清零'" click="dataModel.initRecordParams"></header>
                    <item datasource="setTypeArr" text="name"></item>
                </pcombobox-normal>
            </div>

            <!-- 时间选择控件 -->
            <div class="time_controller_box">
                <ptime-calendar id="time_controller" orientation="down" sel="staticEvent.getTimeRangeEvent">
                    <panel timetype="dwMy" startyear="2012" align="left"></panel>
                </ptime-calendar>
            </div>

            <!-- 选择建筑下拉框 -->
            <div class="choose_building_box" v-show="m_buildingFlag">
                <pcombobox-normal sel="staticEvent.selectRecordBuildingItemEvent" bind="true" id="'choose_building'">
                    <header placeholder="'请选择建筑'" prefix="建筑：" click="dataModel.initRecordParams"></header>
                    <item datasource="recordBuildingsArr" text="name"></item>
                </pcombobox-normal>
            </div>

            <!-- 即时搜索框 -->
            <div class="search_record_box" v-if="false">
                <psearch-promptly change="" focus="" placeholder="租户名称或租户编号">
                    <tip advisesource="" advisetext="name" suggestsource="" suggesttext="name"></tip>
                </psearch-promptly>
            </div>
        </div>

        <!-- 右侧下载按钮 -->
        <div class="operating_record_header_right _right">
            <div class="down_load_record" v-show="m_downloadBtnFlag">
                <pbutton-white text="'下载' + m_downloadBtnText" icon="'D'" click="staticEvent.downloadRecordListEvent"
                    bind="true"></pbutton-white>
            </div>
        </div>
    </div>

    <!-- 页面主要内容 -->
    <div class="operating_record_body">

        <!-- 操作记录表格 -->
        <div class="record_girds_box">
            <pgrid-multifunction style="width: 100%; max-height:100%;" id="'operating_record_gird'">
                <panel datasource="recordGridArr" lineclick="dataModel.initRecordParams" bind="true"></panel>
                <header>
                    <column name="'操作时间'" source="createTime" visible="colspanShowFlagObj.colspanFlag_1" bind="true">
                    </column>
                    <column name="'操作类型'" source="operateTypeName" visible="colspanShowFlagObj.colspanFlag_2"
                        bind="true"></column>
                    <column name="'操作值'" source="value" visible="colspanShowFlagObj.colspanFlag_3" bind="true"></column>
                    <column name="'故障时间'" source="faultTime" visible="colspanShowFlagObj.colspanFlag_4" bind="true">
                    </column>
                    <column name="'账单'" source="orderTime" visible="colspanShowFlagObj.colspanFlag_5" bind="true">
                    </column>
                    <column name="recordGridTittleObj.billNum + '单号'" source="orderId"
                        visible="colspanShowFlagObj.colspanFlag_6" bind="true"></column>
                    <column name="'租户名称' + recordGridTittleObj.tenementName" source="tenantName"
                        visible="colspanShowFlagObj.colspanFlag_7" bind="true"></column>
                    <column name="'仪表ID'" source="meterId" visible="colspanShowFlagObj.colspanFlag_8" bind="true">
                    </column>
                    <column name="'协议编码'" source="protocolId" visible="colspanShowFlagObj.colspanFlag_9" bind="true">
                    </column>
                    <column name="'请求'" source="request" visible="colspanShowFlagObj.colspanFlag_10" bind="true">
                    </column>
                    <column name="'呼应'" source="respond" visible="colspanShowFlagObj.colspanFlag_11" bind="true">
                    </column>
                    <column name="'充值类型'" source="channelType" bind="true" visible="managementModel.data.m_recordType == 1"></column>
                    <column name="recordGridTittleObj.amountMoney + '金额（元）'" source="money"
                        visible="colspanShowFlagObj.colspanFlag_12" bind="true"></column>
                    <column name="'充值量' + recordGridTittleObj.amountUnit" source="amount"
                        visible="colspanShowFlagObj.colspanFlag_13" bind="true"></column>
                    <!-- 20180605wp++ -->
                    <column name="'退费金额（元）'" source="money" visible="colspanShowFlagObj.colspanFlag_27" bind="true">
                    </column>
                    <column name="'退费量' + recordGridTittleObj.amountUnit" source="amount"
                        visible="colspanShowFlagObj.colspanFlag_28" bind="true"></column>
                    <!-- end -->
                    <column name="recordGridTittleObj.tenementTime + '时间'" source="operteTime"
                        visible="colspanShowFlagObj.colspanFlag_14" bind="true"></column>
                    <column name="'上次结算日期'" source="lastClearingTime" visible="colspanShowFlagObj.colspanFlag_15"
                        bind="true"></column>
                    <column name="'本次结算日期'" source="currentClearingTime" visible="colspanShowFlagObj.colspanFlag_16"
                        bind="true"></column>
                    <column name="'本次结算能耗' + recordGridTittleObj.currentSumEnergyUnit" source="currentSumEnergy"
                        visible="colspanShowFlagObj.colspanFlag_17" bind="true"></column>
                    <column name="'本次结算金额（元）'" source="money" visible="colspanShowFlagObj.colspanFlag_18" bind="true">
                    </column>
                    <column name="'修改前价格方案'" source="beforePriceName" visible="colspanShowFlagObj.colspanFlag_19"
                        bind="true"></column>
                    <column name="'修改后价格方案'" source="afterPriceName" visible="colspanShowFlagObj.colspanFlag_20"
                        bind="true"></column>
                    <column name="'操作人'" source="userName" visible="colspanShowFlagObj.colspanFlag_21" bind="true">
                    </column>
                    <column name="'租户编号'" source="tenantId" visible="colspanShowFlagObj.colspanFlag_22" bind="true">
                    </column>
                    <column name="'所在建筑'" source="buildingName" visible="colspanShowFlagObj.colspanFlag_23" bind="true">
                    </column>
                    <column name="'房间编号'" source="roomIds" visible="colspanShowFlagObj.colspanFlag_24" bind="true">
                    </column>
                    <column name="'安装位置'" source="faultPlace" visible="colspanShowFlagObj.colspanFlag_25" bind="true">
                    </column>
                    <column name="'故障类型'" source="faultName" visible="colspanShowFlagObj.colspanFlag_26" bind="true">
                    </column>
                    <!-- 20180608wp++ -->
                    <column name="'短信内容'" source="message" visible="colspanShowFlagObj.colspanFlag_29" bind="true">
                    </column>
                    <column name="'收信人'" source="contactName" visible="colspanShowFlagObj.colspanFlag_30" bind="true">
                    </column>
                    <column name="'手机号'" source="mobile" visible="colspanShowFlagObj.colspanFlag_31" bind="true">
                    </column>
                    <column name="'发送状态'" source="sendStatus" visible="colspanShowFlagObj.colspanFlag_32" bind="true">
                    </column>
                    <!-- 3.6版本++ -->
                    <column name="'房间编号'" source="roomCode" visible="colspanShowFlagObj.colspanFlag_33" bind="true">
                    </column>
                    <column name="'设置类型'" source="setTypeName" visible="colspanShowFlagObj.colspanFlag_34" bind="true">
                    </column>
                    <column name="'能源类型'" source="energyTypeName" visible="colspanShowFlagObj.colspanFlag_35"
                        bind="true"></column>
                    <column name="'操作时间'" source="operateTime" visible="colspanShowFlagObj.colspanFlag_36" bind="true">
                    </column>
                    <!-- end -->
                </header>
                <notice>
                    <div class="per-prompt-abnormalmess">
                        <span class="per-prompt_icon"></span>
                        <span class="per-prompt_title">暂无数据</span>
                    </div>
                </notice>
            </pgrid-multifunction>
        </div>

        <!-- 翻页控件 -->
        <div class="page_controller_box clearfix">
            <div class="page_con_box _right clearfix">
                <div>
                    <p>每页加载</p>
                </div>
                <div class="page_ipt">
                    <ptext-text id="'page_text'" value="m_pageSizeText" blur="staticEvent.verifyPageSizeEvent"
                        bind="true">
                        <verify errtip="请输入正整数" verifytype="positiveint"></verify>
                    </ptext-text>
                </div>
                <div>
                    <p>条</p>
                </div>
                <div class="page_set_btn" @click="setPageSizeEvent">
                    <pbutton-blue text="确定"></pbutton-blue>
                </div>
            </div>
            <div class="page_controller _right">
                <ppage-simple id="'page_controller_select'" orientation="up" sel="staticEvent.pageChangeEvent"
                    bind="true"></ppage-simple>
            </div>
        </div>
    </div>
</div>