<div id="backstage_config" >
    <!-- 仪表设置标题及表格 -->
    <div class="meter_config">
        <pgrid-multifunction style="width: 100%; min-height: 193px;" id="'meter_config_table'" bind="true">
            <panel datasource="meterConfigArr" templateid ="gridTpl"></panel>
            <header>
                <column name="配置名称"></column>
                <column name="配置文件"></column>
                <column name="上传时间"></column>
                <column name="操作"></column>
            </header>
        </pgrid-multifunction>
    </div>

    <!-- 基础设置标题及表格 -->
    <div class="basics_config">
        <h2>基础配置</h2>
        <pgrid-multifunction style="width: 100%; min-height: 207px;" id="'basics_config_table'" bind="true">
            <panel datasource="basicsConfigArr" templateid ="gridTpl"></panel>
            <header>
                <column name="配置名称"></column>
                <column name="配置文件"></column>
                <column name="上传时间"></column>
                <column name="操作"></column>
            </header>
            <button>
                <pcombobox-normal sel="staticEvent.selectBuildingItemEvent" bind="true" id="'select_box'">
                    <header placeholder="'请选择建筑'" prefix="建筑："></header>
                    <item datasource="bulidingsArr" text="name"></item>
                </pcombobox-normal>
            </button>
        </pgrid-multifunction>
    </div>
</div>