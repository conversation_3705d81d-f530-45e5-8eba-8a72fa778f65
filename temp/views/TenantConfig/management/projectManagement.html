<div id="projectManagement">
    <!--项目列表数据展示-->
    <div class="listModel" v-show="curPage=='listPage'">
        <div class="addProjectBtn">
            <pbutton-blue text="添加项目" click="staticEvent.addProject" id="addProject"></pbutton-blue>
        </div>
        <!--主体列表展示-->
        <div class="content_body">  
            <div class="projectManagement_girds_box">
                <pgrid-normal style="width: 100%;height: 138px;border-bottom: none" id="'projectManagement_gird'">
                    <panel datasource="projectGridArr" lineclick="staticEvent.showDetail" bind="true"></panel>
                    <header>
                        <column name="项目名称" source="name"></column>
                        <column name="项目编码" source="id"></column>
                        <column name="项目位置" source="address"></column>
                    </header>
                    <notice>
                        <div class="per-prompt-abnormalmess">
                            <span class="per-prompt_icon"></span>
                            <span class="per-prompt_title">暂无数据</span>
                        </div>
                    </notice>
                </pgrid-normal>
            </div>
        </div>   
    </div>
</div>