{"name": "uuid", "version": "3.1.0", "description": "RFC4122 (v1, v4, and v5) UUIDs", "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"mocha": "3.1.2"}, "scripts": {"test": "mocha test/test.js"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js"}, "repository": {"type": "git", "url": "git+https://github.com/kelektiv/node-uuid.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "AJ <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "gitHead": "c50ac88f098ecfbff9a940816c8e6825ffd7e05a", "bugs": {"url": "https://github.com/kelektiv/node-uuid/issues"}, "homepage": "https://github.com/kelektiv/node-uuid#readme", "_id": "uuid@3.1.0", "_npmVersion": "5.0.3", "_nodeVersion": "7.10.0", "_npmUser": {"name": "broofa", "email": "<EMAIL>"}, "maintainers": [{"name": "broofa", "email": "<EMAIL>"}, {"name": "defunctzombie", "email": "<EMAIL>"}, {"name": "vvo", "email": "<EMAIL>"}], "dist": {"integrity": "sha512-DIWtzUkw04M4k3bf1IcpS2tngXEL26YUD2M0tMDUpnUrz2hgzUBlD55a4FjdLGPvfHxS6uluGWvaVEqgBcVa+g==", "shasum": "3dd3d3e790abc24d7b0d3a034ffababe28ebbc04", "tarball": "https://registry.npmjs.org/uuid/-/uuid-3.1.0.tgz"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/uuid-3.1.0.tgz_1497635691778_0.6424044836312532"}, "directories": {}, "_shasum": "3dd3d3e790abc24d7b0d3a034ffababe28ebbc04", "_resolved": "https://registry.npmjs.org/uuid/-/uuid-3.1.0.tgz", "_from": "uuid@latest", "readme": "ERROR: No README data found!"}