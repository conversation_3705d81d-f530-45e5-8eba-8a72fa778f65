{"author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://debuggable.com/"}, "name": "form-data", "description": "A library to create readable \"multipart/form-data\" streams. Can be used to submit forms and file uploads to other web applications.", "version": "1.0.0-rc4", "repository": {"type": "git", "url": "git://github.com/form-data/form-data.git"}, "main": "./lib/form_data", "browser": "./lib/browser", "scripts": {"pretest": "rimraf coverage test/tmp", "test": "istanbul cover --report none test/run.js", "posttest": "istanbul report", "lint": "eslint lib/*.js test/*.js test/**/*.js", "predebug": "rimraf coverage test/tmp", "debug": "verbose=1 ./test/run.js", "check": "istanbul check-coverage coverage/coverage*.json", "coverage": "codacy-coverage < ./coverage/lcov.info; true"}, "pre-commit": ["lint", "test", "check"], "engines": {"node": ">= 0.10"}, "dependencies": {"async": "^1.5.2", "combined-stream": "^1.0.5", "mime-types": "^2.1.10"}, "license": "MIT", "devDependencies": {"codacy-coverage": "^1.1.3", "coveralls": "^2.11.8", "cross-spawn": "^2.1.5", "eslint": "^2.4.0", "fake": "^0.2.2", "far": "^0.0.7", "formidable": "^1.0.17", "istanbul": "^0.4.2", "pre-commit": "^1.1.2", "request": "^2.69.0", "rimraf": "^2.5.2"}, "gitHead": "f73996e0508ee2d4b2b376276adfac1de4188ac2", "bugs": {"url": "https://github.com/form-data/form-data/issues"}, "homepage": "https://github.com/form-data/form-data#readme", "_id": "form-data@1.0.0-rc4", "_shasum": "05ac6bc22227b43e4461f488161554699d4f8b5e", "_from": "form-data@>=1.0.0-rc3 <1.1.0", "_npmVersion": "2.14.9", "_nodeVersion": "0.12.11", "_npmUser": {"name": "alexindigo", "email": "<EMAIL>"}, "dist": {"shasum": "05ac6bc22227b43e4461f488161554699d4f8b5e", "tarball": "https://registry.npmjs.org/form-data/-/form-data-1.0.0-rc4.tgz"}, "maintainers": [{"name": "felix<PERSON>", "email": "<EMAIL>"}, {"name": "idralyuk", "email": "<EMAIL>"}, {"name": "alexindigo", "email": "<EMAIL>"}, {"name": "mikeal", "email": "<EMAIL>"}, {"name": "celer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "piercey<PERSON><PERSON>@gmail.com"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/form-data-1.0.0-rc4.tgz_1458059747097_0.14101114077493548"}, "directories": {}, "_resolved": "https://registry.npmjs.org/form-data/-/form-data-1.0.0-rc4.tgz"}