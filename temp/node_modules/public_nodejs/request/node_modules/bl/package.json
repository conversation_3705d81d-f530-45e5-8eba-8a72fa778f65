{"name": "bl", "version": "1.1.2", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "main": "bl.js", "scripts": {"test": "node test/test.js | faucet"}, "repository": {"type": "git", "url": "https://github.com/rvagg/bl.git"}, "homepage": "https://github.com/rvagg/bl", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "dependencies": {"readable-stream": "~2.0.5"}, "devDependencies": {"faucet": "0.0.1", "hash_file": "~0.1.1", "tape": "~4.4.0"}, "gitHead": "ea42021059dc65fc60d7f4b9217c73431f09d23d", "bugs": {"url": "https://github.com/rvagg/bl/issues"}, "_id": "bl@1.1.2", "_shasum": "fdca871a99713aa00d19e3bbba41c44787a65398", "_from": "bl@>=1.1.2 <1.2.0", "_npmVersion": "3.3.12", "_nodeVersion": "5.3.0", "_npmUser": {"name": "rvagg", "email": "<EMAIL>"}, "maintainers": [{"name": "rvagg", "email": "<EMAIL>"}], "dist": {"shasum": "fdca871a99713aa00d19e3bbba41c44787a65398", "tarball": "https://registry.npmjs.org/bl/-/bl-1.1.2.tgz"}, "_npmOperationalInternal": {"host": "packages-9-west.internal.npmjs.com", "tmp": "tmp/bl-1.1.2.tgz_1455246621698_0.6300242957659066"}, "directories": {}, "_resolved": "https://registry.npmjs.org/bl/-/bl-1.1.2.tgz"}