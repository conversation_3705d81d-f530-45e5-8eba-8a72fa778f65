{"name": "string_decoder", "version": "0.10.31", "description": "The string_decoder module from Node core", "main": "index.js", "dependencies": {}, "devDependencies": {"tap": "~0.4.8"}, "scripts": {"test": "tap test/simple/*.js"}, "repository": {"type": "git", "url": "git://github.com/rvagg/string_decoder.git"}, "homepage": "https://github.com/rvagg/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "readme": "**string_decoder.js** (`require('string_decoder')`) from Node.js core\n\nCopyright Joyent, Inc. and other Node contributors. See LICENCE file for details.\n\nVersion numbers match the versions found in Node core, e.g. 0.10.24 matches Node 0.10.24, likewise 0.11.10 matches Node 0.11.10. **Prefer the stable version over the unstable.**\n\nThe *build/* directory contains a build script that will scrape the source from the [joyent/node](https://github.com/joyent/node) repo given a specific Node version.", "readmeFilename": "README.md", "bugs": {"url": "https://github.com/rvagg/string_decoder/issues"}, "_id": "string_decoder@0.10.31", "_from": "string_decoder@>=0.10.0 <0.11.0"}