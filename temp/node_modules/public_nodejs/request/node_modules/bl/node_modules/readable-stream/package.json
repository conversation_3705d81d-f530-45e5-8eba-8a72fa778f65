{"name": "readable-stream", "version": "2.0.6", "description": "Streams3, a user-land copy of the stream library from Node.js", "main": "readable.js", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "~1.0.0", "process-nextick-args": "~1.0.6", "string_decoder": "~0.10.x", "util-deprecate": "~1.0.1"}, "devDependencies": {"tap": "~0.2.6", "tape": "~4.5.1", "zuul": "~3.9.0"}, "scripts": {"test": "tap test/parallel/*.js test/ours/*.js", "browser": "npm run write-zuul && zuul -- test/browser.js", "write-zuul": "printf \"ui: tape\nbrowsers:\n  - name: $BROWSER_NAME\n    version: $BROWSER_VERSION\n\">.zuul.yml"}, "repository": {"type": "git", "url": "git://github.com/nodejs/readable-stream.git"}, "keywords": ["readable", "stream", "pipe"], "browser": {"util": false}, "license": "MIT", "gitHead": "01fb5608a970b42c900b96746cadc13d27dd9d7e", "bugs": {"url": "https://github.com/nodejs/readable-stream/issues"}, "homepage": "https://github.com/nodejs/readable-stream#readme", "_id": "readable-stream@2.0.6", "_shasum": "8f90341e68a53ccc928788dacfcd11b36eb9b78e", "_from": "readable-stream@>=2.0.5 <2.1.0", "_npmVersion": "3.6.0", "_nodeVersion": "5.7.0", "_npmUser": {"name": "cwmma", "email": "<EMAIL>"}, "dist": {"shasum": "8f90341e68a53ccc928788dacfcd11b36eb9b78e", "tarball": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz"}, "maintainers": [{"name": "isaacs", "email": "<EMAIL>"}, {"name": "tootallnate", "email": "<EMAIL>"}, {"name": "rvagg", "email": "<EMAIL>"}, {"name": "cwmma", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/readable-stream-2.0.6.tgz_1457893507709_0.369257491780445"}, "directories": {}, "_resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.0.6.tgz"}