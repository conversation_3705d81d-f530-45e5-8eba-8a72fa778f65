{"name": "generate-function", "version": "2.0.0", "description": "Module that helps you write generated functions in Node", "main": "index.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mafintosh/generate-function.git"}, "keywords": ["generate", "code", "generation", "function", "performance"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/generate-function/issues"}, "homepage": "https://github.com/mafintosh/generate-function", "devDependencies": {"tape": "^2.13.4"}, "readme": "# generate-function\n\nModule that helps you write generated functions in Node\n\n```\nnpm install generate-function\n```\n\n[![build status](http://img.shields.io/travis/mafintosh/generate-function.svg?style=flat)](http://travis-ci.org/mafintosh/generate-function)\n\n## Disclamer\n\nWriting code that generates code is hard.\nYou should only use this if you really, really, really need this for performance reasons (like schema validators / parsers etc).\n\n## Usage\n\n``` js\nvar genfun = require('generate-function')\n\nvar addNumber = function(val) {\n  var fn = genfun()\n    ('function add(n) {')\n      ('return n + %d', val) // supports format strings to insert values\n    ('}')\n\n  return fn.toFunction() // will compile the function\n}\n\nvar add2 = addNumber(2)\n\nconsole.log('1+2=', add2(1))\nconsole.log(add2.toString()) // prints the generated function\n```\n\nIf you need to close over variables in your generated function pass them to `toFunction(scope)`\n\n``` js\nvar multiply = function(a, b) {\n  return a * b\n}\n\nvar addAndMultiplyNumber = function(val) {\n  var fn = genfun()\n    ('function(n) {')\n      ('if (typeof n !== \"number\") {') // ending a line with { will indent the source\n        ('throw new Error(\"argument should be a number\")')\n      ('}')\n      ('var result = multiply(%d, n+%d)', val, val)\n      ('return result')\n    ('}')\n\n  // use fn.toString() if you want to see the generated source\n\n  return fn.toFunction({\n    multiply: multiply\n  })\n}\n\nvar addAndMultiply2 = addAndMultiplyNumber(2)\n\nconsole.log('(3 + 2) * 2 =', addAndMultiply2(3))\n```\n\n## Related\n\nSee [generate-object-property](https://github.com/mafintosh/generate-object-property) if you need to safely generate code that\ncan be used to reference an object property\n\n## License\n\nMIT", "readmeFilename": "README.md", "_id": "generate-function@2.0.0", "_from": "generate-function@>=2.0.0 <3.0.0"}