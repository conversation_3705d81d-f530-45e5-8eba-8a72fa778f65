[{"description": "integer type matches integers", "schema": {"type": "integer"}, "tests": [{"description": "an integer is an integer", "data": 1, "valid": true}, {"description": "a float is not an integer", "data": 1.1, "valid": false}, {"description": "a string is not an integer", "data": "foo", "valid": false}, {"description": "an object is not an integer", "data": {}, "valid": false}, {"description": "an array is not an integer", "data": [], "valid": false}, {"description": "a boolean is not an integer", "data": true, "valid": false}, {"description": "null is not an integer", "data": null, "valid": false}]}, {"description": "number type matches numbers", "schema": {"type": "number"}, "tests": [{"description": "an integer is a number", "data": 1, "valid": true}, {"description": "a float is a number", "data": 1.1, "valid": true}, {"description": "a string is not a number", "data": "foo", "valid": false}, {"description": "an object is not a number", "data": {}, "valid": false}, {"description": "an array is not a number", "data": [], "valid": false}, {"description": "a boolean is not a number", "data": true, "valid": false}, {"description": "null is not a number", "data": null, "valid": false}]}, {"description": "string type matches strings", "schema": {"type": "string"}, "tests": [{"description": "1 is not a string", "data": 1, "valid": false}, {"description": "a float is not a string", "data": 1.1, "valid": false}, {"description": "a string is a string", "data": "foo", "valid": true}, {"description": "an object is not a string", "data": {}, "valid": false}, {"description": "an array is not a string", "data": [], "valid": false}, {"description": "a boolean is not a string", "data": true, "valid": false}, {"description": "null is not a string", "data": null, "valid": false}]}, {"description": "object type matches objects", "schema": {"type": "object"}, "tests": [{"description": "an integer is not an object", "data": 1, "valid": false}, {"description": "a float is not an object", "data": 1.1, "valid": false}, {"description": "a string is not an object", "data": "foo", "valid": false}, {"description": "an object is an object", "data": {}, "valid": true}, {"description": "an array is not an object", "data": [], "valid": false}, {"description": "a boolean is not an object", "data": true, "valid": false}, {"description": "null is not an object", "data": null, "valid": false}]}, {"description": "array type matches arrays", "schema": {"type": "array"}, "tests": [{"description": "an integer is not an array", "data": 1, "valid": false}, {"description": "a float is not an array", "data": 1.1, "valid": false}, {"description": "a string is not an array", "data": "foo", "valid": false}, {"description": "an object is not an array", "data": {}, "valid": false}, {"description": "an array is not an array", "data": [], "valid": true}, {"description": "a boolean is not an array", "data": true, "valid": false}, {"description": "null is not an array", "data": null, "valid": false}]}, {"description": "boolean type matches booleans", "schema": {"type": "boolean"}, "tests": [{"description": "an integer is not a boolean", "data": 1, "valid": false}, {"description": "a float is not a boolean", "data": 1.1, "valid": false}, {"description": "a string is not a boolean", "data": "foo", "valid": false}, {"description": "an object is not a boolean", "data": {}, "valid": false}, {"description": "an array is not a boolean", "data": [], "valid": false}, {"description": "a boolean is not a boolean", "data": true, "valid": true}, {"description": "null is not a boolean", "data": null, "valid": false}]}, {"description": "null type matches only the null object", "schema": {"type": "null"}, "tests": [{"description": "an integer is not null", "data": 1, "valid": false}, {"description": "a float is not null", "data": 1.1, "valid": false}, {"description": "a string is not null", "data": "foo", "valid": false}, {"description": "an object is not null", "data": {}, "valid": false}, {"description": "an array is not null", "data": [], "valid": false}, {"description": "a boolean is not null", "data": true, "valid": false}, {"description": "null is null", "data": null, "valid": true}]}, {"description": "multiple types can be specified in an array", "schema": {"type": ["integer", "string"]}, "tests": [{"description": "an integer is valid", "data": 1, "valid": true}, {"description": "a string is valid", "data": "foo", "valid": true}, {"description": "a float is invalid", "data": 1.1, "valid": false}, {"description": "an object is invalid", "data": {}, "valid": false}, {"description": "an array is invalid", "data": [], "valid": false}, {"description": "a boolean is invalid", "data": true, "valid": false}, {"description": "null is invalid", "data": null, "valid": false}]}]