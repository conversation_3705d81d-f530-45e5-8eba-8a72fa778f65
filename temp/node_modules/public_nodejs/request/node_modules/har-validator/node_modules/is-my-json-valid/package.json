{"name": "is-my-json-valid", "version": "2.13.1", "description": "A JSONSchema validator that uses code generation to be extremely fast", "main": "index.js", "dependencies": {"generate-function": "^2.0.0", "generate-object-property": "^1.1.0", "jsonpointer": "2.0.0", "xtend": "^4.0.0"}, "devDependencies": {"tape": "^2.13.4"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/is-my-json-valid"}, "keywords": ["json", "schema", "orderly", "jsonschema"], "author": {"name": "<PERSON>"}, "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/is-my-json-valid/issues"}, "homepage": "https://github.com/mafintosh/is-my-json-valid", "gitHead": "5bacc71441750bc6e79829abcfc21d4f2f0c4396", "_id": "is-my-json-valid@2.13.1", "_shasum": "d55778a82feb6b0963ff4be111d5d1684e890707", "_from": "is-my-json-valid@>=2.12.4 <3.0.0", "_npmVersion": "2.14.7", "_nodeVersion": "4.2.3", "_npmUser": {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "d55778a82feb6b0963ff4be111d5d1684e890707", "tarball": "https://registry.npmjs.org/is-my-json-valid/-/is-my-json-valid-2.13.1.tgz"}, "maintainers": [{"name": "freeall", "email": "<EMAIL>"}, {"name": "ma<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "watson", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-5-east.internal.npmjs.com", "tmp": "tmp/is-my-json-valid-2.13.1.tgz_1456180270224_0.17748022079467773"}, "directories": {}, "_resolved": "https://registry.npmjs.org/is-my-json-valid/-/is-my-json-valid-2.13.1.tgz"}