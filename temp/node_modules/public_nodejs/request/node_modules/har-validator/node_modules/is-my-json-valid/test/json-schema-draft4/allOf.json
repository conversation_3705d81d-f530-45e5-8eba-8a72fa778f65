[{"description": "allOf", "schema": {"allOf": [{"properties": {"bar": {"type": "integer"}}, "required": ["bar"]}, {"properties": {"foo": {"type": "string"}}, "required": ["foo"]}]}, "tests": [{"description": "allOf", "data": {"foo": "baz", "bar": 2}, "valid": true}, {"description": "mismatch second", "data": {"foo": "baz"}, "valid": false}, {"description": "mismatch first", "data": {"bar": 2}, "valid": false}, {"description": "wrong type", "data": {"foo": "baz", "bar": "quux"}, "valid": false}]}, {"description": "allOf with base schema", "schema": {"properties": {"bar": {"type": "integer"}}, "required": ["bar"], "allOf": [{"properties": {"foo": {"type": "string"}}, "required": ["foo"]}, {"properties": {"baz": {"type": "null"}}, "required": ["baz"]}]}, "tests": [{"description": "valid", "data": {"foo": "quux", "bar": 2, "baz": null}, "valid": true}, {"description": "mismatch base schema", "data": {"foo": "quux", "baz": null}, "valid": false}, {"description": "mismatch first allOf", "data": {"bar": 2, "baz": null}, "valid": false}, {"description": "mismatch second allOf", "data": {"foo": "quux", "bar": 2}, "valid": false}, {"description": "mismatch both", "data": {"bar": 2}, "valid": false}]}, {"description": "allOf simple types", "schema": {"allOf": [{"maximum": 30}, {"minimum": 20}]}, "tests": [{"description": "valid", "data": 25, "valid": true}, {"description": "mismatch one", "data": 35, "valid": false}]}]