[{"description": "additionalProperties being false does not allow other properties", "schema": {"properties": {"foo": {}, "bar": {}}, "patternProperties": {"^v": {}}, "additionalProperties": false}, "tests": [{"description": "no additional properties is valid", "data": {"foo": 1}, "valid": true}, {"description": "an additional property is invalid", "data": {"foo": 1, "bar": 2, "quux": "boom"}, "valid": false}, {"description": "ignores non-objects", "data": [1, 2, 3], "valid": true}, {"description": "patternProperties are not additional properties", "data": {"foo": 1, "vroom": 2}, "valid": true}]}, {"description": "additionalProperties allows a schema which should validate", "schema": {"properties": {"foo": {}, "bar": {}}, "additionalProperties": {"type": "boolean"}}, "tests": [{"description": "no additional properties is valid", "data": {"foo": 1}, "valid": true}, {"description": "an additional valid property is valid", "data": {"foo": 1, "bar": 2, "quux": true}, "valid": true}, {"description": "an additional invalid property is invalid", "data": {"foo": 1, "bar": 2, "quux": 12}, "valid": false}]}, {"description": "additionalProperties can exist by itself", "schema": {"additionalProperties": {"type": "boolean"}}, "tests": [{"description": "an additional valid property is valid", "data": {"foo": true}, "valid": true}, {"description": "an additional invalid property is invalid", "data": {"foo": 1}, "valid": false}]}, {"description": "additionalProperties are allowed by default", "schema": {"properties": {"foo": {}, "bar": {}}}, "tests": [{"description": "additional properties are allowed", "data": {"foo": 1, "bar": 2, "quux": true}, "valid": true}]}]