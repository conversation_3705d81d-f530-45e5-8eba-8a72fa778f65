[{"description": "integer", "schema": {"type": "integer"}, "tests": [{"description": "a bignum is an integer", "data": 12345678910111213141516171819202122232425262728293031, "valid": true}]}, {"description": "number", "schema": {"type": "number"}, "tests": [{"description": "a bignum is a number", "data": 98249283749234923498293171823948729348710298301928331, "valid": true}]}, {"description": "integer", "schema": {"type": "integer"}, "tests": [{"description": "a negative bignum is an integer", "data": -12345678910111213141516171819202122232425262728293031, "valid": true}]}, {"description": "number", "schema": {"type": "number"}, "tests": [{"description": "a negative bignum is a number", "data": -98249283749234923498293171823948729348710298301928331, "valid": true}]}, {"description": "string", "schema": {"type": "string"}, "tests": [{"description": "a bignum is not a string", "data": 98249283749234923498293171823948729348710298301928331, "valid": false}]}, {"description": "integer comparison", "schema": {"maximum": 18446744073709551615}, "tests": [{"description": "comparison works for high numbers", "data": 18446744073709551600, "valid": true}]}, {"description": "float comparison with high precision", "schema": {"maximum": 9.727837981879871e+26, "exclusiveMaximum": true}, "tests": [{"description": "comparison works for high numbers", "data": 9.727837981879871e+26, "valid": false}]}, {"description": "integer comparison", "schema": {"minimum": -18446744073709551615}, "tests": [{"description": "comparison works for very negative numbers", "data": -18446744073709551600, "valid": true}]}, {"description": "float comparison with high precision on negative numbers", "schema": {"minimum": -9.727837981879871e+26, "exclusiveMinimum": true}, "tests": [{"description": "comparison works for very negative numbers", "data": -9.727837981879871e+26, "valid": false}]}]