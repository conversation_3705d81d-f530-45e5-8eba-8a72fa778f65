{"name": "<PERSON><PERSON><PERSON>er", "description": "Simple JSON Addressing.", "tags": ["util", "simple", "util", "utility"], "version": "2.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "git+ssh://**************/janl/node-jsonpointer.git"}, "bugs": {"url": "http://github.com/janl/node-jsonpointer/issues"}, "engines": {"node": ">=0.6.0"}, "main": "./jsonpointer", "scripts": {"test": "node test.js"}, "license": "MIT", "gitHead": "26ea4a5c0fcb6d9a2e87f733403791dd05637af8", "homepage": "https://github.com/janl/node-jsonpointer#readme", "_id": "j<PERSON><PERSON>er@2.0.0", "_shasum": "3af1dd20fe85463910d469a385e33017d2a030d9", "_from": "j<PERSON><PERSON>er@2.0.0", "_npmVersion": "2.10.1", "_nodeVersion": "0.10.36", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "jan", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3af1dd20fe85463910d469a385e33017d2a030d9", "tarball": "https://registry.npmjs.org/jsonpointer/-/jsonpointer-2.0.0.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/jsonpointer/-/jsonpointer-2.0.0.tgz"}