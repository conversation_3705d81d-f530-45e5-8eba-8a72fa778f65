[{"description": "dependencies", "schema": {"dependencies": {"bar": ["foo"]}}, "tests": [{"description": "neither", "data": {}, "valid": true}, {"description": "nondependant", "data": {"foo": 1}, "valid": true}, {"description": "with dependency", "data": {"foo": 1, "bar": 2}, "valid": true}, {"description": "missing dependency", "data": {"bar": 2}, "valid": false}, {"description": "ignores non-objects", "data": "foo", "valid": true}]}, {"description": "multiple dependencies", "schema": {"dependencies": {"quux": ["foo", "bar"]}}, "tests": [{"description": "neither", "data": {}, "valid": true}, {"description": "nondependants", "data": {"foo": 1, "bar": 2}, "valid": true}, {"description": "with dependencies", "data": {"foo": 1, "bar": 2, "quux": 3}, "valid": true}, {"description": "missing dependency", "data": {"foo": 1, "quux": 2}, "valid": false}, {"description": "missing other dependency", "data": {"bar": 1, "quux": 2}, "valid": false}, {"description": "missing both dependencies", "data": {"quux": 1}, "valid": false}]}, {"description": "multiple dependencies subschema", "schema": {"dependencies": {"bar": {"properties": {"foo": {"type": "integer"}, "bar": {"type": "integer"}}}}}, "tests": [{"description": "valid", "data": {"foo": 1, "bar": 2}, "valid": true}, {"description": "no dependency", "data": {"foo": "quux"}, "valid": true}, {"description": "wrong type", "data": {"foo": "quux", "bar": 2}, "valid": false}, {"description": "wrong type other", "data": {"foo": 2, "bar": "quux"}, "valid": false}, {"description": "wrong type both", "data": {"foo": "quux", "bar": "quux"}, "valid": false}]}]