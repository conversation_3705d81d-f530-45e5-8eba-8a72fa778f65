[{"description": "object properties validation", "schema": {"properties": {"foo": {"type": "integer"}, "bar": {"type": "string"}}}, "tests": [{"description": "both properties present and valid is valid", "data": {"foo": 1, "bar": "baz"}, "valid": true}, {"description": "one property invalid is invalid", "data": {"foo": 1, "bar": {}}, "valid": false}, {"description": "both properties invalid is invalid", "data": {"foo": [], "bar": {}}, "valid": false}, {"description": "doesn't invalidate other properties", "data": {"quux": []}, "valid": true}, {"description": "ignores non-objects", "data": [], "valid": true}]}, {"description": "properties, patternProperties, additionalProperties interaction", "schema": {"properties": {"foo": {"type": "array", "maxItems": 3}, "bar": {"type": "array"}}, "patternProperties": {"f.o": {"minItems": 2}}, "additionalProperties": {"type": "integer"}}, "tests": [{"description": "property validates property", "data": {"foo": [1, 2]}, "valid": true}, {"description": "property invalidates property", "data": {"foo": [1, 2, 3, 4]}, "valid": false}, {"description": "patternProperty invalidates property", "data": {"foo": []}, "valid": false}, {"description": "patternProperty validates nonproperty", "data": {"fxo": [1, 2]}, "valid": true}, {"description": "patternProperty invalidates nonproperty", "data": {"fxo": []}, "valid": false}, {"description": "additionalProperty ignores property", "data": {"bar": []}, "valid": true}, {"description": "additionalProperty validates others", "data": {"quux": 3}, "valid": true}, {"description": "additionalProperty invalidates others", "data": {"quux": "foo"}, "valid": false}]}]