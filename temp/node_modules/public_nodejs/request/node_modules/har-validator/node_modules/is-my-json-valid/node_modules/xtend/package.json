{"name": "xtend", "version": "4.0.1", "description": "extend like a boss", "keywords": ["extend", "merge", "options", "opts", "object", "array"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/Raynos/xtend.git"}, "main": "immutable", "scripts": {"test": "node test"}, "dependencies": {}, "devDependencies": {"tape": "~1.1.0"}, "homepage": "https://github.com/Raynos/xtend", "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "bugs": {"url": "https://github.com/Raynos/xtend/issues", "email": "<EMAIL>"}, "license": "MIT", "testling": {"files": "test.js", "browsers": ["ie/7..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "engines": {"node": ">=0.4"}, "gitHead": "23dc302a89756da89c1897bc732a752317e35390", "_id": "xtend@4.0.1", "_shasum": "a5c6d532be656e23db820efb943a1f04998d63af", "_from": "xtend@>=4.0.0 <5.0.0", "_npmVersion": "2.14.1", "_nodeVersion": "0.10.32", "_npmUser": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "a5c6d532be656e23db820efb943a1f04998d63af", "tarball": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "directories": {}, "_resolved": "https://registry.npmjs.org/xtend/-/xtend-4.0.1.tgz"}