[{"description": "a schema given for items", "schema": {"items": {"type": "integer"}}, "tests": [{"description": "valid items", "data": [1, 2, 3], "valid": true}, {"description": "wrong type of items", "data": [1, "x"], "valid": false}, {"description": "ignores non-arrays", "data": {"foo": "bar"}, "valid": true}]}, {"description": "an array of schemas for items", "schema": {"items": [{"type": "integer"}, {"type": "string"}]}, "tests": [{"description": "correct types", "data": [1, "foo"], "valid": true}, {"description": "wrong types", "data": ["foo", 1], "valid": false}]}]