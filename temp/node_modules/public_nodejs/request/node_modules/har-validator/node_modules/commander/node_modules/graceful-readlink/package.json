{"name": "graceful-readlink", "version": "1.0.1", "description": "graceful fs.readlink", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/zhiyelee/graceful-readlink.git"}, "homepage": "https://github.com/zhiyelee/graceful-readlink", "bugs": {"url": "https://github.com/zhiyelee/graceful-readlink/issues"}, "keywords": ["fs.readlink", "readlink"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "license": "MIT", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "readme": "# graceful-readlink\n[![NPM Version](http://img.shields.io/npm/v/graceful-readlink.svg?style=flat)](https://www.npmjs.org/package/graceful-readlink)\n[![NPM Downloads](https://img.shields.io/npm/dm/graceful-readlink.svg?style=flat)](https://www.npmjs.org/package/graceful-readlink)\n\n\n## Usage\n\n```js\nvar readlinkSync = require('graceful-readlink').readlinkSync;\nconsole.log(readlinkSync(f));\n// output\n//  the file pointed to when `f` is a symbolic link\n//  the `f` itself when `f` is not a symbolic link\n```\n## Licence\n\nMIT License\n", "readmeFilename": "README.md", "_id": "graceful-readlink@1.0.1", "_from": "graceful-readlink@>=1.0.0"}