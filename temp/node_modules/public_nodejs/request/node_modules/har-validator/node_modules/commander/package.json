{"name": "commander", "version": "2.9.0", "description": "the complete solution for node.js command-line programs", "keywords": ["command", "option", "parser"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tj/commander.js.git"}, "devDependencies": {"should": ">= 0.0.1", "sinon": ">=1.17.1"}, "scripts": {"test": "make test"}, "main": "index", "engines": {"node": ">= 0.6.x"}, "files": ["index.js"], "dependencies": {"graceful-readlink": ">= 1.0.0"}, "gitHead": "b2aad7a8471d434593a85306aa73777a526e9f75", "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "homepage": "https://github.com/tj/commander.js#readme", "_id": "commander@2.9.0", "_shasum": "9c99094176e12240cb22d6c5146098400fe0f7d4", "_from": "commander@>=2.9.0 <3.0.0", "_npmVersion": "2.11.3", "_nodeVersion": "0.12.7", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}, "dist": {"shasum": "9c99094176e12240cb22d6c5146098400fe0f7d4", "tarball": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz"}, "maintainers": [{"name": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "somekittens", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "zhiye<PERSON>@gmail.com"}], "directories": {}, "_resolved": "https://registry.npmjs.org/commander/-/commander-2.9.0.tgz"}