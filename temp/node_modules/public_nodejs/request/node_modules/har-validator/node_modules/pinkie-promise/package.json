{"name": "pinkie-promise", "version": "2.0.1", "description": "ES2015 Promise ponyfill", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/floatdrop/pinkie-promise"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["promise", "promises", "es2015", "es6", "polyfill", "ponyfill"], "dependencies": {"pinkie": "^2.0.0"}, "devDependencies": {"mocha": "*"}, "gitHead": "4a936c09c34ad591a25db93f1216d242de0d6184", "bugs": {"url": "https://github.com/floatdrop/pinkie-promise/issues"}, "homepage": "https://github.com/floatdrop/pinkie-promise", "_id": "pinkie-promise@2.0.1", "_shasum": "2135d6dfa7a358c069ac9b178776288228450ffa", "_from": "pinkie-promise@>=2.0.0 <3.0.0", "_npmVersion": "2.14.20", "_nodeVersion": "4.4.1", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "dist": {"shasum": "2135d6dfa7a358c069ac9b178776288228450ffa", "tarball": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz"}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/pinkie-promise-2.0.1.tgz_1460309839126_0.3422858319245279"}, "directories": {}, "_resolved": "https://registry.npmjs.org/pinkie-promise/-/pinkie-promise-2.0.1.tgz"}