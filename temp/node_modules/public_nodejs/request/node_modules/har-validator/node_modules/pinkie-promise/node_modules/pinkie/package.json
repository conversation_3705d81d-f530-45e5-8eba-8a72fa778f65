{"name": "pinkie", "version": "2.0.4", "description": "Itty bitty little widdle twinkie pinkie ES2015 Promise implementation", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/floatdrop/pinkie"}, "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && nyc mocha", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["promise", "promises", "es2015", "es6"], "devDependencies": {"core-assert": "^0.1.1", "coveralls": "^2.11.4", "mocha": "*", "nyc": "^3.2.2", "promises-aplus-tests": "*", "xo": "^0.10.1"}, "gitHead": "8d4a92447a5c62bff9f89756caeb4c9c8770579b", "bugs": {"url": "https://github.com/floatdrop/pinkie/issues"}, "homepage": "https://github.com/floatdrop/pinkie", "_id": "pinkie@2.0.4", "_shasum": "72556b80cfa0d48a974e80e77248e80ed4f7f870", "_from": "pinkie@>=2.0.0 <3.0.0", "_npmVersion": "2.14.12", "_nodeVersion": "4.2.4", "_npmUser": {"name": "floatdrop", "email": "<EMAIL>"}, "dist": {"shasum": "72556b80cfa0d48a974e80e77248e80ed4f7f870", "tarball": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz"}, "maintainers": [{"name": "floatdrop", "email": "<EMAIL>"}], "directories": {}, "_resolved": "https://registry.npmjs.org/pinkie/-/pinkie-2.0.4.tgz"}