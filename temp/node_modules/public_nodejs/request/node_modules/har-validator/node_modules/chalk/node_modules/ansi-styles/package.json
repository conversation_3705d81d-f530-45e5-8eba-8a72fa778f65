{"name": "ansi-styles", "version": "2.2.1", "description": "ANSI escape codes for styling strings in the terminal", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": [{"name": "sindresor<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "devDependencies": {"mocha": "*"}, "gitHead": "95c59b23be760108b6530ca1c89477c21b258032", "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "homepage": "https://github.com/chalk/ansi-styles#readme", "_id": "ansi-styles@2.2.1", "_shasum": "b432dd3358b634cf75e1e4664368240533c1ddbe", "_from": "ansi-styles@>=2.2.1 <3.0.0", "_npmVersion": "3.8.3", "_nodeVersion": "4.3.0", "_npmUser": {"name": "sindresor<PERSON>", "email": "<EMAIL>"}, "dist": {"shasum": "b432dd3358b634cf75e1e4664368240533c1ddbe", "tarball": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/ansi-styles-2.2.1.tgz_1459197317833_0.9694824463222176"}, "directories": {}, "_resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-2.2.1.tgz"}