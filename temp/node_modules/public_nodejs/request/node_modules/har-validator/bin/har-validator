#!/usr/bin/env node

'use strict'

var chalk = require('chalk')
var cmd = require('commander')
var fs = require('fs')
var path = require('path')
var pkg = require('../package.json')
var Promise = require('pinkie-promise')
var validate = require('..')
var ValidationError = require('../lib/error')

cmd
  .version(pkg.version)
  .usage('[options] <files ...>')
  .option('-s, --schema [name]', 'validate schema name (log, request, response, etc ...)')
  .parse(process.argv)

if (!cmd.args.length) {
  cmd.help()
}

cmd.args.map(function (fileName) {
  var file = chalk.yellow.italic(path.basename(fileName))

  new Promise(function (resolve, reject) {
    fs.readFile(fileName, function (err, data) {
      return err === null ? resolve(data) : reject(err)
    })
  })

    .then(JSON.parse)

    .then(cmd.schema ? validate[cmd.schema] : validate)

    .then(function (data) {
      console.log('%s [%s] is valid', chalk.green('✓'), file)
    })

    .catch(function (err) {
      if (err instanceof SyntaxError) {
        return console.error('%s [%s] failed to read JSON: %s', chalk.red('✖'), file, chalk.red(err.message))
      }

      if (err instanceof ValidationError) {
        err.errors.forEach(function (details) {
          console.error('%s [%s] failed validation: (%s: %s) %s', chalk.red('✖'), file, chalk.cyan.italic(details.field), chalk.magenta.italic(details.value), chalk.red(details.message))
        })

        return
      }

      console.error('%s [%s] an unknown error has occured: %s', chalk.red('✖'), file, chalk.red(err.message))
    })
})
