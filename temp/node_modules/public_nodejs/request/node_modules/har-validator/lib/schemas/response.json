{"type": "object", "required": ["status", "statusText", "httpVersion", "cookies", "headers", "content", "redirectURL", "headersSize", "bodySize"], "properties": {"status": {"type": "integer"}, "statusText": {"type": "string"}, "httpVersion": {"type": "string"}, "cookies": {"type": "array", "items": {"$ref": "#cookie"}}, "headers": {"type": "array", "items": {"$ref": "#record"}}, "content": {"$ref": "#content"}, "redirectURL": {"type": "string"}, "headersSize": {"type": "integer"}, "bodySize": {"type": "integer"}, "comment": {"type": "string"}}}