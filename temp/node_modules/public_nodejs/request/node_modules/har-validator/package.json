{"version": "2.0.6", "name": "har-validator", "description": "Extremely fast HTTP Archive (HAR) validator using JSON Schema", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.ahmadnassri.com/"}, "homepage": "https://github.com/ahmadnassri/har-validator", "repository": {"type": "git", "url": "https://github.com/ahmadnassri/har-validator"}, "license": "ISC", "main": "lib/index", "bin": {"har-validator": "bin/har-validator"}, "keywords": ["har", "http", "archive", "validate", "validator"], "engines": {"node": ">=0.10"}, "files": ["bin", "lib"], "bugs": {"url": "https://github.com/ahmadnassri/har-validator/issues"}, "scripts": {"pretest": "standard && echint", "test": "mocha", "posttest": "npm run coverage", "coverage": "istanbul cover --dir coverage _mocha -- -R dot", "codeclimate": "codeclimate < coverage/lcov.info"}, "echint": {"ignore": ["coverage/**"]}, "devDependencies": {"codeclimate-test-reporter": "0.2.1", "echint": "^1.5.1", "istanbul": "^0.4.2", "mocha": "^2.3.4", "require-directory": "^2.1.1", "should": "^8.1.1", "standard": "^5.4.1"}, "dependencies": {"chalk": "^1.1.1", "commander": "^2.9.0", "is-my-json-valid": "^2.12.4", "pinkie-promise": "^2.0.0"}, "gitHead": "92ccddad2e5d13e6e32c764e06c347d67805b211", "_id": "har-validator@2.0.6", "_shasum": "cdcbc08188265ad119b6a5a7c8ab70eecfb5d27d", "_from": "har-validator@>=2.0.6 <2.1.0", "_npmVersion": "3.3.12", "_nodeVersion": "5.2.0", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "cdcbc08188265ad119b6a5a7c8ab70eecfb5d27d", "tarball": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.6.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/har-validator/-/har-validator-2.0.6.tgz"}