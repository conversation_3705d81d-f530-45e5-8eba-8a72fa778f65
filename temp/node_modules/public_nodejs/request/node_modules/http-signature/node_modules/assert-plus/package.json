{"author": {"name": "<PERSON>", "email": "<EMAIL>"}, "name": "assert-plus", "description": "Extra assertions on top of node's assert module", "version": "0.2.0", "license": "MIT", "main": "./assert.js", "devDependencies": {"tape": "4.2.2", "faucet": "0.0.1"}, "optionalDependencies": {}, "scripts": {"test": "tape tests/*.js | ./node_modules/.bin/faucet"}, "repository": {"type": "git", "url": "git+https://github.com/mcavage/node-assert-plus.git"}, "engines": {"node": ">=0.8"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "ralph<PERSON><PERSON><PERSON>@riseup.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "bugs": {"url": "https://github.com/mcavage/node-assert-plus/issues"}, "homepage": "https://github.com/mcavage/node-assert-plus#readme", "dependencies": {}, "_id": "assert-plus@0.2.0", "_shasum": "d74e1b87e7affc0db8aadb7021f3fe48101ab234", "_resolved": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz", "_from": "assert-plus@>=0.2.0 <0.3.0", "_npmVersion": "3.3.8", "_nodeVersion": "0.10.36", "_npmUser": {"name": "pfmooney", "email": "<EMAIL>"}, "dist": {"shasum": "d74e1b87e7affc0db8aadb7021f3fe48101ab234", "tarball": "https://registry.npmjs.org/assert-plus/-/assert-plus-0.2.0.tgz"}, "maintainers": [{"name": "mcavage", "email": "<EMAIL>"}, {"name": "pfmooney", "email": "<EMAIL>"}], "directories": {}}