{"description": "A representation of a person, company, organization, or place", "type": "object", "properties": {"fn": {"description": "Formatted Name", "type": "string"}, "familyName": {"type": "string", "required": true}, "givenName": {"type": "string", "required": true}, "additionalName": {"type": "array", "items": {"type": "string"}}, "honorificPrefix": {"type": "array", "items": {"type": "string"}}, "honorificSuffix": {"type": "array", "items": {"type": "string"}}, "nickname": {"type": "string"}, "url": {"type": "string", "format": "url"}, "email": {"type": "object", "properties": {"type": {"type": "string"}, "value": {"type": "string", "format": "email"}}}, "tel": {"type": "object", "properties": {"type": {"type": "string"}, "value": {"type": "string", "format": "phone"}}}, "adr": {"$ref": "http://json-schema.org/address"}, "geo": {"$ref": "http://json-schema.org/geo"}, "tz": {"type": "string"}, "photo": {"format": "image", "type": "string"}, "logo": {"format": "image", "type": "string"}, "sound": {"format": "attachment", "type": "string"}, "bday": {"type": "string", "format": "date"}, "title": {"type": "string"}, "role": {"type": "string"}, "org": {"type": "object", "properties": {"organizationName": {"type": "string"}, "organizationUnit": {"type": "string"}}}}}