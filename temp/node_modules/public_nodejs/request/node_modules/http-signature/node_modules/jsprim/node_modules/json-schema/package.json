{"name": "json-schema", "version": "0.2.2", "author": {"name": "<PERSON>"}, "description": "JSON Schema validation and specifications", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["json", "schema"], "licenses": [{"type": "AFLv2.1", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L43"}, {"type": "BSD", "url": "http://trac.dojotoolkit.org/browser/dojo/trunk/LICENSE#L13"}], "repository": {"type": "git", "url": "http://github.com/kriszyp/json-schema"}, "directories": {"lib": "./lib"}, "main": "./lib/validate.js", "devDependencies": {"vows": "*"}, "scripts": {"test": "echo TESTS DISABLED vows --spec test/*.js"}, "readme": "JSON Schema is a repository for the JSON Schema specification, reference schemas and a CommonJS implementation of JSON Schema (not the only JavaScript implementation of JSON Schema, JSV is another excellent JavaScript validator).\r\n\r\nCode is licensed under the AFL or BSD license as part of the Persevere \r\nproject which is administered under the Dojo foundation,\r\nand all contributions require a Dojo CLA.", "_id": "json-schema@0.2.2", "dist": {"shasum": "50354f19f603917c695f70b85afa77c3b0f23506", "tarball": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.2.tgz"}, "_npmVersion": "1.1.59", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_shasum": "50354f19f603917c695f70b85afa77c3b0f23506", "_resolved": "https://registry.npmjs.org/json-schema/-/json-schema-0.2.2.tgz", "_from": "json-schema@0.2.2"}