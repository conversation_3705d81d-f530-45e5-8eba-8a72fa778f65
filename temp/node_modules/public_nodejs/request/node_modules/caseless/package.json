{"name": "caseless", "version": "0.11.0", "description": "Caseless object set/get/has, very useful when working with HTTP headers.", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/mikeal/caseless.git"}, "keywords": ["headers", "http", "caseless"], "test": "node test.js", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/mikeal/caseless/issues"}, "devDependencies": {"tape": "^2.10.2"}, "gitHead": "c578232a02cc2b46b6da8851caf57fdbfac89ff5", "homepage": "https://github.com/mikeal/caseless#readme", "_id": "caseless@0.11.0", "_shasum": "715b96ea9841593cc33067923f5ec60ebda4f7d7", "_from": "caseless@>=0.11.0 <0.12.0", "_npmVersion": "2.8.3", "_nodeVersion": "1.8.1", "_npmUser": {"name": "mikeal", "email": "<EMAIL>"}, "maintainers": [{"name": "mikeal", "email": "<EMAIL>"}, {"name": "nylen", "email": "<EMAIL>"}, {"name": "simov", "email": "simeon<PERSON><PERSON><PERSON>@gmail.com"}], "dist": {"shasum": "715b96ea9841593cc33067923f5ec60ebda4f7d7", "tarball": "https://registry.npmjs.org/caseless/-/caseless-0.11.0.tgz"}, "directories": {}, "_resolved": "https://registry.npmjs.org/caseless/-/caseless-0.11.0.tgz"}