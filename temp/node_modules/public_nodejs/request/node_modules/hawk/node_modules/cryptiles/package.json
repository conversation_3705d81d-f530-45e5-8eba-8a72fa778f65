{"name": "cryptiles", "description": "General purpose crypto utilities", "version": "2.0.5", "repository": {"type": "git", "url": "git://github.com/hapijs/cryptiles.git"}, "main": "lib/index.js", "keywords": ["cryptography", "security", "utilites"], "engines": {"node": ">=0.10.40"}, "dependencies": {"boom": "2.x.x"}, "devDependencies": {"code": "1.x.x", "lab": "5.x.x"}, "scripts": {"test": "lab -a code -t 100 -L", "test-cov-html": "lab -a code -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "9bc5a852f01cd51e615814e1cb255fe2df810649", "bugs": {"url": "https://github.com/hapijs/cryptiles/issues"}, "homepage": "https://github.com/hapijs/cryptiles#readme", "_id": "cryptiles@2.0.5", "_shasum": "3bdfecdc608147c1c67202fa291e7dca59eaa3b8", "_from": "cryptiles@>=2.0.0 <3.0.0", "_npmVersion": "2.14.2", "_nodeVersion": "4.0.0", "_npmUser": {"name": "hueniverse", "email": "<EMAIL>"}, "dist": {"shasum": "3bdfecdc608147c1c67202fa291e7dca59eaa3b8", "tarball": "https://registry.npmjs.org/cryptiles/-/cryptiles-2.0.5.tgz"}, "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "cee<PERSON><PERSON>", "email": "ceejce<PERSON>@gmail.com"}], "directories": {}, "_resolved": "https://registry.npmjs.org/cryptiles/-/cryptiles-2.0.5.tgz"}