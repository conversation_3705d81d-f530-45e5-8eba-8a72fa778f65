{"name": "hoek", "description": "General purpose node utilities", "version": "2.16.3", "repository": {"type": "git", "url": "git://github.com/hapijs/hoek.git"}, "main": "lib/index.js", "keywords": ["utilities"], "engines": {"node": ">=0.10.40"}, "dependencies": {}, "devDependencies": {"code": "1.x.x", "lab": "5.x.x"}, "scripts": {"test": "lab -a code -t 100 -L", "test-cov-html": "lab -a code -t 100 -L -r html -o coverage.html"}, "license": "BSD-3-<PERSON><PERSON>", "gitHead": "20f36e85616264d4b73a64a374803175213a9121", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "homepage": "https://github.com/hapijs/hoek#readme", "_id": "hoek@2.16.3", "_shasum": "20bb7403d3cea398e91dc4710a8ff1b8274a25ed", "_from": "hoek@>=2.0.0 <3.0.0", "_npmVersion": "3.3.3", "_nodeVersion": "4.1.0", "_npmUser": {"name": "nlf", "email": "<EMAIL>"}, "dist": {"shasum": "20bb7403d3cea398e91dc4710a8ff1b8274a25ed", "tarball": "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz"}, "maintainers": [{"name": "hueniverse", "email": "<EMAIL>"}, {"name": "wyatt", "email": "<EMAIL>"}, {"name": "nlf", "email": "<EMAIL>"}], "directories": {}, "_resolved": "https://registry.npmjs.org/hoek/-/hoek-2.16.3.tgz"}