module.exports = function (app) {
    var frameController = require('../controller/frame');
    var settingController = require('../controller/setting');
    var workController = require('../controller/workcalendar');
    var moduleController = require('../controller/module');
    var equementController = require('../controller/equementController');
    var reportController = require('../controller/reportController');
    var alarmController = require('../controller/alarmController');
    var mindinSetController = require('../controller/mindinSetController');
    var tenantController = require('../controller/tenantController');

    /*-----------------------框架部分------------------------------*/
    /*所有的请求进行验证*/
    app.all('*', frameController.allReqValid());
    app.get('/', frameController.rootReq());
    /*根据路径渲染不同的页面*/
    app.get('/:cpcode/:fcode', frameController.renderController());
    /*获取黑条上的主建筑名称及F名称*/
    app.get('/fgmnfn', frameController.getMainBuildFn());
    /*尚格云登录操作*/
    app.post('/login', frameController.login());
    /*免密登录操作*/
    app.get('/osys', frameController.noPassLogin());
    /*获取左侧F列表*/
     app.get('/f', frameController.getLf());
    /*获取总建筑信息*/
    app.get('/gmb', frameController.getMainBuild());
    /*天气信息*/
    // app.get('/gwt', frameController.getWeather());
    /*某用户所能看到的未读的报警*/
    // app.get('/alarm', frameController.getAlarms());
    /*获取某一用户信息*/
    app.get('/gsu', frameController.getSelfUser());
    /*个人信息修改*/
    app.post('/esu', frameController.editSelfUser());
    /*个人修改密码*/
    app.post('/ups', frameController.updatePass());
    /*上传文件*/
    app.post('/upload', frameController.upload());
    /*退出系统*/
    app.get('/pexit', frameController.pexit());
    /*获取组织结构*/
    // app.get('/fstructs', frameController.getStructs());
    /*根据用户Id（用户标识，非登录名称）获取用户信息，用于单点登录*/
    app.get('/getuserbyid', frameController.getUserById());

    /*-----------------------系统管理部分------------------------------*/
    /*获取所有的功能类型，内含子级*/
    app.get('/psftps', settingController.getPsftps());
    /*获取所有的省份，内含子级*/
    app.get('/pspris', settingController.getPspris());
    /*获取所有的经济指标，内含价格类型*/
    app.get('/pseils', settingController.getPseils());
    /*获取所有的朝向*/
    app.get('/psotls', settingController.getPsotls());
    /*获取所有的空调类型*/
    app.get('/psairs', settingController.getPsairs());
    /*获取所有的采暖类型*/
    app.get('/pshtls', settingController.getPshtls());
    /*获取所有的建筑结构类型*/
    app.get('/psstls', settingController.getPsstls());
    /*获取所有的外保温类型*/
    app.get('/psetis', settingController.getPsetis());

    /*判断建筑ID是否重复*/
    app.get('/buildidvalid', settingController.buildIdValid());
    /*判断建筑名称是否重复*/
    app.get('/buildnamevalid', settingController.buildNameValid());
    /*获取所有建筑*/
    app.get('/sbuilds', settingController.getAllBuilds());
    /*删除一个建筑*/
    app.post('/sdelbuild', settingController.delBuild());
    /*新增建筑*/
    app.post('/saddbuild', settingController.addBuild());
    /*修改建筑*/
    app.post('/seditbuild', settingController.editBuild());

    /*批量修改F名称*/
    app.post('/pupdatafname', settingController.updateFName());
    /*批量修改F的url*/
    app.post('/pupdatafurl', settingController.updateFUrl());
    /*批量更新产品线顺序*/
    app.post('/pupdatecpsort', settingController.updateCpSort());
    /*更新某产品线的开启关闭状态*/
    app.post('/updatecpstate', settingController.updateCpState());
    /*获取所有F*/
    app.get('/sallf', settingController.getAllF());
    /*添加F*/
    app.post('/paddf', settingController.addF());


    /*获取所有用户*/
    app.get('/pusers', settingController.getUsers());
    /*删除用户*/
    app.get('/pdeluser', settingController.delUser());
    /*添加用户*/
    app.post('/padduser', settingController.addUser());
    /*修改用户*/
    app.post('/pedituser', settingController.editUser());
    /*重置密码*/
    app.post('/pinitpass', settingController.initPass());
    /*获取所有启用的产品线及其下属角色*/
    app.get('/pcproles', settingController.getcpRoles());
    /*判断用户登录名是否重复*/
    app.post('/pvaliduid', settingController.validUserId());
    /*判断用户邮箱是否重复*/
    app.post('/pvalidemail', settingController.validEmail());
    /*判断用户手机号是否重复*/
    app.post('/pvalidphone', settingController.validPhone());
    /*判断用户姓名是否重复*/
    app.post('/pvalidrelyname', settingController.validUserRelyName());

    /*更新部门*/
    app.post('/pupstructs', settingController.updateStructs());


    /*-----------------------工作历------------------------------*/
    /*分栏保存*/
    app.post('/psaveworkcolumn', workController.saveWorkColumn());
    /*分栏设置内获取所有栏目*/
    app.post('/pgetadmincolumns', workController.pgetAdminColumns());
    /*获取某用户的所有栏目*/
    app.post('/pgetusercolumns', workController.pgetUserColumns());
    /*用户对日志栏目排序*/
    app.post('/psortcolumns', workController.psortColumns());
    /*左侧全部日志*/
    app.post('/pallogs', workController.pallLogs());
    /*左侧我发布的日志*/
    app.post('/pselflogs', workController.pselfLogs());
    /*左侧我收藏的日志*/
    app.post('/pselfcollectlogs', workController.pselfCollectLogs());
    /*左侧@我的日志*/
    app.post('/pcallselflogs', workController.pcallSelfLogs());
    /*左侧回复我的日志*/
    app.post('/preplyselflogs', workController.preplySelfLogs());
    /*新增日志*/
    app.post('/pnewlog', workController.pnewLog());
    /*回复日志*/
    app.post('/preplylog', workController.preplyLog());
    /*获取某日志的回复*/
    app.post('/pgetreplys', workController.pgetReplys());
    /*删除日志*/
    app.post('/premovelog', workController.premoveLog());
    /*收藏日志*/
    app.post('/pcollectlog', workController.pcollectLog());
    /*点赞日志*/
    app.post('/pfavourlog', workController.pfavourLog());
    /*根据@搜索用户*/
    app.post('/psusers', workController.psusers());
    /*根据#搜索设备*/
    app.post('/psequms', workController.psequms());
    /*根据$搜索分项*/
    app.post('/psenergyitem', workController.psenergyitem());
    /*发布公告*/
    app.post('/pnewnotice', workController.pnewNotice());
    /*删除公告*/
    app.post('/premovenotice', workController.premoveNotice());
    /*获取公告*/
    app.get('/pgetnotices', workController.pgetNotices());
    /*获取某日的工作历信息*/
    app.get('/pdayworkinfo', workController.pdayWorkinfo());
    /*获取主建筑某日的能耗、能耗定额、能耗定额比例*/
    app.get('/pdayenergy', workController.pdayEnergy());
    /*获取某月内的每天是否具有特殊日和工作日*/
    app.get('/pmonthdayspecialwork', workController.pmonthDaySpecialWork());
    /*获取某建筑的所有的暖通工作季*/
    app.get('/pallntgzj', workController.pgetAllNtgzjs());
    /*修改某天的暖通工作季属性*/
    app.get('/psetdaynt', workController.psetDaynt());
    /*修改某建筑的暖通工作季属性*/
    app.post('/psetntgzjdatebybuild', workController.pntgzjUpdateByBuild());
    /*修改某天为工作日或非工作日*/
    app.get('/psetdaywd', workController.psetDaywd());
    /*获取某建筑的所有的特殊日*/
    app.get('/pgetspecials', workController.pgetSpecials());
    /*修改某建筑的特殊日*/
    app.post('/psetspecialdatebybuild', workController.pspecialdateUpdateByBuild());
    /*修改某天为特殊日或非特殊日*/
    app.post('/psetdayspecial', workController.psetDaySpecial());
    /*修改某天的营业时间*/
    app.post('/psetdayworktime', workController.psetDayWorktime());
    /*获取某年所有建筑的暖通工作季信息、营业时间信息、特殊日信息*/
    app.post('/pgetyearnttmsp', workController.pgetYearNtTmSp());
    /*获取工作日非工作日的默认营业时间数据*/
    app.get('/pgetgzrfyysjdate', workController.pgetGzrfyysjDate());
    /*获取暖通工作季的默认营业时间数据*/
    app.get('/pgetntgzjyysjdate', workController.pgetNtgzjyysjDate());
    /*修改某建筑的营业时间*/
    app.post('/psetbuildworktime', workController.psetBuildWorktime());
    /*获取工作历的@我的数量和回复我的数量*/
    app.get('/wgetnewsnum', workController.wgetNewsNum());
    /*清空@我的数量或回复我的数量*/
    app.post('/wclearnewsnum', workController.wclearNewsNum());


    /*-------------------------组件----------------------------*/
    /*根据产品线编码获取组件信息，用于组件设置*/
    app.post('/mleinfo', moduleController.getMleSetInfo());
    /*根据组件的条件编码，获取该条件的所有项、默认项，用于组件设置*/
    app.post('/mgetfactorinfo', moduleController.getFactorInfo());
    /*保存组件设置*/
    app.post('/savemodule', moduleController.moduleSave());
    /*获取某用户的组件配置信息*/
    app.get('/getmodulebyuser', moduleController.getUserModule());
    /*获取某组件的内容数据*/
    app.post('/getmodulecontentdata', moduleController.getModuleContentData());
    /*组件预览请求*/
    app.post('/modulepreview', moduleController.modulePreiew());
    /*获取GIS组件信息*/
    app.get('/gismodule', moduleController.getGisInfo());
    /*保存GIS组件信息*/
    app.post('/savegismodule', moduleController.saveGisInfo());


    /*-------------------------设备管理----------------------------*/
    /*下载设备标签*/
    app.post('/edowneqinfotag', equementController.edownEqInfoTag());
    /*下载设备管理表格*/
    app.post('/edowneqinfoexcel', equementController.edownEqInfoExcel());
    /*获取设备列表*/
    app.post('/egetequments', equementController.egetEquments());
    /*根据设备ID获取设备信息*/
    app.post('/egetequmentinfo', equementController.egetEqumentById());
    /*获取分组后的所有的设备类型和建筑*/
    app.get('/egeteqtypesandbuilds', equementController.egetEqTypesAndBuilds());
    /*根据建筑获取所有的安装位置*/
    app.post('/egeteqaddresss', equementController.egetEqAddresss());
    /*根据关键字搜索设备*/
    app.post('/esearcheqs', equementController.esearchEqs());
    /*新建设备*/
    app.post('/eaddeq', equementController.eaddEq());
    /*编辑设备*/
    app.post('/eediteq', equementController.eeditEq());
    /*报废设备*/
    app.post('/ebfeq', equementController.ebfEq());
    /*延长使用寿命*/
    app.post('/eyceqsm', equementController.eyceqsm());
    /*删除设备*/
    app.post('/eremoveeq', equementController.eremoveeq());
    /*获取操作记录的所有负责人*/
    app.post('/egetrecordfzr', equementController.egetRecordFzr());
    /*获取某设备的操作记录*/
    app.post('/egetrecordbyeq', equementController.egetRecordByeq());
    /*下载某设备的操作记录*/
    app.post('/edownrecordbyeq', equementController.downRecordByeq());
    /*添加某设备的操作记录*/
    app.post('/eaddrecordbyeq', equementController.eaddRecordByeq());
    /*获取报警记录的报警原因名称*/
    app.get('/egetalarmreasons', equementController.egetAlarmReasons());
    /*获取某设备的报警记录*/
    app.post('/egetalarmsbyeq', equementController.egetAlarmsByeq());
    /*下载某设备的报警记录*/
    app.post('/edownalarmsbyeq', equementController.edownAlarmsByeq());
    /*获取已有分组及下属设备类型*/
    app.get('/eqgroupsandtypes', equementController.eqGroupsAndTypes());
    /*添加分组*/
    app.post('/eaddgroup', equementController.eaddGroup());
    /*修改分组*/
    app.post('/eeditgroup', equementController.eeditGroup());
    /*删除分组*/
    app.post('/eremovegroup', equementController.eremoveGroup());
    /*把设备最低一级的分类放进某个分组*/
    app.post('/etypeintogroup', equementController.etypeIntoGroup());
    /*把设备最低一级的某个分类从某分组移除*/
    app.post('/eremovetypefromgroup', equementController.eremoveTypeFromGroup());
    /*获取所有建筑及其下属位置*/
    app.post('/egetbuildsandaddress', equementController.egetBuildsAndAddress());
    /*添加位置*/
    app.post('/eaddaddress', equementController.eaddAddress());
    /*编辑位置*/
    app.post('/eeditaddress', equementController.eeditAddress());
    /*删除位置*/
    app.post('/eremoveaddress', equementController.eremoveAddress());
    /*获取所有的生产厂家*/
    app.post('/egetsccjs', equementController.egetSccjs());
    /*新建生产厂家*/
    app.post('/eaddsccj', equementController.eaddSccj());
    /*修改生产厂家*/
    app.post('/eeditsccj', equementController.eeditSccj());
    /*删除生产厂家*/
    app.post('/eremovesccj', equementController.eremoveSccj());
    /*获取所有的供应商*/
    app.post('/egeteqgys', equementController.egetEqGyss());
    /*新建供应商*/
    app.post('/eaddgys', equementController.eaddGys());
    /*修改供应商*/
    app.post('/eeditgys', equementController.eeditGys());
    /*删除供应商*/
    app.post('/eremovegys', equementController.eremoveGys());
    /*获取所有的维修商*/
    app.post('/egetwxss', equementController.egetWxss());
    /*新建维修商*/
    app.post('/eaddwxs', equementController.eaddWxs());
    /*修改维修商*/
    app.post('/eeditwxs', equementController.eeditWxs());
    /*删除维修商*/
    app.post('/eremovewxs', equementController.eremoveWxs());
    /*获取设备标签*/
    app.post('/egeteqtag', equementController.egeteqTag());
    /*保存设备标签*/
    app.post('/esaveeqtag', equementController.esaveeqTag());
    /*获取所有的系统操作记录类型*/
    app.get('/sysrecordtypes', equementController.getSystemRecordTypes());
    /*获取所有的系统操作记录*/
    app.get('/sysrecords', equementController.getSystemRecords());
    /*获取所有的系统操作记录个数*/
    app.get('/sysrecordscount', equementController.getSystemRecordsCount());
    /*系统操作记录搜索*/
    app.get('/sysrecordsfilter', equementController.getSystemRecordsFilter());
    /*下载系统操作记录*/
    app.post('/edownsystemrecord', equementController.downSystemRecord());

    /*-------------------------能耗报告----------------------------*/
    /*获取报告模板列表*/
    app.get('/reporttemplatearr', reportController.getReportTemplateArr());
    /*获取条件值*/
    app.post('/reportcondition', reportController.getReportCondition());
    /*获取用户报告列表*/
    app.get('/selfreportarr', reportController.getSelfReportArr());
    /*获取生成中报告列表*/
    app.get('/creatingreportarr', reportController.getCreatingReportArr());
    /*生成报告*/
    app.post('/createreport', reportController.createReport());
    /*获取报告id*/
    app.post('/getreportid', reportController.getReportId());
    /*完成报告*/
    app.post('/completereport', reportController.completeReport());
    /*删除报告*/
    app.post('/deletereport', reportController.deleteReport());
    /*读报告*/
    app.post('/readreport', reportController.readReport());
    /*获取报告内容*/
    app.post('/reportcontent', reportController.getReportContent());
    /*查看报告详情*/
    app.get('/reportdetail', reportController.getReportDetail());
    /*获取报告名称列表*/
    app.post('/reportnamearr', reportController.getReportNameArr());
    /*获取冷机运行记录表体数据和所选时段天气*/
    app.get('/contentandweathers', reportController.getContentAndWeathers());

    /*----------------------------------全局报警---------------------------------*/
    /*获取所有的报警类型及其下属方案*/
    app.get('/agetalarmtypes', alarmController.agetAlarmTypesAndCase());
    /*根据条件获取报警列表*/
    app.post('/agetalarms', alarmController.agetAlarms());
    /*收藏或取消收藏*/
    app.post('/acollectalarm', alarmController.acollectAlarm());
    /*获取某报警的详情*/
    app.post('/agetalarmdetail', alarmController.agetAlarmDetail());
    /*设置静音*/
    app.post('/asetalarmoffsound', alarmController.asetAlarmOffSound());
    /*取消静音*/
    app.post('/asetalarmonsound', alarmController.asetAlarmOnSound());
    /*添加批注*/
    app.post('/asetalarmcomment', alarmController.asetAlarmComment());
    /*获取所有的推送方案*/
    app.post('/agetalarmpushcase', alarmController.agetAlarmPushCase());
    /*某方案修改内容*/
    app.post('/aeditcasetype', alarmController.aeditCaseType());
    /*某方案移除*/
    app.post('/acaseremove', alarmController.acaseRemove());
    /*获取所有组织结构及其下属用户*/
    app.post('/adepartandusers', alarmController.adepartAndUsers());
    /*新建方案*/
    app.post('/anewcase', alarmController.aaddCase());
    /*修改方案*/
    app.post('/aeditcase', alarmController.aeditCase());
    /*修改报警类型下属的方案*/
    app.post('/aeditalarmtypetocase', alarmController.aeditAlarmTypeToCase());
    /*把之前查看过详情的报警的状态更新成已读*/
    app.post('/updatereadstate', alarmController.updateReadState());

    app.get('/pagesizedefault', tenantController.pageSizeDefault());
    /*----------------------------------配电设置---------------------------------*/
    //根据参数sub_route进入业务指定方法
    //app.post('/business/:sub_route', function (req, res) {
    //    try {
    //        var sub_route = req.params.sub_route;
    //        var business_F = mindinSetController[sub_route];
    //        if (typeof business_F !== "function") {
    //            res.send({
    //                result: "failure",
    //                reason: "非法请求"
    //            });
    //            return;
    //        }
    //        business_F(req.body, function (err, result) {
    //            if (err) {
    //                res.send({ reason: err, result: "failure" });
    //                return;
    //            }
    //            res.send({
    //                result: 'success',
    //                reason: undefined,
    //                content: result
    //            });
    //        });
    //    } catch (e) {
    //        console.log(sub_route + '执行[send]错误\r\n' + e.stack + '\r\n\r\n\r\n\r\n\r\n\r\n');
    //        pLogger.error(sub_route + '执行[send]错误\r\n' + e.stack + '\r\n\r\n\r\n\r\n\r\n\r\n');
    //        res.send({ route: sub_route, err: e, reason: "前端服务器异常", });
    //    };
    //});
    //上传文件
    //app.post('/business/uploadFile/:sub_route', function (req, res) {
    //    try {
    //        var sub_route = req.params.sub_route;
    //        var business_F = mindinSetController[sub_route];
    //        if (typeof business_F !== "function") {
    //            console.log('非法请求');
    //            res.send({
    //                result: "failure",
    //                reason: "非法请求"
    //            });
    //            return;
    //        };
    //        var reqBody = req.body;
    //        var inputName = reqBody.fileInputName;
    //        var files = req.files;
    //        business_F(req.body, files[inputName], function (err, result) {
    //            if (err || result == null) {
    //                res.send({ reason: err, result: result });
    //                return;
    //            }
    //            res.send({
    //                result: 'success',
    //                reason: undefined,
    //                content: result
    //            });
    //        });
    //    } catch (e) {
    //        console.log(sub_route + '执行[send]错误\r\n' + e.stack + '\r\n\r\n\r\n\r\n\r\n\r\n');
    //        pLogger.error(sub_route + '执行[send]错误\r\n' + e.stack + '\r\n\r\n\r\n\r\n\r\n\r\n');
    //        res.send({ route: sub_route, err: e, message: "前端服务器异常", });
    //    };
    //});
    app.get('/login',function(req,res,next){
        res.redirect('/');
    })
};