<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>订单汇总</title>
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <!-- <script type="text/javascript" src="/scripts/lib/vue-2.2.0.min.js"></script> -->
    <link rel="stylesheet" href="../../scripts/lib/element-ui/theme-chalk/index.css">
    <link rel="stylesheet" href="../../css/orderSum/index.css">
    <script type="text/javascript" src="../../scripts/lib/knockout-2.2.1.debug.js"></script>
        <script type="text/javascript" src="/script/global/model.js"></script>
    <script type="text/javascript" src="/script/global/controller.js"></script>
    <script type="text/javascript" src="/script/global/tool.js"></script>
     <script type="text/javascript" src="../../script/frame/model.js"></script>
    <script type="text/javascript" src="../../scripts/lib/jquery-2.0.0.min.js"></script>
    <script type="text/javascript" src="../../scripts/lib/highcharts-4.2.5.js"></script>
    <script type="text/javascript" src="../../scripts/lib/xlsx.full.min.js"></script>
    <script type="text/javascript" src="../../pcontrol/pchart.js"></script>
    <!-- <script type="text/javascript" src="../../scripts/lib/vue-2.2.0.min.js"></script> -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/vue@2.7.16/dist/vue.js"></script>    -->
    <script type="text/javascript" src="../../scripts/lib/vue-2.7.16.js"></script>
    <script type="text/javascript" src="../../scripts/lib/element-ui/index.js"></script>
    <!-- <script src="https://unpkg.com/element-ui/lib/index.js"></script> -->
    <script type="text/javascript" src="../../scripts/tool/ptool.js"></script>
    <script type="text/javascript" src="../../scripts/tool/asynTool.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pconst.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pajax.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pautoComplete.js"></script>
    <script type="text/javascript" src="../../scripts/tool/psecret.js"></script>
    <script type="text/javascript" src="../../scripts/extend/Date.js"></script>
    <script type="text/javascript" src="../../scripts/extend/Math.js"></script>
    <script type="text/javascript" src="../../scripts/extend/String.js"></script>
    <script type="text/javascript" src="../../scripts/extend/jQueryDom.js"></script>
    <script type="text/javascript" src="../../scripts/extend/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../pcontrol/flatBlueSeries_src_2.0.js"></script>
    <!-- <script type="text/javascript" src="/script/tement/tenantMng/controller.js"></script> -->
    <script type="text/javascript" src="/script/orderSum/controller.js"></script>
    <!-- <script type="text/javascript" src="/script/tement/tenantMng/event/addtement_event.js"></script> -->
    <script type="text/javascript" src="/script/orderSum/event/addtement_event.js"></script>
    <!-- <script type="text/javascript" src="/script/tement/tenantMng/event.js"></script> -->
     <!-- <script type="text/javascript" src="/script/orderSum//event.js"></script> -->
    <script type="text/javascript" src="/script/tement/common/common.js"></script>
    <!-- <script type="text/javascript" src="/script/tement/tenantMng/model.js"></script> -->
    <script type="text/javascript" src="/script/orderSum/model.js"></script>
    <script type="text/javascript" src="/script/tement/tenantMng/component.js"></script>
    <script type="text/javascript" src="/script/tement/searchComplete.js"></script>
</head>
<body>
    <div id="orderSum" class="orderSum-container">
        <!-- <pnotice-message id="message"></pnotice-message> -->
        <!-- TODO -->
        <div class="mainBody mainBodyShow" :style="{display: 'block', height: '100%'}">
            <!--租户列表-->
            <div class="t_p_box content" style="height: 100%" v-show="currentPage=='tementListPage'">
                <!--头部标题栏  -->
                <div class="main_title clearFloat">
                    <el-form :inline="true" :model="searchCondition" class="search-condition-form" size="small">
                        <div class="row-left">
                            <el-form-item label="订单来源:">
                                <el-select
                                    v-model="searchCondition.systemCode"
                                    style="width: 100px;"
                                    placeholder="请选择">
                                    <el-option
                                        v-for="item in condition1Options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="操作类型:">
                                <!-- <el-input v-model="" placeholder="请选择"/> -->
                                <el-select 
                                    v-model="searchCondition.rechargeType"
                                    style=" width: 70px;"
                                    placeholder="请选择">
                                    <el-option
                                        v-for="item in condition2Options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="订单结果:">
                                <!-- <el-input v-model="" placeholder="请选择"/> -->
                                <el-select
                                    v-model="searchCondition.status"
                                    style="width:90px;"
                                    placeholder="请选择">
                                    <el-option
                                        v-for="item in condition3Options"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="订单时间:">
                                <el-date-picker
                                    v-model="searchCondition.time"
                                    type="datetimerange"
                                    :picker-options="pickerOptions"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    :style="{  width: '350px' }">
                                </el-date-picker>
                            </el-form-item>
                            <el-form-item label="搜索:">
                                <el-input
                                    title="可查询订单号/租户名称/合同号/铺位号/电表号"
                                    placeholder="订单号/租户名称/合同号/铺位号/电表号"
                                    prefix-icon="el-icon-search"
                                    v-model="searchCondition.keyword"
                                    @keyup.enter.native="onSearchSubmit"
                                    :style="{  width: '240px' }">
                                </el-input>
                            </el-form-item>
                        </div>
                        <div class="row-right">
                            <el-form-item >
                                <el-button type="primary" @click="onSearchSubmit" :loading="tableLoading"> 查询</el-button>
                                <el-button @click="onReset" > 重置</el-button>
                                <el-button @click="downloadClick" > 下载</el-button>
                            </el-form-item>
                        </div>
                    </el-form>
                    <!-- <el-button @click="rehandleDialogVisible = true" type="primary" size="small">测试按钮</el-button> -->
                </div>
                <div class="content-body">
                    <el-table
                    v-loading="tableLoading"
                    :data="tableData"
                    style="width: 100%; height: 100%"
                    :header-cell-style="{ padding: '6px 0' }"
                    :cell-style="cellStyle"
                    :row-key="row => row.id"
                    :loading="loading"
                    :border="true"
                    :stripe="true">
                        <el-table-column
                        v-for="col in columns"
                        :key="col.prop"
                        :prop="col.prop"
                        :label="col.label"
                        :min-width="col.width || 'auto'"
                        :align="col.align"
                        :formatter="col.formatter"/>
                        </el-table-column>
                        <el-table-column label="特殊处理" prop="handle" :width="126" :key="'handle'" :align="'center'" >
                            <template slot-scope="scope">
                                <!-- 状态为失败的订单处理选项 -->
                                <el-button
                                    v-if="scope.row.status === 2 && operationPermissions.OrderReprocess"
                                    size="medium"
                                    style="font-weight: 600;"
                                    type="text" 
                                    @click="rehandleClick(scope.$index, scope.row)">
                                    再次处理
                                </el-button>
                                <el-button
                                    v-if="scope.row.status === 2 && operationPermissions.OrderClose"
                                    style="font-weight: 600;"
                                    size="medium"
                                    @click="closeOrderClick(scope.$index, scope.row)"
                                    type="text">
                                    关闭
                                </el-button>
                                <!-- 充后余额为空时为异常，特殊处理选项 -->
                                <el-button
                                    v-if=" scope.row.status === 1 && scope.row.afterBalance === null && operationPermissions.OrderBalanceUpdate"
                                    style="font-weight: 600;padding: 6px 0;"
                                    size="medium"
                                    @click="handelAfterBalanceClick(scope.$index, scope.row)"
                                    type="text">
                                    充后余额处理
                                </el-button>
                                <!-- <el-popconfirm
                                    title="确定关闭该订单吗？"
                                    v-if="scope.row.status === 2"
                                    @confirm="closeOrderConfirm(scope.$index, scope.row)">
                                    <el-button
                                        style="font-weight: 600;"
                                        slot="reference"
                                        size="medium"
                                        type="text">
                                        关闭
                                    </el-button>
                                </el-popconfirm> -->
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- 订单充值/退费失败再次处理弹框 -->
                    <el-dialog
                        v-loading="rehandleConfirmLoading"
                        :visible.sync="rehandleDialogVisible"
                        width="800px"
                        custom-class="rehandle-dialog"
                        @close="onRehandleDialogClose">
                        <div slot="title" class="dialog-title" style="font-weight: 600; line-height: 24px;">
                            &nbsp;&nbsp;<i class="el-icon-info" style="font-size: 24px;color: #4389FD;"></i> &nbsp;&nbsp;请先确认订单实际充值状态后，再操作!
                        </div>
                        <div slot="footer" class="dialog-footer">
                            <el-button size="small" @click="rehandleDialogVisible = false">取 消</el-button>
                            <el-button
                                size="small"
                                type="primary"
                                :loading="rehandleConfirmLoading"
                                @click="function(){rehandleConfirmClick(abnormalOrder.rechargeType)}">
                                再次{{abnormalOrder.rechargeTypeName}}
                            </el-button>
                        </div>
                        <div class="content">
                            <div class="content-part1">
                                <p style="line-height: 1.5;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;请认真确认该笔异常订单前后三小时内，电表实际剩余金额是否实际发生变化:</p>
                                <p style="font-size: 13px;">① 如果电表金额已变化，请点击“<span style="font-weight: 600;">取消</span>”后在页面上点击“<span style="font-weight: 600;">关闭</span>”；</p>
                                <p style="font-size: 13px;">② 如金额未变化，请点击“<span style="font-weight: 600;">再次处理</span>”；</p>
                                <p style="font-size: 13px;">③ 无需操作点“<span style="font-weight: 600;">取消</span>”。</p>
                            </div>
                            <div class="content-part2" style="font-weight: 600;">
                                异常订单信息：{{abnormalOrder.timeArr && abnormalOrder.timeArr[0]}}
                                <span style="color: red;">{{abnormalOrder.timeArr && abnormalOrder.timeArr[1]}}</span>
                                 {{abnormalOrder.rechargeTypeName}}<span style="color: red;">{{abnormalOrder.money}}</span>元
                            </div>
                            <div class="content-part3">
                                <div class="error-info-table">
                                    <div class="row row1">
                                        <div class="column column1">异常订单前后时间</div>
                                        <div class="column">{{abnormalOrderDetails[0] && abnormalOrderDetails[0].time}}</div>
                                        <div class="column">{{abnormalOrderDetails[1] && abnormalOrderDetails[1].time}}</div>
                                        <div class="column column4">{{abnormalOrderDetails[2] && abnormalOrderDetails[2].time}}</div>
                                    </div>
                                    <div class="row row2">
                                        <div class="column column1">电表剩余金额/元</div>
                                        <div class="column">{{abnormalOrderDetails[0] && abnormalOrderDetails[0].balance}}</div>
                                        <div class="column">{{abnormalOrderDetails[1] && abnormalOrderDetails[1].balance}}</div>
                                        <div class="column column4">{{abnormalOrderDetails[2] && abnormalOrderDetails[2].balance}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-dialog>
                    <!-- 订单关闭弹框 -->
                    <el-dialog
                        v-loading="rehandleConfirmLoading"
                        custom-class="closeorder-dialog"
                        :visible.sync="closeOrderDialogVisible"
                        width="30%">
                        <div slot="title" class="dialog-title" style="font-weight: 600; line-height: 24px;">
                            &nbsp;&nbsp;<i class="el-icon-info" style="font-size: 24px;color: #4389FD;"></i> &nbsp;&nbsp;是否确认关闭该订单
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-button size="small" @click="closeOrderDialogVisible = false">取 消</el-button>
                            <el-button size="small" type="primary" @click="closeOrderConfirmClick">确 定</el-button>
                        </span>
                    </el-dialog>
                    <!-- 订单汇总下载弹框 -->
                    <el-dialog
                        custom-class="download-dialog"
                        :visible.sync="downloadDialogVisible"
                        width="480px">
                        <div slot="title" class="dialog-title" style="font-weight: 600; line-height: 24px;">
                            &nbsp;&nbsp;<i class="el-icon-info" style="font-size: 24px;color: #4389FD;"></i> &nbsp;&nbsp;订单汇总下载
                        </div>
                        <div class="dialog-body">
                            <el-form
                            :model="downloadCondition"
                            class="search-condition-form"
                            ref="downloadForm"
                            :rules="downloadFormRules"
                            size="small">
                                <el-form-item label="订单时间:" prop="timeRange">
                                    <el-date-picker
                                        v-model="downloadCondition.timeRange"
                                        type="datetimerange"
                                        :picker-options="pickerOptionsDownload"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        :style="{  width: '350px' }">
                                    </el-date-picker>
                                </el-form-item>
                            </el-form>
                            <div class="time-range-tip">
                                （提示：请选择下载时间范围，最多可下载31天内的数据）
                            </div>
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-button size="small" @click="downloadDialogVisible = false">取 消</el-button>
                            <el-button :loading="downloadDialogLoading" size="small" type="primary" @click="downloadDialogConfirm">确 定</el-button>
                        </span>
                    </el-dialog>
                    <!-- 充后余额异常状态确认 -->
                    <el-dialog
                        custom-class="pay-status-dialog"
                        :visible.sync="payStatusDialogVisible"
                        width="480px">
                        <div slot="title" class="dialog-title" style="font-weight: 600; line-height: 24px;">
                            &nbsp;&nbsp;<i class="el-icon-info" style="font-size: 24px;color: #4389FD;"></i> &nbsp;&nbsp;请确认订单是否充值成功
                        </div>
                        <div class="dialog-body">
                            <el-radio-group class="confirm-pay-radio" v-model="confirmPayStatus">
                                <el-radio :label="1">充值成功</el-radio>
                                <el-radio :label="0">充值失败</el-radio>
                            </el-radio-group>
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-button size="small" @click="payStatusDialogVisible = false">取 消</el-button>
                            <el-button 
                            v-if="confirmPayStatus !== 0"
                            :disabled="confirmPayStatus === null || !operationPermissions.OrderBalanceUpdate"
                            :title="operationPermissions.OrderBalanceUpdate ? '' : '无操作权限，请联系管理员'"
                            :loading="payStatusDialogLoading"
                            size="small"
                            type="primary"
                            @click="payStatusDialogConfirm">
                                确 定
                            </el-button>
                            <el-button
                            v-if="confirmPayStatus === 0"
                            :disabled="!operationPermissions.OrderReprocess"
                            :title="operationPermissions.OrderReprocess ? '' : '无操作权限，请联系管理员'"
                            :loading="payStatusDialogLoading"
                            size="small"
                            type="primary"
                            @click="rehandleClick_afterPay">
                                再次处理
                            </el-button>
                            <el-button
                            v-if="confirmPayStatus === 0"
                            :disabled="!operationPermissions.OrderClose"
                            :title="operationPermissions.OrderClose ? '' : '无操作权限，请联系管理员'"
                            :loading="payStatusDialogLoading"
                            size="small"
                            type="primary"
                            @click="closeOrderClick_afterPay">
                                关闭订单
                            </el-button>
                        </span>
                    </el-dialog>
                    <!-- 修改充后余额弹框 -->
                    <el-dialog
                        custom-class="update-balance-dialog"
                        :visible.sync="updateBalanceDialogVisible"
                        width="480px">
                        <div slot="title" class="dialog-title" style="font-weight: 600; line-height: 24px;">
                            &nbsp;&nbsp;<i class="el-icon-info" style="font-size: 24px;color: #4389FD;"></i> &nbsp;&nbsp;修改充后余额
                        </div>
                        <div class="dialog-body">
                            <el-form
                            class="update-balance-form"
                            :model="updateBalanceForm"
                            ref="updateBalanceForm"
                            :rules="updateBalanceFormRules"
                            label-width="90px"
                            size="small">
                                <el-form-item label="订单金额:" prop="money">
                                    <el-input disabled v-model="updateBalanceForm.money" placeholder="请输入订单余额" style="width: 300px;"> </el-input>
                                    <span>&nbsp;&nbsp;元</span>
                                </el-form-item>
                                <el-form-item label="充前余额:" prop="beforeBalance">
                                    <el-input disabled v-model="updateBalanceForm.beforeBalance" placeholder="请输入充前余额" style="width: 300px;"> </el-input>
                                    <span>&nbsp;&nbsp;元</span>
                                </el-form-item>
                                <el-form-item label="修改余额:" prop="updateBalance">
                                    <!-- <el-input type="number" v-model="updateBalanceForm.updateBalance" placeholder="请输入修改余额" style="width: 300px;"> </el-input> -->
                                    <el-input-number
                                    class="update-balance-form-update-balance"
                                    v-model="updateBalanceForm.updateBalance"
                                    placeholder="请输入修改余额"
                                    style="width: 300px;text-align: left;"
                                    controls-position="right"
                                    :min="0"
                                    :step="1"
                                    :precision="2">
                                    </el-input-number>
                                    <span>&nbsp;&nbsp;元</span>
                                </el-form-item>
                            </el-form>
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-button size="small" @click="updateBalanceDialogVisible = false">取 消</el-button>
                            <el-button :loading="updateBalanceDialogLoading" size="small" type="primary" @click="updateBalanceDialogConfirm">确 定</el-button>
                        </span>
                    </el-dialog>
                </div>

                <div class="content-footer">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="tableCurrentPage"
                        :page-sizes="[20, 50, 100, 200]"
                        :page-size="20"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="tableTotal">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
