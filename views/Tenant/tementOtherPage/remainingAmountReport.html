<div class="r_a_r_box" v-if="currentPage == 'remainingAmountReport'">
    <!-- 操作栏 -->
    <div class="r_a_r_header clearfix">
        <!-- 返回按钮 -->
        <div class="r_a_r_go_back">
            <pbutton-white text="返回" click="ldp.backToTemenListPage"></pbutton-white>
        </div>

        <!-- 标题 -->
        <h1>剩余{{remainingAmountReportList.notice}}报表 - {{remainingAmountReportList.energyTypeName}} - {{remainingAmountReportList.gridTime}}</h1>

        <!-- 下载按钮 -->
        <div class="r_a_r_download">
            <pbutton-white text="'下载剩余' + remainingAmountReportList.notice + '报表'" icon="'D'" click="staticEvent.downloadRemainingAmountReport" bind="true"></pbutton-white>
        </div>
    </div>

    <!-- 剩余天数/金额/量记录表格 -->
    <div class="r_a_r_gird_box">
        <div class="r_a_r_gird">
            <div class="r_a_r_gird_tit">
                <div>租户编号</div>
                <div>租户名称</div>
                <div style="flex: 2;">房间编号</div>
                <div style="flex: 2;">仪表地址</div>
                <div v-if="remainingAmountReportList.remainType != 0">剩余金额（元）</div>
                <div v-if="remainingAmountReportList.remainType != 1">剩余量（{{remainingAmountReportList.energyUnit}}）</div>
                <div>剩余天数（天）</div>
            </div>
            <div id="rArScroll" class="r_a_r_gird_body">
                <pscroll-small templateid="rArScrollBox"></pscroll-small>
            </div>
        </div>
        <div ndm="" class="per-grid-nodata">
            <div pc="" _pt="pnotice-nodata" _id="bfbfheiegahhgdbfafh" class="per-prompt-abnormalmess">
                <span class="per-prompt_icon"></span>
                <span class="per-prompt_title">暂无数据</span>
                <span class="per-prompt_subtitle">请检查网络是否通畅</span>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="rArScrollBox">
    <ul>
        <li class="r_a_r_gird_item" v-for="(model, index) in remainingAmountReportList.tenantList" :key="index">
            <div>{{model.tenantId || '--'}}</div>
            <div>{{model.tenantName || '--'}}</div>
            <div style="flex: 2;" :title="model.roomCode">{{model.roomCode || '--'}}</div>
            <div style="flex: 2;" :title="model.address">{{model.address || '--'}}</div>
            <div v-if="remainingAmountReportList.remainType != 0">{{tenantCtrl.numberFormat(model.remainMoney, tenantCtrl.fixType_money, true)}}</div>
            <div v-if="remainingAmountReportList.remainType != 1">{{tenantCtrl.numberFormat(model.remainEnergy, tenantCtrl.fixType_dynamic, true)}}</div>
            <div>{{tenantCtrl.numberFormat(model.remainDays, tenantCtrl.fixType_day, true)}}</div>
        </li>
    </ul>
</script>