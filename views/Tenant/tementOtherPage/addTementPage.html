<div v-show="currentPage == 'addTementPage'" class="addTementPage">
    <div class="header">
        <div class="cancelBtn">
            <pbutton-white text="取消" click="ldp.gotoTenementDetails"></pbutton-white>
        </div>
        <span class="title">{{addOrEdit==0?'添加租户':'编辑租户'}}</span>
    </div>
    <div class="body">
        <componentstep :step-arr="stepArr" :step="step"></componentstep>
        <div class="step_detail">
            <%include template/addtement_step1.html%>
            <%include template/addtement_step2.html%>
            <%include template/addtement_step3.html%>
        </div>
    </div>
    <div class="floot">
        <h2 class="floot_btnWrapper">
            <pbutton-white v-show="currentPage == 'addTementPage' && subaddPage !== 'step1'" text="上一步" id="add_editPrev" click="ldp.add_editPrev"></pbutton-white>
            <pbutton-blue v-show="currentPage == 'addTementPage' && subaddPage !== 'step3'" text="下一步" id="add_editNext" click="ldp.add_editNext"></pbutton-blue>
            <pbutton-blue v-show="currentPage == 'addTementPage' && subaddPage === 'step3'" text="'完成'" id="'add_editFinish'" click="ldp.add_editFinish" disabled="!isAllChooseRoomGetMeterReady" bind="true"></pbutton-blue>
        </h2>
    </div>
</div>
<!--选择房间-->
<pwindow-float id="chooseRoom" isshade="true" title="选择房间" templateid="chooseRoomTemp">
    <animate maxpx="0" minpx="-830" orientation="right"></animate>
</pwindow-float>
