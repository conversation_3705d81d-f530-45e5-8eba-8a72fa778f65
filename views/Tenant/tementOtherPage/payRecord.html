<!--缴费记录电  -->
<div class="payRecord" style="height: 100%;" v-if="currentPage=='payRecord'">
    <!--头部  -->
    <div class="payRecord_title">
        <div>
            <pbutton-white text="返回" click="ldp.energyCosttempback" id="payRecord_title_button1"></pbutton-white>
        </div>
        <div class="payRecord_title_title">
            缴费记录-{{energyTypeName}}
        </div>
        <div>
            <pbutton-white text="下载缴费记录" icon="D" id="payRecord_title_button2" click="tenantCtrl.downFeeRecord_bat"></pbutton-white>
        </div>
    </div>
    <!--时间栏  -->
    <div class="payRecord_time"  >
        <ptime-calendar align="left" id="fee_record_bat_time" orientation="down" sel="tenantCtrl.feeRecordChangeTime_bat">
            <panel timetype="dwMy" startyear="2015"></panel>
        </ptime-calendar>
    </div>
    <!--表格部分  -->
    <div class="payRecord_grid">
        <!--表头  -->
        <div class="payRecord_grid_top">
            <ul>
                <li class="inline">
                    <div class="slh payRecord_grid_name">租户名称</div>
                    <div class="slh payRecord_grid_namenum">租户编号</div>
                    <div class="slh payRecord_grid_home">房间编号</div>
                    <div class="inline payRecord_grid_con">
                        <div class="slh">缴费时间</div>
                        <div class="slh">账单</div>
                        <div class="slh">账单号</div>
                        <div class="slh">缴费金额(元)</div>
                        <div class="slh">充值类型</div>
                        <div class="slh">操作人</div>
                    </div>
                </li>
            </ul>
        </div>
        <!--表格内容  -->
        <div class="payRecord_grid_body">
            <ul>
                <li class="inline" v-for="model in checkedTenantArr">
                    <div class="slh payRecord_grid_name">{{model.tenantName}}</div>
                    <div class="slh payRecord_grid_namenum">{{model.tenantId}}</div>
                    <div class="slh payRecord_grid_home">{{model.roomIds}}</div>
                    <div class="payRecord_grid_body_right">
                        <div class="inline payRecord_grid_con" v-show="model.feeRecordArr.length == 0">
                            <div>--</div>
                            <div>--</div>
                            <div>--</div>
                            <div>--</div>
                            <div>--</div>
                            <div>--</div>
                        </div>
                        <div class="inline payRecord_grid_con" v-for="record in model.feeRecordArr" v-hide="model.feeRecordArr.length == 0">
                            <div class="slh">{{record.payTime}}</div>
                            <div class="slh">{{record.orderTime}}</div>
                            <div class="slh">{{record.orderId}}</div>
                            <div class="slh">{{tenantCtrl.numberFormat(record.money,tenantCtrl.fixType_money,true)}}</div>
                            <div class="slh">{{record.channelType}}</div>
                            <div class="slh">{{record.userName}}</div>
                        </div>
                    </div>
                </li>
            </ul>
            <div v-show="checkedTenantArr.length == 0">
                <pnotice-nodata text="暂无数据" subtitle="请重新加载"></pnotice-nodata>
            </div>
        </div>
    </div>
</div>
