<div class="basic_information" v-show="subaddPage==='step1'">
    <div class="input_comboxPart">
        <label>租户名称</label>
        <div>
            <ptext-text bind="true" id="'tementName'" placeholder="'不超过20位字符'" value="selTenant.tenantName">
                <verify errtip="不可为空" verifytype="space"></verify>
                <verify errtip="不超过20个字符" verifytype="length" length="20"></verify>
            </ptext-text>   
        </div>
        <div class="input_comboxPart">
            <label>租户编号</label>
            <span v-if="addOrEdit===0" style="color: #FD9054; font-size: 12px;">创建后不可更改</span>
            <div v-show="addOrEdit===0">
                <ptext-text bind="true" id="'tementNo'" placeholder="'不超过20位字符'" value="selTenant.tenantId">
                    <verify errtip="不可为空" verifytype="space"></verify>
                    <verify errtip="不超过20个字符" verifytype="length" length="20"></verify>
                </ptext-text>
            </div>
            <div v-if="addOrEdit==1">
                <ptext-text bind="true" placeholder="'不超过20位字符'" value="selTenant.tenantId"></ptext-text>
             </div>
        </div>
        <div class="input_comboxPart">
            <label>所属建筑</label>
            <div v-show="addOrEdit==0||selTenant.tenantStatus==0">
                <pcombobox-normal id="'parentBuilding'" bind="true">
                    <header placeholder="'请选择'"></header>
                    <item datasource="buildingArr2" text="name"></item>
                </pcombobox-normal>
            </div>
            <div v-if="addOrEdit==1&&selTenant.tenantStatus==1">
                <ptext-text bind="true" placeholder="'不超过20位字符'" value="selTenant.buildingName"></ptext-text></div>
        </div>
        <div class="input_comboxPart">
            <label>所属业态</label>
            <div>
                <pcombobox-normal id="'parentYetai'" bind="true">
                    <header placeholder="'请选择'"></header>
                    <item datasource="tenantTypeArr" text="name"></item>
                </pcombobox-normal>
            </div>
        </div>
        <div class="input_comboxPart">
            <label>面积</label>
            <div>
                <ptext-text bind="true" id="'tementArea'" placeholder="'精确到小数点后2位'" text="'㎡'" value="selTenant.area" blur="ldp.tementArea">
                    <verify errtip="不可为空" verifytype="space"></verify>
                    <verify errtip="请输入数字" verifytype="positivenumber"></verify>
                </ptext-text>
            </div>
        </div>
        <ul class="input_comboxPartGroup">
            <li class="clearFloat" v-for="(item,$index) in contacts" :class="$index>0?'newAddList':''">
                <div class="contact">
                    <label>联系人<em v-if="$index>0">(选填)</em></label>
                    <div v-if="$index===0">
                        <ptext-text id="'contact'" placeholder="'请输入联系人姓名'" value="item.contactName" bind="true">
                            <verify errtip="联系人不能为空" verifytype="space"></verify>
                            <verify errtip="不能超过20位字符" verifytype="length" length="20"></verify>
                        </ptext-text>
                    </div>
                    <div v-if="$index>0">
                        <!--选填-->
                        <ptext-text placeholder="'请输入联系人姓名'" value="item.contactName" bind="true">
                            <verify errtip="联系人不能为空" verifytype="space"></verify>
                            <verify errtip="不能超过20位字符" verifytype="length" length="20"></verify>
                        </ptext-text>
                    </div>
                </div>
                <div class="contact_number">
                    <label>联系电话<em v-if="$index>0">(选填)</em></label>
                    <div v-if="$index===0">
                        <ptext-text id="'contact_tel'" placeholder="'请输入11位手机号'" value="item.contactMobile" bind="true"></ptext-text>
                    </div>
                    <div v-if="$index>0">
                        <!--选填-->
                        <ptext-text placeholder="'请输入11位手机号'" value="item.contactMobile" bind="true"></ptext-text>
                    </div>
                </div>
                <b v-if="$index>0" :index="$index" class="minusBtn" onclick="ldp.minuscontacts(event)">x</b>
            </li>
            <div class="addContact" :class="{_disable:contacts.length>2}" onclick="ldp.addContact(event)"><em>J</em>添加联系人</div>
        </ul>
        <div class="input_comboxPart">
            <label>备注(选填)</label>
            <div>
                <ptext-textarea bind="true" id="'tementRemarks'" placeholder="'填写相关备注…'" class="packExplain" value="selTenant.remark">
                    <verify errtip="上限为50位字符" length="50" friendlyip="" verifytype="length"></verify>
                </ptext-textarea>
            </div>
        </div>
    </div>
</div>
