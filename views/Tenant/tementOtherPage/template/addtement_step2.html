<div class="basic_information2" v-show="subaddPage==='step2'">
    <div class="roomPart">
        <label>所在房间</label>
        <ul class="roomPart_list">
            <li class="nodata" v-if="addChooseRoomArr.length===0">请点击下方按钮添加房间</li>
            <li class="roomMeter_list" v-if="addChooseRoomArr.length>0" v-for="item in addChooseRoomArr">
                <b class="minusroomBtn" @click="ldp.minusRoom(item,event)">x</b>
                <div class="roomNo">房间编号: <em>{{item.code}}</em></div>
                <ul class="meter_list">
                    <li>
                        <div>电表</div>
                        <div class="slh">{{item.DianMeterIdStr}}</div>
                    </li>
                    <li>
                        <div>水表</div>
                        <div class="slh">{{item.ShuiMeterIdStr}}</div>
                    </li>
                    <li>
                        <div>热水表</div>
                        <div class="slh">{{item.ReShuiMeterIdStr}}</div>
                    </li>
                    <li>
                        <div>燃气表</div>
                        <div class="slh">{{item.RanQiMeterIdStr}}</div>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
    <div onclick="ldp.addRoomBtn(event)" class="addRoomBtn"><em>J</em>添加房间</div>
    <ul class="formulaList">
        <li v-for="(item,$index) in meterformulalist">
            <b :index="$index" class="minusformulaBtn" onclick="ldp.minusMeterformula(event)">x</b>
            <div class="formula_combox">
                <pcombobox-normal bind="true" :name="'formula_energy_type'" id="item.energyTypeId+'_formula_type'">
                    <header placeholder="'能源类型：请选择'" prefix="能源类型："></header>
                    <item datasource="energyTypeArr" text="name"></item>
                </pcombobox-normal>
            </div>
            <div class="formula_title">拆分公式：</div>
            <div onkeyup="ldp.checkchinese(event)">
                <ptext-text id="'formula_text'" click="ldp.formulaclick" placeholder="'请将所有要使用的仪表全部填入公式。例如：[仪表.功能号]*0.6+[仪表.功能号]+[仪表.功能号]...'" :name="'formula_val'" value="item.expression" bind="true">
                    <verify errtip="请输入拆分公式" verifytype="space"></verify>
                </ptext-text>
            </div>
        </li>
    </ul>
    <div onclick="ldp.addMeterformula(event)" class="addMeterformula" style="display: none;">
        <em>J</em>添加仪表拆分公式
        <div class="formulaIcon">
            <b>?</b>
            <div class="tips">
                <em>仪表拆分公式：</em>当多个租户共用一个仪表时，需要为仪表添加拆分公式，公式中仪表的格式为[表号.功能号]，例如：租户A只使用电表1001的60%，电表的功能号是10101，那么需要为A添加拆分公式[1001.10101]*0.6
            </div>
        </div>
    </div>
</div>
