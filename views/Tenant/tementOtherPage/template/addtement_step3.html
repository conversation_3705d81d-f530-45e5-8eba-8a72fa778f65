<div class="basic_information" v-show="subaddPage==='step3'">
    <div class="choosesystem_remind remind"><i>i</i>能耗系统、付费类型以及充值类型激活后不可更改</div>
    <ul class="choosesystem">
        <li class="system_list" v-for="model in energyTypeArr" :pdisabled="(addOrEdit ==0||selTenant.tenantStatus==0)&&!add_energyType[model.id]">
            <div class="system_list_head" :pdisabled="addOrEdit == 1 && selTenant.tenantStatus==1">
                <pswitch-checkbox bind="true" id="model.id+'_system'" text="model.name+'系统'" change="ldp.selectEnergyType"></pswitch-checkbox>
            </div>
            <div class="system_list_body">
                <div :pdisabled="addOrEdit == 1 && selTenant.tenantStatus==1">
                    <label>付费类型</label>
                    <div v-show="addOrEdit==0 || selTenant.tenantStatus==0">
                        <pswitch-radio bind="true" text="'预付费'" name="model.id+'_payment_types'" id="model.id+'_0_payment_types'" change="tenantCtrl.changePayType_yu"></pswitch-radio>
                        <pswitch-radio bind="true" text="'后付费'" name="model.id+'_payment_types'" id="model.id+'_1_payment_types'" change="tenantCtrl.changePayType_hou"></pswitch-radio>
                    </div>
                    <div v-if="addOrEdit==1&&selTenant.tenantStatus==1" :id="model.id+'_payment_name'">后付费</div>
                </div>
                <div :id="model.id+'_fee_type'" :pdisabled="addOrEdit==1 && selTenant.tenantStatus==1">
                    <label>扣费类型</label>
                    <div>
                        <pswitch-radio bind="true"  text="'表充表扣'" name="model.id+'_deduction_types'" id="model.id+'_0_deduction_types'"></pswitch-radio>
                        <pswitch-radio bind="true" text="'软件充值仪表扣费'" name="model.id+'_deduction_types'" id="model.id+'_1_deduction_types'"></pswitch-radio>
                        <pswitch-radio bind="true" text="'软件充值软件扣费'" name="model.id+'_deduction_types'" id="model.id+'_2_deduction_types'"></pswitch-radio>
                    </div>
                </div>
                <div :id="model.id+'_recharge_type'" :pdisabled="addOrEdit==1&&selTenant.tenantStatus==1">
                    <label>充值类型</label>
                    <div>
                        <pswitch-radio bind="true"  text="'充钱'" name="model.id+'_recharge_types'" id="model.id+'_1_recharge_types'"></pswitch-radio>
                        <pswitch-radio bind="true" text="'充'+model.name+'量'" name="model.id+'_recharge_types'" id="model.id+'_0_recharge_types'"></pswitch-radio>
                    </div>
                </div>
                <div id="pricePlan">
                    <label>价格方案</label>  
                    <div @click="ldp.managePricePlan(model,event)" class="managePricePlan">管理价格方案></div>
                    <div class="pricePlan_combox">
                        <pcombobox-normal id="model.id+'_price_sel'" sel="ldp.checkedPricePlan" bind="true">
                            <header placeholder="'请选择'"></header>
                            <item datasource="model.priceArr" text="name"></item>
                        </pcombobox-normal>
                    </div>
                    <span pdisabled="true" type="detail" @click="ldp.showPricePlan(event,model)" class="checkPriceDetail">查看价格详情</span>
                </div>
            </div>
        </li>
    </ul>
</div>
