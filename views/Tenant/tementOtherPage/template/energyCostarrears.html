<!--欠费账单  -->
<div v-if="currentPage=='energyCostReport'&&pdfPage=='energyCostarrears'">
    <!--欠费账单  头部  -->
     <div class="arrears_head" v-if="!selTenant.energyCostReport.isCurrentOrder">
        <div class="arrears_head_left">
            <h2><em><u>{{(selTenant.tenantName+"").substring(0,1)}}</u>{{(selTenant.tenantName+"").substring(1)}}</em>{{selTenant.energyCostReport.isCurrentOrder?'账单':'欠费账单'}}</h2>
        </div>
        <div class="arrears_head_right">{{selTenant.buildingName}}</div>
    </div>
    <!--欠费账单  内容部分  -->
    <div class="arrears_body" v-if="!selTenant.energyCostReport.isCurrentOrder">
        <!--内容部分头部  -->
         <div class="arrears_body_title ">
            <div class="arrears_body_title_right">
                <h2>应缴总计：</h2>
                <span>¥<em>{{tenantCtrl.numberFormat(selTenant.energyCostReport.totalMoney,tenantCtrl.fixType_money,true)}}</em></span>
            </div>
            <div class="arrears_body_title_left">
                <h2>租户编号：<em>{{selTenant.tenantId}}</em></h2>
                <h2>能耗类型：<em>{{selTenant.energyCostReport.energyTypeName}}-后付费</em></h2>
            </div>
        </div>
        <!--内容部分表格  -->
         <div class="arrears_body_grid ">
            <div class="arrears_body_grid_head">
                <div class="title_item">
                    <div>账单</div>
                    <div>本期结算能耗（{{selTenant.energyCostReport.energyUnit}}）</div>
                    <div>本期结算金额（元）</div>
                </div>
            </div>
            <div class="arrears_body_grid_body">
                <div v-for="order in selTenant.energyCostReport.dataList" class="arrears_body_grid_body_list changeColor">
                    <div>
                        <p>{{order.orderTime}}</p>
                        <p>编号：<em>{{order.orderId}}</em></p>
                    </div>
                    <div>{{tenantCtrl.numberFormat(order.amount,tenantCtrl.fixType_dynamic,true)}}</div>
                    <div>{{tenantCtrl.numberFormat(order.money,tenantCtrl.fixType_money,true)}}</div>
                </div>
            </div>
        </div>
    </div>

    <div :class="{'pageBreak':!selTenant.energyCostReport.isCurrentOrder,'isdownPdf':!selTenant.energyCostReport.isCurrentOrder}">
        <!--欠费账单  头部  -->
        <div class="arrears_head">
            <div class="arrears_head_left">
                <h2><em><u>{{(selTenant.tenantName+"").substring(0,1)}}</u>{{(selTenant.tenantName+"").substring(1)}}</em>欠费账单</h2>
            </div>
            <div class="arrears_head_right">{{selTenant.buildingName}}</div>
        </div>
        <!--欠费账单  内容部分  -->
        <div class="arrears_body">
            <!--内容部分头部  -->
            <div class="arrears_body_title ">
                <div class="arrears_body_title_left">
                    <h2>租户编号：<em>{{selTenant.tenantId}}</em></h2>
                    <h2>能耗类型：<em>{{selTenant.energyCostReport.energyTypeName}}-后付费</em></h2>
                </div>
            </div>
            <!--内容部分表格  -->
            <div class="arrears_body_grid" v-for="order in selTenant.energyCostReport.dataList">
                <div class="arrears_body_grid_head">
                    <div class="title_item">
                        <div>账单</div>
                        <div>本期结算能耗（{{selTenant.energyCostReport.energyUnit}}）</div>
                        <div>本期结算金额（元）</div>
                    </div>
                </div>
                <div class="arrears_body_grid_body">
                    <div class="arrears_body_grid_body_list">
                        <div>
                            <p>{{order.orderTime}}</p>
                            <p>编号：<em>{{order.orderId}}</em></p>
                        </div>
                        <div>{{tenantCtrl.numberFormat(order.amount,tenantCtrl.fixType_dynamic,true)}}</div>
                        <div>{{tenantCtrl.numberFormat(order.money,tenantCtrl.fixType_money,true)}}</div>
                    </div>


                     <div class="temp_body_grid_head">
                        <div class="title">账单明细</div>
                        <div class="title_item">
                            <div>日期</div>
                            <div>当日耗{{selTenant.energyCostReport.energyTypeName}}量({{selTenant.energyCostReport.energyUnit}})</div>
                            <div>累计能耗({{selTenant.energyCostReport.energyUnit}})</div>
                            <div>累计金额(元)</div>
                        </div>
                        <div class="temp_body_grid_body">
                            <div class="temp_body_grid_body_list" v-for="detail in order.orderDetail">
                                <div>{{detail.time}}</div>
                                <div>{{tenantCtrl.numberFormat(detail.dayEnergy,tenantCtrl.fixType_dynamic,true)}}</div>
                                <div>{{tenantCtrl.numberFormat(detail.totalEnergy,tenantCtrl.fixType_dynamic,true)}}</div>
                                <div>{{tenantCtrl.numberFormat(detail.totalMoney,tenantCtrl.fixType_money,true)}}</div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
