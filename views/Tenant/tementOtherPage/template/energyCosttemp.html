<!--能耗费用报表-->
<div v-if="currentPage=='energyCostReport'&&pdfPage=='energyCosttemp'">
    <div class="temp_head">
        <div class="temp_head_left">
            <h2><em><u>{{(selTenant.tenantName+"").substring(0,1)}}</u>{{(selTenant.tenantName+"").substring(1)}}</em>能耗费用报表</h2>
            <span>{{selTenant.energyCostReport.timeShow()}}</span>
        </div>
        <div class="temp_head_right">{{selTenant.buildingName}}</div>
    </div>
    <div class="temp_body">
        <div class="temp_body_title">
            <div class="temp_body_title_right">
                <h2>总费用：</h2>
                <span>¥<em>{{tenantCtrl.numberFormat(selTenant.energyCostReport.totalMoney,tenantCtrl.fixType_money,true)}}</em></span>
            </div>
            <div class="temp_body_title_right temp_body_title_right2">
                <h2>总能耗：</h2>
                <span><em>{{tenantCtrl.numberFormat(selTenant.energyCostReport.totalEnergy,tenantCtrl.fixType_dynamic,true)}}</em>{{selTenant.energyCostReport.energyUnit}}</span>
            </div>
            <div class="temp_body_title_left">
                <h2>租户编号：<em>{{selTenant.tenantId}}</em></h2>
                <h2>能耗类型：<em>{{selTenant.energyCostReport.energyTypeName}}</em></h2>
            </div>
        </div>
        <div class="temp_body_grid ">
            <div class="temp_body_grid_head">
                <div class="title">报表明细</div>
                <div class="title_item">
                    <div>日期</div>
                    <div>当日能耗({{selTenant.energyCostReport.energyUnit}})</div>
                    <div>累计能耗({{selTenant.energyCostReport.energyUnit}})</div>
                    <div>累计金额(元)</div>
                </div>
            </div>
            <div class="temp_body_grid_body">
                <div v-for="data in selTenant.energyCostReport.dataList" class="temp_body_grid_body_list">
                    <div>{{(data.time+"").substring(0,10).replace(/-/g,'.')}}</div>
                    <div>{{tenantCtrl.numberFormat(data.energy,tenantCtrl.fixType_dynamic,true)}}</div>
                    <div>{{tenantCtrl.numberFormat(data.sumEnergy,tenantCtrl.fixType_dynamic,true)}}</div>
                    <div>{{tenantCtrl.numberFormat(data.money,tenantCtrl.fixType_money,true)}}</div>
                </div>
            </div>
        </div>
    </div>
</div>
