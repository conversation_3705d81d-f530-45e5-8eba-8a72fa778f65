<!--begin:选择房间的模板-->
<script type="text/html" id="chooseRoomTemp_wrap_scrollTemp">
    <ul class="chooseRoomTemp_wrap_body">
        <li v-for="model in addTenantSelBuilding.roomShowArr">
            <pswitch-checkbox bind="true" id="model.id" text="model.code" state="model.isChecked" change="ldp.chooseRoom"></pswitch-checkbox>
        </li>
    </ul>
    <div v-show="addTenantSelBuilding.roomShowArr.length == 0">
        <pnotice-nodata text="暂无数据" subtitle="请重新加载"></pnotice-nodata>
    </div>
</script>
<script type="text/html" id="chooseRoomTemp">
    <div class="chooseRoomTemp_wrap">
        <div class="chooseRoomTemp_wrap_top">
            <div class="chooseRoomTemp_combox">
                <pcombobox-normal bind="true" sel="tenantCtrl.filterRoom" id="'floor_filter'">
                    <header placeholder="'选择楼层'" prefix="楼层："></header>
                    <item datasource="addTenantSelBuilding.floorArr" text="name" ></item>
                </pcombobox-normal>
            </div>
            <div class="chooseRoomTemp_search">
                <psearch-promptly change="tenantCtrl.filterRoom" id="room_search"  placeholder="选择房间">
                </psearch-promptly>
            </div>
        </div>
        <pscroll-small templateid="chooseRoomTemp_wrap_scrollTemp" bind="true"></pscroll-small>
        <div class="chooseRoomLoading">
            <ploading-part id="chooseRoomLoadingListen" text="加载中，请稍后..."></ploading-part>
        </div>
    </div>
</script>
<!--end:选择房间的模板-->

<!--新建价格方案编辑价格方案-->
<pwindow-float id="build_edit_pricePlan" isshade="true" title="新建价格方案" templateid="build_edit_pricePlanTemp">
    <button>
        <!-- <div v-show="!priceOperateType" v-if="operationPermissions.UpdatePrice">
            <pbutton-blue text="编辑" icon="e" click="ldp.editPriceBtn" isborder="false" id="editPriceBtn"></pbutton-blue>
        </div>  -->
         <div v-show="priceOperateType">
            <pbutton-blue text="保存" icon="h" click="tenantCtrl.priceSaveClick" isborder="false" id="savePriceBtn">
            </pbutton-blue>
        </div>
    </button>
    <animate maxpx="0" minpx="-830" orientation="right"> </animate>
</pwindow-float> 

<!--begin:新建价格方案/编辑价格方案      -->
<script type="text/html" id="build_edit_pricePlanTemp">
    <div class="price_plan_wrapper">
         <div v-show="payType != '0'&&priceOperateType==1" class="plan_price_remind">
            <i>i</i>编辑了价格方案后，所有使用该价格方案的租户都会受到影响
        </div>
        <div class="plan_name">
            <div v-show="!priceOperateType">{{selPrice.name}}</div>
            <lable v-show="priceOperateType">方案名称：</lable>
            <div v-show="priceOperateType">
                <ptext-text placeholder="'请输入50个字符以内的方案名称'" id="'price_name'" value="selPrice.name" bind="true">
                    <verify errtip="请输入方案名称" verifytype="space"></verify>
                    <verify errtip="上限为50位字符" verifytype="length" length="50"></verify>
                </ptext-text>
            </div>
        </div>
        <div class="plan_price">
            <div class="electricitypriceType clearFloat" v-show="priceOperateType!=0&&selEnergyType.id=='Dian'">
                <pswitch-radio id="'pjdjRadio'" text="'平均电价'" name="'electricitypriceType'" change="tenantCtrl.switchPJ" disabled="priceOperateType==1" bind="true"></pswitch-radio>
                <pswitch-radio id="'fsdjRadio'" text="'分时电价'" name="'electricitypriceType'" change="tenantCtrl.switchFS" disabled="selEnergyType.id!='Dian'||priceOperateType==1" bind="true"></pswitch-radio>
            </div>
            <div class="pj_dj" v-show="selPrice.type==0">
                <!--平均电价-->
                <ptext-text v-show="priceOperateType" placeholder="" text="selPrice.unit" bind="true" id="'pj_value'" value="selPrice.content.PJ"  >
                    <verify errtip="请输入单价" verifytype="space"></verify>
                    <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                </ptext-text>
                <ul class="time_electricity_price" v-show="!priceOperateType">
                    <li class="averagePrice">
                        <div>平均</div>
                        <div>
                            <div class="textState">{{selPrice.content.PJ}}{{selPrice.unit}}</div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="fs_dj" v-show="selPrice.type==1">
                <!--分时电价-->
                <ul class="time_electricity_price">
                    <li>
                        <div>尖峰</div>
                        <div onclick="ldp.edit_electricityprice(event)">
                            <div v-show="!priceOperateType" class="textState"><em>{{selPrice.content.J}}</em><em>{{selPrice.unit}}</em></div>
                            <div v-show="priceOperateType" class="inputState">
                                <ptext-text placeholder="" text="selPrice.unit" bind="true" id="'jf_value'" value="selPrice.content.J"  >
                                     <verify errtip="请输入单价" verifytype="space"></verify>
                                     <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                                </ptext-text>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div>高峰</div>     
                        <div onclick="ldp.edit_electricityprice(event)">
                            <div v-show="!priceOperateType" class="textState"><em>{{selPrice.content.F}}</em><em>{{selPrice.unit}}</em></div>
                            <div v-show="priceOperateType" class="inputState">
                                <ptext-text placeholder="" text="selPrice.unit" bind="true" id="'gf_value'" value="selPrice.content.F" >
                                     <verify errtip="请输入单价" verifytype="space"></verify>
                                     <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                                </ptext-text>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div>平段</div>
                        <div onclick="ldp.edit_electricityprice(event)">
                            <div v-show="!priceOperateType" class="textState"><em>{{selPrice.content.P}}</em><em>{{selPrice.unit}}</em></div>
                            <div v-show="priceOperateType" class="inputState">
                                <ptext-text placeholder="" text="selPrice.unit" bind="true" id="'pd_value'" value="selPrice.content.P">
                                    <verify errtip="请输入单价" verifytype="space"></verify>
                                    <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                                </ptext-text>
                            </div>
                        </div>
                    </li>
                    <li>
                        <div>低谷</div>
                        <div onclick="ldp.edit_electricityprice(event)">
                            <div v-show="!priceOperateType" class="textState"><em>{{selPrice.content.G}}</em><em>{{selPrice.unit}}</em></div>
                            <div v-show="priceOperateType" class="inputState">
                                <ptext-text placeholder="" text="selPrice.unit" bind="true" id="'dg_value'" value="selPrice.content.G" >
                                    <verify errtip="请输入单价" verifytype="space"></verify>
                                     <verify errtip="请输入正数" verifytype="positivenumber"></verify>
                                </ptext-text>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="plan_price_space">
                <i>i</i>请输入单价
            </div>

        </div>
    </div>
    </script>
     <!--修改价格方案  -->
<pwindow-modal id="modalWindowPrice" isshade="true" title="修改价格方案" templateid="list_upt_price_detail">
</pwindow-modal>
<script type="text/html" id="list_upt_price_detail">
    <!--修改价格方案模板  -->
<div class="float" style="min-width:480px">
    <!-- <p class="colorGray">所选能源类型：</p>
    <p>{{energyName == 'Dian' ? '电' : (energyName == 'Shui' ? '水' : '无') }}</p>
    <br /> -->
    <div class="flexbox spaceBetween" style="padding-bottom:10px">
        <span>价格方案列表：</span>
        <span class="colorBlue" onclick="tenantCtrl.tomanagePrivcePlan(6)" style="cursor: pointer;padding:0">管理价格方案 > </span>
    </div>
    <div>
        <pcombobox-normal bind="true" id="'floatTextPrice'">
            <header placeholder="'选择价格方案'"></header>
            <item datasource="dianPriceArr" text="name"></item>
        </pcombobox-normal>
    </div>
    <div class="selectTime">
        <label>执行时间：</label>
        <div class="radioSty">
            <pswitch-radio text="月末执行" change="tenantCtrl.monthExecute"  name="isExecute" id="monthExecute0"></pswitch-radio>
            <pswitch-radio text="立即执行" change="tenantCtrl.nowExecute" name="isExecute" id="nowExecute1"></pswitch-radio>
        </div>
    </div>
    <p class="textSmall" id="upt_price_bat_tip">电价方案作用范围需同时满足以下条件：</p>
    <p  class="textSmall" style="line-height:25px">1、已激活的租户<br/>2、软充表扣计费方式<br/>3、仅针对分时电表<br/>4、电表支持修改电价、查询电价。</p>
    <p class="colorBlue" onclick="tenantCtrl.viewLog(0,-1)" style="cursor: pointer;">查看批量修改电价日志 ></p>
        <div class="resetBox clearfix">
        <pbutton-blue text="保存" click="tenantCtrl.batchPriceSet" id="determine_global" data-flag="global"></pbutton-blue>
        <pbutton-white text="取消" click="staticEvent.floatHide"  id="cancel_global"></pbutton-white>
</div>
</div>
</script>