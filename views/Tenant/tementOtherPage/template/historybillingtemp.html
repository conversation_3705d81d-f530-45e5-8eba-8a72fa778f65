<div v-if="currentPage=='energyCostReport'&&pdfPage==='historybillingtemp'">
    <div class="temp_head">
        <div class="temp_head_left">
            <h2><em><u>{{(selTenant.tenantName+"").substring(0,1)}}</u>{{(selTenant.tenantName+"").substring(1)}}</em>历史账单
            </h2>
            <span>{{selTenant.energyCostReport.timeShow()}} </span>
        </div>
        <div class="temp_head_right">{{selTenant.buildingName}}</div>
    </div>
    <div class="temp_body">
        <div class="temp_body_title ">
            <div class="temp_body_title_right">
                <h2>缴费总计：</h2>
                <span>¥<em>{{tenantCtrl.numberFormat(selTenant.energyCostReport.totalMoney,tenantCtrl.fixType_money,true)}}</em></span>
            </div>
            <div class="temp_body_title_left">
                <h2>租户编号：<em>{{selTenant.tenantId}}</em></h2>
                <h2>能耗类型：<em>{{selTenant.energyCostReport.energyTypeName}}-后付费</em></h2>
            </div>
        </div>
        <div class="temp_body_grid ">
            <div class="historyBillinggrid_head ">
                <div>账单</div>
                <div>本期结算能耗({{selTenant.energyCostReport.energyUnit}})</div>
                <div>本期结算金额(元)</div>
            </div>
            <div class="temp_body_grid_body">
                <div v-for="order in selTenant.energyCostReport.dataList"
                    class="temp_body_grid_body_list historyBillinggrid_list">
                    <div>
                        <h2><em>{{order.orderTime}}</em><i
                                :class="{'nopay':order.status==0}">{{order.status==0?'未缴费':'已缴费'}}</i></h2>
                        <h2>编号：<em>{{order.orderId}}</em></h2>
                    </div>
                    <div>{{tenantCtrl.numberFormat(order.amount,tenantCtrl.fixType_dynamic,true)}}</div>
                    <div>{{tenantCtrl.numberFormat(order.money,tenantCtrl.fixType_money,true)}}</div>
                </div>
            </div>
        </div>
    </div>

    <!--明细只下载时显示-->
    <div class="pageBreak isdownPdf">
        <div class="temp_head ">
            <div class="temp_head_left">
                <h2><em><u>{{(selTenant.tenantName+"").substring(0,1)}}</u>{{(selTenant.tenantName+"").substring(1)}}</em>历史账单
                </h2>
                <span>{{selTenant.energyCostReport.timeShow()}}</span>
            </div>
            <div class="temp_head_right">{{selTenant.buildingName}}</div>
        </div>
        <div class="temp_body">
            <div class="temp_body_title ">
                <div class="temp_body_title_left">
                    <h2>租户编号：<em>{{selTenant.tenantId}}</em></h2>
                    <h2>能耗类型：<em>{{selTenant.energyCostReport.energyTypeName}}-后付费</em></h2>
                </div>
            </div>
            <div v-for="order in selTenant.energyCostReport.dataList" class="temp_body_grid "
                style="margin-bottom: 20px;">
                <div class="historyBillinggrid_head ">
                    <div>账单</div>
                    <div>本期结算能耗({{selTenant.energyCostReport.energyUnit}})</div>
                    <div>本期结算金额(元)</div>
                </div>
                <div class="temp_body_grid_body">
                    <div class="temp_body_grid_body_list historyBillinggrid_list">
                        <div>
                            <h2><em>{{order.orderTime}}</em><i
                                    :class="{'nopay':order.status==0}">{{order.status==0?'未缴费':'已缴费'}}</i></h2>
                            <h2>编号：<em>{{order.orderId}}</em></h2>
                        </div>
                        <div>{{tenantCtrl.numberFormat(order.amount,tenantCtrl.fixType_dynamic,true)}}</div>
                        <div>{{tenantCtrl.numberFormat(order.money,tenantCtrl.fixType_money,true)}}</div>
                    </div>
                    <div class="temp_body_grid_head">
                        <div class="title">账单明细</div>
                        <div class="title_item">
                            <div>日期</div>
                            <div>
                                当日耗{{selTenant.energyCostReport.energyTypeName}}量({{selTenant.energyCostReport.energyUnit}})
                            </div>
                            <div>累计能耗({{selTenant.energyCostReport.energyUnit}})</div>
                            <div>累计金额(元)</div>
                        </div>
                    </div>
                    <div class="temp_body_grid_body">
                        <div v-for="detail in order.orderDetail" class="temp_body_grid_body_list">
                            <div>{{(detail.time||'--').substring(0,10)}}</div>
                            <div>{{tenantCtrl.numberFormat(detail.dayEnergy,tenantCtrl.fixType_dynamic,true)}}</div>
                            <div>{{tenantCtrl.numberFormat(detail.totalEnergy,tenantCtrl.fixType_dynamic,true)}}</div>
                            <div>{{tenantCtrl.numberFormat(detail.totalMoney,tenantCtrl.fixType_money,true)}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>