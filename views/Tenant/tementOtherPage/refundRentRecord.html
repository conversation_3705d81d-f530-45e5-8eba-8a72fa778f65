<div class="r_r_r_box" v-show="currentPage == 'refundRentRecord'">
    <!-- 操作栏 -->
    <div class="r_r_r_header clearfix">
        <!-- 返回按钮 -->
        <div class="r_r_r_go_back">
            <pbutton-white text="返回" click="ldp.backToTemenListPage"></pbutton-white>
        </div>

        <!-- 标题 -->
        <h1>退费记录-{{refundRentRecordsData.energyTypeName}}-软充软扣</h1>

    </div>

    <!-- 退费记录表格 -->
    <div class="r_r_r_gird_box">
        <div class="r_r_r_gird_operation clearfix">
            <div class="r_r_r_gird_operation_left clearfix">
                <!-- 时间选择控件 -->
                <div class="time_controller_box">
                    <ptime-calendar id="refund_rent_time_controller"
                        sel="tenantCtrl.getRefundRent_bat(false, 'refund_rent_time_controller')">
                        <panel timetype="dwMy" startyear="2015" align="left"></panel>
                    </ptime-calendar>
                </div>
            </div>

            <div class="r_r_r_gird_operation_right">
                <!-- 下载按钮 -->
                <div class="r_r_r_download">
                    <pbutton-white text="'下载退费记录'" icon="'D'"
                        click="tenantCtrl.getRefundRent_bat(true, 'refund_rent_time_controller')" bind="true">
                    </pbutton-white>
                </div>
            </div>
        </div>

        <div class="r_r_r_gird">
            <div class="r_r_r_gird_tit">
                <div>租户名称</div>
                <div>租户编号</div>
                <div>房间编号</div>
                <div>退费时间</div>
                <div>退费单号</div>
                <div>退费金额（元）</div>
                <div>退费量（{{refundRentRecordsData.energyUnit}}）</div>
                <div>操作人</div>
            </div>
            <div class="r_r_r_gird_body">
                <pscroll-small templateid="RRRScrollBox"></pscroll-small>
            </div>
        </div>
        <div ndm="" class="per-grid-nodata">
            <div pc="" _pt="pnotice-nodata" _id="bfbfheiegahhgdbfafh" class="per-prompt-abnormalmess">
                <span class="per-prompt_icon"></span>
                <span class="per-prompt_title">暂无数据</span>
                <span class="per-prompt_subtitle">请检查网络是否通畅</span>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="RRRScrollBox">
        <ul class="r_r_r_gird_item_box">
            <li class="r_r_r_gird_item" v-for="(model, index) in refundRentRecordsData.tenantList" :key="index">
                <div class="r_r_r_gird_item_0"><span>{{model.tenantName || '--'}}</span></div>
                <div class="r_r_r_gird_item_0"><span>{{model.tenantId || '--'}}</span></div>
                <div class="r_r_r_gird_item_0"><span>{{model.roomIds || '--'}}</span></div>
                <div class="r_r_r_gird_item_1 room_code_1">
                    <div class="r_r_r_gird_item_2 sort_code_1" v-for="(model, index) in model.orderList" :key="index">{{model.payTime || '--'}}</div>
                    <span v-if="model.orderList.length == 0">--</span>
                </div>
                <div class="r_r_r_gird_item_1 room_code_2">
                    <div class="r_r_r_gird_item_2 sort_code_2" v-for="(model, index) in model.orderList" :key="index">{{model.orderId || '--'}}</div>
                    <span v-if="model.orderList.length == 0">--</span>
                </div>
                <div class="r_r_r_gird_item_1 room_code_3">
                    <div class="r_r_r_gird_item_2 sort_code_3" v-for="(model, index) in model.orderList" :key="index">{{tenantCtrl.numberFormat(model.money, tenantCtrl.fixType_money, true)}}</div>
                    <span v-if="model.orderList.length == 0">--</span>
                </div>
                <div class="r_r_r_gird_item_1 room_code_4">
                    <div class="r_r_r_gird_item_2 sort_code_4" v-for="(model, index) in model.orderList" :key="index">{{tenantCtrl.numberFormat(model.amount, tenantCtrl.fixType_dynamic, true)}}</div>
                    <span v-if="model.orderList.length == 0">--</span>
                </div>
                <div class="r_r_r_gird_item_1 room_code_5">
                    <div class="r_r_r_gird_item_2 sort_code_5" v-for="(model, index) in model.orderList" :key="index">{{model.userName || '--'}}</div>
                    <span v-if="model.orderList.length == 0">--</span>
                </div>
            </li>
        </ul>
    </script>