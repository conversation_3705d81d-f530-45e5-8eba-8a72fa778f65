<div class="pdfReport" v-if="currentPage=='energyCostReport'">
    <div class="pdfReport_head">
        <div class="downloadGroup">
            <pbutton-white v-show="pdfPage=='energyCosttemp'" icon="D" text="下载能耗费用报表" click="ldp.downPdf"></pbutton-white>
            <pbutton-white v-show="pdfPage=='historybillingtemp'" icon="D" text="下载历史账单" click="ldp.downPdf"></pbutton-white>
            <pbutton-white v-show="pdfPage=='energyCostcharge'" icon="D" text="下载充值记录" click="ldp.downPdf"></pbutton-white>
            <pbutton-white v-show="pdfPage=='energyCostarrears'" icon="D" text="下载欠费账单" click="ldp.downPdf"></pbutton-white>
            <pbutton-white v-show="pdfPage=='returnPremiumRecord'" icon="D" text="下载退费记录" click="ldp.downPdf"></pbutton-white>
            <pbutton-white  icon="S" text="打印记录" click="ldp.printHtml('#pdf_wrapper')"></pbutton-white>
        </div>
        <div class="gobackBtn">
            <pbutton-white text="返回" click="ldp.energyCosttempback"></pbutton-white>
        </div>
        <div class="title">{{pdfPage == 'returnPremiumRecord' ? '退费记录' : (pdfPage == 'energyCosttemp' ? ('能耗费用报表') : (pdfPage == 'historybillingtemp' ? ('历史账单') : (pdfPage == 'energyCostcharge' ? ('充值记录') : ('欠费账单'))))}}</div>
    </div>
    <div class="pdfReport_body">
        <div class="func_wrapper">
            <div v-show="pdfPage!='energyCostarrears'">
                <ptime-calendar id="reportCalendar" align="left" orientation="down" sel="ldp.updataPdf">
                    <panel timetype="dMy" startyear="2015" commontime="['d','pd','M','pM','y','py']"></panel>
                </ptime-calendar>
            </div>
            <div v-if="pdfPage=='energyCosttemp'">
                <pcombobox-normal bind="true" id="'report_energy_cbx'" >
                    <header click="ldp.hideTimeCalendar"  placeholder="'选择能耗类型'" prefix="能耗类型："></header>
                    <item datasource="selfenergyTypeArr" text="name"></item>
                </pcombobox-normal>
            </div>
            <div v-if="pdfPage=='energyCosttemp'">
                <pbutton-blue text="生成报告" click="tenantCtrl.createReport_single"></pbutton-blue>
            </div>
        </div>
        <div class="scroll_wrapper">
            <div class="report_wrapper" id="pdf_wrapper">
                <div class="noReport">
                    请点击“生成报告”按钮
                </div>
                <!--欠费账单  -->
                <%include /template/energyCostarrears.html%>
                <!--充值记录  -->
                <%include /template/energyCostcharge.html%>
                <!--能耗费用报表  -->
                <%include /template/energyCosttemp.html%>
                <!--历史账单-->
                <%include /template/historybillingtemp.html%>
                <!--退费账单-->
                <%include /template/returnPremiumRecord.html%>
            </div>
        </div>
    </div>
</div>
