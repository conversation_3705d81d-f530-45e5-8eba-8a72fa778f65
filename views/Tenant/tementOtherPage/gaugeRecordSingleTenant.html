<div class="g_r_s_t_box" v-show="currentPage == 'gaugeRecordSingleTenant'">
    <!-- 操作栏 -->
    <div class="g_r_s_t_header clearfix">
        <!-- 返回按钮 -->
        <div class="g_r_s_t_go_back">
            <pbutton-white text="返回" click="staticEvent.goBackForGaugeRecordReportSingle"></pbutton-white>
        </div>

        <!-- 标题 -->
        <h1>表底数记录 - {{selTenant.tenantName}}</h1>

    </div>

    <!-- 表底数记录表格 -->
    <div class="g_r_s_t_gird_box">
        <div class="g_r_s_t_gird_operation clearfix">
            <div class="g_r_s_t_gird_operation_left clearfix">
                <!-- 能耗类型下拉框 -->
                <div class="energy_type_box">
                    <pcombobox-normal sel="staticEvent.selectEnergyTypeGaugeRecordSingle" bind="true" align="right" orientation="down" id="'energy_type_single'">
                        <header placeholder="'请选择能耗类型'" prefix="能源类型：" click=""></header>
                        <item datasource="energyTypeArrForGauge" text="name"></item>
                    </pcombobox-normal>
                </div>  

                <!-- 时间选择控件 -->
                <div class="time_controller_box">
                    <ptime-calendar id="time_controller_single" orientation="down" sel="staticEvent.chooseTimeRangeGaugeRecordSingle">
                        <panel timetype="d" startyear="2012" align="left" double="false" commontime="['d','pd']"></panel>
                    </ptime-calendar>
                </div>

                <!-- 小时选择下拉框 -->
                <div class="time_hour_box">
                    <pcombobox-normal sel="staticEvent.chooseHourGaugeRecordSingle" bind="true" align="right" orientation="down" id="'time_hour_single'">
                        <header placeholder="'选择小时'" click=""></header>
                        <item datasource="hourArrForGauge" text="hour"></item>
                    </pcombobox-normal>
                </div>

                <div class="check_activate_time">
                    <pswitch-checkbox id="check_activate_time_single" text="查看激活时间表底数" change="staticEvent.checkActivateTimeSingle"></pswitch-checkbox>
                </div>
            </div>

            <div class="g_r_s_t_gird_operation_right">
                <!-- 下载按钮 -->
                <div class="g_r_s_t_download">
                    <pbutton-white text="'下载表底数记录'" icon="'D'" click="staticEvent.downloadGaugeRecordReportSingle" bind="true"></pbutton-white>
                </div>
            </div>
        </div>

        <div class="g_r_s_t_gird">
            <div class="g_r_s_t_gird_tit">
                <div>房间编号</div>
                <div>仪表ID</div>
                <div>仪表能源类型</div>
                <div>{{!gaugeRecordSingleTenantParams.energyTypeId ? 'CT/倍率' : (gaugeRecordSingleTenantParams.energyTypeId == 'Dian' ? 'CT' : '倍率')}}</div>
                <div style="flex: 2">所选时间的仪表读数</div>
                <div>是否乘以{{!gaugeRecordSingleTenantParams.energyTypeId ? 'CT/倍率' : (gaugeRecordSingleTenantParams.energyTypeId == 'Dian' ? 'CT' : '倍率')}}</div>
            </div>
            <div class="g_r_s_t_gird_body">
                <pscroll-small id="GRSTScroll" templateid="GRSTScrollBox"></pscroll-small>
            </div>
        </div>
        <div ndm="" class="per-grid-nodata">
            <div pc="" _pt="pnotice-nodata" _id="bfbfheiegahhgdbfafh" class="per-prompt-abnormalmess">
                <span class="per-prompt_icon"></span>
                <span class="per-prompt_title">暂无数据</span>
                <span class="per-prompt_subtitle">请检查网络是否通畅</span>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="GRSTScrollBox">
    <ul class="g_r_s_t_gird_item_box">
        <li class="g_r_s_t_gird_item" v-for="(model, index) in gaugeRecordSingleTenantArr" :key="index">
            <div class="g_r_s_t_gird_item_0"><span>{{model.roomCode}}</span></div>
            <div>
                <div class="g_r_s_t_gird_item_1 sort_code_1" v-for="(model, index) in model.meterList" :key="index"><span>{{model.meterId}}</span></div>
            </div>
            <div>
                <div class="g_r_s_t_gird_item_1 sort_code_2" v-for="(model, index) in model.meterList" :key="index"><span>{{model.energyTypeName}}</span></div>
            </div>
            <div>
                <div class="g_r_s_t_gird_item_1 sort_code_3" v-for="(model, index) in model.meterList" :key="index"><span>{{model.ct || '--'}}</span></div>
            </div>
            <div style="flex: 2">
                <div class="g_r_s_t_gird_item_1 sort_code_4" v-for="(model, index) in model.meterList" :key="index">
                    <div class="g_r_s_t_gird_item_2" v-for="(model, index) in model.list" :key="index">
                        {{model.meterType == 0 ? '' : model.name + '：'}}
                        <span>{{tenantCtrl.numberFormat(model.value, tenantCtrl.fixType_meter, true)}}</span>
                        <span v-show="model.value">（{{model.energyUnit}}）</span>
                    </div>
                </div>
            </div>
            <div>
                <div class="g_r_s_t_gird_item_1 sort_code_5" v-for="(model, index) in model.meterList" :key="index">
                    <div class="g_r_s_t_gird_item_2" v-for="(model, index) in model.list" :key="index"><span>{{model.isCt == 1 ? '是' : (model.isCt == 0 ? '否' : '--')}}</span></div>
                </div>
            </div>
        </li>
    </ul>
</script>