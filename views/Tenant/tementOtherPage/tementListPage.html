<!--租户列表-->

<div class="t_p_box" style="height: 100%" v-show="currentPage=='tementListPage'">
    <!--头部标题栏  -->
    <div class="main_title clearFloat">
        <!--左侧状态栏  -->
        <div class="main_title_combobox">
            <pcombobox-normal sel="tenantCtrl.t_status_filter" bind="true" id="'t_status_cbx'">
                <header placeholder="'选择租户状态'" prefix="状态：" click="tenantCtrl.tenantOperationHide"></header>
                <item datasource="tenantStatusArr" text="name"></item>
            </pcombobox-normal>
        </div>
        <!--左侧选择栏  -->
        <div class="main_title_combobox">
            <pcombobox-normal sel="tenantCtrl.t_energytype_filter" bind="true" id="'t_energy_type'">
                <header placeholder="'选择能源类型'" prefix="能源类型：" click="tenantCtrl.tenantOperationHide"></header>
                <item datasource="energyTypePayTypeArr" text="name"></item>
            </pcombobox-normal>
        </div>
        <!--右侧添加按钮  -->
        <div class="main_title_button" onclick="ldp.addTement()" v-if="operationPermissions.AddTenant">
            <pbutton-blue text="添加租户"></pbutton-blue>
        </div>
        <div class="main_title_button main_title_button_D" onclick="ldp.allCode()">
            <pbutton-white isborder="true" text="租户全码下载"></pbutton-white>
        </div>
        <!--右侧搜索栏  -->
        <div class="main_title_right">
            <div class="searchText" >
                <psearch-promptly id="searchText" change="tenantCtrl.searchTenantEvent"
                    focus="tenantCtrl.searchTenantEvent" placeholder="输入租户名称或编号"></psearch-promptly>
            </div>
            <div class="searchHint">
                <ul v-show="searchResultArr.length>0">
                    <li v-for="building in searchResultArr">
                        <p>{{building.buildingName}}</p>
                        <div v-for="tenant in building.tenantList"
                            @click="tenantCtrl.searchSelect(tenant,building.buildingId)">
                            {{tenant.tenantName}}（{{tenant.tenantId}}）</div>
                    </li>
                </ul>
                <ul v-show="searchResultArr.length==0">
                    <li>
                        <p>未搜索到租户</p>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!--中央内容部分  表格-->
    <div class="main_grid">
        <pgrid-multifunction style="width: 100%; max-height: 100%;" id="'grid1'">
            <panel datasource="tenantArr" sel="tenantCtrl.tenantPageChange" checkbox="true" templateid="mainGridTemp"
                operation="true" change="tenantCtrl.tenantCheck" lineclick="tenantCtrl.goTenantDetail"
                sortevent="tenantCtrl.tenantArrColumnSort"></panel>
            <!--表格内容  -->
            <header>
                <column name="租户编号" sort="true"></column>
                <column name="租户名称"></column>
                <column name="(selTenantStatus.id != 2?'':'原')+'房间编号'" bind="true"></column>
                <column name="所属建筑"></column>
                <column name="'面积（m²）'"
                    visible="(selTenantStatus.id == -1 || selTenantStatus.id == 0  || payType == 'all')" bind="true">
                </column>
                <column name="'联系人'" bind="true"></column>
                <column name="'电话'" bind="true"></column>
                <column name="'激活时间'" visible="selTenantStatus.id == 2" bind="true"></column>
                <column name="'退租时间'" visible="selTenantStatus.id == 2" bind="true"></column>
                <column name="'未缴费账单数'" visible="(selTenantStatus.id == 1 && payType == '1' && selFeeType.id == 0)"
                    bind="true"></column>
                <column name="'上次结算时间'" sort="true" visible="(selTenantStatus.id == 1 && payType == '1')" bind="true">
                </column>
                <column name="'结算耗'+energyTypeName+'量('+(tenantArr_unit||'--')+')'"
                    visible="(selTenantStatus.id == 1 && payType == '1' && selFeeType.id == 0)" bind="true"></column>
                <column name="'应缴金额(元)'" visible="(selTenantStatus.id == 1 && payType == '1' && selFeeType.id == 0)"
                    bind="true"></column>
                <column name="'未结算耗'+energyTypeName+'量('+(tenantArr_unit||'--')+')'"
                    visible="(selTenantStatus.id == 1 && payType == '1' && selFeeType.id != 0)" bind="true"></column>
                <column name="'未结算金额(元)'" visible="(selTenantStatus.id == 1 && payType == '1' && selFeeType.id != 0)"
                    bind="true"></column>
                <column name="'当月耗'+energyTypeName+'量('+(tenantArr_unit||'--')+')'"
                    visible="(selTenantStatus.id == 1 && payType == '0')" bind="true"></column>
                <column name="'剩余金额（元）'" sort="true"
                    visible="(selTenantStatus.id == 1 && payType == '0' && selShowData.id != 0)" bind="true"></column>
                <column name="'剩余'+energyTypeName+'量('+(tenantArr_unit||'--')+')'" sort="true"
                    visible="(selTenantStatus.id == 1 && payType == '0' && selShowData.id != 1)" bind="true"></column>
                <column name="'剩余天数(天)'" sort="true" visible="(selTenantStatus.id == 1 && payType == '0')" bind="true">
                </column>
            </header>

            <!--表格上方按钮  -->
            <button>
                <div id="absoluteLeft">
                    <pcombobox-normal sel="tenantCtrl.t_building_filter" bind="true" id="'t_building_cbx'">
                        <header placeholder="'选择建筑'" prefix="建筑：" click="tenantCtrl.tenantOperationHide"></header>
                        <item datasource="buildingArr" text="name"></item>
                    </pcombobox-normal>

                    <pcombobox-normal sel="tenantCtrl.t_feetype_filter" bind="true" id="'t_feetype_cbx'"
                        v-show="tenantFilterParam.length>0&&payType=='0'">
                        <header placeholder="'选择扣费类型'" prefix="扣费类型：" click="tenantCtrl.tenantOperationHide"></header>
                        <item datasource="tenantFilterParam" text="name"></item>
                    </pcombobox-normal>
                    <pcombobox-normal sel="tenantCtrl.t_feetype_filter" bind="true" id="'t_feetype_cbx_hou'"
                        v-show="tenantFilterParam.length>0&&payType=='1'">
                        <header placeholder="'请选择'" prefix="" click="tenantCtrl.tenantOperationHide"></header>
                        <item datasource="tenantFilterParam" text="name"></item>
                    </pcombobox-normal>

                    <pcombobox-normal sel="tenantCtrl.t_showdata_filter" bind="true" id="'t_showdata_cbx'"
                        v-show="selFeeType.childArr.length>0">
                        <header placeholder="'选择显示项'" click="tenantCtrl.tenantOperationHide"></header>
                        <item datasource="selFeeType.childArr" text="name"></item>
                    </pcombobox-normal>
                    <!--左侧按钮  -->
                    <div id="Pbutton-white1" onclick="tenantCtrl.limit(0)"
                        :style="{display:selTenantStatus.id==1&&(payType=='0'||(payType=='1'&&selFeeType.id!=-1))?'block':'none'}"
                        :class="{'btnVisited':selLimitType==0}">不限</div>
                    <div id="Pbutton-white2" onclick="tenantCtrl.limit(1)"
                        :style="{display:selTenantStatus.id==1&&payType=='0'?'block':'none'}"
                        :class="{'btnVisited':selLimitType==1}">不足</div>
                    <div id="Pbutton-white3" onclick="tenantCtrl.limit(1)"
                        :style="{display:selTenantStatus.id==1&&payType=='1'&&selFeeType.id==0?'block':'none'}"
                        :class="{'btnVisited':selLimitType==1}">多张账单欠费</div>
                    <div id="Pbutton-white4" onclick="tenantCtrl.limit(2)"
                        :style="{display:selTenantStatus.id==1&&payType=='1'&&selFeeType.id==0?'block':'none'}"
                        :class="{'btnVisited':selLimitType==2}">一张账单欠费</div>
                    <div id="Pbutton-white5" onclick="tenantCtrl.limit(3)"
                        :style="{display:selTenantStatus.id==1&&payType=='1'&&selFeeType.id==1?'block':'none'}"
                        :class="{'btnVisited':selLimitType==3}">一个月以上未结算</div>
                    <div id="Pbutton-white6" onclick="tenantCtrl.limit(4)"
                        :style="{display:selTenantStatus.id==1&&payType=='1'&&selFeeType.id==1?'block':'none'}"
                        :class="{'btnVisited':selLimitType==4}">三个月以上未结算</div>
                    <div id="Pbutton-white7" onclick="tenantCtrl.limit(5)"
                        :style="{display:selTenantStatus.id==1&&payType=='1'&&selFeeType.id==1?'block':'none'}"
                        :class="{'btnVisited':selLimitType==5}">半年以上未结算</div>
                </div>
                <!--右侧设置报警权限  -->
                <!-- <div > -->
                <!-- <div onclick="staticEvent.abnormalBillShow()" v-show="operationPermissions.ErrorOrderProcess">
                    <pbutton-blue icon="G" isborder="false" text="异常账单列表"></pbutton-blue>
                </div>
                <div onclick="staticEvent.Remotetopupshow()" v-show="operationPermissions.WXPrePay">
                    <pbutton-blue icon="U" isborder="false" text="微信充值设置"></pbutton-blue>
                </div>
                <div onclick="staticEvent.policeshow()" v-show="operationPermissions.GobalAlarmSet">
                    <pbutton-blue icon="u" isborder="false" text="全局报警设置"></pbutton-blue>
                </div>
                <div onclick="staticEvent.autoMsgshow()" v-show="operationPermissions.MessageParticipateSend">
                    <pbutton-blue icon="j" isborder="false" text="自动发送短信设置"></pbutton-blue>
                </div>
                <div onclick="staticEvent.batchupdateElecshow()" v-show="operationPermissions.TimingUpdatePrice" >  
                    <pbutton-blue icon="e" isborder="false" text="电价方案设置"></pbutton-blue>
                </div> -->
                <!-- </div> -->
            </button>
            <!--表格下方 分页 和 按钮  -->
            <page>            
                
                <ppage-simple orientation="up" id="page_simple"></ppage-simple>
                <div class="setPage">
                    <span v-for="(item,index) in ['首页','尾页']" :key="index" @click="tenantCtrl.setPage(index)">{{ item }}</span>
                </div>
                <div class="main_foot_right">
                    <div class="page_con">
                        <p>每页显示</p>
                        <ptext-text placeholder="" id="'page_text'" click="staticEvent.comboboxhide" blur="staticEvent.verifyPageSizeEvent" bind="true">
                            <verify errtip="请输入正整数" verifytype="positiveint"></verify>
                        </ptext-text>
                        <p>条</p>
                        <pbutton-white text="确定" id="page_determine" click="tenantCtrl.setPageSize"></pbutton-white>
                    </div>
                </div>
                <div class="main_floot_left">
                    <pbutton-white text="'能耗费用报表'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&selEnergyTypePayType.id!='_all'"
                        click="tenantCtrl.goEnergyCostReport_bat"></pbutton-white>
                    <pbutton-white text="'表底数记录'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&(payType=='0'||payType=='1')" click="staticEvent.gaugeRecordBtn">
                    </pbutton-white>
                    <pbutton-white text="'修改价格方案'" bind="true" id="'upt_price'" v-if="operationPermissions.UpdatePrice"
                        disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id!=2&&selEnergyTypePayType.id!='_all'"
                        click="staticEvent.upt_price_bat"></pbutton-white>
                    <pbutton-white text="'欠费账单'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='1'&&selFeeType.id==0"
                        click="tenantCtrl.goNoPayBillPage_bat"></pbutton-white>
                    <pbutton-white text="'缴费'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='1'&&selFeeType.id==0"
                        v-if="operationPermissions.PostPay" click="tenantCtrl.goPayBillPage_bat"></pbutton-white>
                    <pbutton-white text="'发送缴费提醒'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='1'&&selFeeType.id==0"
                        v-if="operationPermissions.SendMessage" click="staticEvent.modal2show"></pbutton-white>
                    <pbutton-white text="'缴费记录'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='1'" onclick="tenantCtrl.goFeeRecordPage_bat(0)">
                    </pbutton-white>
                    <pbutton-white text="'结算'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='1'" v-if="operationPermissions.PostPayBilling"
                        click="tenantCtrl.goSettlePage_bat"></pbutton-white>
                    <pbutton-white text="'充值记录'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='0'&&(selFeeType.id==1||selFeeType.id==2)"
                        click="tenantCtrl.goRecharge_bat"></pbutton-white>
                    <pbutton-white text="'发送充值提醒'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='0'" v-if="operationPermissions.SendMessage"
                        click="staticEvent.modal2show"></pbutton-white>
                    <pbutton-white text="'退费记录'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='0'&&selFeeType.id==2"
                        click="tenantCtrl.goRefundRent_bat"></pbutton-white>
                    <pbutton-white text="'剩余天数报表'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='0'&&selShowData.id==-1"
                        click="tenantCtrl.getRemainingAmountReport"></pbutton-white>
                    <pbutton-white text="'剩余金额报表'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='0'&&selShowData.id==1"
                        click="tenantCtrl.getRemainingAmountReport"></pbutton-white>
                    <pbutton-white text="'剩余量报表'" bind="true" disabled="checkedTenantArr.length==0"
                        v-show="selTenantStatus.id==1&&payType=='0'&&selShowData.id==0"
                        click="tenantCtrl.getRemainingAmountReport"></pbutton-white>
                </div>
            </page>
        </pgrid-multifunction>
    </div>

    <!--修改价格方案  -->
    <pwindow-float id="floatWindow" isshade="true" title="修改价格方案" templateid="list_upt_price">
        <animate maxpx="0" minpx="-830" orientation="right"></animate>
    </pwindow-float>
    <script type="text/html" id="list_upt_price">
        <!--修改价格方案模板  -->
    <div class="float">
        <p class="colorGray">所选能源类型：</p>
        <p>{{(selEnergyTypePayType.name+"").split('-')[0]}}</p>
        <br />
        <p class="colorGray">当前价格方案：</p>
        <div>
            <pcombobox-normal bind="true" id="'floatText'">
                <header placeholder="'选择价格方案'"></header>
                <item datasource="bat_upt_priceArr" text="name"></item>
            </pcombobox-normal>
        </div>
        <p class="textSmall" id="upt_price_bat_tip">所选的租户将被统一修改成新的价格方案，预付费充值量的租户不收影响</p>
        <p class="colorBlue" onclick="tenantCtrl.tomanagePrivcePlan(0)" style="cursor: pointer;">管理价格方案 > </p>
        <pbutton-blue text="保存" click="tenantCtrl.updatePriceBat" id="buttonCenter"></pbutton-blue>
    </div>
    </script>
    <!--中央表格模板  -->
    <script type="text/html" id="mainGridTemp">
        <div v-text='model.tenantId' :title="model.tenantId">-</div>
        <div v-text='model.tenantName' :title="model.tenantName">-</div>
        <div v-text='model.roomIds' :title="model.roomIds">-</div>
        <div v-text='model.buildingName' :title="model.buildingName">-</div>
        <div v-text='model.area' :title="model.area">-</div>
        <div v-text='model.contactName' :title="model.contactName">-</div>
        <div v-text='model.contactMobile' :title="model.contactMobile">-</div>
        <div v-text='(model.activeTime||"--").substring(0,10).replace(/-/g,".")' :title="(model.activeTime+'').substring(0,10).replace(/-/g,'.')">-</div>
        <div v-text='(model.leaveTime||"--").substring(0,10).replace(/-/g,".")' :title="(model.leaveTime+'').substring(0,10).replace(/-/g,'.')">-</div>
        <div v-text='model.orderSize' :title="model.orderSize">-</div>
        <div v-text='(model.lastClearingTime||"--").substring(0,10).replace(/-/g,".")' :title="(model.lastClearingTime+'').substring(0,10).replace(/-/g,'.')">-</div>
        <div v-text='model.billingEnergy' :title="model.billingEnergy">-</div>
        <div v-text='model.billingMoney' :title="model.billingMoney">-</div>
        <div v-text='model.noBillingEnergy' :title="model.noBillingEnergy">-</div>
        <div v-text='model.noBillingMoney' :title="model.noBillingMoney">-</div>
        <div v-text='model.monthEnergy' :title="model.monthEnergy">-</div>
        <div v-text='model.remainMoney' :title="model.remainMoney">-</div>
        <div v-text='model.remainEnergy' :title="model.remainEnergy">-</div>
        <div v-text='model.remainDays==null?"--":model.remainDays' :title="model.remainDays" :class="{'colorff7b7b':model.isAlarm}">-</div>
        <div>
            <!-- <div v-show="selTenantStatus.id!=2">
                <div class="operationCon"> -->
                    <!-- <i class="operationButton" :data-idx="model.tagId" @click="tenant_operation(event,model)">n</i>
                    <ul> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==1" @click="tenant_operation_detail(model,event,'down_energy_report')">能耗费用报告</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==1" @click="tenant_operation_detail(model,event,'gauge_record_single')">表底数记录</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus!=2&&selEnergyTypePayType.id!='_all'" @click="tenant_operation_detail(model,event,'upt_price')"
                            v-if="operationPermissions.UpdatePrice">修改价格方案</li> -->
                        <!-- <li id="upt_price" v-show="model.tenantStatus==1&&payType=='1'&&selFeeType.id!=1&&model.orderSize>0" @click="tenant_operation_detail(model,event,'down_no_bill')">欠费账单</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==1&&payType=='1'&&selFeeType.id!=1&&model.orderSize>0" @click="tenant_operation_detail(model,event,'fee')"
                            v-if="operationPermissions.PostPay">缴费</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==1&&payType=='1'&&selFeeType.id!=1&&model.orderSize>0" @click="tenant_operation_detail(model,event,'send_fee_msg')"
                            v-if="operationPermissions.SendMessage">发送缴费提醒</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==1&&payType=='1'" @click="tenant_operation_detail(model,event,'get_fee_records')">缴费记录</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==1&&payType=='1'" @click="tenant_operation_detail(model,event,'account')" v-if="operationPermissions.PostPayBilling">结算</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==1&&payType=='0'&&(model.prePayType==1||model.prePayType==2)" @click="tenant_operation_detail(model,event,'get_recharge_record')">充值记录</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==1&&payType=='0'&&model.prePayType==2" @click="tenant_operation_detail(model,event,'get_return_premium_record')">退费记录</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==1&&payType=='0'" @click="tenant_operation_detail(model,event,'send_recharge_msg')" v-if="operationPermissions.SendMessage">发送充值提醒</li> -->
                        <!-- <li id="opt_btns" v-show="model.tenantStatus==0" @click="tenant_operation_detail(model,event,'active')" v-if="operationPermissions.ActiveTenant">激活</li>
                        <li id="opt_btns" v-if="false" @click="tenant_operation_detail(model,event,'recharge')">充值</li>
                        <li id="opt_btns" style="color: #ff7b7b;" v-show="model.tenantStatus==0" @click="tenant_operation_detail(model,event,'delete')">删除</li>
                        <li id="opt_btns" style="color: #ff7b7b;" v-show="model.tenantStatus==1" @click="tenant_operation_detail(model,event,'leave')" v-if="operationPermissions.LeaveTenant">退租</li> -->
                    <!-- </ul> -->
                <!-- </div>
            </div> -->
        </div>
    </script>
    <!-- 激活租户按钮弹出框 -->
    <div class="start_lessee_from_list_btn_box">
        <pwindow-modal id="start_lessee_from_list_btn" title="激活" templateid="start_lessee_from_list_tpl">
        </pwindow-modal>
    </div>
    <!-- 发送缴费提醒弹出框 -->
    <pwindow-modal id="send_massage_from_list_btn" title="发送缴费提醒" templateid="send_massage_from_list_tpl">
    </pwindow-modal>
    <!-- 点击激活按钮弹窗模板 -->
    <script type="text/html" id="start_lessee_from_list_tpl">
        <div class="start_lessee_confirm">
            <div class="start_lessee_text">
                <p>
                    激活时间：
                    <span>（该租户将于当日00:00被激活）</span>
                </p>
                <div class="choose_date_controller">
                    <ptime-form id="start_lessee_from_list_time" sel="staticEvent.proofStartLesseeTimeFromList">
                        <panel timetype="yMd" startyear="2017"></panel>
                    </ptime-form>
                </div>
                <div class="t_d_choose_date_error_msg">
                    <p class="t_d_choose_date_error_msg_lt">
                        <i></i>
                        选择的时间必须晚于今天
                    </p>
                </div>
            </div>
            <div class="start_lessee_btns clearfix">
                <div class="go_start">
                    <pbutton-blue text="确定激活" click="staticEvent.startLesseeYes" data-flag="0"></pbutton-blue>
                </div>
                <div class="cancel_start" onclick="staticEvent.startLesseeFromListHide()">
                    <pbutton-white text="取消"></pbutton-white>
                </div>
            </div>
        </div>
    </script>
    <!-- 点击发送缴费信息提醒弹窗模板 -->
    <script type="text/html" id="send_massage_from_list_tpl">
        <div class="send_message_confirm">
            <p>
                <em id="pay_type_name">缴费</em>提醒短信将发送给：
            </p>
            <p>{{selTenant.tenantName}}的联系人{{selTenant.contactName}}（{{selTenant.contactMobile}}）</p>
            <div class="clearfix">
                <div class="go_send">
                    <pbutton-blue text="确定发送" click="staticEvent.sendMassageFromListYes"></pbutton-blue>
                </div>
                <div class="cancel_send">
                    <pbutton-white text="取消" click="staticEvent.sendMassageFromListHide"></pbutton-white>
                </div>
            </div>
        </div>
    </script>
    <!-- 删除租户按钮弹出框 -->
    <pwindow-confirm id="delete_lessee_from_list_btn">
        <button>
            <pbutton-backred text="确定" click="tenantCtrl.confirmDeleteTenant"></pbutton-backred>
            <pbutton-white text="取消" click="staticEvent.deleteLesseeFromListBtnHide"></pbutton-white>
        </button>
    </pwindow-confirm>
</div>