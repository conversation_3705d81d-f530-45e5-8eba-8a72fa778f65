<div class="p_r_s_m_box" v-show="currentPage == 'prepaidRecordsSofterMeter'">
    <!-- 操作栏 -->
    <div class="p_r_s_m_header clearfix">
        <!-- 返回按钮 -->
        <div class="p_r_s_m_go_back">
            <pbutton-white text="返回" click="ldp.backToTemenListPage"></pbutton-white>
        </div>

        <!-- 标题 -->
        <h1>充值记录-{{prepaidRecordsSofterMeterData.energyTypeName}}-软充表扣</h1>

    </div>

    <!-- 充值记录表格 -->
    <div class="p_r_s_m_gird_box">
        <div class="p_r_s_m_gird_operation clearfix">
            <div class="p_r_s_m_gird_operation_left clearfix">
                <!-- 时间选择控件 -->
                <div class="time_controller_box">
                    <ptime-calendar id="recharge_bat_calendar_software_meter" sel="tenantCtrl.getRechargeRecord_bat_changeTime">
                        <panel timetype="dwMy" startyear="2015" align="left"></panel>
                    </ptime-calendar>
                </div>
            </div>

            <div class="p_r_s_m_gird_operation_right">
                <!-- 下载按钮 -->
                <div class="p_r_s_m_download">
                    <pbutton-white text="'下载充值记录'" icon="'D'" click="tenantCtrl.getRechargeRecord_bat(true, 'recharge_bat_calendar_software_meter')" bind="true"></pbutton-white>
                    <pbutton-white text="'打印充值记录'" icon="'S'" click="ldp.printHtml('.p_r_s_m_gird',2)" bind="true"></pbutton-white>
                </div>
            </div>
        </div>

        <div class="p_r_s_m_gird">
            <div class="p_r_s_m_gird_tit">
                <div  style="flex:1.3">租户名称</div>
                <div>租户编号</div>
                <div>房间编号</div>
                <div>仪表地址</div>
                <div>充值时间</div>
                <div>充值单号</div>
                <div>充值金额（元）</div>
                <div>充值量（{{prepaidRecordsSofterMeterData.energyUnit}}）</div>
                <div>操作人</div>
            </div>
            <div class="p_r_s_m_gird_body">
                <pscroll-small templateid="PRSMScrollBox"></pscroll-small>
            </div>
        </div>
        <div ndm="" class="per-grid-nodata">
            <div pc="" _pt="pnotice-nodata" _id="bfbfheiegahhgdbfafh" class="per-prompt-abnormalmess">
                <span class="per-prompt_icon"></span>
                <span class="per-prompt_title">暂无数据</span>
                <span class="per-prompt_subtitle">请检查网络是否通畅</span>
            </div>
        </div>
    </div>
</div>

<script type="text/html" id="PRSMScrollBox">
    <ul class="p_r_s_m_gird_item_box">
        <template v-for="(model, index) in prepaidRecordsSofterMeterData.tenantList" >
            <li class="p_r_s_m_gird_item" >
                <div class="p_r_s_m_gird_item_0"  style="flex:1.3"><span>{{model.tenantName || '--'}}</span></div>
                <div class="p_r_s_m_gird_item_0"><span>{{model.tenantId || '--'}}</span></div>
                <div class="room_code_1">
                    <div class="p_r_s_m_gird_item_1 sort_code_1" v-for="(model, index) in model.roomList" :key="index"><span :title="model.roomCode">{{model.roomCode || '--'}}</span></div>
                </div>
                <div class="room_code_2">
                    <div class="p_r_s_m_gird_item_1 sort_code_2" v-for="(model, index) in model.roomList" :key="index">
                        <div class="p_r_s_m_gird_item_2" v-for="(model, index) in model.orderList" :key="index">{{model.address || '--'}}</div>
                        <span v-if="model.orderList.length==0">--</span>
                    </div>
                </div>
                <div class="room_code_3">
                    <div class="p_r_s_m_gird_item_1 sort_code_3" v-for="(model, index) in model.roomList" :key="index">
                        <div class="p_r_s_m_gird_item_2" v-for="(model, index) in model.orderList" :key="index">
                            {{model.payTime || '--'}}
                        </div>
                        <span v-if="model.orderList.length==0">--</span>
                    </div>
                </div>
                <div class="room_code_4">
                    <div class="p_r_s_m_gird_item_1 sort_code_4" v-for="(model, index) in model.roomList" :key="index">
                        <div class="p_r_s_m_gird_item_2" v-for="(model, index) in model.orderList" :key="index">{{model.orderId || '--'}}</div>
                        <span v-if="model.orderList.length==0">--</span>
                    </div>
                </div>
                <div class="room_code_5">
                    <div class="p_r_s_m_gird_item_1 sort_code_5" v-for="(model, index) in model.roomList" :key="index">
                        <div class="p_r_s_m_gird_item_2" v-for="(model, index) in model.orderList" :key="index">{{tenantCtrl.numberFormat(model.money, tenantCtrl.fixType_money, true)}}</div>
                        <span v-if="model.orderList.length==0">--</span>
                    </div>
                </div>
                <div class="room_code_6">
                    <div class="p_r_s_m_gird_item_1 sort_code_6" v-for="(model, index) in model.roomList" :key="index">
                        <div class="p_r_s_m_gird_item_2" v-for="(model, index) in model.orderList" :key="index">{{tenantCtrl.numberFormat(model.amount, tenantCtrl.fixType_dynamic, true)}}</div>
                        <span v-if="model.orderList.length==0">--</span>
                    </div>
                </div>

                <div class="room_code_7">
                    <div class="p_r_s_m_gird_item_1 sort_code_7" v-for="(model, index) in model.roomList" :key="index">
                        <div class="p_r_s_m_gird_item_2" v-for="(model, index) in model.orderList" :key="index">{{model.userName || '--'}}</div>
                        <span v-if="model.orderList.length==0">--</span>
                    </div>
                </div>
            </li>
            <li class="p_r_s_m_gird_item">
                <div class="p_r_s_m_gird_item_0"  style="flex:1.3"><span>{{model.tenantName || '--'}}合计充值</span></div>
                <div class="p_r_s_m_gird_item_0"></div>
                <div class="room_code_1">
                    <div class="p_r_s_m_gird_item_1 sort_code_1"></div>
                </div>
                <div class="room_code_2">
                    <div class="p_r_s_m_gird_item_1 sort_code_2"></div>
                </div>
                <div class="room_code_3"><div class="p_r_s_m_gird_item_1 sort_code_3"></div></div>
                <div class="room_code_4"><div class="p_r_s_m_gird_item_1 sort_code_4"></div></div>
                <div class="room_code_5">
                    <div class="p_r_s_m_gird_item_1 sort_code_5">
                       {{tenantCtrl.numberFormat(model.tenantMoneySum, tenantCtrl.fixType_dynamic, true)}}
                    </div>
                </div>
                <div class="room_code_6">
                    <div class="p_r_s_m_gird_item_1 sort_code_6">
                        {{tenantCtrl.numberFormat(model.tenantAmountSum, tenantCtrl.fixType_dynamic, true)}}
                    </div>
                </div>
                <div class="room_code_7"><div class="p_r_s_m_gird_item_1 sort_code_7"></div></div>
            </li>
        </template>
        <li class="p_r_s_m_gird_item">
            <div class="p_r_s_m_gird_item_0" style="flex:1.3">
                <span>所选租户合计充值</span>
            </div>
            <div class="p_r_s_m_gird_item_0"></div>
            <div class="room_code_1"><div class="p_r_s_m_gird_item_1 sort_code_1"></div></div>
            <div class="room_code_2"><div class="p_r_s_m_gird_item_1 sort_code_2"></div></div>
            <div class="room_code_3"><div class="p_r_s_m_gird_item_1 sort_code_3"></div></div>
            <div class="room_code_4"><div class="p_r_s_m_gird_item_1 sort_code_4"></div></div>
            <div class="room_code_5">
                <div class="p_r_s_m_gird_item_1 sort_code_5">
                    {{tenantCtrl.numberFormat(prepaidRecordsSofterMeterData.moneySum, tenantCtrl.fixType_dynamic, true)}}
                </div>
            </div>
            <div class="room_code_6">
                <div class="p_r_s_m_gird_item_1 sort_code_6">
                    {{tenantCtrl.numberFormat(prepaidRecordsSofterMeterData.amountSum, tenantCtrl.fixType_dynamic, true)}}
                </div>
            </div>
            <div class="room_code_7"><div class="p_r_s_m_gird_item_1 sort_code_7"></div></div>
        </li>
    </ul>
</script>