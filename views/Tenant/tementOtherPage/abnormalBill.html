<div id="abnormalBill" v-show="currentPage === 'abnormalBill'">
    <div class="head">
        <div class="back">
            <pbutton-white text="返回" @click="tenantMngModel.instance().currentPage = 'tementListPage'"></pbutton-white>
        </div>
        <div class="time">
            <ptime-calendar id="abnormalBillTime" orientation="down">
                <panel timetype="d" iscommontime="false" align="left" double="true" startyear="2003"></panel>
            </ptime-calendar>
        </div>
        <div>能源类型：</div>
        <div class="energyType">
            <pcombobox-normal id="'abnormalBillEnergyCombo'" bind="true">
                <header></header>
                <item datasource="energyTypes" text="name" id="id"></item>
            </pcombobox-normal>
        </div>
        <div>账单类型：</div>
        <div class="billType">
            <pcombobox-normal id="'abnormalBillTypeCombo'" bind="true">
                <header></header>
                <item datasource="billTypes" text="name" id="id"></item>
            </pcombobox-normal>
        </div>
        <div class="btn">
            <pbutton-blue text="查询" click="staticEvent.getAbnormalBillList(false)"></pbutton-blue>
        </div>
    </div>
    <div class="title">
        <span :style="{flex: '2'}">建筑名称</span>
        <span :style="{flex: '2'}">租户编号</span>
        <span :style="{flex: '2'}">租户名称</span>
        <span :style="{flex: '3'}">账单编号</span>
        <span :style="{flex: '4'}">账单生成时间</span>
        <span :style="{flex: '2'}">付费方式</span>
        <span :style="{flex: '2'}">仪表编号</span>
        <span :style="{flex: '2'}">充值量</span>
        <span :style="{flex: '2'}">充值系统</span>
        <span :style="{flex: '4'}">操作时间</span>
        <span :style="{flex: '2'}">操作人</span>
        <span :style="{flex: '3'}">操作</span>
    </div>
    <ul class="grid">
        <li v-for="item in abnormalBillList">
            <span :style="{flex: '2'}" v-text="item.buildingName" :title="item.buildingName">建筑名称</span>
            <span :style="{flex: '2'}" v-text="item.tenantId" :title="item.tenantId">租户编号</span>
            <span :style="{flex: '2'}" v-text="item.tenantName" :title="item.tenantName">租户名称</span>
            <span :style="{flex: '3'}" v-text="item.orderId" :title="item.orderId">账单id</span>
            <span :style="{flex: '4'}" v-text="item.orderTime" :title="item.orderTime">账单生成时间</span>
            <span :style="{flex: '2'}" v-text="item.bodyTypeName" :title="item.bodyTypeName">付费方式</span>
            <span :style="{flex: '2'}" v-text="item.bodyCode" :title="item.bodyCode">仪表编号</span>
            <span :style="{flex: '2'}" v-text="item.moneyAmount" :title="item.moneyAmount">充值量</span>
            <span :style="{flex: '2'}" v-text="item.systemName" :title="item.systemName">充值系统</span>
            <span :style="{flex: '4'}" v-text="item.operateTime" :title="item.operateTime">操作时间</span>
            <span :style="{flex: '2'}" v-text="item.operateUserName" :title="item.operateUserName">操作人</span>
            <span :style="{flex: '3'}" >
                <span v-show="item.status == 1">再次充值</span>
                <span v-show="item.status == 2">关闭</span>
                <span v-show="item.status == 0"><pbutton-blue isborder="false" text="再次充值" @click="staticEvent.abnormalBillOperate(item,0)"></pbutton-blue></span>
                <span v-show="item.status == 0"><pbutton-borderred isborder="false" text="关闭" @click="staticEvent.abnormalBillOperate(item,1)"></pbutton-borderred></span>
            </span>
        </li>
    </ul>
    <div class="page">
        <ppage-full bind="true" id="'abnormalBillPage'" sel="staticEvent.getAbnormalBillList(true,event)"></ppage-full>
    </div>

    <pwindow-modal id="abnormalBillConfirmWindow" title="再次充值" templateid="abnormalBillChargeAgainTemplagte"></pwindow-modal>

</div>
<!-- 重新充值弹窗 -->
<script type="text/html" id="abnormalBillChargeAgainTemplagte">
    <div style="width:280px;" class="abnormalBillChargeAgainWrap">
        <div class="info">
            <div class="title">租户名称</div>
            <div v-text="abnormalBillInfo.tenantName" :title="abnormalBillInfo.tenantName"></div>
        </div>
        <div class="info">
            <div class="title">租户编号</div>
            <div v-text="abnormalBillInfo.tenantId" :title="abnormalBillInfo.tenantId"></div>
        </div>
        <div class="info">
            <div class="title">账单编号</div>
            <div v-text="abnormalBillInfo.orderId" :title="abnormalBillInfo.orderId"></div>
        </div>
        <div class="info">
            <div class="title">充值量</div>
            <div v-text="abnormalBillInfo.moneyAmount" :title="abnormalBillInfo.moneyAmount"></div>
        </div>
        <div class="info">
            <div class="title">确认密码</div>
            <div>
                <ptext-text id="abnormalBillPassword">
                    <verify errtip="不可为空" verifytype="space"></verify>
                </ptext-text>
            </div>
        </div>
        <div class="btn">
            <pbutton-blue text="确定" click="staticEvent.abnormalBillSubmit"></pbutton-blue>
            <pbutton-white text="取消" click="staticEvent.closeAbnormalBillWindow"></pbutton-white>
        </div>
    </div>
</script>