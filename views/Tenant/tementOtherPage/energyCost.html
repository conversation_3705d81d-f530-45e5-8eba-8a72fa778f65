<!--能耗费用报表电-->
<div class="energyCost" style="height: 100%" v-if="currentPage == 'energyCost'">
    <!--头部  -->
    <div class="energyCost_title">
        <div>
            <pbutton-white text="返回" id="energyCost_title_button" click="ldp.backToTemenListPage"></pbutton-white>
        </div>
        <div id="energyCost_title_con">
            能耗费用报表-{{energyTypeName}}
        </div>
        <div>
            <pbutton-white text="下载能耗费用报表" icon="D" id="energyCost_title_button2" @click="tenantCtrl.getTenantEnergyMoneyReport_bat(true)"></pbutton-white>
        </div>
    </div>
    <!--时间栏  -->
    <div class="energyCost_time">
        <ptime-calendar id="energy_cost_calendar" sel="tenantCtrl.getTenantEnergyMoneyReport_bat_changeTime">
            <panel timetype="My" startyear="2015" commontime="['M','pM','y','py']"></panel>
        </ptime-calendar>
    </div>
    <!--表格  -->
    <div class="energyCost_wrap">
        <div class="energyCost_table">
            <!--表头部分  -->
            <div class="energyCost_table_top">
                <ul>
                    <li class="inline">
                        <div class="tenentNo slh">
                            <div>租户编号</div>
                        </div>
                        <div class="tenentName slh">
                            <div>租户名称</div>
                        </div>
                        <div class="inline energyCost_table_top_right">
                            <div v-for="title in energyCostTimeList">
                                <div class="_timer">{{(title.time+"").substring(0,7).replace(/-/g,'.')}}</div>
                                <div class="inline energyCost_table_top_right_bottom">
                                    <div :title="'能耗('+bat_energyUnit+')'">能耗({{bat_energyUnit}})</div>
                                    <div title="费用(元)">费用(元)</div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="energyCost_table_con">
                <!--表格合计部分  -->
                <div class="energyCost_table_center">
                    <ul>
                        <li class="inline">
                            <div class="total">合计</div>
                            <div class="inline energyCost_table_center_right">
                                <template v-for="item in energyCostTimeList">
                                    <div class="slh"><span>{{tenantCtrl.numberFormat(item.energy,tenantCtrl.fixType_dynamic,true)}}</span>
                                    </div>
                                    <div class="slh"><span>{{tenantCtrl.numberFormat(item.money,tenantCtrl.fixType_money,true)}}</span>
                                    </div>
                                </template>
                            </div>
                        </li>
                    </ul>
                </div>
                <!--表格内容部分  -->
                <div class="energyCost_table_body">
                    <pscroll-small templateid="eneryCostScroll"></pscroll-small>
                </div>
            </div>
        </div>
    </div>

</div>
