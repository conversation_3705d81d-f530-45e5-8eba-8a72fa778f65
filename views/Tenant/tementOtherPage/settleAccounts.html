<div class="s_a_box" v-if="currentPage == 'settleAccounts'">
    <!-- 操作栏 -->
    <div class="s_a_header clearfix">
        <!-- 取消按钮 -->
        <div class="s_a_cancel">
            <pbutton-white text="取消" click="ldp.energyCosttempback"></pbutton-white>
        </div>
        <!-- 标题 -->
        <h1>结算-{{selTenant.tenantName}}</h1>
    </div>
    <!-- 结算页内容     -->
    <div class="s_a_content">
        <div class="s_a_content_mid" id="settle_content">
            <!-- 能耗类型 -->
            <div class="s_a_energy">
                <h2 class="s_a_energy_title">本次结算能耗：</h2>
                <p class="s_a_energy_type">{{selTenant.settle.energyTypeName}}</p>
            </div>
            <!-- 选择日期控件 -->
            <div class="s_a_choose_date">
                <h2 class="s_a_choose_date_title">请选择结算日期</h2>
                <div class="s_a_choose_date_controller">
                    <ptime-form id="settle_accounts_time" sel="tenantCtrl.tenantSettle_single">
                        <panel timetype="yMd" startyear="2014"></panel>
                    </ptime-form>
                </div>
                <div class="s_a_choose_date_error_msg">
                    <p class="s_a_choose_date_error_msg_bf"><i></i>选择的时间必须晚于上次结算时间或激活时间</p>
                    <p class="s_a_choose_date_error_msg_lt"><i></i>选择的时间不得晚于今天</p>
                </div>
            </div>
            <!-- 结算表格数据 -->
            <div class="s_a_gird">
                <pgrid-normal style="width: 100%; height: 100%;" bind="true" align="left">
                    <panel datasource="selTenant.settle.dataArr" lineclick="tenantCtrl.goSettleDetail_single"></panel>
                    <header>
                        <column name="上次结算日期" source="lastClearingTime.substring(0,10).replace(/-/g,'.')"></column>
                        <!-- <column name="'本期结算能耗（{{selTenant.settle.energyUnit?selTenant.settle.energyUnit:"--"}}）'" source="currentBillingEnergy" bind="true"></column> -->
                        <column name="本期结算能耗（{{selTenant.settle.energyUnit||'--'}}）" source="currentBillingEnergy" bind="true"></column>
                        <column name="本期结算金额（元）" source="currentBillingMoney"></column>
                    </header>
                </pgrid-normal>
                <div v-show="selTenant.settle.dataArr.length == 0">
                    <pnotice-nodata text="暂无数据" subtitle="请重新加载"></pnotice-nodata>
                </div>
                <div v-if="meterFaultList.length != 0 || clearingTimeFaultList.length != 0" class="prompt_settleAccounts">
                    <p class="prompt_con">
                        <i class="prompt_icon">i</i>
                        <span v-for="(item, index) in meterFaultList" :key="index">
                            {{item}}
                            <span v-if="index != meterFaultList.length - 1">、</span>
                        </span>
                        <span v-if="meterFaultList.length != 0">仪表暂无数据<span v-if="clearingTimeFaultList.length != 0">；</span></span>
                        <span v-for="(item, index) in clearingTimeFaultList" :key="index">
                            {{item}}
                            <span v-if="index != clearingTimeFaultList.length - 1">、</span>
                        </span>
                        <span v-if="clearingTimeFaultList.length != 0">未到结算时间</span>
                        <span>。</span>
                    </p>
                </div>
            </div>
        </div>

        <!--结算失败中央内容部分  -->
        <div class="settlementErr_content" id="settle_failure">
            <div class="settlementErr_content_top">
                <div></div>
                <span>结算失败</span>
            </div>
            <div class="settlementErr_content_bottom">
                <p>请点击下方按钮重新结算</p>
            </div>
        </div>

        <!--结算成功中央内容部分  -->
        <div class="settlementSucc_content" id="settle_success">
            <div class="settlementSucc_content_top">
                <div></div>
                <span>结算成功</span>
            </div>
            <div class="settlementSucc_content_bottom">
                <pbutton-white text="下载本期账单" icon="D" click="tenantCtrl.downCurrentBill"></pbutton-white>
            </div>
        </div>
    </div>

    <!-- 结算页底部 -->
    <div class="s_a_footer">
        <!-- 结算按钮 -->
        <div class="s_a_settle_btn" id="settle_ok">
            <pbutton-blue id="settle_ok_button" text="确认并结算" click="staticEvent.showPassWordForSettlement"></pbutton-blue>
        </div>
        <div class="s_a_settle_btn" style="display: none;" id="re_settle_ok">
            <pbutton-blue text="重新结算" click="staticEvent.showPassWordForSettlement"></pbutton-blue>
        </div>
        <div class="s_a_settle_btn" style="display: none;" id="settle_back">
            <pbutton-blue text="完成" click="ldp.downPageGoback"></pbutton-blue>
        </div>
    </div>
</div>
