<div class="p_r_box" v-if="currentPage == 'paymentRecords'">
    <!-- 操作栏 -->
    <div class="p_r_header clearfix">
        <!-- 返回按钮 -->
        <div class="p_r_go_back">
            <pbutton-white text="返回" @click="ldp.energyCosttempback"></pbutton-white>
        </div>
        <!-- 标题 -->
        <h1>缴费记录-{{selTenant.tenantName}}-{{tenantCtrl.getEnergyTypeName(selTenant.feeRecordArr_energyTypeId)}}</h1>
        <!-- 下载报表按钮 -->
        <div class="p_r_download">
            <pbutton-white text="下载缴费记录" icon="D" click="tenantCtrl.downFeeRecord"></pbutton-white>
        </div>
    </div>
    <!-- 表格内容 -->
    <div class="p_r_content">
        <!-- 时间选择控件 -->
        <div class="p_r_choose_time">
            <ptime-calendar id="fee_record_calendar" orientation="down" sel="tenantCtrl.feeRecordChangeTime">
                <panel timetype="dwMy" startyear="2015" align="left" ></panel>
            </ptime-calendar>
        </div>
        <!-- 缴费记录表格 -->
        <div class="p_r_gird">
            <pgrid-normal style="width: 100%; max-height: 100%;" bind="true">
                <panel datasource="selTenant.feeRecordArr"></panel>
                <header>
                    <column name="缴费时间" source="payTime"></column>
                    <column name="账单" source="orderTime"></column>
                    <column name="账单号" source="orderId"></column>
                    <column name="缴费金额（元）" source="money"></column>
                    <column name="操作人" source="userName"></column>
                </header>
            </pgrid-normal>
            <div v-show="selTenant.feeRecordArr.length == 0">
                <pnotice-nodata text="暂无数据" subtitle="请重新加载"></pnotice-nodata>
            </div>
        </div>
    </div>
</div>
