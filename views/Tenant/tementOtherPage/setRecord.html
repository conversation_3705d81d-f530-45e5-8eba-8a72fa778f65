<div id="setRecord" v-show="currentPage == 'setRecord'">
    <div class="setRecord_con">
        <div class="top">
            <div class="goBack">
                <pbutton-white text="返回" click="ldp.toTementDetail"></pbutton-white>
            </div>
            <div class="title">
                仪表设置记录
            </div>
        </div>
        <div class="list">
            <div class="top">
                <div class="left">
                    <section>
                        <pcombobox-normal bind="true" id="'sr_type_sel'" sel="tenantCtrl.sr_selType">
                            <header prefix="操作记录：" placeholder="'请选择'"></header>
                            <item datasource="sr_typeArr" text="name"></item>
                        </pcombobox-normal>
                    </section>
                    <section>
                        <pcombobox-normal bind="true" id="'sr_energy_sel'" sel="tenantCtrl.sr_energyType">
                            <header prefix="能源类型：" placeholder="'请选择'"></header>
                            <item datasource="sr_energyTypeArr" text="name"></item>
                        </pcombobox-normal>
                    </section>
                    <section>
                        <ptime-calendar id="setRecordCalendar" orientation="down" sel="tenantCtrl.setRecordChangeTime">
                            <panel timetype="Mdwy" align="left" double="true"></panel>
                        </ptime-calendar>
                    </section>
                </div>
                <div class="right">
                    <pbutton-white icon="D" text="下载仪表设置记录" click="tenantCtrl.downSetRecord"></pbutton-white>
                </div>
            </div>
            <div class="tableBox">
                <div class="table">
                    <section class="title list_title">
                        <div>房间ID</div>
                        <div>仪表ID</div>
                        <div>仪表能源类型</div>
                        <div>操作时间</div>
                        <div>操作人</div>
                        <div style="padding:0 30px;">{{sr_selTypeId==3?"透支金额":(sr_selTypeId==4?"更新后价格":"操作")}}</div>
                    </section>
                    <div class="content">
                        <section v-for="item in sr_dataArr">
                            <div>
                                <span v-text="item.roomCode"></span>
                            </div>
                            <div>
                                <span v-text="item.meterId"></span>
                            </div>
                            <div>
                                <span v-text="item.energyTypeName"></span>
                            </div>
                            <div class="time">
                                <span v-text="item.operateTime"></span>
                            </div>
                            <div>
                                <span v-text="item.userName"></span>
                            </div>
                            <div>
                                <!-- 变更后价格 -->
                                <div v-for="info in item.data" class="tableUpdatePrice" :title="item.data[0].value">{{sr_selTypeId==3?(tenantCtrl.numberFormat(info.value,tenantCtrl.fixType_money,true)+item.unit):(sr_selTypeId==4?(item.data[0].value):(info.value))}}</div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
            <div class="page">
                <ppage-simple orientation="up" id="page_simple_sr" sel="tenantCtrl.changePageSizeForMeterSetRecord"></ppage-simple>
                <section>
                    <!-- <div class="page_con"> -->
                    <p>每页显示</p>
                    <ptext-text placeholder="" id="'sr_page_text'" click="staticEvent.comboboxhide" blur="staticEvent.verifyPageSizeEvent_sr" bind="true">
                        <verify errtip="请输入正整数" verifytype="positiveint"></verify>
                    </ptext-text>
                    <i>条</i>
                    <pbutton-white text="确定" id="sr_page_determine" click="tenantCtrl.setPageSize_sr"></pbutton-white>
                    <!-- </div> -->
                </section>
            </div>
        </div>
    </div>
</div>