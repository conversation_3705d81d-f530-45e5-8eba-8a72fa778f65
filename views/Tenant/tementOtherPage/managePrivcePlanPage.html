<!--管理价格方案-->

<div v-if="currentPage==='managePrivcePlanPage'" class="addTementPage managePrivcePlanPage" >
    <div class="header">
        <div class="newPricePlanBtn">
            <pbutton-blue text="新建价格方案" click="ldp.showPricePlan"></pbutton-blue>
        </div>
        <div class="cancelBtn">
            <pbutton-white text="返回" click="ldp.energyCosttempback"></pbutton-white>
        </div>
        <span class="title">管理价格方案-{{selEnergyType.name}}</span>
    </div>
    <div class="body">
        <div class="managePrivce_grid_wrapper">
            <ul class="managePrivce_grid_title">
                <li class="name">价格方案名称</li>
                <li class="type" v-show="selEnergyType.id=='Dian'">类型</li>
                <li class="plandetail">价格方案详情</li>
            </ul>
            <h2 class="managePrivce_grid_body_wraper">
                <ul class="managePrivce_grid_body">
                    <li v-for="item in selEnergyType.priceArr">
                        <div class="name slh">{{item.name}}</div>
                        <div class="type" v-show="selEnergyType.id=='Dian'">{{item.type==0?'平均电价':'分时电价'}}</div>
                        <div class="plandetail" v-show="item.type==0">
                            <span><em>{{tenantCtrl.numberFormat(item.content.PJ,tenantCtrl.fixType_price,true)}}</em><em>{{item.unit}}</em></span>
                        </div>
                        <div class="plandetail" v-show="item.type==1">
                            <span><em>尖峰：</em><em>{{tenantCtrl.numberFormat(item.content.J,tenantCtrl.fixType_price,true)}}</em><em>{{item.unit}}</em></span>
                            <span><em>高峰：</em><em>{{tenantCtrl.numberFormat(item.content.F,tenantCtrl.fixType_price,true)}}</em><em>{{item.unit}}</em></span>
                            <span><em>平段：</em><em>{{tenantCtrl.numberFormat(item.content.P,tenantCtrl.fixType_price,true)}}</em><em>{{item.unit}}</em></span>
                            <span><em>低谷：</em><em>{{tenantCtrl.numberFormat(item.content.G,tenantCtrl.fixType_price,true)}}</em><em>{{item.unit}}</em></span>
                        </div>
                        <h2 type="edit" @click="ldp.showPricePlan(event,item)" class="managePrivce_grid_edit">
                            <em>e</em>编辑
                        </h2>
                        <h2 @click="ldp.deletePricePlan(event,item)" class="managePrivce_grid_delete">
                            <em>p</em>删除
                        </h2>
                    </li>
                </ul>
            </h2>
        </div>
    </div>
</div>
<!--begin:删除价格方案-->
<pwindow-confirm id="deletePricePlan_noSamePlan">
    <button>
        <pbutton-backred text="确定" click="tenantCtrl.deletePrice"></pbutton-backred>
        <pbutton-white text="取消" click="ldp.hideFloat()"></pbutton-white>
    </button>
</pwindow-confirm>
<!--begin:删除价格方案-->
<pwindow-confirm id="delete_notice">
    <button>
        <pbutton-backred text="确定"></pbutton-backred>
    </button>
</pwindow-confirm>