<!-- 总览查看 -->
<div id="overviewPage" class="overviewPage scroll_big" v-show="currentPage === 'overviewPage'">
        <ul id="floorTable">
            <li v-for="(model,index) in floorTable.floorList">
                <div class="floorNo">
                    <span><em>{{model.floorName}}</em></span>
                </div>
                <div class="piece" v-for="(model,index1) in model.tenantList" @click.stop="goToAlarmDetail(model, index,index1)">
                    <div class="pieceItem" v-if="model" v-bind:class="{alarmStatus:model.isAlarm}" v-bind:style="{'overflow':model.crossFloor>1?'':'hidden'}" v-on:mouseleave="overviewTenementOut($event,model)" v-on:mouseenter="overviewTenementHover($event,model)"  >
                        <span class="tentName" v-if="!model.isCross">{{model.tenantName}}</span>
                        <div class="alarmContent basicsposition" v-if="!model.isCross" v-bind:ih="model.height+'px'">
                            <div class="alarmContentWrap">
                                <h4>
                                    <span class="alarmName">{{model.tenantName}}</span>
                                    <span class="alarmNO">{{model.roomCodes||'--'}}</span>
                                </h4>
                                <div class="alarmDetail">
                                    <div class="hasalarm" v-if="model.isAlarm">
                                        <h4 v-for="model in alarmTement.alarmList">
                                            <span><em>{{model.typeName}}</em>报警：<em v-show="model.isLimit"  class="alarmColor">{{model.alarmValue}}</em><em v-show="model.isLimit" class="alarmColor" >{{model.unit}}</em> </span>
                                            <span class="electricMeter" v-show="model.typeId==='ZHBJ_16'">电表：<em>{{model.meterId}}</em></span>
                                            <ul class="alarm_detail">
                                                <li v-show="model.alarmPositionId">报警位置：<em>{{model.alarmPositionId}}</em></li>
                                                <li v-show="model.alarmTime">开始报警时间：<em>{{model.alarmTime}}</em></li>
                                                <li v-show="model.isLimit">报警门限：<em>{{model.limitValue}}{{model.unit}}</em></li>
                                                <li v-show="model.interruptTime">中断时间：<em>{{model.interruptTime}}</em></li>
                                                <li v-show="model.historyMaxValue">历史能耗最大值：<em>{{model.historyMaxValue}}{{model.cumulanUnit}}</em></li>
                                                <li v-show="model.historyAvgValue">历史能耗平均值：<em>{{model.historyAvgValue}}{{model.cumulanUnit}}</em></li>
                                                <li v-show="model.powerValue">功率值：<em>{{model.powerValue}}kW</em></li>
                                                <li v-show="model.baseLoad">基础负荷：<em>{{model.baseLoad}}kW</em></li>
                                            </ul>
                                        </h4>
                                    </div>
                                    <div class="noalarm" v-show="!model.isAlarm">
                                        暂无报警
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="across" v-if="model.isCross" v-bind:class="{alarmStatus:model.isAlarm}"
                             v-bind:style="{height:model.height+'px'}">
                            <span class="tentName" v-bind:style="{'line-height':model.height+'px'}">{{model.tenantName}}</span>
                            <div class="alarmContent basicsposition" v-if="model.isCross" v-bind:ih="model.height+'px'">
                                <div class="alarmContentWrap">
                                    <h4>
                                        <span class="alarmName">{{model.tenantName}}</span>
                                        <span class="alarmNO">{{model.roomCodes||'--'}}</span>
                                    </h4>
                                    <div class="alarmDetail">
                                        <div class="hasalarm" v-if="model.isAlarm">
                                            <h4 v-for="model in alarmTement.alarmList">
                                                <span><em>{{model.typeName}}</em>报警：<em class="alarmColor">{{model.alarmValue}}</em><em class="alarmColor">{{model.unit}}</em> </span>
                                                <span class="electricMeter" v-show="model.typeId==='ZHBJ_16'">电表：<em>{{model.meterId}}</em></span>
                                                <ul class="alarm_detail">
                                                    <li v-show="model.alarmPositionId">报警位置：<em>{{model.alarmPositionId}}</em></li>
                                                    <li v-show="model.alarmTime">开始报警时间：<em>{{model.alarmTime}}</em></li>
                                                    <li v-show="model.isLimit">报警门限：<em>{{model.limitValue}}{{model.unit}}</em></li>
                                                    <li v-show="model.interruptTime">中断时间：<em>{{model.interruptTime}}</em></li>
                                                    <li v-show="model.historyMaxValue">历史能耗最大值：<em>{{model.historyMaxValue}}{{model.cumulanUnit}}</em></li>
                                                    <li v-show="model.historyAvgValue">历史能耗平均值：<em>{{model.historyAvgValue}}{{model.cumulanUnit}}</em></li>
                                                    <li v-show="model.powerValue">功率值：<em>{{model.powerValue}}kW</em></li>
                                                    <li v-show="model.baseLoad">基础负荷：<em>{{model.baseLoad}}kW</em></li>
                                                </ul>
                                            </h4>
                                        </div>
                                        <div class="noalarm" v-show="!model.isAlarm">
                                            暂无报警
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
        <!-- 无数据 -->
        <div class="monitorNodata" v-show="!floorTable|| floorTable.row==0||floorTable.cell==0">
            <pnotice-nodata text="暂无数据"></pnotice-nodata>
        </div>
</div>
