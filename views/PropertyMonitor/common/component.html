<script type="x/template" id="template-downCombox">
    <div class="downCombox">
        <div class="combobox-menu">
            <div class="combobox-title" @click.stop="handle">
                <i>D</i>
            </div>
                <div class="downcombobox-con" transition="expand">
                    <ul>
                        <li @click.stop="downSomething($event,index)" v-for="(item,index) in downArr">
                            <b>下载{{item}}</b>
                        </li>
                    </ul>
                </div>
        </div>
    </div>
</script>

<script type="text/html" id="commonBodyScrollTemp">
    <!--图表表格滚动条模板  -->
    <div class="common__column--body">
        <div class="common__column">
            <!--时间-->
            <ul>
                <li v-for="(model,index) in reportTimeArr" v-bind:key="index">
                    <div v-text="model"></div>
                </li>
            </ul>
            <!--数据-->
            <ul v-for="model in tenementReportArr">
                <li v-for="model in model.dataArr">
                    <div>{{model.y==null?'--':model.y}}</div>
                </li>
            </ul>
        </div>
    </div>
</script>

<script type="text/html" id="popTreeScrollTemp">
    <!--租户滚动条模板  -->
    <div id="dataCompara_tementTree" v-show="world.length>0">
         <component-recursion-tree :recursion-arr="world"></component-recursion-tree>
    </div>
</script>

<script type="text/html" id="popTreeScrollTemp1">
    <!--租户滚动条模板  -->
    <div id="dataCompara_tementTree1" v-show="roomwatch.length>0">
         <component-recursion-tree2 :isfold="isfold" :recursion-arr="roomwatch"></component-recursion-tree2>
    </div>
</script>


<script type="x/template" id="template-recursion-tree">
    <!--递归树组件
        [{name:'a',
        childrenArr:[{name:'a-1',childrenArr:[],isFirst:true}],
        isFirst:true}]
    props属性: ["isFirst","recursionArr"]
 -->
    <ul class="treeCont">
        <li v-for="model in recursionArr" :class="{'isFirst':!model.level}">
            <div class="treeTitle" :class="{'isFloor':model.level===0}"  :style="{paddingLeft:model.level*20+'px'}" @click.stop="viewToggleTit"  v-show="model.level===0||model.isShow">
                <label :title="model.name"  v-if="model.level!==0" :class="choose" :for="model.id" @click.stop="choose(model)"></label>
                <span class="arrow" @click.stop="viewToggle" v-show="model.tenantList">r</span>
                <span class="cont slh" >{{model.name}}</span>
                <b v-show="model.isChecked">Z</b>
            </div>
            <component-recursion-tree  :recursion-arr="model.tenantList" style="display: none;"></component-recursion-tree>
        </li>
    </ul>
</script>

<script type="x/template" id="template-chooseType">
    <div class="chooseType">
        <div class="combobox-level" @click.stop="toggle"><span>v</span><em v-text="'类型：'+selecteddatacpmparatype.name"></em></div>
        <div class="combobox-level-menu">
            <ul>
                <li v-for="model in datachoosetypes">
                    <div class="combobox-content" v-text="model.name">电</div>
                    <ul class="combobox-level2">
                        <!-- <li v-for="model in model.subTypeList" v-show="_isshow(model)" -->
                        <li v-for="model in model.subTypeList"
                            @click="_selType(model, selecteddatacpmparatype)"
                            :class="selecteddatacpmparatype.id === model.id ? 'pitch': ''"><span
                                v-text="model.name">耗电量</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</script>

<script type="x/template" id="template-recursion-tree2">
    <!--递归树组件
        [{name:'a',
        childrenArr:[{name:'a-1',childrenArr:[],isFirst:true}],
        isFirst:true}]
    props属性: ["isFirst","recursionArr"]
 -->
    <ul class="treeCont" style="display: block;">
        <li v-for="model in recursionArr" :class="{'isFirst':!model.level}">
            <div v-if="model.isShow" class="treeTitle" :class="{'isFloor':model.level===0}"  :style="{paddingLeft:model.level!=2?model.level*20+'px':'60px'}" @click.stop="viewToggleTit">
                <label :title="model.name"  v-if="model.level==2" :class="choose" :for="model.id" @click.stop="choose(model)"></label>
                <span class="arrow" v-if="model.dataList" @click.stop="viewToggle"  v-text="isfold?'b':'r'"></span>
                <span class="cont slh" :style="{width:'calc(100% - '+(model.level+1)*20+'px)'}">{{model.name}}</span>
                <b v-show="model.isChecked">Z</b>
            </div>
            <component-recursion-tree2  :recursion-arr="model.dataList" v-show="isfold" :isfold="isfold"></component-recursion-tree2>
        </li>
    </ul>
</script>