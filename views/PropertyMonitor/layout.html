﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>物业监控</title>
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <script type="text/javascript" src="../../scripts/lib/jquery-2.0.0.min.js"></script>
    <script type="text/javascript" src="../../scripts/lib/vue-2.2.0.js"></script>
    <script type="text/javascript" src="../../scripts/lib/highcharts-4.2.5.js"></script>
    <script type="text/javascript" src="../../scripts/tool/ptool.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pconst.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pajax.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pautoComplete.js"></script>
    <script type="text/javascript" src="../../scripts/tool/psecret.js"></script>
    <script type="text/javascript" src="../../scripts/extend/Date.js"></script>
    <script type="text/javascript" src="../../scripts/extend/Math.js"></script>
    <script type="text/javascript" src="../../scripts/extend/jQueryDom.js"></script>
    <script type="text/javascript" src="../../scripts/extend/String.js"></script>
    <script type="text/javascript" src="../../pcontrol/pchart.js"></script>
    <script type="text/javascript" src="../../pcontrol/flatBlueSeries_src_2.0.js"></script>
    <script type="text/javascript" src="/script/PropertyMonitor/searchComplete.js"></script>
    <script type="text/javascript" src="/script/PropertyMonitor/controller.js"></script>
    <script type="text/javascript" src="/script/PropertyMonitor/component.js"></script>
    <script type="text/javascript" src="/script/PropertyMonitor/model.js"></script>
    <script type="text/javascript" src="/script/PropertyMonitor/event.js"></script>
    <script type="text/javascript" src="/script/PropertyMonitor/tentchart.js"></script>
    <link rel="stylesheet" href="../../pcontrol/css/flatBlueSeries_min_2.0.css" />
    <link rel="stylesheet" href="../../css/PropertyMonitor/common.css">
    <link rel="stylesheet" href="../../css/PropertyMonitor/overviewPage.css">
    <link rel="stylesheet" href="../../css/PropertyMonitor/roundsPage.css">
    <link rel="stylesheet" href="../../css/PropertyMonitor/detailPage.css">
    <link rel="stylesheet" href="../../css/PropertyMonitor/dataAnalysispage.css">
    <link rel="stylesheet" href="../../css/PropertyMonitor/alarmRecord.css">
</head>

<body>
    <div id="tenementMonitor">
        <pnotice-message id="notice-con"></pnotice-message>
        <ploading-global id="loading"></ploading-global>
        <div class="maskdetailFloat" onclick="hideFloatevent(event)"></div>
        <div class="mainBody">
            <ptab-navigation id="mainTab" datasource="tenementEvent.dataArr" text="name" icon="icon"
                templateid="mainTabTemp" sel="selTab"></ptab-navigation>
            <div class="rightTop">
                <div class="topsearchWrap" v-show="currentPage=='overviewPage'||currentPage=='roundsPage'">
                    <psearch-promptly id="globalSearch" change="changeTopsearch" focus="changeTopsearch"
                        placeholder="搜索租户"></psearch-promptly>
                    <div class="searchHint">
                        <ul>
                            <li class="nodata" v-show="topSearchArr.length==0">
                                <pnotice-nodata text="暂无数据"></pnotice-nodata>
                            </li>
                            <li v-show="topSearchArr.length>0" v-for="item in topSearchArr"
                                v-bind:title="item.nameFloor" v-on:click.stop="chooseTopsearch($event,item)">
                                {{item.nameFloor}}
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="situationAlarm" v-show="GobalAlarmSet">
                    <pbutton-white id="datacomparebuild" text="全局报警设置" icon="u" click="getGlobalAlarmSet">
                    </pbutton-white>
                </div>
            </div>
            <pwindow-float id="'detailFloat'" bind="true" title="detailTitle" templateid="floatTemp"
                beforehide="hidebeforeevent">

                <!-- 侧弹框 -->
                <button>
                    <!--该租户报警记录-->
                    <pbutton-white text="租户详情" style="margin-top: 26px;" click="goToDetail" v-show="TenantDetail">
                    </pbutton-white>
                    <pbutton-white text="该租户报警记录" id="alarmRecord" click="getTenantAlarmRecord" v-show="showChart">
                    </pbutton-white>
                    <pbutton-white text="数据分析" id="alarmRecordBack" click="getTenantAlarmRecordBack"
                        v-show="!showChart"></pbutton-white>

                    <div id="chooseType" v-show="showChart">
                        <component-choosetype :datachoosetypes="dataChooseTypes"
                            :selecteddatacpmparatype="selecteddataCpmparaType"></component-choosetype>
                    </div>


                    <div id="detailTimerWrap">
                        <div v-show="selecteddataCpmparaType.name!=='电功率'&&selecteddataCpmparaType.name!=='剩余量'">
                            <ptime-calendar sel="detailPage_tcBtn" id="detailTimerAll" orientation="down">
                                <panel lock="false" double="false" timetype="dMy" align="right" startyear="2015"
                                    commontime="['d','pd','M','pM','y','py']"></panel>
                            </ptime-calendar>
                        </div>
                        <div v-show="selecteddataCpmparaType.name=='剩余量'">
                            <ptime-calendar sel="detailPage_tcBtn" id="detailTimerexceptY" orientation="down">
                                <panel lock="false" double="false" timetype="dM" align="right" startyear="2015"
                                    commontime="['d','pd','M','pM']"></panel>
                            </ptime-calendar>
                        </div>
                        <div v-show="selecteddataCpmparaType.name==='电功率'">
                            <ptime-calendar sel="detailPage_tcBtn" id="detailTimerOnlyD" orientation="down">
                                <panel lock="false" double="false" timetype="d" align="right" startyear="2015"
                                    commontime="['d','pd']"></panel>
                            </ptime-calendar>
                        </div>
                    </div>
                    <pbutton-white id="detailsetdiyAlarm" text="自定义报警" icon="u" click="getDiyAlarmSet"></pbutton-white>

                    <div id="float-diyAlarmset" class="float_w_detail">
                        <div class="gAlarmsetWrap">
                            <div class="gaBody police">
                                <div class="radioGroup">
                                    <pswitch-radio name="diyAlarm" id="followGalarm" text="跟随全局报警设置"
                                        change="followGlobalAlarm"></pswitch-radio>
                                    <pswitch-radio name="diyAlarm" id="diyAlarm" text="自定义报警设置" change="customizeAlarm">
                                    </pswitch-radio>
                                </div>
                                <div class="gaBPartCon plice_sel" style="padding:0px;width: 100%;">
                                    <div style="position: relative;"
                                        v-for="(model, index) in customAlarmSetArr.typeList" :key="index"
                                        v-if="model.typeId != 'ZHBJ_16'">
                                        <div class="police_tit">
                                            <span v-if="model.type=='Dian'">电费用不足</span>
                                            <span v-if="model.type=='Shui'">水费用不足</span>
                                            <span v-if="model.type=='ReShui'">热水费用不足</span>
                                            <span v-if="model.type=='RanQi'">燃气费用不足</span>
                                            <span v-if="model.type=='fuHeLv'">负荷率</span>
                                        </div>
                                        <div v-for="(model,index1) in model.alarmList">
                                            <div class="police_con clearfix">
                                                <div style="min-width: 125px">{{model.typeName}}</div>
                                                <div class="police_con_mid" :class="model.valid?'':'disabled'">
                                                    <ptext-text id="model.typeId" bind="true" value="model.limit">
                                                        <verify errtip="报警门限值不能为空" verifytype="space"></verify>
                                                        <verify errtip="格式不正确，请填写正整数" verifytype="int"></verify>
                                                        <verify errtip="不能超过3位数，请重新填写" verifytype="length" length="3">
                                                        </verify>
                                                    </ptext-text>
                                                </div>
                                                <div>{{model.unit}}报警</div>
                                                <div class="btn">
                                                    <pswitch-slide id="'s'+index+index1" change="checkInput"
                                                        state="model.valid" bind="true"></pswitch-slide>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="cover" v-show="!isdiy"
                                            style="position: absolute; top: 0px; left: 0px; width: 100%; height: 100%;">
                                        </div>
                                    </div>
                                </div>

                                <div class="gaFoot">
                                    <pbutton-blue text="保存" click="_saveDiyAlarmSet"></pbutton-blue>
                                    <pbutton-white text="取消" click="_resetToGlobalAlarm"></pbutton-white>
                                </div>
                            </div>
                        </div>
                    </div>
                </button>
                <animate maxpx="0" minpx="-2000" orientation="right"></animate>
            </pwindow-float>
            <pwindow-modal id="modal-gAlarmset" title="全局报警设置" templateid="windowTemp"></pwindow-modal>
            <script type="text/html" id="mainTabTemp">
            <div class="tabmain">
                <div :class="currentbuildChangebtn" style="width: 200px">
                    <pcombobox-normal bind="true" id="'globalBuilds'" sel="monitorModel.instance().changeBuilds">
                        <header click="restoreSearch"></header>
                        <item datasource="builds" text="name"></item>
                    </pcombobox-normal>
                </div>
                <%include overviewPage.html%>
                <%include roundsPage.html%>
                <%include dataAnalysispage.html%>
                <%include alarmLists.html%>
            </div>

        </script>
            <script type="text/html" id="floatTemp">
            <%include detailPage.html%>
        </script>
            <script type="text/html" id="windowTemp">
            <div class=" police">
                <div class="_gaTitle">
                    全局报警设置
                </div>
                <div class="plice_sel">
                    <!-- 20180614wp++ -->
                    <div v-for="(model, index) in globalAlarmSetArr.typeList" :key="index"
                         v-if="model.typeId != 'ZHBJ_16'">
                        <div class="police_tit">
                            <span v-if="model.type=='Dian'">电费用不足</span>
                            <span v-if="model.type=='Shui'">水费用不足</span>
                            <span v-if="model.type=='ReShui'">热水费用不足</span>
                            <span v-if="model.type=='RanQi'">燃气费用不足</span>
                            <span v-if="model.type=='fuHeLv'">负荷率</span>
                        </div>
                        <div v-for="(model,index1) in model.alarmList">
                            <div class="police_con clearfix">
                                <div style="min-width: 125px">{{model.typeName}}</div>
                                <div class="police_con_mid" :class="model.valid?'':'disabled'">
                                    <ptext-text id="model.typeId" bind="true" value="model.limit">
                                        <verify errtip="报警门限值不能为空" verifytype="space"></verify>
                                        <verify errtip="格式不正确，请填写正整数" verifytype="int"></verify>
                                        <verify errtip="不能超过3位数，请重新填写" verifytype="length" length="3"></verify>
                                    </ptext-text>
                                </div>
                                <div>{{model.unit}}报警</div>
                                <div class="btn">
                                    <pswitch-slide id="'s'+index+index1" change="checkInput" state="model.valid"
                                                   bind="true"></pswitch-slide>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="plice_ope clearfix">
                    <pbutton-blue text="保存" id="determine_global" click="saveGlobalAlarmSet"></pbutton-blue>
                    <pbutton-white text="取消" id="cancel_global" click="closegAlarmset"></pbutton-white>
                </div>
            </div>
        </script>
        </div>
    </div>

    <%include common/component.html%>
</body>

</html>