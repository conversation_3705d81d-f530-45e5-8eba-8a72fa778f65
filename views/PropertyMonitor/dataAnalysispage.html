<!-- 数据分析 -->
<div id="dataAnalysispage" class="clearFloat" v-show="currentPage === 'dataAnalysispage'">
    <div class="dataAnalysispage-left-wrapper">
        <div class="chooseType-wrapper">
            <ptab-button style="margin: 10px 50px;" id="buttonTab" datasource="dataArr" text="name" sel="topTapEventCall"></ptab-button>
        </div>
        <div class="chooseType-wrapper">
            <component-choosetype :datachoosetypes="dataChooseTypesCompare" :selecteddatacpmparatype="selecteddataCpmpara" :isshow="isshowMoney"></component-choosetype>
        </div>
        <div class="drop-wrapper">选择时间</div>
        <div class="timegroup-wrapper">
            <div class="time-part-wrapper" v-show="chooseTimeArr.length>0">
                <div class="time-part" v-for="(model,index) in chooseTimeArr">
                    <span class="time-conent" @click.stop="editTimer(model)" :style="{cursor:pointerMap,width:widthMap}">{{model.timer}}</span>
                    <span class="timer-reduce" v-show="!(chooseTimeArr.length==1&&tenementReportArr.length>1)" @click.stop="reduceTimer(index,model,$root.selectedtementArr)">-</span>
                </div>
            </div>
            <div class="addTimer-wrapper">
                <div class="time-add" onclick="popTimer(event)"
                     :class="{disabledNoevent:tenementReportArr.length>1&&chooseTimeArr.length===1||isDuiBiCha&&chooseTimeArr.length===1}">
                    <em>J</em> 添加时间
                </div>
                <ptime-calendar id="dataComparaTimer" orientation="down" sel="_addTimer">
                    <panel startyear="2000" endyear="new Date().getFullYear()" timetype="dM" align="left" iscommontime="false"></panel>
                </ptime-calendar>
            </div>
        </div>
        <div class="drop-wrapper" id="selecteddataArrItem">选择租户</div>
        <div class="toggleBuild">
            <pcombobox-normal bind="true" id="'dataCompataBulid'" sel="monitorModel.instance().changeCompareBUild">
                <header click="datacomboxHead"></header>
                <item datasource="copybuilds" text="name"></item>
            </pcombobox-normal>
        </div>  
        <div class="searchTement" id="searchTement">
            <psearch-promptly placeholder="搜索租户" change="dataCompara_searchTement" id="searchInput"></psearch-promptly>
        </div>
        <div class="floor-wrapper" v-if="isshowMoney">
            <div class="popTree-wrap" id="popTree-wrap" v-show="world.length==0">
                <!-- 无数据 -->
                <pnotice-nodata text="暂无数据"></pnotice-nodata>
            </div>
            <!--租户滚动条模板  -->
            <pscroll-small style="width: 100%; height: 100%;" templateid="popTreeScrollTemp"></pscroll-small>
        </div>
        <div class="floor-wrapper" v-else>
            <div class="popTree-wrap" id="popTree-wrap2" v-show="roomwatch.length==0">
                <!-- 无数据 -->
                <pnotice-nodata text="暂无数据"></pnotice-nodata>
            </div>
            <!--租户滚动条模板  -->
            <pscroll-small style="width: 100%; height: 100%;" templateid="popTreeScrollTemp1"></pscroll-small>
        </div>
    </div>
    <div class="dataAnalysispage-right-wrapper">
        <div class="name">
            <div class="checkBox-wrapper">
                <pswitch-checkbox id="chartCheck" name="ckeckType" text="图表" state="true" change="toggleshow"></pswitch-checkbox>
                <pswitch-checkbox id="reportCheck" name="ckeckType" text="报表" state="true" change="toggleshow"></pswitch-checkbox>

            </div>
            <component-downcombobox :down-arr="downArr"  v-bind:class="{'disabled':tenementReportArr.length<=0}"></component-downcombobox>
        </div>
        <div class="main">
            <div class="dataComparaNodata" v-show="selectedAllid.length===0&&chooseTimeArr.length===0">
                <pnotice-nodata text="暂无数据" subtitle="请先在左侧列表选择参数"></pnotice-nodata>
            </div>
            <div id="comparative-chart" class="comparative-chart"></div>
            <div id="comparative-table" class="comparative-table">
                <div class="dataComparaGrid">
                    <div class="formCont" id="formTableCont" v-bind:style="{'min-width':130+(tenementReportArr.length)*120+'px'}">
                        <div class="table__box--analysis">
                            <div class="common__row cr--header cr--noborder">
                                <div><b>时间</b></div>
                                <template v-for="model in tenementReportArr">
                                    <div class="slh"><b>{{model.showName}}</b></div>
                                </template>
                            </div>

                            <!--图表表格滚动条模板  -->
                            <pscroll-small style="width: 100%; height: calc(100% - 36px);" templateid="commonBodyScrollTemp"></pscroll-small>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="right-wrapper">
            <div class="fun-wrapper">
                已选项
                <span @click="emptyselectedtement" v-bind:class="{'disabled':tenementReportArr.length<=0}"><em>p</em>清空</span>
            </div>
            <div class="comparative-wrapper">
                <ul>
                    <li class="comparative-item" v-for="model in tenementReportArr">
                        <div class="_name">
                            <div class="point">
                                <i :style="{background: model.color}"></i>
                            </div>
                            <div class="itemname slh">{{model.name}}</div>
                        </div>
                        <div class="_time">
                            <div class="itemtimer slh">{{model.shortDate}}</div>
                        </div>
                        <div class="_energy">
                            <div class="itemtimer slh" v-show="sumShow">
                                <em class="energyNo">{{model.sumData==null?'--':model.sumData}}</em><em v-show="model.sumData!=null">{{model.unit}}</em>
                            </div>
                        </div>
                        <span class="itemreduce" @click="removeOne(model._tenentId,model._timeId)" v-show="!isDuiBiCha">x</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
