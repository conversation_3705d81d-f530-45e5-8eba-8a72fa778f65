﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title><%=fname%></title>
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <script type="text/javascript" src="/scripts/lib/jquery-2.0.0.js"></script>
    <script type="text/javascript" src="../../pcontrol/flatBlueSeries_src.js"></script>
    <script type="text/javascript" src="/scripts/lib/AutoComplete.js"></script>
    <script type="text/javascript" src="/scripts/lib/knockout-2.2.1.debug.js"></script>
    <script type="text/javascript" src="/scripts/lib/Date.js"></script>
    <script type="text/javascript" src="/scripts/lib/highcharts.src-4.2.5.js"></script>
    <script type="text/javascript" src="/scripts/lib/highcharts-more-4.1.8.js"></script>
    <script type="text/javascript" src="../../pcontrol/pchart.js"></script>
    <script type="text/javascript" src="/scripts/lib/secret.js"></script>
    <script type="text/javascript" src="/scripts/lib/math.js"></script>
    <script type="text/javascript">
        $.ajaxSetup({
            cache: false //关闭AJAX缓存
        });
    </script>
    <script type="text/javascript" src="/script/global/model.js"></script>
    <script type="text/javascript" src="/script/global/controller.js"></script>
    <script type="text/javascript" src="/script/global/tool.js"></script>
    <script type="text/javascript" src="/script/frame/model.js"></script>
    <script type="text/javascript" src="/script/frame/controller.js"></script>
    <script type="text/javascript" src="/script/frame/register.js"></script>

    <link rel="stylesheet" href="../../pcontrol/css/flatBlueSeries_min.css" />
    <link rel="stylesheet" href="/css/frame/common.css" />

    <link rel="shortcut icon" href="<%=icon%>" type="image/x-icon" />
</head>
<body onunload="return windowClose(event);">
    <%include ../global/template.html%>
    <div class="v6s-wrap">
        <div id="v6s-wrap">
            <!-- loading -->
            <div id="v6s-loading" p-create="progress-waiting" p-bind="attr:{text:'加载中,请稍候...'}"></div>
            <!-- 二次确认框 -->
            <div id="v6s-setwrap-warning" p-create="modal-warning" p-bind="attr:{title:'您确定要删除该建筑吗？',subtitle:'被删除的建筑将无法恢复',buttons:[{text:'确定'}]}"></div>
            <!-- title  begin -->
            <div class="v6s-header">
                <div class="v6s-h-navButton active"></div>
                <div class="v6s-h-title"><span id="spanTiMbname"><%=mname %></span>><span id="spanTiFName"><%=fname %></span></div>
                <div class="v6s-h-nav">
                    <!-- 用户 -->
                    <div class="v6s-h-nav-user">
                        <div class="user-title">
                            <b>v</b><span class="pic"
                                data-bind="style: { background: currUser().icon ? 'url(' + currUser().icon + ') no-repeat center center / cover' : '' }"></span><em data-bind="    text: currUser().name">--</em>
                        </div>
                        <div class="user-con">
                            <ul>
                                <li class="userLi-1"><a>个人资料</a></li>
                                <li class="userLi-2"><a>修改密码</a></li>
                                <li><a href="/pexit">退出系统</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- 个人资料 -->
                <div class="user-data" datasign="select" id="divSelfInfopp">
                    <h1>个人资料</h1>
                    <div class="user-data-x">x</div>
                    <div class="user-pic">
                        <img data-bind="attr: { src: currUser().icon }" id="imgEditHeader">
                    </div>
                    <div class="user-name" data-bind="text: currUser().userId">工程副总</div>
                    <!-- 查看 -->
                    <div class="user-data-select">
                        <ul>
                            <li><em>姓名</em><span data-bind="text: currUser().name">王毅</span></li>
                            <li><em>邮箱</em><span data-bind="text: currUser().email"><EMAIL></span></li>
                            <li><em>手机</em><span data-bind="text: currUser().phone">15111156556</span></li>
                        </ul>
                    </div>
                </div>
                <!-- 修改密码 -->
                <div class="user-password-edit" id="user-password-edit">
                    <h1>修改密码</h1>
                    <div class="password-edit-x">x</div>
                    <ul>
                        <li><em>原密码</em>
                            <div p-create="text-password" p-bind="attr:{spaceerrtext:'原密码不可为空！'}" id="originPassword"></div>
                        </li>
                        <li><em>新密码</em>
                            <div p-create="text-password" p-bind="attr:{spaceerrtext:'请输入新密码！'},event:{blur:verifyPassword,focus:hidePassTip}" id="newPassword"></div>
                        </li>
                        <li><em>再次输入新密码</em>
                            <div p-create="text-password" p-bind="attr:{spaceerrtext:'请再次输入新密码'},event:{blur:verifyPassword}" id="againPassword"></div>
                        </li>
                    </ul>
                    <div class="edit-button">保存</div>
                </div>

            </div>
            <!-- title  end -->
            <div class="v6s-nav blckScroll">
                <dl class="v6s-build-wrap" style="display:none;">
                    <dt class="build-pic">
                        <b></b>
                        <img data-bind="attr: { src: selBuild().icon }" /></dt>
                    <dt data-bind="text: selBuild().name, attr: { title: selBuild().name }"></dt>
                    <dt data-bind="text: selBuild().description, attr: { title: selBuild().description }"></dt>
                </dl>
                <div class="v6s-nav-wrap" data-bind="foreach: fGroups">
                    <div class="v6s-nav-temp">
                        <div class="temp-title" data-bind="text: name"></div>
                        <ul data-bind="foreach: fList">
                            <li>
                                <a target="_parent" data-bind="attr: { href: url }">
                                    <b>></b>
                                    <em class="em1" data-bind="style: { 'background-image': 'url(' + icon + ')' }"></em>
                                    <span data-bind="text: name, attr: { title: name }"></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

            </div>
        </div>
        <div class="v6s-content">
