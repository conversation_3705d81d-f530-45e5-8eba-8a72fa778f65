<div id="template_manage" style="display: none;">
	<header class="templateheader">
		<!--<p class="returnreport">返回</p>-->
		<button class="returnreport" p-create="button-grayBorder" p-bind="attr:{text:'返回'}"></button>
		<!--<p class="scteplate">上传模板</p>-->
		<div class="scteplate" p-create="button-backBlueBorder" p-bind="attr:{text:'上传模板'}"></div>
		模板管理
	</header>
	<section class="templatecon">
		<div class="templaterongqi">
			<div id="templaterongqi_box">
				<article class="templzhiyuanshu" v-for="item in getTempList" @click="clickTemplate(item, $event)">
					<div class="templatecond">
						<div class="rihuozhou">
							<p class="rihuozhoup" v-text="item.formatType"></p>
						</div>
						<div class="rihuozhouxinxi">
							<p>模板名称:</p>
							<textarea readonly="true" class="bianjineirong" v-model="item.name" :id="item.id" @focus="recoveryStyle($event)" @keyup="limitInput($event)"></textarea>
						</div>
					</div>
					<div class="tempbianji" >
						<p class="tempbianjip">
							<b class="templshanchu"></b><b class="templbianji" ></b>
							<input class="switch switch-anim" type="checkbox" @click="switchTemp(item,$event)" v-if="item.valid==0">
							<input class="switch switch-anim" type="checkbox" @click="switchTemp(item,$event)" checked="checked" v-if="item.valid==1">
							<i style="color: #8fa0a9;font-size: 12px;margin-top: 10px;margin-right: 5px">
								是否可见</i>

						</p>

					</div>
					<div class="tempbianjibc">
						<p class="tempbaocunp">						
							<b class="tempbaocun liangbaocun" @click="editTemp(item,$event)"></b>

							<b class="mingchengchuts" style="display: none;">该名称已被使用,请修改。</b>
							<b class="mingchengempty" style="display: none;">模板名称不可为空</b>
						</p>					
					</div>
	                <aside class="shamchudamceng" style="display: none;">
					<div>
						<p>您确定要删除该模板吗</p>
						<p>被删除的模板将无法恢复</p>
					</div>
					<div>
						<p class="shamchudamcengsc" @click="delTemp(item,$index)">删除</p>
						<p class="shamchudamcengqx">取消</p>
					</div>
				</aside>			
				</article>
			</div>						
		</div>
	</section>
	
	
</div>