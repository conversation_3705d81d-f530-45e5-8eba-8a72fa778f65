<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>仪表状态</title>
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <!-- <script type="text/javascript" src="/scripts/lib/vue-2.2.0.min.js"></script> -->
    <link rel="stylesheet" href="../../scripts/lib/element-ui/theme-chalk/index.css">
    <link rel="stylesheet" href="../../css/powerProtectionRec/index.css">
    <link rel="stylesheet" href="../../css/powerProtectionRec/recordDetail.css">
    <script type="text/javascript" src="../../scripts/lib/knockout-2.2.1.debug.js"></script>
        <script type="text/javascript" src="/script/global/model.js"></script>
    <script type="text/javascript" src="/script/global/controller.js"></script>
    <script type="text/javascript" src="/script/global/tool.js"></script>
     <script type="text/javascript" src="../../script/frame/model.js"></script>
    <script type="text/javascript" src="../../scripts/lib/jquery-2.0.0.min.js"></script>
    <script type="text/javascript" src="../../scripts/lib/highcharts-4.2.5.js"></script>
    <script type="text/javascript" src="../../scripts/lib/xlsx.full.min.js"></script>
    <script type="text/javascript" src="../../pcontrol/pchart.js"></script>
    <!-- <script type="text/javascript" src="../../scripts/lib/vue-2.2.0.min.js"></script> -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/vue@2.7.16/dist/vue.js"></script>    -->
    <script type="text/javascript" src="../../scripts/lib/vue-2.7.16.js"></script>
    <script type="text/javascript" src="../../scripts/lib/element-ui/index.js"></script>
    <!-- <script src="https://unpkg.com/element-ui/lib/index.js"></script> -->
    <script type="text/javascript" src="../../scripts/tool/ptool.js"></script>
    <script type="text/javascript" src="../../scripts/tool/asynTool.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pconst.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pajax.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pautoComplete.js"></script>
    <script type="text/javascript" src="../../scripts/tool/psecret.js"></script>
    <script type="text/javascript" src="../../scripts/extend/Date.js"></script>
    <script type="text/javascript" src="../../scripts/extend/Math.js"></script>
    <script type="text/javascript" src="../../scripts/extend/String.js"></script>
    <script type="text/javascript" src="../../scripts/extend/jQueryDom.js"></script>
    <script type="text/javascript" src="../../scripts/extend/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../pcontrol/flatBlueSeries_src_2.0.js"></script>
    <!-- <script type="text/javascript" src="/script/tement/tenantMng/controller.js"></script> -->
    <script type="text/javascript" src="/script/powerProtectionRec/controller.js"></script>
    <!-- <script type="text/javascript" src="/script/tement/tenantMng/event/addtement_event.js"></script> -->
    <script type="text/javascript" src="/script/orderSum/event/addtement_event.js"></script>
    <!-- <script type="text/javascript" src="/script/tement/tenantMng/event.js"></script> -->
     <!-- <script type="text/javascript" src="/script/orderSum//event.js"></script> -->
    <script type="text/javascript" src="/script/tement/common/common.js"></script>
    <!-- <script type="text/javascript" src="/script/tement/tenantMng/model.js"></script> -->
    <script type="text/javascript" src="/script/powerProtectionRec/model.js"></script>
    <script type="text/javascript" src="/script/tement/tenantMng/component.js"></script>
    <script type="text/javascript" src="/script/tement/searchComplete.js"></script>
</head>
<body>
    <div id="power-protection-record" class="power-protection-record">
        <div class="mainBody mainBodyShow" :style="{display: 'block', height: '100%'}">
            <%include childPage/recordDetail.html%>
            <!--租户列表-->
            <div class="t_p_box content" style="height: 100%" v-show="currentPage=='PowerProtectionRec'">
                <!--头部标题栏  -->
                <div class="main_title clearFloat">
                    <el-form 
                    :inline="true"
                    :model="searchFormData"
                    class="search-condition-form"
                    @submit.native.prevent="onFormEnter"
                    size="small">
                        <el-form-item label="搜索:" prop="keyword">
                            <el-input
                                title="可查询商户号、商户名称、电表ID"
                                placeholder="商户号、商户名称、电表ID"
                                prefix-icon="el-icon-search"
                                v-model="searchFormData.keyword"
                                @keyup.enter.native="onSearchSubmit"
                                :style="{  width: '240px' }">
                            </el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" size="small" @click="onSearchSubmit" :loading="tableLoading"> 查询</el-button>
                        </el-form-item>
                    </el-form>
                    <!-- <el-input
                        size="small"
                        title="可查询商户号、商户名称、电表ID"
                        placeholder="商户号、商户名称、电表ID"
                        prefix-icon="el-icon-search"
                        v-model="searchFormData.keyword"
                        @keyup.enter.native="onSearchSubmit"
                        :style="{  width: '240px' }">
                    </el-input> -->
                    <!-- <el-button @click="rehandleDialogVisible = true" type="primary" size="small">测试按钮</el-button> -->
                </div>
                <div class="content-body">
                    <el-table
                        v-loading="tableLoading"
                        :data="tableData"
                        style="width: 100%; height: 100%"
                        :header-cell-style="{ padding: '6px 0' }"
                        :cell-style="cellStyle"
                        :row-key="row => row.id"
                        v-loading="loading"
                        element-loading-text="预计数据加载时间较长，请耐心等待..."
                        :border="true"
                        :stripe="true">
                        <el-table-column
                            v-for="col in columns"
                            :key="col.prop"
                            :prop="col.prop"
                            :label="col.label"
                            :min-width="col.width || 'auto'"
                            :align="col.align"
                            :formatter="col.formatter"
                        /></el-table-column>
                        <el-table-column label="操作" prop="handle" :width="126" :key="'handle'" :align="'center'" >
                            <template slot-scope="scope">
                                <!-- v-if="scope.row.status === 2" -->
                                <el-button
                                    style="font-weight: 600;"
                                    size="medium"
                                    @click="gotoRecordDetailsClick(scope.$index, scope.row)"
                                    type="text">
                                    查看 >>
                                </el-button>
                                <!-- <el-popconfirm
                                    title="确定关闭该订单吗？"
                                    v-if="scope.row.status === 2"
                                    @confirm="closeOrderConfirm(scope.$index, scope.row)">
                                    <el-button
                                        style="font-weight: 600;"
                                        slot="reference"
                                        size="medium"
                                        type="text">
                                        关闭
                                    </el-button>
                                </el-popconfirm> -->
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

                <div class="content-footer">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="tableCurrentPage"
                        :page-sizes="[20, 50]"
                        :page-size="20"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="tableTotal">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
