<div class="record-detail" v-show="currentPage === 'recordDetail'">
    <div class="content record-detail-content">
        <div class="content-header">
            <div class="header-left">
                <el-button size="small" @click="backClick_record"><< 返回</el-button>
                <div class="singleTenant-info" v-if="originalMeterId">
                    <div class="info-item">
                        <span class="item-name" style="font-weight: 600;">租户名称：</span>
                        <span class="item-value">{{singleTenant.tenantName}}</span>
                    </div>
                    <div class="info-item">
                        <span class="item-name" style="font-weight: 600;">电表ID：</span>
                        <span class="item-value">{{singleTenant.meterId}}</span>
                    </div>
                </div>
                <el-form
                :inline="true"
                class="search-condition-form-record"
                size="small"
                :model="recordSearchFormData"
                >
                    <el-form-item label="起止时间:">
                        <el-date-picker
                            v-model="recordSearchFormData.timeRange"
                            type="datetimerange"
                            :picker-options="pickerOptions"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :style="{  width: '350px' }">
                        </el-date-picker>
                    </el-form-item>
                    <!-- <el-form-item>
                        <el-button size="small" type="primary" @click="recordTableSearchClick" :loading="tableLoadingRecord">查询</el-button>
                    </el-form-item> -->
                </el-form>
            </div>
            <div class="header-right">
                <el-button size="small" type="primary" @click="recordTableSearchClick" :loading="tableLoadingRecord">查询</el-button>
            </div>
        </div>
        <div class="content-body">
            <el-table
            :data="tableDataRecord"
            v-loading="tableLoadingRecord"
            :cell-style="cellStyle" 
            style="width: 100%">
                <el-table-column
                v-for="item in columns_record"
                :prop="item.prop"
                :key="item.key"
                :cell-style="cellStyle"
                :label="item.label"
                :min-width="item.width"
                :align="item.align"
                :formatter="item.formatter">
                </el-table-column>
            </el-table>
        </div>
        <div class="content-footer">
            <el-pagination
            @size-change="handleSizeChange_record"
            @current-change="handleCurrentChange_record"
            :current-page="tableCurrentPageRecord"
            :page-sizes="[20, 50, 100, 200]"
            :page-size="tablePageSizeRecord"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableTotalRecord">
        </div>
    </div>
</div>