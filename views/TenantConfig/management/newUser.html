<!-- 用户新增 编辑 -->
<div class="buildFloat" v-show="framePage=='addPage'||framePage=='editPage'" style="width: 600px">
    <div class="detailMessage">
        <div class="item">
            <div class="inputname"><b>用户名:</b></div>
            <div class="inputwrap" v-if="framePage=='editPage'">
                <span>{{itemToUpdate.name}}</span>
            </div>
            <div class="inputwrap" v-else>
                <ptext-text value="itemToUpdate.name" bind="true">
                    <verify errtip="用户名不可为空" verifytype="space"></verify>
                    <verify errtip="用户名不可超过20个字符！" verifytype="length" length="20"></verify>
                </ptext-text>
            </div>
        </div>
        <div class="item">
            <span class="inputname"><b>姓名:</b></span>
            <div class="inputwrap">
                <ptext-text value="itemToUpdate.showName" bind="true">
                    <verify errtip="姓名不可超过50个字符！" verifytype="length" length="50"></verify>
                </ptext-text>
            </div>
        </div>

        <div class="item">
            <span class="inputname"><b>邮箱:</b></span>
            <div class="inputwrap verificationFor">
                <ptext-text value="itemToUpdate.email" bind="true">
                    <verify errtip="请输入正确的邮箱！" verifytype="email"></verify>
                </ptext-text>
            </div>
        </div>
        <div class="item">
            <div class="inputname"><b>手机号码:</b></div>
            <div class="inputwrap verificationFor verificationForphone">
                <ptext-text value="itemToUpdate.mobile" bind="true" mouseout="staticEvent.textphoneFun">
                    <verify errtip="请输入正确的手机号码！" verifytype="mobile"></verify>
                </ptext-text>
            </div>
        </div>
    </div>
    <div class="position" v-show="saveBtn">
        <div class="positionName">岗位(可根据岗位快速选择菜单和功能)</div>
        <ul>
            <li v-for="(item,index) in roleList">
                <input type="checkbox" name1="checks" :name="item.id" @click="staticEvent.oneChoice('checks')"
                    :value="item.id" :id="item.id">
                <label v-text="item.name" :for="item.id">主管</label>
            </li>
        </ul>
    </div>
    <div class="position">
        <div class="positionName">菜单权限</div>
        <div class="menuPermission">
            <div class="menuPermissionchild" v-for="forfunctionList in functionList">
                <input type="checkbox" name2="checks" :name="forfunctionList.id" :value="forfunctionList.id"
                    :id="forfunctionList.id" :checked="forfunctionList.isHave"
                    @click="staticEvent.checkedForchildren(forfunctionList)">
                <label v-text="forfunctionList.name" :for="forfunctionList.id">租户后台设置</label>

                <div class="menuPermissionchild2" v-for="item2 in forfunctionList.List">
                    <div class="menuPermissionchild3">
                        <input type="checkbox" name3="checks" :name="item2.id" :value="item2.id" :id="item2.id"
                            :checked="item2.isHave" @click="staticEvent.checkedForFarter(item2,forfunctionList)">
                        <label v-text="item2.name" :for="item2.id">后台配置</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="position">
        <div class="positionName">功能权限</div>
        <div class="forul">
            <div class="forli" v-for="forpermissionList in permissionList">
                <input type="checkbox" name4="checks" :name="forpermissionList.id" :value="forpermissionList.id"
                    :id="forpermissionList.id" :checked="forpermissionList.isHave">
                <label v-text="forpermissionList.name" :for="forpermissionList.id">充值</label>

            </div>
        </div>
    </div>
    <div class="butWrap">
        <button @click="staticEvent.addSave" v-show="saveBtn">保存</button>
        <button @click="staticEvent.editSave" v-show="!saveBtn">保存</button>
    </div>

</div>