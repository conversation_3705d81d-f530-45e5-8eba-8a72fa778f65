<div id="userManagement">
    <!--用户列表展示-->
    <div class="listModel" v-show="curPage=='listPage'">
        <div class="addUserBtn">
            <pbutton-blue text="添加用户" click="staticEvent.addUser" icon="J"></pbutton-blue>
        </div>
        <!--主体列表展示-->
        <div class="content_body">
            <div class="projectManagement_girds_box">
                <pgrid-normal style="width: 100%;max-height: 100%" id="'userManagement_gird'">
                    <panel datasource="userGridArr" lineclick="staticEvent.showDetail" bind="true"></panel>
                    <header>
                        <column name="用户名" source="name"></column>
                        <column name="姓名" source="showName"></column>
                        <column name="邮箱" source="email"></column>
                        <column name="手机号码" source="mobile"></column>
                    </header>
                </pgrid-normal>
            </div>
        </div>
    </div>
</div>