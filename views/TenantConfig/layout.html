<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />
    <title>租户后台配置</title>
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <link rel="stylesheet" href="/pcontrol/css/flatBlueSeries_min_2.0.css" />
    <link rel="stylesheet" href="/css/tement/management.css">
    <script type="text/javascript" src="/scripts/lib/jquery-2.0.0.min.js"></script>
    <script type="text/javascript" src="/scripts/lib/vue-2.2.0.min.js"></script>
    <script type="text/javascript" src="/scripts/lib/highcharts-4.2.5.js"></script>
    <script type="text/javascript" src="/scripts/tool/ptool.js"></script>
    <script type="text/javascript" src="/scripts/tool/pconst.js"></script>
    <script type="text/javascript" src="/scripts/tool/pajax.js"></script>
    <script type="text/javascript" src="/scripts/tool/pautoComplete.js"></script>
    <script type="text/javascript" src="/scripts/tool/psecret.js"></script>
    <script type="text/javascript" src="/scripts/tool/asynTool.js"></script>
    <script type="text/javascript" src="/scripts/extend/jQueryDom.js"></script>
    <script type="text/javascript" src="/scripts/extend/String.js"></script>
    <script type="text/javascript" src="/scripts/extend/Date.js"></script>
    <script type="text/javascript" src="/scripts/extend/Math.js"></script>
    <script type="text/javascript" src="/script/tement/common/common.js"></script>
    <script type="text/javascript" src="/pcontrol/flatBlueSeries_src_2.0.js"></script>
    <script type="text/javascript" src="/script/tement/management/model.js"></script>
    <script type="text/javascript" src="/script/tement/management/controller.js"></script>
    <script type="text/javascript" src="/script/tement/management/event.js"></script>
    <script type="text/javascript" src="/script/tement/management/data.js"></script>

</head>

<body style="background:#f0f3f6;overflow: hidden">
    <div id="tab_page" class="tab_page">
        <ptab-navigation datasource="_tabTitleArr_" text="name" icon="icon" templateid="lesseeSetting"
            sel="staticEvent.tabPageEvent" id="'tenantTab'" bind="true"></ptab-navigation>

        <!-- 全局加载 -->
        <ploading-global id="gloadLoading"></ploading-global>

        <!-- 局部加载 -->
        <ploading-part id="partLoading" text="加载中，请稍后..."></ploading-part>

        <!-- 成功失败提示 -->
        <pnotice-message id="message"></pnotice-message>


        <!--侧弹框展示-->
        <!-- 项目详情 -->
        <pwindow-float id="projectFloatWindow" isshade="false" templateid="projectManagementTemp">
            <animate maxpx="0" minpx="-830" orientation="right"> </animate>
        </pwindow-float>
        <!-- 项目添加 -->
        <pwindow-float id="addProjectFloatWindow" isshade="false" templateid="addProjectManagementTemp">
            <animate maxpx="0" minpx="-830" orientation="right"> </animate>
        </pwindow-float>
        <!-- 建筑配置 详情 -->
        <pwindow-float id="buildFloatWindow" isshade="false" templateid="buildManagementTemp">
            <animate maxpx="0" minpx="-830" orientation="right"> </animate>
        </pwindow-float>
         <!-- 项目添加 -->
         <pwindow-float id="addBuildFloatWindow" isshade="false" templateid="addBuildManagementTemp">
            <animate maxpx="0" minpx="-830" orientation="right"> </animate>
        </pwindow-float>
        <!-- 用户管理 详情 -->
        <pwindow-float id="userFloatWindow" isshade="false" templateid="userManagementTemp">
            <animate maxpx="0" minpx="-830" orientation="right"> </animate>
        </pwindow-float>
        <!--上传excel-->
        <input id="upload_input_excel" type="file" name="excel_file" value="上传表格" style="display: none"
            accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet">
    </div>

    <!-- tab页内容模板 -->
    <script type="text/html" id="lesseeSetting">
        <div class="tab_select">
            <!--项目管理页面-->
            <%include management/projectManagement.html%>
            <!--建筑配置页面-->
            <%include management/buildManagement.html%>
            <!--用户管理页面-->
            <%include management/userManagement.html%>
            <!-- 后台配置页 -->
            <%include management/backstageConfig.html%>
            <!-- 操作记录页面 -->
            <%include management/operatingRecord.html%>
        </div>
    </script>

    <!-- 表格模板 -->
    <script type="text/html" id="gridTpl">
        <div v-text="model.name"></div>
        <div v-text="model.fileName"></div>
        <div v-text="model.uploadTime"></div>
        <div class="clearfix">
            <div class="btn" @click="downloadFileEvent(model, $event)" data-flag="0"><i>j</i>下载模板</div>
            <div class="btn" @click="uploadFileEvent(model)"><i>d</i>上传</div>
            <div class="btn" @click="downloadFileEvent(model, $event)" data-flag="1" v-if="model.isExsit"><i>D</i>下载</div>
            <!-- <div class="btn del_btn" @click="deleteConfigFileEvent(model)" v-if="model.isExsit"><i>p</i>删除</div> -->
        </div>
    </script>
    <!-- 项目编辑 侧弹框展示 -->
    <script type="text/html" id="projectManagementTemp">
        <div class="buildFloat">
            <div class="detailMessage">
                <div class="item">
                    <div class="inputname"><b>项目ID :</b></div>
                    <div class="inputwrap">
                        <span v-text="itemInfo.id">这里是项目ID</span>
                    </div>
                </div>
                <div class="item">
                    <span class="inputname"><b>项目名称 :</b></span>
                    <div class="inputwrap">
                        <input type="text" placeholder="请输入" id="editProjectName" @blur="staticEvent.editVerify('editProjectName')"
                        v-model="itemInfo.name">
                        <p class="error-tip">*请输入长度不超过50个字符的数字，英文或中文！</p>
                    </div>
                </div>
                <div class="item">
                    <span class="inputname"><b>项目位置 :</b></span>
                    <div class="inputwrap">
                        <input type="text" placeholder="请输入"  id="editProjectAddress"  @blur="staticEvent.editVerify('editProjectAddress')"  
                        v-model="itemInfo.address">
                        <p class="error-tip">*请输入长度不超过50个字符的数字，英文或中文！</p>
                    </div>
                </div>
            </div>
            <div class="butWrap">
                <button @click="staticEvent.editSave(event)" style="border: 1px solid #7a94ad;background:#7a94ad">保存</button>
            </div>
        </div>
    </script>
    <!-- 项目添加  侧弹框展示 -->
    <script type="text/html" id="addProjectManagementTemp">
        <div class="buildFloat">
            <div class="detailMessage">
                <div class="item">
                    <div class="inputname"><b>项目ID:</b></div>
                    <div class="inputwrap">
                     <ptext-text placeholder="请输入" id="addProjectID">
                        <verify errtip="不可为空" verifytype="space"></verify>
                        <verify errtip="不可超过20个字" verifytype="length" length="20"></verify>
                        <verify errtip="请输入数字" verifytype="number"></verify>
                        <tip friendlytip="请输入长度不超过20个字符的数字！"></tip>
                  </ptext-text>
                    </div>
                  </div>
                  <div class="item">
                    <span class="inputname"><b>项目名称:</b></span>
                    <div class="inputwrap">
                      <ptext-text placeholder="请输入" id="addProjectName" blur="staticEvent.addVerify('addProjectName')">
                        <verify errtip="不可为空" verifytype="space"></verify>
                        <tip friendlytip="请输入长度不超过50个字符的数字，英文和中文！"></tip>
                  </ptext-text>
                    </div>
                  </div>
                  <div class="item">
                    <span class="inputname"><b>项目位置:</b></span>
                    <div class="inputwrap">
                      <ptext-text placeholder="请输入" id="addProjectAddress"  blur="staticEvent.addVerify('addProjectAddress')">
                        <verify errtip="不可为空" verifytype="space"></verify>
                        <tip friendlytip="请输入长度不超过50个字符的数字，英文和中文！"></tip>
                  </ptext-text>
                    </div>
                  </div>
                </div>
                <div class="butWrap">
                  <button @click="staticEvent.addSave(event)" style="border: 1px solid #7a94ad;background:#7a94ad">保存</button>
                </div>
        </div>
    </script>

    <!--建筑详情 侧弹框展示-->
    <script type="text/html" id="buildManagementTemp">
        <div class="buildFloat">
            <div class="detailMessage">
                <div class="item">
                    <div class="inputname"><b>建筑ID:</b></div>
                    <div class="inputwrap">
                        <span v-text="itemInfo.id">这里是建筑ID</span>
                    </div>
                </div>
                <div class="item">
                    <span class="inputname"><b>建筑名称:</b></span>
                    <div class="inputwrap">
                        <input type="text" placeholder="请输入" id="editBuildName" @blur="staticEvent.editVerify('editBuildName')"
                        v-model="itemInfo.name">
                        <p class="error-tip">*请输入长度不超过50个字符的数字，英文或中文！</p>
                    </div>
                </div>
                <div class="item">
                    <span class="inputname"><b>建筑位置:</b></span>
                    <div class="inputwrap">
                        <input type="text" placeholder="请输入" id="editBuildAddress" @blur="staticEvent.editVerify('editBuildAddress')" v-model="itemInfo.address">
                        <p class="error-tip">*请输入长度不超过50个字符的数字，英文或中文！</p>
                    </div>
                </div>
            </div>
            <div class="butWrap">
                <button @click="staticEvent.editSave">保存</button>
                <button @click="staticEvent.deleteBuild">删除</button>
            </div>
        </div>

    </script>

    <!-- 建筑添加  侧弹框展示 -->
    <script type="text/html" id="addBuildManagementTemp">
        <div class="buildFloat">
            <div class="detailMessage">
                <div class="item">
                    <div class="inputname"><b>建筑ID:</b></div>
                    <div class="inputwrap">
                     <ptext-text placeholder="请输入" id="addBuildID">
                        <verify errtip="不可为空" verifytype="space"></verify>
                        <verify errtip="不可超过20个字" verifytype="length" length="20"></verify>
                        <verify errtip="请输入数字" verifytype="number"></verify>
                        <tip friendlytip="请输入长度不超过20个字符的数字！"></tip>
                  </ptext-text>
                    </div>
                  </div>
                  <div class="item">
                    <span class="inputname"><b>建筑名称:</b></span>
                    <div class="inputwrap">
                      <ptext-text placeholder="请输入" id="addBuildName" blur="staticEvent.addVerify('addBuildName')">
                        <verify errtip="不可为空" verifytype="space"></verify>
                        <tip friendlytip="请输入长度不超过50个字符的数字，英文和中文！"></tip>
                  </ptext-text>
                    </div>
                  </div>
                  <div class="item">
                    <span class="inputname"><b>建筑位置:</b></span>
                    <div class="inputwrap">
                      <ptext-text placeholder="请输入" id="addBuildAddress"  blur="staticEvent.addVerify('addBuildAddress')">
                        <verify errtip="不可为空" verifytype="space"></verify>
                        <tip friendlytip="请输入长度不超过50个字符的数字，英文和中文！"></tip>
                  </ptext-text>
                    </div>
                  </div>
                </div>
                <div class="butWrap">
                  <button @click="staticEvent.addSave(event)" style="border: 1px solid #7a94ad;background:#7a94ad">保存</button>
                </div>
        </div>
    </script>

    <!--用户相关页面侧弹框显示-->
    <script type="text/html" id="userManagementTemp">
        <%include management/newUser.html%>
        <div class="buildFloat" v-show="framePage=='detailPage'">
            <div class="detailMessage">
                <div class="item">
                    <div class="inputname"><b>用户名:</b></div>
                    <div class="inputwrap">
                        <span v-text="itemInfo.name?itemInfo.name:'--'">你说呢</span>
                        <span class="resetPassword" @click="staticEvent.resetPassword">重置密码</span>
                    </div>
                </div>
                <div class="item">
                    <span class="inputname"><b>姓名:</b></span>
                    <div class="inputwrap">
                        <span v-text="itemInfo.showName?itemInfo.showName:'itemInfo.name'">球球同学</span>
                    </div>
                </div>
                <div class="item">
                    <span class="inputname"><b>邮箱:</b></span>
                    <div class="inputwrap">
                        <span v-text="itemInfo.email?itemInfo.email:'--'"><EMAIL></span>
                    </div>
                </div>
                <div class="item">
                    <div class="inputname"><b>手机号码:</b></div>
                    <div class="inputwrap">
                        <span v-text="itemInfo.mobile?itemInfo.mobile:'--'">18840873312</span>
                    </div>
                </div>
              <!--  <div class="item">
                    <div class="inputname"><b>职务:</b></div>
                    <div class="inputwrap">
                        <span v-text="itemInfo.position?itemInfo.position:'&#45;&#45;'">财务人员</span>
                    </div>
                </div>-->
                <div class="item">
                    <div class="inputname"><b>菜单权限:</b></div>
                    <div class="inputwrap">
                        <span v-text="itemInfo.functions?itemInfo.functions:'--'">财务人员</span>
                    </div>
                </div>
                <div class="item">
                    <div class="inputname"><b>功能权限:</b></div>
                    <div class="inputwrap">
                        <span v-text="itemInfo.permissions?itemInfo.permissions:'--'">财务人员</span>
                    </div>
                </div>
            </div>

            <div class="butWrap">
                <button @click="staticEvent.edit">编辑</button>
                <button @click="staticEvent.deleteUser">删除</button>
            </div>
        </div>
    </script>

    <!--删除的二次弹框-->
    <pwindow-confirm id="confirmWindow">
        <button>
            <pbutton-backred text="确定" click="staticEvent.confirmDel"></pbutton-backred>
            <pbutton-white text="取消" click="staticEvent.confirmHide"></pbutton-white>
        </button>
    </pwindow-confirm>
    <!--密码重置的二次弹框-->
    <pwindow-confirm id="passwordResetConfirm">
        <button>
            <pbutton-backred text="确定" click="staticEvent.passWordResetConfirm"></pbutton-backred>
            <pbutton-white text="取消" click="staticEvent.passWordResetCancel"></pbutton-white>
        </button>
    </pwindow-confirm>

</body>


<script type="text/javascript">

    function itemClick(obj) {
        var currItem = obj.pEventAttr.currItem;
        console.log(currItem);
    }

</script>

</html>