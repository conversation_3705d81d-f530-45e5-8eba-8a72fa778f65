<div class="edit-log" v-show="currentPage == 'editLog'">
    <div class="content edit-log-content">
        <div class="content-header">
            <el-button size="small" @click="backClick_log"><< 返回</el-button>
            <div class="singleTenant-info" v-if="originalMeterId">
                <div class="info-item">
                    <span class="item-name" style="font-weight: 600;">租户名称：</span>
                    <span class="item-value">{{singleTenant.tenantName}}</span>
                </div>
                <div class="info-item">
                    <span class="item-name" style="font-weight: 600;">电表ID：</span>
                    <span class="item-value">{{singleTenant.meterId}}</span>
                </div>
            </div>
            <el-form
            :inline="true"
            class="search-condition-form-log"
            size="small"
            :model="logSearchForm"
            >
                <el-form-item v-if="!originalMeterId">
                    <el-input
                    v-model="logSearchForm.keyword"
                    prefix-icon="el-icon-search"
                    placeholder="搜索租户名称/电表ID"
                    clearable
                    @keyup.enter.native="searchEnter_log">
                    </el-input>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="logSearchForm.status"  @change="tableSearchStatusChange_log">
                        <el-option v-for="(item, index) in statusOptions" :label="item.label" :value="item.value"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button size="small" type="primary" @click="logTableSearchClick" :loading="logTableLoading">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="content-body">
            <el-table
            :data="tableDataLog"
            v-loading="logTableLoading"
            :cell-style="logTableCellStyle" 
            style="width: 100%">
                <el-table-column
                v-for="item in tableColumnsLog"
                :prop="item.prop"
                :key="item.key"
                :cell-style="cellStyle"
                :label="item.label"
                :width="item.width"
                :align="item.align"
                :formatter="item.formatter">
                </el-table-column>
            </el-table>
        </div>
        <div class="content-footer">
            <el-pagination
            @size-change="handleSizeChange_log"
            @current-change="handleCurrentChange_log"
            :current-page="curPageIndexLog"
            :page-sizes="[20, 50, 100, 200]"
            :page-size="pageSizeLog"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableTotalLog">
        </div>
    </div>
</div>