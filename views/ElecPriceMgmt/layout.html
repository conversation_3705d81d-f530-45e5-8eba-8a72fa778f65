<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>电价管理</title>
    <!-- <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <link rel="stylesheet" href="../../scripts/lib/element-ui/theme-chalk/index.css">
    <link rel="stylesheet" href="../../css/elecPriceMgmt/index.css">
    <script type="text/javascript" src="../../scripts/lib/jquery-2.0.0.min.js"></script>
    <script type=“text/javascript” src="../../scripts/lib/vue-2.7.16.js"></script>
    <script type="text/javascript" src="../../scripts/lib/element-ui/index.js"></script>
    <script type="text/javascript" src="../../scripts/tool/asynTool.js"></script>
    <script type="text/javascript" src="/script/elecPriceMgmt/controller.js"></script>

    <script type="text/javascript" src="/script/elecPriceMgmt/event/add_event.js"></script>
    <script type="text/javascript" src="/script/elecPriceMgmt/model.js"></script> -->
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <link rel="stylesheet" href="../../scripts/lib/element-ui/theme-chalk/index.css">
    <link rel="stylesheet" href="../../css/elecPriceMgmt/index.css">
    <link rel="stylesheet" href="../../css/elecPriceMgmt/editLog.css">
    <script type="text/javascript" src="../../scripts/lib/jquery-2.0.0.min.js"></script>
    <script type="text/javascript" src="../../scripts/lib/vue-2.7.16.js"></script>
    <script type="text/javascript" src="../../scripts/lib/element-ui/index.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pconst.js"></script>
    <script type="text/javascript" src="../../scripts/tool/pajax.js"></script>
    <script type="text/javascript" src="../../scripts/tool/asynTool.js"></script>
    <script type="text/javascript" src="/script/elecPriceMgmt/controller.js"></script>
    <script type="text/javascript" src="/script/elecPriceMgmt/event/add_event.js"></script>
    <script type="text/javascript" src="/script/elecPriceMgmt/model.js"></script>
</head>
<body>
    <div id="elecPriceMgmt" class="container-elecPriceMgmt">
        <div class="mainBody mainBodyShow" :style="{display: 'block', height: '100%'}">
            <%include childPage/editLog.html%>
            <div class="content" style="height: 100%" v-show="currentPage == 'elecPriceMgmt'">
                <div class="content-header">
                    <div class="header-left">
                        <div class="select-number">
                            <span>已选：</span>
                            <span style="color: #A0A0A0;">{{tenantTableSelection.length}}/{{tableData.length}}</span>
                        </div>
                        <!-- 查询条件 -->
                        <el-form
                        :inline="true"
                        v-model="tenantTableSearchForm"
                        class="search-condition-form"
                        size="small">
                            <el-form-item>
                                <el-input
                                v-model="tenantTableSearchForm.keyword"
                                prefix-icon="el-icon-search"
                                placeholder="搜索租户名称/电表ID"
                                clearable
                                @keyup.enter.native="searchEnter">
                                </el-input>
                            </el-form-item>
                            <el-form-item label="状态:">
                                <el-select v-model="tenantTableSearchForm.status" @change="tableSearchStatusChange">
                                    <el-option v-for="(item, index) in statusOptions" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item>
                                <el-button size="small" type="primary" @click="tenantTableSearchClick" :loading="tenantTableLoading">查询</el-button>
                            </el-form-item>
                        </el-form>
                    </div>
                    <div class="header-right">
                        <el-button size="small" @click="logEntryClick">日 志</el-button>
                        <el-button
                            v-if="operationPermissions.BatchModifyElectricityPrice"
                            size="small"
                            icon="el-icon-edit-outline"
                            type="primary"
                            :disabled="!tenantTableSelection.length"
                            @click="batchEditEntryClick">
                            批量修改电价
                        </el-button>
                    </div>                    
                </div>
                <div class="content-body">
                    <el-table
                    :data="tableData"
                    style="width: 100%"
                    @selection-change="tenantTableSelectionChange"
                    :cell-style="cellStyle"
                    v-loading="tenantTableLoading"
                    element-loading-text="预计数据加载时间较长，请耐心等待..."
                    row-key="tenantId">
                        <el-table-column
                        type="selection"
                        width="50">
                        </el-table-column>
                        <el-table-column
                        v-for="item in tableColumns"
                        :prop="item.prop"
                        :key="item.key"
                        :label="item.label"
                        :width="item.width || 'auto'"
                        :min-width="item.minWidth || ''"
                        :align="item.align"
                        :formatter="item.formatter">
                        </el-table-column>
                        <el-table-column label="操作" :align="'center'" width="120px">
                            <template slot-scope="scope">
                                <el-button
                                v-if="operationPermissions.ModifyElectricityPrice"
                                type="text"
                                @click="singleEditEntryClick(scope.$index, scope.row)">
                                    编辑
                                </el-button>
                                <el-button type="text" @click="logEntryClick(scope.$index, scope.row)">查看>></el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div class="content-footer">
                    <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="curPageIndex"
                    :page-sizes="[20, 50]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="tableTotal">
                </div>
                <el-dialog
                custom-class="price-edit-dialog"
                :visible.sync="editDialogVisible"
                :close-on-click-modal="false"
                v-loading="editDialogLoadiong"
                width="500px">
                    <div slot="title" class="dialog-title">
                         &nbsp;&nbsp;<i class="el-icon-info" style="font-size: 24px;color: #4389FD;"></i> &nbsp;&nbsp;
                        {{editDialogType === 0 ? '修改租户电价' : '批量修改电价'}}
                    </div>
                    <div class="dialog-body">
                        <el-form
                        :model="editForm"
                        label-width="80px"
                        
                        size="small">
                            <el-form-item label="修改电价:">
                                <el-input-number
                                style="width: 150px;display: inline-block;"
                                controls-position="right"
                                :min="0"
                                :step="0.001"
                                :precision="4"
                                v-model="editForm.price">
                                </el-input-number>
                                <span>&nbsp;&nbsp;元/kWh</span>
                            </el-form-item>
                            <el-form-item label="生效时间:">
                                <el-col :span="16">
                                    <el-form-item prop="effectiveTime">
                                        <el-date-picker
                                        v-model="editForm.effectiveTime"
                                        @change="handleEffectiveTimeChange"
                                        :picker-options="pickerOptions"
                                        type="datetime"
                                        placeholder="选择日期时间">
                                        </el-date-picker>       
                                    </el-form-item>
                                </el-col>
                                <el-col class="line" :span="1"></el-col>
                                <el-col :span="7" >
                                    <el-form-item prop="effectiveType">
                                        <el-radio
                                        v-model="editForm.effectiveType"
                                        @change="handleEffectiveTypeChange"
                                        :label="1">
                                            立即生效
                                        </el-radio>
                                    </el-form-item>
                                </el-col>                                
                            </el-form-item>
                        </el-form>
                        <div class="tips-info">
                            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;电价方案作用范围需同时满足以下条件:</p>
                            <p style="font-size:13px;">&nbsp;&nbsp;&nbsp;&nbsp;1、已激活的租户;</p>
                            <p style="font-size:13px;">&nbsp;&nbsp;&nbsp;&nbsp;2、软充表扣计费方式;</p>
                            <p style="font-size:13px;">&nbsp;&nbsp;&nbsp;&nbsp;3、仅针对均时电表;</p>
                            <p style="font-size:13px;">&nbsp;&nbsp;&nbsp;&nbsp;4、电表支持修改电价、查询电价。</p>
                        </div>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button size="small" @click="editDialogVisible = false">取 消</el-button>
                        <el-button :loading="editDialogLoadiong" size="small" type="primary" @click="editDialogConfirm">确 定</el-button>
                    </span>
                </el-dialog>
                <!-- 修改租户电价确认框 -->
                <!-- <el-dialog
                    v-loading="rehandleConfirmLoading"
                    custom-class="closeorder-dialog"
                    :visible.sync="editConfirmDialogVisible"
                    width="30%">
                    <div slot="title" class="dialog-title" style="font-weight: 600; line-height: 24px;">
                        &nbsp;&nbsp;<i class="el-icon-info" style="font-size: 24px;color: #4389FD;"></i> &nbsp;&nbsp;是否确认{{editDialogType === 0 ? '' : '批量'}}修改租户电价
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button size="small" @click="editConfirmDialogVisible = false">取 消</el-button>
                        <el-button size="small" type="primary" @click="editConfirmEnsureClick">确 定</el-button>
                    </span>
                </el-dialog> -->
            </div>
        </div>
    </div>
</body>
</html>
