﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>昆山吾悦租户充值平台</title>
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <script type="text/javascript" src="../../scripts/lib/math.js"></script>
    <script type="text/javascript" src="../../scripts/lib/jquery-2.0.0.min.js"></script>
    <link rel="stylesheet" href="../../pcontrol/css/flatBlueSeries_min.css" />
    <link rel="stylesheet" href="../../css/frame/login.css">
    <script type="text/javascript">
        function inputFocus() {
            $('#emErr').text('');
            $('#txtName,#txtPass').removeClass('input-error');
        }

        function login() {
            var name = document.getElementById('txtName').value;
            var pass = document.getElementById('txtPass').value;
            if (!name && !pass) {
                $('#txtName,#txtPass').addClass('input-error');
                $('#emErr').text('用户名和密码不可为空！');
                return;
            }

            var isValid = true;
            if (!name) {
                $('#txtName').addClass('input-error');
                $('#emErr').text('用户名不可为空！');
                isValid = false;
            } else
                $('#txtName').removeClass('input-error');

            if (!pass) {
                $('#txtPass').addClass('input-error');
                $('#emErr').text('密码不可为空！');
                isValid = false;
            } else
                $('#txtPass').removeClass('input-error');

            if (isValid == false) return;
            $('#emErr').text('');

            sessionStorage.setItem("key", name);

            //request.getSession().setAttribute("user",name);//把当前用户存入名为user的session中
            //alert(value)
            formLogin.submit();
        };

        var currClientHeight, currClientWidth, currImgWidth, imgTimeOut, middlePart, imgMoveOrien;
        var orienObj = { left: 'left', right: 'right' };
        $(function () {
            //首先根据当前可视区域高对背景图做等比缩放
            var clientHeight = currClientHeight = document.body.clientHeight;
            var clientWidth = currClientWidth = document.body.clientWidth;
            var imgWidth = 4446, imgHeight = 1080;
            var scale = Math.division(clientHeight, imgHeight);
            imgHeight = clientHeight;
            var newImgWidth = Math.multiplication(imgWidth, scale);
            imgWidth = currImgWidth = Math.max(newImgWidth, clientWidth);

            var imgTarget = document.getElementById('imgback');
            imgTarget.width = imgWidth;
            imgTarget.height = imgHeight;

            var middle = Math.subtraction(imgWidth, clientWidth);
            var left = Math.division(middle, 2);
            imgTarget.style.left = (left == 0 ? 0 : '-' + left) + 'px';
            middlePart = Math.division(currClientWidth, 2);
        });

        function imgMouseMove(event) {
            var x = event.clientX;
            var part = x <= middlePart ? orienObj.left : orienObj.right;
            imgMoveOrien = part == orienObj.left ? orienObj.right : orienObj.left;
        };

        function imgMouseOver(event) {
            $('#imgback').on({
                mousemove: imgMouseMove
            });

            if (currImgWidth == currClientWidth) return;
            var x = event.clientX;

            var part = x <= middlePart ? orienObj.left : orienObj.right;
            imgMoveOrien = part == orienObj.left ? orienObj.right : orienObj.left;
            imgAnimate();
        };

        function imgMouseOut(event) {
            if (imgTimeOut) clearTimeout(imgTimeOut);
            $('#imgback').off('mousemove');
        };

        function imgAnimate() {
            var minLeft = Math.subtraction(currClientWidth, currImgWidth);
            var imgTarget = document.getElementById('imgback');
            var currImgLeft = parseFloat(imgTarget.style.left);
            move();
            function move() {
                switch (imgMoveOrien) {
                    case orienObj.left:
                        currImgLeft = Math.subtraction(currImgLeft, 1);
                        if (currImgLeft < minLeft) {
                            return;
                            imgMoveOrien = orienObj.right;
                            return arguments.callee();
                        }
                        break;
                    case orienObj.right:
                        currImgLeft = Math.summation(currImgLeft, 1);
                        if (currImgLeft > 0) {
                            return;
                            imgMoveOrien = orienObj.left;
                            return arguments.callee();
                        }
                        break;
                }
                return imgTarget.style.left = currImgLeft + 'px';
            }

            imgTimeOut = setTimeout(function () {
                imgAnimate();
            }, 35);
        };

        function enterLogin(event) {
            if (event.keyCode == 13) document.getElementById('btnLogin').click();
        };
    </script>
</head>
<body>
    <div class="v6s-login-wrap">
        <div class="v6s-login-con">
            <div class="v6s-login-bg">
                <div class="login-title">昆山吾悦租户充值平台</div>
                <form method="post" action="/login" id="formLogin">

                    <div class="login-con">
                        <input type="text" placeholder="用户名" name="name" id="txtName" class="<%=!errTip?'':'input-error'%>" onfocus="inputFocus()" />
                        <input type="password" placeholder="密码" name="pass" id="txtPass" class="<%=!errTip?'':'input-error'%>" onfocus="inputFocus()" onkeyup="enterLogin(event)" />
                        <div class="error-tip"><i></i><em id="emErr"><%=errTip %></em></div>
                    </div>
                    <input type="button" value="登录" class="login-button" onclick="login()" id="btnLogin" />
                </form>
            </div>
        </div>
        <img class="login-img" src="<%=backImg%>" id="imgback" onmouseenter="imgMouseOver(event)" onmouseleave="imgMouseOut(event)" />
        <div class="jing-ICP">技术支持：吾盛（上海）能源科技有限公司</div>
    </div>
</body>
</html>
