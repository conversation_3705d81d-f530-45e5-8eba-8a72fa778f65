.overviewPage {
    width: 100%;
    height: calc(100vh - 123px);
    border: 1px solid #d9e2e8;
    box-sizing: border-box;
    padding: 8px 0 10px 8px;
    overflow-x: scroll;
    overflow-y: scroll;
    margin-top: 6px;
}

#overviewPage::-webkit-scrollbar-thumb:horizontal {
    width:15px;
    height: 15px;
    background: #c8cedb;
    border-image: none;
    border-radius: 10px;
    border-width: 0;
}
#overviewPage::-webkit-scrollbar-thumb:horizontal:hover {
    border-image: none;
    background: #a6a6a6;
}



/*列表项*/
#floorTable>li {
    width: 100%;
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    flex-wrap: nowrap;
    /*IE10*/
    display: -ms-flexbox;
    -ms-flex-wrap: nowrap;
}

.floorNo,
.piece {
    display: -webkit-box;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-align: stretch;
    align-items: stretch;
    box-sizing: border-box;
    width: 80px;
    margin: 2px 1px 1px;
    border-radius: 2px;
    /*IE10*/
    display: -ms-flexbox;
    -ms-flex-direction: column;
    -ms-flex-align: stretch;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    -ms-flex-preferred-size: 80px;
    flex-basis: 80px;
}

.floorNo {
    padding: 0 5px;
    background: #C8CEDB;
    -ms-flex-preferred-size: 56px;
    flex-basis: 56px;
    width: 56px;
    margin-right: 2px;
}

.floorNo>span {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
    height: 100%;
    min-height: 30px;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #fff;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.piece>div {
    display: -webkit-box;
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    height: 100%;
    min-height: 30px;
    font-size: 14px;
    color: #4a5568;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding-left: 10px;
    padding-right: 10px;
    box-sizing: border-box;
    position: relative;
    cursor: pointer;
    border-radius: 2px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    /*IE10*/
    display: -ms-flexbox;
    -ms-flex-align: center;
}

.piece>div:hover {
    overflow: visible !important;
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.pieceItem .tentName {
    width: 100%;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-overflow: ellipsis;
    /* IE/Safari */
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    /* Opera */
    font-size: 14px;
    text-align: center;
}

.across {
    position: absolute;
    width: 100%;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding-left: 10px;
    padding-right: 10px;
    box-sizing: border-box;
    cursor: pointer;
    top: 0;
    left: 0;
    z-index: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-overflow: ellipsis;
    /* IE/Safari */
    -ms-text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    /* Opera */
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.across:hover {
    color: #fff;
    background: #01A3C9;
}

/*报警状态*/
.piece>div.alarmStatus,
div.across.alarmStatus {
    background: #FDEEEE;
    color: #F77C7C;
}

.piece>div.alarmStatus:hover,
div.across.alarmStatus:hover {
    color: #fff;
    background: #F77C7C;
}

.alarmColor {
    color: #ff7b7b !important;
    font-weight: bold;
}

.noalarm {
    color: #6d6d6d;
    margin: 5px 0;
    padding-left: 5px;
    text-align: left;
}

.alarmContent {
    display: none;
    position: absolute;
    z-index: 50;
    box-sizing: border-box;
    top: 100%;
    max-height: 250px;
}

.basicsposition {
    left: 50%;
    transform: translateX(-50%);
}

/*边缘的单元格*/
.edge {
    bottom: auto;
    top: 100%;
}


.alarmContent .alarmContentWrap {
    border: 1px solid #cacccc;
    background: #ffffff;
    border-radius: 4px;
    position: relative;
    box-shadow: 0px 1px 6px rgba(0, 0, 0, 0.2);
    color: #333;
    min-width: 272px;
    font-family: Arial;
}



.alarmContent .alarmName {
    color: #333333;
    font-weight: 600;
}

.alarmContent .alarmNO {
    color: #6d6d6d;
    font-size: 12px;
    line-height: 12px;
    margin: 0 5px;
}

.alarmContent .alarmContentWrap>h4 {
    margin: 0 10px;
    height: 32px;
    line-height: 32px;
    box-sizing: border-box;
    border-bottom: 1px solid #d2d2d2;
    max-width: 340px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
}

.alarmDetail {
    min-width: 272px;
    max-height: 174px;
    overflow: auto;
}

.alarmDetail .hasalarm>h4 {
    margin: 5px 0;
    text-align: left;
}

.alarmDetail .hasalarm>h4>span:first-child {
    margin-left: 10px;
}

.alarmDetail .hasalarm>h4 .electricMeter {
    margin-left: 5px;
    color: #666666;
}

.alarmDetail .hasalarm>h4 .electricMeter>em {
    font-size: 12px;
}

.alarmDetail .hasalarm>h4 .alarm_detail {
    font-size: 12px;
    color: #999999;
    overflow: hidden;
    min-width: 246px;
    margin-top: 4px;
}

.alarmDetail .hasalarm>h4 .alarm_detail li {
    float: left;
    display: inline-block;
    margin: 0 10px;
}

#modal-gAlarmset .per-modal-custom {
    border-radius: 2px;
}

#modal-gAlarmset .per-modal-custom_title {
    border-radius: 2px 2px 0 0;
}

#modal-gAlarmset .gaTitle {
    text-align: center;
    height: 40px;
    font-size: 24px;
    line-height: 40px;
    margin-bottom: 20px;
    margin-top: -40px;
}

#modal-gAlarmset ._gaTitle {
    text-align: center;
    height: 40px;
    line-height: 40px;
    margin-top: -40px;
    font-size: 24px;
}

#modal-gAlarmset .titleCombobx {
    width: 300px;
    margin: 0 auto;
}

#modal-gAlarmset .gaBody {
    overflow: auto;
    overflow: overlay;
    padding: 10px 90px;
    max-height: 472px;
    border-top: 1px dashed #e3e3e3;
    border-bottom: 1px dashed #e3e3e3;
    box-sizing: border-box;
    margin-bottom: 24px;
}

/*浅黑色*/
.lightBlack {
    color: #ADB0C1 !important;
}

.gaBPartCon {
    max-height: calc(100% - 130px) !important;
    overflow: auto;
}

.gaBPart {
    width: 100%;
    min-width: 250px;
    border: 1px solid #cccccc;
    margin: 10px 0;
    border-radius: 4px;
    box-sizing: border-box;
}

.gabpTitle {
    padding: 10px;
    background: #f6f6f6;
}

.gabpBody {
    padding: 10px;
    border-top: 1px solid #cccccc;
}

.gabpBody p {
    margin: 15px 0;
}

.gabpBody .gainput {
    display: inline-block;
    position: relative;
    width: 86px;
    height: 28px;
    margin: 0 5px;
}

.gabpBody .gatxt {
    display: inline-block;
    margin-right: -3px;
}

.gabpBody .gatxt>span:first-child {
    margin: 0 10px;
    font-weight: 400;
    font-style: normal;
    font-size: 16px;
    color: #199ED8;
}

.gabpBody .gainput>input {
    margin: 0;
    width: 100%;
    padding-right: 35px;
    box-sizing: border-box;
}

.gabpBody .gainput>span {
    display: inline-block;
    border-left: 1px solid #cacaca;
    float: left;
    height: 26px;
    color: #648098;
    line-height: 28px;
    text-align: center;
    box-sizing: border-box;
    padding: 0 8px;
    position: absolute;
    right: 1px;
    top: 1px;
    background: #fff;
    font-family: Arial, '微软雅黑';
}

.muteSet {
    margin: 15px 0;
}

.muteDuration {
    line-height: 30px;
}

.muteSet .muteSetswitch {
    margin: 0 20px;
}

.muteDuration .setDay {
    width: 142px;
    margin: 0 20px;
}

#float-diyAlarmset .gAlarmsetWrap .gaFoot {
    margin-bottom: 20px;
    text-align: center;
}

#float-diyAlarmset .gAlarmsetWrap .gaFoot>div {
    display: inline-block;
    width: 100px;
    margin: 10px 20px;
}

#chart_Moth {
    position: absolute;
    top: 89px;
    bottom: 20px;
    left: 20px;
    right: 20px;
}

.searchHint {
    width: 178px;
    max-height: 200px;
    overflow: auto;
    padding: 5px 10px;
    background: white;
    border: 1px solid #cacaca;
    border-top: none;
    position: absolute;
    top: 31px;
    z-index: 6;
    cursor: pointer;
    display: none;
}

.searchHint ul {
    max-height: 200px;
    line-height: 20px;
    font-size: 14px;
    position: relative;
    -ms-overflow-x: hidden;
}

.searchHint ul li {
    width: 100%;
    height: 30px;
    line-height: 30px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.searchHint ul li.nodata {
    height: 200px;
}


/*设置报警门限 头部标题  */

#windowTemp .per-modal-custom_title {
    font-size: 16px;
}


/*设置报警门限中   输入框的属性  */

#windowTemp .per-input-basic {
    width: 70px;
    height: 30px;
    display: inline-block;
    margin: 0 5px;
}


/*设置报警门限中   确认按钮大小  */
#determine_custom,
#determine_globle {
    float: left;
    width: 80px;
    height: 30px;
    font-size: 14px;
}


/*设置报警门限中   取消按钮大小  */
#cancel_custom,
#cancel_global {
    float: right !important;
    width: 80px;
    height: 30px;
    font-size: 14px;
}

/*设置报警门限中  字体，位置的设置  */
.police {
    box-sizing: border-box;
    line-height: 30px;
    font-size: 14px;
}

.police div {
    box-sizing: border-box;
}

.police .plice_sel {
    width: 700px;
    padding: 50px 72px 16px;
    max-height: 490px;
    overflow: auto;
}

.police .plice_sel>div>div {
    padding-left: 18px;
}

.police .police_tit {
    position: relative;
    height: 30px;
    line-height: 30px;
    background-color: #F6F6F6;
    border-radius: 3px 3px 0 0;
}

.police .police_tit>div {
    height: 16px;
    position: absolute;
    right: 18px;
    top: 7px;
}

.police .police_tit>div>div {
    vertical-align: top;
}

.police .plice_sel>div {
    margin-bottom: 10px;
    border: 1px solid #CCCCCC;
    border-radius: 4px;
}

.police .plice_sel>div:last-child {
    margin-bottom: 0;
}

.police .police_con {
    position: relative;
    height: 49px;
    padding: 9px 0;
    border-top: 1px solid #CCCCCC;

}

.police .police_con .btn {
    position: absolute;
    right: 18px;
    top: 7px;
}

.police .police_con>div {
    line-height: 30px;
    float: left;
}

.police .police_con .police_con_mid {
    margin-left: 12px;
    margin-right: 10px;
}

.police .police_con .police_con_mid>div {
    width: 50px !important;
}

.police .police_con .police_con_mid>div>input {
    text-align: center;
}

.police .police_con .police_con_mid .error-tip {
    left: 130px;
    bottom: 4px;
}

.police .plice_ope {
    height: 109px;
    padding: 38px 248px 41px;
}

#modal-gAlarmset .per-modal-custom_con {
    max-height: 600px;
}